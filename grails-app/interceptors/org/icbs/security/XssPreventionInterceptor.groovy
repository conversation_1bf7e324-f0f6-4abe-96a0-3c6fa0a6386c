package org.icbs.security

class XssPreventionInterceptor {
    
    XssPreventionInterceptor() {
        matchAll()
    }
    
    boolean before() {
        // Sanitize all request parameters to prevent XSS
        sanitizeParams(params)
        return true
    }
    
    private void sanitizeParams(Map params) {
        params.each { key, value ->
            if (value instanceof String) {
                params[key] = sanitizeInput(value)
            } else if (value instanceof List) {
                params[key] = value.collect { item ->
                    item instanceof String ? sanitizeInput(item) : item
                }
            } else if (value instanceof Map) {
                sanitizeParams(value)
            }
        }
    }
    
    private String sanitizeInput(String input) {
        if (!input) return input
        
        return input
            // Remove script tags
            .replaceAll("(?i)<script[^>]*>.*?</script>", "")
            .replaceAll("(?i)<script[^>]*>", "")
            .replaceAll("(?i)</script>", "")
            
            // Remove javascript: protocol
            .replaceAll("(?i)javascript:", "")
            .replaceAll("(?i)vbscript:", "")
            .replaceAll("(?i)data:", "")
            
            // Remove event handlers
            .replaceAll("(?i)\\s*on\\w+\\s*=", "")
            
            // Remove iframe tags
            .replaceAll("(?i)<iframe[^>]*>.*?</iframe>", "")
            .replaceAll("(?i)<iframe[^>]*>", "")
            
            // Remove object and embed tags
            .replaceAll("(?i)<object[^>]*>.*?</object>", "")
            .replaceAll("(?i)<embed[^>]*>.*?</embed>", "")
            
            // Remove style tags with javascript
            .replaceAll("(?i)<style[^>]*>.*?expression\\s*\\(.*?\\).*?</style>", "")
            
            // Remove dangerous attributes
            .replaceAll("(?i)\\s*style\\s*=\\s*[\"'][^\"']*expression\\s*\\([^\"']*[\"']", "")
            
            // Encode remaining HTML entities
            .replace("&", "&amp;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace("\"", "&quot;")
            .replace("'", "&#x27;")
            .replace("/", "&#x2F;")
    }
    
    boolean after() { true }
    
    void afterView() {
        // Additional security headers
        response.setHeader("X-Content-Type-Options", "nosniff")
        response.setHeader("X-Frame-Options", "DENY")
        response.setHeader("X-XSS-Protection", "1; mode=block")
        response.setHeader("Referrer-Policy", "strict-origin-when-cross-origin")
        
        // Content Security Policy
        def csp = "default-src 'self'; " +
                  "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
                  "style-src 'self' 'unsafe-inline'; " +
                  "img-src 'self' data:; " +
                  "font-src 'self'; " +
                  "connect-src 'self'; " +
                  "frame-ancestors 'none';"
        response.setHeader("Content-Security-Policy", csp)
    }
}
