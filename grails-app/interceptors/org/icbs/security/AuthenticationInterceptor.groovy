package org.icbs.security

import grails.web.api.WebAttributes
import groovy.util.logging.Slf4j
import io.jsonwebtoken.Claims
import org.springframework.http.HttpStatus

/**
 * Authentication Interceptor
 * Handles JWT-based authentication for API endpoints and web requests
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 7.0
 */
@Slf4j
class AuthenticationInterceptor implements WebAttributes {
    
    JwtTokenService jwtTokenService
    SecurityAuditService securityAuditService
    
    // Define which controllers/actions require authentication
    private static final List<String> EXCLUDED_CONTROLLERS = [
        'login', 'authentication', 'public', 'health', 'error'
    ]
    
    private static final List<String> EXCLUDED_ACTIONS = [
        'login', 'authenticate', 'logout', 'forgotPassword', 'resetPassword', 'health'
    ]
    
    private static final List<String> API_PREFIXES = [
        '/api/', '/rest/', '/ws/'
    ]
    
    AuthenticationInterceptor() {
        // Apply to all controllers except excluded ones
        matchAll()
            .excludes(controller: 'login')
            .excludes(controller: 'authentication')
            .excludes(controller: 'public')
            .excludes(controller: 'health')
            .excludes(controller: 'error')
    }
    
    boolean before() {
        // Skip authentication for excluded controllers and actions
        if (isExcluded()) {
            return true
        }
        
        try {
            // Determine if this is an API request
            boolean isApiRequest = isApiRequest()
            
            if (isApiRequest) {
                return handleApiAuthentication()
            } else {
                return handleWebAuthentication()
            }
            
        } catch (Exception e) {
            log.error("Authentication error for ${request.requestURI}", e)
            
            // Log security event
            securityAuditService?.logSecurityEvent([
                eventType: 'AUTHENTICATION_ERROR',
                eventDescription: "Authentication error: ${e.message}",
                ipAddress: request.remoteAddr,
                userAgent: request.getHeader('User-Agent'),
                requestUri: request.requestURI,
                result: 'ERROR',
                errorMessage: e.message
            ])
            
            if (isApiRequest()) {
                renderUnauthorizedJson("Authentication error")
            } else {
                redirect(controller: 'login', action: 'index')
            }
            return false
        }
    }
    
    /**
     * Check if current request should be excluded from authentication
     */
    private boolean isExcluded() {
        String controllerName = controllerName?.toLowerCase()
        String actionName = actionName?.toLowerCase()
        
        // Check excluded controllers
        if (controllerName in EXCLUDED_CONTROLLERS) {
            return true
        }
        
        // Check excluded actions
        if (actionName in EXCLUDED_ACTIONS) {
            return true
        }
        
        // Check for health check endpoints
        if (request.requestURI?.contains('/health') || 
            request.requestURI?.contains('/actuator')) {
            return true
        }
        
        // Check for static resources
        if (request.requestURI?.matches(/.*\.(css|js|png|jpg|jpeg|gif|ico|woff|woff2|ttf|svg)$/)) {
            return true
        }
        
        return false
    }
    
    /**
     * Check if this is an API request
     */
    private boolean isApiRequest() {
        String uri = request.requestURI?.toLowerCase()
        String contentType = request.contentType?.toLowerCase()
        String accept = request.getHeader('Accept')?.toLowerCase()
        
        // Check URI patterns
        if (API_PREFIXES.any { uri?.startsWith(it) }) {
            return true
        }
        
        // Check content type
        if (contentType?.contains('application/json') || 
            contentType?.contains('application/xml')) {
            return true
        }
        
        // Check Accept header
        if (accept?.contains('application/json') || 
            accept?.contains('application/xml')) {
            return true
        }
        
        // Check for AJAX requests
        if (request.getHeader('X-Requested-With') == 'XMLHttpRequest') {
            return true
        }
        
        return false
    }
    
    /**
     * Handle API authentication using JWT tokens
     */
    private boolean handleApiAuthentication() {
        String authHeader = request.getHeader('Authorization')
        String token = jwtTokenService.extractTokenFromHeader(authHeader)
        
        if (!token) {
            log.debug("No JWT token found in Authorization header for API request")
            renderUnauthorizedJson("Missing or invalid authorization token")
            return false
        }
        
        if (!jwtTokenService.validateToken(token)) {
            log.debug("Invalid JWT token for API request")
            
            // Log failed authentication
            securityAuditService?.logSecurityEvent([
                eventType: 'API_AUTH_FAILURE',
                eventDescription: "Invalid JWT token for API request",
                ipAddress: request.remoteAddr,
                userAgent: request.getHeader('User-Agent'),
                requestUri: request.requestURI,
                result: 'FAILURE'
            ])
            
            renderUnauthorizedJson("Invalid or expired token")
            return false
        }
        
        // Extract user information from token
        try {
            Claims claims = jwtTokenService.getClaimsFromToken(token)
            String username = claims.subject
            Long userId = claims.get('userId', Long.class)
            String sessionId = claims.get('sessionId', String.class)
            
            // Set user context for the request
            request.setAttribute('currentUser', username)
            request.setAttribute('currentUserId', userId)
            request.setAttribute('currentSessionId', sessionId)
            request.setAttribute('jwtClaims', claims)
            request.setAttribute('authenticationMethod', 'JWT')
            
            // Update session activity if session ID is present
            if (sessionId) {
                updateSessionActivity(sessionId)
            }
            
            // Log successful authentication
            securityAuditService?.logSecurityEvent([
                eventType: 'API_AUTH_SUCCESS',
                eventDescription: "Successful JWT authentication for API request",
                username: username,
                userId: userId.toString(),
                sessionId: sessionId,
                ipAddress: request.remoteAddr,
                userAgent: request.getHeader('User-Agent'),
                requestUri: request.requestURI,
                result: 'SUCCESS'
            ])
            
            return true
            
        } catch (Exception e) {
            log.error("Error processing JWT token claims", e)
            renderUnauthorizedJson("Token processing error")
            return false
        }
    }
    
    /**
     * Handle web authentication using sessions
     */
    private boolean handleWebAuthentication() {
        // Check for existing session
        def currentUser = session.getAttribute('currentUser')
        def sessionId = session.id
        
        if (!currentUser) {
            log.debug("No authenticated user found in session for web request")
            
            // Log failed authentication
            securityAuditService?.logSecurityEvent([
                eventType: 'WEB_AUTH_FAILURE',
                eventDescription: "No authenticated user in session",
                sessionId: sessionId,
                ipAddress: request.remoteAddr,
                userAgent: request.getHeader('User-Agent'),
                requestUri: request.requestURI,
                result: 'FAILURE'
            ])
            
            // Store original URL for redirect after login
            session.setAttribute('originalUrl', request.requestURI)
            redirect(controller: 'login', action: 'index')
            return false
        }
        
        // Validate session is still active
        if (!isSessionValid(sessionId, currentUser)) {
            log.debug("Session validation failed for user: ${currentUser}")
            
            // Clear invalid session
            session.invalidate()
            
            securityAuditService?.logSecurityEvent([
                eventType: 'SESSION_INVALID',
                eventDescription: "Session validation failed",
                username: currentUser,
                sessionId: sessionId,
                ipAddress: request.remoteAddr,
                userAgent: request.getHeader('User-Agent'),
                requestUri: request.requestURI,
                result: 'FAILURE'
            ])
            
            redirect(controller: 'login', action: 'index')
            return false
        }
        
        // Set user context for the request
        request.setAttribute('currentUser', currentUser)
        request.setAttribute('currentSessionId', sessionId)
        request.setAttribute('authenticationMethod', 'SESSION')
        
        // Update session activity
        updateSessionActivity(sessionId)
        
        return true
    }
    
    /**
     * Validate if session is still active and valid
     */
    private boolean isSessionValid(String sessionId, String username) {
        try {
            UserSession userSession = UserSession.findBySessionIdAndUsername(sessionId, username)
            
            if (!userSession || !userSession.isActive) {
                return false
            }
            
            // Check if session is expired
            if (userSession.isExpired()) {
                userSession.expire()
                userSession.save()
                return false
            }
            
            return true
            
        } catch (Exception e) {
            log.error("Error validating session", e)
            return false
        }
    }
    
    /**
     * Update session activity timestamp
     */
    private void updateSessionActivity(String sessionId) {
        try {
            UserSession userSession = UserSession.findBySessionId(sessionId)
            if (userSession) {
                userSession.updateActivity(request.requestURI)
                userSession.save()
            }
        } catch (Exception e) {
            log.error("Error updating session activity", e)
        }
    }
    
    /**
     * Render unauthorized JSON response for API requests
     */
    private void renderUnauthorizedJson(String message) {
        response.status = HttpStatus.UNAUTHORIZED.value()
        response.contentType = 'application/json'
        
        def errorResponse = [
            error: 'Unauthorized',
            message: message,
            status: HttpStatus.UNAUTHORIZED.value(),
            timestamp: new Date().format("yyyy-MM-dd'T'HH:mm:ss.SSSZ"),
            path: request.requestURI
        ]
        
        render(contentType: 'application/json') {
            errorResponse
        }
    }
    
    boolean after() {
        // Log request completion if user is authenticated
        def currentUser = request.getAttribute('currentUser')
        if (currentUser) {
            securityAuditService?.logSecurityEvent([
                eventType: 'REQUEST_COMPLETED',
                eventDescription: "Request completed successfully",
                username: currentUser,
                sessionId: request.getAttribute('currentSessionId'),
                ipAddress: request.remoteAddr,
                userAgent: request.getHeader('User-Agent'),
                requestUri: request.requestURI,
                requestMethod: request.method,
                result: 'SUCCESS',
                processingTime: System.currentTimeMillis() - (request.getAttribute('requestStartTime') ?: System.currentTimeMillis())
            ])
        }
        
        return true
    }
    
    void afterView() {
        // Clean up request attributes
        request.removeAttribute('currentUser')
        request.removeAttribute('currentUserId')
        request.removeAttribute('currentSessionId')
        request.removeAttribute('jwtClaims')
        request.removeAttribute('authenticationMethod')
    }
}
