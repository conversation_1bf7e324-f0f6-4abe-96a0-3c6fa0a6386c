/*! alertifyjs - v1.6.1 - <PERSON> <<PERSON>@alertifyjs.com> (http://alertifyjs.com) */
!function(a){"use strict";function b(a,b){a.className+=" "+b}function c(a,b){for(var c=a.className.split(" "),d=b.split(" "),e=0;e<d.length;e+=1){var f=c.indexOf(d[e]);f>-1&&c.splice(f,1)}a.className=c.join(" ")}function d(){return"rtl"===a.getComputedStyle(document.body).direction}function e(){return document.documentElement&&document.documentElement.scrollTop||document.body.scrollTop}function f(){return document.documentElement&&document.documentElement.scrollLeft||document.body.scrollLeft}function g(a){for(;a.lastChild;)a.removeChild(a.lastChild)}function h(a){if(null===a)return a;var b;if(Array.isArray(a)){b=[];for(var c=0;c<a.length;c+=1)b.push(h(a[c]));return b}if(a instanceof Date)return new Date(a.getTime());if(a instanceof RegExp)return b=new RegExp(a.source),b.global=a.global,b.ignoreCase=a.ignoreCase,b.multiline=a.multiline,b.lastIndex=a.lastIndex,b;if("object"==typeof a){b={};for(var d in a)a.hasOwnProperty(d)&&(b[d]=h(a[d]));return b}return a}function i(a,b){var c=a.elements.root;c.parentNode.removeChild(c),delete a.elements,a.settings=h(a.__settings),a.__init=b,delete a.__internal}function j(a,b){return function(){if(arguments.length>0){for(var c=[],d=0;d<arguments.length;d+=1)c.push(arguments[d]);return c.push(a),b.apply(a,c)}return b.apply(a,[null,a])}}function k(a,b){return{index:a,button:b,cancel:!1}}function l(){function a(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c]);return a}function b(a){var b=d[a].dialog;return b&&"function"==typeof b.__init&&b.__init(b),b}function c(b,c,e,f){var g={dialog:null,factory:c};return void 0!==f&&(g.factory=function(){return a(new d[f].factory,new c)}),e||(g.dialog=a(new g.factory,s)),d[b]=g}var d={};return{defaults:n,dialog:function(d,e,f,g){if("function"!=typeof e)return b(d);if(this.hasOwnProperty(d))throw new Error("alertify.dialog: name already exists");var h=c(d,e,f,g);f?this[d]=function(){if(0===arguments.length)return h.dialog;var b=a(new h.factory,s);return b&&"function"==typeof b.__init&&b.__init(b),b.main.apply(b,arguments),b.show.apply(b)}:this[d]=function(){if(h.dialog&&"function"==typeof h.dialog.__init&&h.dialog.__init(h.dialog),0===arguments.length)return h.dialog;var a=h.dialog;return a.main.apply(h.dialog,arguments),a.show.apply(h.dialog)}},closeAll:function(a){for(var b=o.slice(0),c=0;c<b.length;c+=1){var d=b[c];(void 0===a||a!==d)&&d.close()}},setting:function(a,c,d){if("notifier"===a)return t.setting(c,d);var e=b(a);return e?e.setting(c,d):void 0},set:function(a,b,c){return this.setting(a,b,c)},get:function(a,b){return this.setting(a,b)},notify:function(a,b,c,d){return t.create(b,d).push(a,c)},message:function(a,b,c){return t.create(null,c).push(a,b)},success:function(a,b,c){return t.create("success",c).push(a,b)},error:function(a,b,c){return t.create("error",c).push(a,b)},warning:function(a,b,c){return t.create("warning",c).push(a,b)},dismissAll:function(){t.dismissAll()}}}var m={ENTER:13,ESC:27,F1:112,F12:123,LEFT:37,RIGHT:39},n={modal:!0,basic:!1,frameless:!1,movable:!0,moveBounded:!1,resizable:!0,closable:!0,closableByDimmer:!0,maximizable:!0,startMaximized:!1,pinnable:!0,pinned:!0,padding:!0,overflow:!0,maintainFocus:!0,transition:"pulse",autoReset:!0,notifier:{delay:5,position:"bottom-right"},glossary:{title:"AlertifyJS",ok:"OK",cancel:"Cancel",acccpt:"Accept",deny:"Deny",confirm:"Confirm",decline:"Decline",close:"Close",maximize:"Maximize",restore:"Restore"},theme:{input:"ajs-input",ok:"ajs-ok",cancel:"ajs-cancel"}},o=[],p=function(){return document.addEventListener?function(a,b,c,d){a.addEventListener(b,c,d===!0)}:document.attachEvent?function(a,b,c){a.attachEvent("on"+b,c)}:void 0}(),q=function(){return document.removeEventListener?function(a,b,c,d){a.removeEventListener(b,c,d===!0)}:document.detachEvent?function(a,b,c){a.detachEvent("on"+b,c)}:void 0}(),r=function(){var a,b,c=!1,d={animation:"animationend",OAnimation:"oAnimationEnd oanimationend",msAnimation:"MSAnimationEnd",MozAnimation:"animationend",WebkitAnimation:"webkitAnimationEnd"};for(a in d)if(void 0!==document.documentElement.style[a]){b=d[a],c=!0;break}return{type:b,supported:c}}(),s=function(){function l(a){if(!a.__internal){delete a.__init,a.__settings||(a.__settings=h(a.settings)),null===xa&&document.body.setAttribute("tabindex","0");var c;"function"==typeof a.setup?(c=a.setup(),c.options=c.options||{},c.focus=c.focus||{}):c={buttons:[],focus:{element:null,select:!1},options:{}},"object"!=typeof a.hooks&&(a.hooks={});var d=[];if(Array.isArray(c.buttons))for(var e=0;e<c.buttons.length;e+=1){var f=c.buttons[e],g={};for(var i in f)f.hasOwnProperty(i)&&(g[i]=f[i]);d.push(g)}var k=a.__internal={isOpen:!1,activeElement:document.body,timerIn:void 0,timerOut:void 0,buttons:d,focus:c.focus,options:{title:void 0,modal:void 0,basic:void 0,frameless:void 0,pinned:void 0,movable:void 0,moveBounded:void 0,resizable:void 0,autoReset:void 0,closable:void 0,closableByDimmer:void 0,maximizable:void 0,startMaximized:void 0,pinnable:void 0,transition:void 0,padding:void 0,overflow:void 0,onshow:void 0,onclose:void 0,onfocus:void 0},resetHandler:void 0,beginMoveHandler:void 0,beginResizeHandler:void 0,bringToFrontHandler:void 0,modalClickHandler:void 0,buttonsClickHandler:void 0,commandsClickHandler:void 0,transitionInHandler:void 0,transitionOutHandler:void 0,destroy:void 0},l={};l.root=document.createElement("div"),l.root.className=Aa.base+" "+Aa.hidden+" ",l.root.innerHTML=za.dimmer+za.modal,l.dimmer=l.root.firstChild,l.modal=l.root.lastChild,l.modal.innerHTML=za.dialog,l.dialog=l.modal.firstChild,l.dialog.innerHTML=za.reset+za.commands+za.header+za.body+za.footer+za.resizeHandle+za.reset,l.reset=[],l.reset.push(l.dialog.firstChild),l.reset.push(l.dialog.lastChild),l.commands={},l.commands.container=l.reset[0].nextSibling,l.commands.pin=l.commands.container.firstChild,l.commands.maximize=l.commands.pin.nextSibling,l.commands.close=l.commands.maximize.nextSibling,l.header=l.commands.container.nextSibling,l.body=l.header.nextSibling,l.body.innerHTML=za.content,l.content=l.body.firstChild,l.footer=l.body.nextSibling,l.footer.innerHTML=za.buttons.auxiliary+za.buttons.primary,l.resizeHandle=l.footer.nextSibling,l.buttons={},l.buttons.auxiliary=l.footer.firstChild,l.buttons.primary=l.buttons.auxiliary.nextSibling,l.buttons.primary.innerHTML=za.button,l.buttonTemplate=l.buttons.primary.firstChild,l.buttons.primary.removeChild(l.buttonTemplate);for(var m=0;m<a.__internal.buttons.length;m+=1){var n=a.__internal.buttons[m];wa.indexOf(n.key)<0&&wa.push(n.key),n.element=l.buttonTemplate.cloneNode(),n.element.innerHTML=n.text,"string"==typeof n.className&&""!==n.className&&b(n.element,n.className);for(var o in n.attrs)"className"!==o&&n.attrs.hasOwnProperty(o)&&n.element.setAttribute(o,n.attrs[o]);"auxiliary"===n.scope?l.buttons.auxiliary.appendChild(n.element):l.buttons.primary.appendChild(n.element)}a.elements=l,k.resetHandler=j(a,V),k.beginMoveHandler=j(a,$),k.beginResizeHandler=j(a,ea),k.bringToFrontHandler=j(a,z),k.modalClickHandler=j(a,P),k.buttonsClickHandler=j(a,R),k.commandsClickHandler=j(a,D),k.transitionInHandler=j(a,W),k.transitionOutHandler=j(a,X),a.set("title",void 0===c.options.title?u.defaults.glossary.title:c.options.title),a.set("modal",void 0===c.options.modal?u.defaults.modal:c.options.modal),a.set("basic",void 0===c.options.basic?u.defaults.basic:c.options.basic),a.set("frameless",void 0===c.options.frameless?u.defaults.frameless:c.options.frameless),a.set("movable",void 0===c.options.movable?u.defaults.movable:c.options.movable),a.set("moveBounded",void 0===c.options.moveBounded?u.defaults.moveBounded:c.options.moveBounded),a.set("resizable",void 0===c.options.resizable?u.defaults.resizable:c.options.resizable),a.set("autoReset",void 0===c.options.autoReset?u.defaults.autoReset:c.options.autoReset),a.set("closable",void 0===c.options.closable?u.defaults.closable:c.options.closable),a.set("closableByDimmer",void 0===c.options.closableByDimmer?u.defaults.closableByDimmer:c.options.closableByDimmer),a.set("maximizable",void 0===c.options.maximizable?u.defaults.maximizable:c.options.maximizable),a.set("startMaximized",void 0===c.options.startMaximized?u.defaults.startMaximized:c.options.startMaximized),a.set("pinnable",void 0===c.options.pinnable?u.defaults.pinnable:c.options.pinnable),a.set("pinned",void 0===c.options.pinned?u.defaults.pinned:c.options.pinned),a.set("transition",void 0===c.options.transition?u.defaults.transition:c.options.transition),a.set("padding",void 0===c.options.padding?u.defaults.padding:c.options.padding),a.set("overflow",void 0===c.options.overflow?u.defaults.overflow:c.options.overflow),"function"==typeof a.build&&a.build()}document.body.appendChild(a.elements.root)}function n(){ua=f(),va=e()}function s(){a.scrollTo(ua,va)}function t(){for(var a=0,d=0;d<o.length;d+=1){var e=o[d];(e.isModal()||e.isMaximized())&&(a+=1)}0===a?c(document.body,Aa.noOverflow):a>0&&document.body.className.indexOf(Aa.noOverflow)<0&&b(document.body,Aa.noOverflow)}function v(a,d,e){"string"==typeof e&&c(a.elements.root,Aa.prefix+e),b(a.elements.root,Aa.prefix+d),xa=a.elements.root.offsetWidth}function w(a){a.get("modal")?(c(a.elements.root,Aa.modeless),a.isOpen()&&(na(a),L(a),t())):(b(a.elements.root,Aa.modeless),a.isOpen()&&(ma(a),L(a),t()))}function x(a){a.get("basic")?b(a.elements.root,Aa.basic):c(a.elements.root,Aa.basic)}function y(a){a.get("frameless")?b(a.elements.root,Aa.frameless):c(a.elements.root,Aa.frameless)}function z(a,b){for(var c=o.indexOf(b),d=c+1;d<o.length;d+=1)if(o[d].isModal())return;return document.body.lastChild!==b.elements.root&&(document.body.appendChild(b.elements.root),o.splice(o.indexOf(b),1),o.push(b),U(b)),!1}function A(a,d,e,f){switch(d){case"title":a.setHeader(f);break;case"modal":w(a);break;case"basic":x(a);break;case"frameless":y(a);break;case"pinned":M(a);break;case"closable":O(a);break;case"maximizable":N(a);break;case"pinnable":I(a);break;case"movable":ca(a);break;case"resizable":ia(a);break;case"transition":v(a,f,e);break;case"padding":f?c(a.elements.root,Aa.noPadding):a.elements.root.className.indexOf(Aa.noPadding)<0&&b(a.elements.root,Aa.noPadding);break;case"overflow":f?c(a.elements.root,Aa.noOverflow):a.elements.root.className.indexOf(Aa.noOverflow)<0&&b(a.elements.root,Aa.noOverflow);break;case"transition":v(a,f,e)}"function"==typeof a.hooks.onupdate&&a.hooks.onupdate.call(a,d,e,f)}function B(a,b,c,d,e){var f={op:void 0,items:[]};if("undefined"==typeof e&&"string"==typeof d)f.op="get",b.hasOwnProperty(d)?(f.found=!0,f.value=b[d]):(f.found=!1,f.value=void 0);else{var g;if(f.op="set","object"==typeof d){var h=d;for(var i in h)b.hasOwnProperty(i)?(b[i]!==h[i]&&(g=b[i],b[i]=h[i],c.call(a,i,g,h[i])),f.items.push({key:i,value:h[i],found:!0})):f.items.push({key:i,value:h[i],found:!1})}else{if("string"!=typeof d)throw new Error("args must be a string or object");b.hasOwnProperty(d)?(b[d]!==e&&(g=b[d],b[d]=e,c.call(a,d,g,e)),f.items.push({key:d,value:e,found:!0})):f.items.push({key:d,value:e,found:!1})}}return f}function C(a){var b;Q(a,function(a){return b=a.invokeOnClose===!0}),!b&&a.isOpen()&&a.close()}function D(a,b){var c=a.srcElement||a.target;switch(c){case b.elements.commands.pin:b.isPinned()?F(b):E(b);break;case b.elements.commands.maximize:b.isMaximized()?H(b):G(b);break;case b.elements.commands.close:C(b)}return!1}function E(a){a.set("pinned",!0)}function F(a){a.set("pinned",!1)}function G(a){b(a.elements.root,Aa.maximized),a.isOpen()&&t()}function H(a){c(a.elements.root,Aa.maximized),a.isOpen()&&t()}function I(a){a.get("pinnable")?b(a.elements.root,Aa.pinnable):c(a.elements.root,Aa.pinnable)}function J(a){var b=f();a.elements.modal.style.marginTop=e()+"px",a.elements.modal.style.marginLeft=b+"px",a.elements.modal.style.marginRight=-b+"px"}function K(a){var b=parseInt(a.elements.modal.style.marginTop,10),c=parseInt(a.elements.modal.style.marginLeft,10);if(a.elements.modal.style.marginTop="",a.elements.modal.style.marginLeft="",a.elements.modal.style.marginRight="",a.isOpen()){var d=0,g=0;""!==a.elements.dialog.style.top&&(d=parseInt(a.elements.dialog.style.top,10)),a.elements.dialog.style.top=d+(b-e())+"px",""!==a.elements.dialog.style.left&&(g=parseInt(a.elements.dialog.style.left,10)),a.elements.dialog.style.left=g+(c-f())+"px"}}function L(a){a.get("modal")||a.get("pinned")?K(a):J(a)}function M(a){a.get("pinned")?(c(a.elements.root,Aa.unpinned),a.isOpen()&&K(a)):(b(a.elements.root,Aa.unpinned),a.isOpen()&&!a.isModal()&&J(a))}function N(a){a.get("maximizable")?b(a.elements.root,Aa.maximizable):c(a.elements.root,Aa.maximizable)}function O(a){a.get("closable")?(b(a.elements.root,Aa.closable),sa(a)):(c(a.elements.root,Aa.closable),ta(a))}function P(a,b){var c=a.srcElement||a.target;return Ba||c!==b.elements.modal||b.get("closableByDimmer")!==!0||C(b),Ba=!1,!1}function Q(a,b){for(var c=0;c<a.__internal.buttons.length;c+=1){var d=a.__internal.buttons[c];if(!d.element.disabled&&b(d)){var e=k(c,d);"function"==typeof a.callback&&a.callback.apply(a,[e]),e.cancel===!1&&a.close();break}}}function R(a,b){var c=a.srcElement||a.target;Q(b,function(a){return a.element===c&&(Ca=!0)})}function S(a){if(Ca)return void(Ca=!1);var b=o[o.length-1],c=a.keyCode;return 0===b.__internal.buttons.length&&c===m.ESC&&b.get("closable")===!0?(C(b),!1):wa.indexOf(c)>-1?(Q(b,function(a){return a.key===c}),!1):void 0}function T(a){var b=o[o.length-1],c=a.keyCode;if(c===m.LEFT||c===m.RIGHT){for(var d=b.__internal.buttons,e=0;e<d.length;e+=1)if(document.activeElement===d[e].element)switch(c){case m.LEFT:return void d[(e||d.length)-1].element.focus();case m.RIGHT:return void d[(e+1)%d.length].element.focus()}}else if(c<m.F12+1&&c>m.F1-1&&wa.indexOf(c)>-1)return a.preventDefault(),a.stopPropagation(),Q(b,function(a){return a.key===c}),!1}function U(a,b){if(b)b.focus();else{var c=a.__internal.focus,d=c.element;switch(typeof c.element){case"number":a.__internal.buttons.length>c.element&&(d=a.get("basic")===!0?a.elements.reset[0]:a.__internal.buttons[c.element].element);break;case"string":d=a.elements.body.querySelector(c.element);break;case"function":d=c.element.call(a)}"undefined"!=typeof d&&null!==d||0!==a.__internal.buttons.length||(d=a.elements.reset[0]),d&&d.focus&&(d.focus(),c.select&&d.select&&d.select())}}function V(a,b){if(!b)for(var c=o.length-1;c>-1;c-=1)if(o[c].isModal()){b=o[c];break}if(b&&b.isModal()){var d,e=a.srcElement||a.target,f=e===b.elements.reset[1]||0===b.__internal.buttons.length&&e===document.body;f&&(b.get("maximizable")?d=b.elements.commands.maximize:b.get("closable")&&(d=b.elements.commands.close)),void 0===d&&("number"==typeof b.__internal.focus.element?e===b.elements.reset[0]?d=b.elements.buttons.auxiliary.firstChild||b.elements.buttons.primary.firstChild:f&&(d=b.elements.reset[0]):e===b.elements.reset[0]&&(d=b.elements.buttons.primary.lastChild||b.elements.buttons.auxiliary.lastChild)),U(b,d)}}function W(a,b){clearTimeout(b.__internal.timerIn),U(b),s(),Ca=!1,"function"==typeof b.get("onfocus")&&b.get("onfocus").call(b),q(b.elements.dialog,r.type,b.__internal.transitionInHandler),c(b.elements.root,Aa.animationIn)}function X(a,b){clearTimeout(b.__internal.timerOut),q(b.elements.dialog,r.type,b.__internal.transitionOutHandler),ba(b),ha(b),b.isMaximized()&&!b.get("startMaximized")&&H(b),u.defaults.maintainFocus&&b.__internal.activeElement&&(b.__internal.activeElement.focus(),b.__internal.activeElement=null),"function"==typeof b.__internal.destroy&&b.__internal.destroy.apply(b)}function Y(a,b){var c=a[Ga]-Ea,d=a[Ha]-Fa;Ja&&(d-=document.body.scrollTop),b.style.left=c+"px",b.style.top=d+"px"}function Z(a,b){var c=a[Ga]-Ea,d=a[Ha]-Fa;Ja&&(d-=document.body.scrollTop),b.style.left=Math.min(Ia.maxLeft,Math.max(Ia.minLeft,c))+"px",Ja?b.style.top=Math.min(Ia.maxTop,Math.max(Ia.minTop,d))+"px":b.style.top=Math.max(Ia.minTop,d)+"px"}function $(a,c){if(null===La&&!c.isMaximized()&&c.get("movable")){var d,e=0,f=0;if("touchstart"===a.type?(a.preventDefault(),d=a.targetTouches[0],Ga="clientX",Ha="clientY"):0===a.button&&(d=a),d){var g=c.elements.dialog;if(b(g,Aa.capture),g.style.left&&(e=parseInt(g.style.left,10)),g.style.top&&(f=parseInt(g.style.top,10)),Ea=d[Ga]-e,Fa=d[Ha]-f,c.isModal()?Fa+=c.elements.modal.scrollTop:c.isPinned()&&(Fa-=document.body.scrollTop),c.get("moveBounded")){var h=g,i=-e,j=-f;do i+=h.offsetLeft,j+=h.offsetTop;while(h=h.offsetParent);Ia={maxLeft:i,minLeft:-i,maxTop:document.documentElement.clientHeight-g.clientHeight-j,minTop:-j},Ka=Z}else Ia=null,Ka=Y;return Ja=!c.isModal()&&c.isPinned(),Da=c,Ka(d,g),b(document.body,Aa.noSelection),!1}}}function _(a){if(Da){var b;"touchmove"===a.type?(a.preventDefault(),b=a.targetTouches[0]):0===a.button&&(b=a),b&&Ka(b,Da.elements.dialog)}}function aa(){if(Da){var a=Da.elements.dialog;Da=Ia=null,c(document.body,Aa.noSelection),c(a,Aa.capture)}}function ba(a){Da=null;var b=a.elements.dialog;b.style.left=b.style.top=""}function ca(a){a.get("movable")?(b(a.elements.root,Aa.movable),a.isOpen()&&oa(a)):(ba(a),c(a.elements.root,Aa.movable),a.isOpen()&&pa(a))}function da(a,b,c){var e=b,f=0,g=0;do f+=e.offsetLeft,g+=e.offsetTop;while(e=e.offsetParent);var h,i;c===!0?(h=a.pageX,i=a.pageY):(h=a.clientX,i=a.clientY);var j=d();if(j&&(h=document.body.offsetWidth-h,isNaN(Ma)||(f=document.body.offsetWidth-f-b.offsetWidth)),b.style.height=i-g+Pa+"px",b.style.width=h-f+Pa+"px",!isNaN(Ma)){var k=.5*Math.abs(b.offsetWidth-Na);j&&(k*=-1),b.offsetWidth>Na?b.style.left=Ma+k+"px":b.offsetWidth>=Oa&&(b.style.left=Ma-k+"px")}}function ea(a,c){if(!c.isMaximized()){var d;if("touchstart"===a.type?(a.preventDefault(),d=a.targetTouches[0]):0===a.button&&(d=a),d){La=c,Pa=c.elements.resizeHandle.offsetHeight/2;var e=c.elements.dialog;return b(e,Aa.capture),Ma=parseInt(e.style.left,10),e.style.height=e.offsetHeight+"px",e.style.minHeight=c.elements.header.offsetHeight+c.elements.footer.offsetHeight+"px",e.style.width=(Na=e.offsetWidth)+"px","none"!==e.style.maxWidth&&(e.style.minWidth=(Oa=e.offsetWidth)+"px"),e.style.maxWidth="none",b(document.body,Aa.noSelection),!1}}}function fa(a){if(La){var b;"touchmove"===a.type?(a.preventDefault(),b=a.targetTouches[0]):0===a.button&&(b=a),b&&da(b,La.elements.dialog,!La.get("modal")&&!La.get("pinned"))}}function ga(){if(La){var a=La.elements.dialog;La=null,c(document.body,Aa.noSelection),c(a,Aa.capture),Ba=!0}}function ha(a){La=null;var b=a.elements.dialog;"none"===b.style.maxWidth&&(b.style.maxWidth=b.style.minWidth=b.style.width=b.style.height=b.style.minHeight=b.style.left="",Ma=Number.Nan,Na=Oa=Pa=0)}function ia(a){a.get("resizable")?(b(a.elements.root,Aa.resizable),a.isOpen()&&qa(a)):(ha(a),c(a.elements.root,Aa.resizable),a.isOpen()&&ra(a))}function ja(){for(var a=0;a<o.length;a+=1){var b=o[a];b.get("autoReset")&&(ba(b),ha(b))}}function ka(b){1===o.length&&(p(a,"resize",ja),p(document.body,"keyup",S),p(document.body,"keydown",T),p(document.body,"focus",V),p(document.documentElement,"mousemove",_),p(document.documentElement,"touchmove",_),p(document.documentElement,"mouseup",aa),p(document.documentElement,"touchend",aa),p(document.documentElement,"mousemove",fa),p(document.documentElement,"touchmove",fa),p(document.documentElement,"mouseup",ga),p(document.documentElement,"touchend",ga)),p(b.elements.commands.container,"click",b.__internal.commandsClickHandler),p(b.elements.footer,"click",b.__internal.buttonsClickHandler),p(b.elements.reset[0],"focus",b.__internal.resetHandler),p(b.elements.reset[1],"focus",b.__internal.resetHandler),Ca=!0,p(b.elements.dialog,r.type,b.__internal.transitionInHandler),b.get("modal")||ma(b),b.get("resizable")&&qa(b),b.get("movable")&&oa(b)}function la(b){1===o.length&&(q(a,"resize",ja),q(document.body,"keyup",S),q(document.body,"keydown",T),q(document.body,"focus",V),q(document.documentElement,"mousemove",_),q(document.documentElement,"mouseup",aa),q(document.documentElement,"mousemove",fa),q(document.documentElement,"mouseup",ga)),q(b.elements.commands.container,"click",b.__internal.commandsClickHandler),q(b.elements.footer,"click",b.__internal.buttonsClickHandler),q(b.elements.reset[0],"focus",b.__internal.resetHandler),q(b.elements.reset[1],"focus",b.__internal.resetHandler),p(b.elements.dialog,r.type,b.__internal.transitionOutHandler),b.get("modal")||na(b),b.get("movable")&&pa(b),b.get("resizable")&&ra(b)}function ma(a){p(a.elements.dialog,"focus",a.__internal.bringToFrontHandler,!0)}function na(a){q(a.elements.dialog,"focus",a.__internal.bringToFrontHandler,!0)}function oa(a){p(a.elements.header,"mousedown",a.__internal.beginMoveHandler),p(a.elements.header,"touchstart",a.__internal.beginMoveHandler)}function pa(a){q(a.elements.header,"mousedown",a.__internal.beginMoveHandler),q(a.elements.header,"touchstart",a.__internal.beginMoveHandler)}function qa(a){p(a.elements.resizeHandle,"mousedown",a.__internal.beginResizeHandler),p(a.elements.resizeHandle,"touchstart",a.__internal.beginResizeHandler)}function ra(a){q(a.elements.resizeHandle,"mousedown",a.__internal.beginResizeHandler),q(a.elements.resizeHandle,"touchstart",a.__internal.beginResizeHandler)}function sa(a){p(a.elements.modal,"click",a.__internal.modalClickHandler)}function ta(a){q(a.elements.modal,"click",a.__internal.modalClickHandler)}var ua,va,wa=[],xa=null,ya=a.navigator.userAgent.indexOf("Safari")>-1&&a.navigator.userAgent.indexOf("Chrome")<0,za={dimmer:'<div class="ajs-dimmer"></div>',modal:'<div class="ajs-modal" tabindex="0"></div>',dialog:'<div class="ajs-dialog" tabindex="0"></div>',reset:'<button class="ajs-reset"></button>',commands:'<div class="ajs-commands"><button class="ajs-pin"></button><button class="ajs-maximize"></button><button class="ajs-close"></button></div>',header:'<div class="ajs-header"></div>',body:'<div class="ajs-body"></div>',content:'<div class="ajs-content"></div>',footer:'<div class="ajs-footer"></div>',buttons:{primary:'<div class="ajs-primary ajs-buttons"></div>',auxiliary:'<div class="ajs-auxiliary ajs-buttons"></div>'},button:'<button class="ajs-button"></button>',resizeHandle:'<div class="ajs-handle"></div>'},Aa={base:"alertify",prefix:"ajs-",hidden:"ajs-hidden",noSelection:"ajs-no-selection",noOverflow:"ajs-no-overflow",noPadding:"ajs-no-padding",modeless:"ajs-modeless",movable:"ajs-movable",resizable:"ajs-resizable",capture:"ajs-capture",fixed:"ajs-fixed",closable:"ajs-closable",maximizable:"ajs-maximizable",maximize:"ajs-maximize",restore:"ajs-restore",pinnable:"ajs-pinnable",unpinned:"ajs-unpinned",pin:"ajs-pin",maximized:"ajs-maximized",animationIn:"ajs-in",animationOut:"ajs-out",shake:"ajs-shake",basic:"ajs-basic",frameless:"ajs-frameless"},Ba=!1,Ca=!1,Da=null,Ea=0,Fa=0,Ga="pageX",Ha="pageY",Ia=null,Ja=!1,Ka=null,La=null,Ma=Number.Nan,Na=0,Oa=0,Pa=0;return{__init:l,isOpen:function(){return this.__internal.isOpen},isModal:function(){return this.elements.root.className.indexOf(Aa.modeless)<0},isMaximized:function(){return this.elements.root.className.indexOf(Aa.maximized)>-1},isPinned:function(){return this.elements.root.className.indexOf(Aa.unpinned)<0},maximize:function(){return this.isMaximized()||G(this),this},restore:function(){return this.isMaximized()&&H(this),this},pin:function(){return this.isPinned()||E(this),this},unpin:function(){return this.isPinned()&&F(this),this},bringToFront:function(){return z(null,this),this},moveTo:function(a,b){if(!isNaN(a)&&!isNaN(b)){var c=this.elements.dialog,e=c,f=0,g=0;c.style.left&&(f-=parseInt(c.style.left,10)),c.style.top&&(g-=parseInt(c.style.top,10));do f+=e.offsetLeft,g+=e.offsetTop;while(e=e.offsetParent);var h=a-f,i=b-g;d()&&(h*=-1),c.style.left=h+"px",c.style.top=i+"px"}return this},resizeTo:function(a,b){var c=parseFloat(a),d=parseFloat(b),e=/(\d*\.\d+|\d+)%/;if(!isNaN(c)&&!isNaN(d)&&this.get("resizable")===!0){(""+a).match(e)&&(c=c/100*document.documentElement.clientWidth),(""+b).match(e)&&(d=d/100*document.documentElement.clientHeight);var f=this.elements.dialog;"none"!==f.style.maxWidth&&(f.style.minWidth=(Oa=f.offsetWidth)+"px"),f.style.maxWidth="none",f.style.minHeight=this.elements.header.offsetHeight+this.elements.footer.offsetHeight+"px",f.style.width=c+"px",f.style.height=d+"px"}return this},setting:function(a,b){var c=this,d=B(this,this.__internal.options,function(a,b,d){A(c,a,b,d)},a,b);if("get"===d.op)return d.found?d.value:"undefined"!=typeof this.settings?B(this,this.settings,this.settingUpdated||function(){},a,b).value:void 0;if("set"===d.op){if(d.items.length>0)for(var e=this.settingUpdated||function(){},f=0;f<d.items.length;f+=1){var g=d.items[f];g.found||"undefined"==typeof this.settings||B(this,this.settings,e,g.key,g.value)}return this}},set:function(a,b){return this.setting(a,b),this},get:function(a){return this.setting(a)},setHeader:function(b){return"string"==typeof b?(g(this.elements.header),this.elements.header.innerHTML=b):b instanceof a.HTMLElement&&this.elements.header.firstChild!==b&&(g(this.elements.header),this.elements.header.appendChild(b)),this},setContent:function(b){return"string"==typeof b?(g(this.elements.content),this.elements.content.innerHTML=b):b instanceof a.HTMLElement&&this.elements.content.firstChild!==b&&(g(this.elements.content),this.elements.content.appendChild(b)),this},showModal:function(a){return this.show(!0,a)},show:function(a,d){if(l(this),this.__internal.isOpen){ba(this),ha(this),b(this.elements.dialog,Aa.shake);var e=this;setTimeout(function(){c(e.elements.dialog,Aa.shake)},200)}else{if(this.__internal.isOpen=!0,o.push(this),u.defaults.maintainFocus&&(this.__internal.activeElement=document.activeElement),"function"==typeof this.prepare&&this.prepare(),ka(this),void 0!==a&&this.set("modal",a),n(),t(),"string"==typeof d&&""!==d&&(this.__internal.className=d,b(this.elements.root,d)),this.get("startMaximized")?this.maximize():this.isMaximized()&&H(this),L(this),c(this.elements.root,Aa.animationOut),b(this.elements.root,Aa.animationIn),clearTimeout(this.__internal.timerIn),this.__internal.timerIn=setTimeout(this.__internal.transitionInHandler,r.supported?1e3:100),ya){var f=this.elements.root;f.style.display="none",setTimeout(function(){f.style.display="block"},0)}xa=this.elements.root.offsetWidth,c(this.elements.root,Aa.hidden),"function"==typeof this.hooks.onshow&&this.hooks.onshow.call(this),"function"==typeof this.get("onshow")&&this.get("onshow").call(this)}return this},close:function(){return this.__internal.isOpen&&(la(this),c(this.elements.root,Aa.animationIn),b(this.elements.root,Aa.animationOut),clearTimeout(this.__internal.timerOut),this.__internal.timerOut=setTimeout(this.__internal.transitionOutHandler,r.supported?1e3:100),b(this.elements.root,Aa.hidden),xa=this.elements.modal.offsetWidth,"undefined"!=typeof this.__internal.className&&""!==this.__internal.className&&c(this.elements.root,this.__internal.className),"function"==typeof this.hooks.onclose&&this.hooks.onclose.call(this),"function"==typeof this.get("onclose")&&this.get("onclose").call(this),o.splice(o.indexOf(this),1),this.__internal.isOpen=!1,t()),this},closeOthers:function(){return u.closeAll(this),this},destroy:function(){return this.__internal.isOpen?(this.__internal.destroy=function(){i(this,l)},this.close()):i(this,l),this}}}(),t=function(){function d(a){a.__internal||(a.__internal={position:u.defaults.notifier.position,delay:u.defaults.notifier.delay},l=document.createElement("DIV"),h(a)),l.parentNode!==document.body&&document.body.appendChild(l)}function e(a){a.__internal.pushed=!0,m.push(a)}function f(a){m.splice(m.indexOf(a),1),a.__internal.pushed=!1}function h(a){switch(l.className=n.base,a.__internal.position){case"top-right":b(l,n.top+" "+n.right);break;case"top-left":b(l,n.top+" "+n.left);break;case"bottom-left":b(l,n.bottom+" "+n.left);break;default:case"bottom-right":b(l,n.bottom+" "+n.right)}}function i(d,h){function i(a,b){b.dismiss(!0)}function m(a,b){q(b.element,r.type,m),l.removeChild(b.element)}function o(a){return a.__internal||(a.__internal={pushed:!1,delay:void 0,timer:void 0,clickHandler:void 0,transitionEndHandler:void 0,transitionTimeout:void 0},a.__internal.clickHandler=j(a,i),a.__internal.transitionEndHandler=j(a,m)),a}function s(a){clearTimeout(a.__internal.timer),clearTimeout(a.__internal.transitionTimeout)}return o({element:d,push:function(a,c){if(!this.__internal.pushed){e(this),s(this);var d,f;switch(arguments.length){case 0:f=this.__internal.delay;break;case 1:"number"==typeof a?f=a:(d=a,f=this.__internal.delay);break;case 2:d=a,f=c}return"undefined"!=typeof d&&this.setContent(d),t.__internal.position.indexOf("top")<0?l.appendChild(this.element):l.insertBefore(this.element,l.firstChild),k=this.element.offsetWidth,b(this.element,n.visible),p(this.element,"click",this.__internal.clickHandler),this.delay(f)}return this},ondismiss:function(){},callback:h,dismiss:function(a){return this.__internal.pushed&&(s(this),("function"!=typeof this.ondismiss||this.ondismiss.call(this)!==!1)&&(q(this.element,"click",this.__internal.clickHandler),"undefined"!=typeof this.element&&this.element.parentNode===l&&(this.__internal.transitionTimeout=setTimeout(this.__internal.transitionEndHandler,r.supported?1e3:100),c(this.element,n.visible),"function"==typeof this.callback&&this.callback.call(this,a)),f(this))),this},delay:function(a){if(s(this),this.__internal.delay="undefined"==typeof a||isNaN(+a)?t.__internal.delay:+a,this.__internal.delay>0){var b=this;this.__internal.timer=setTimeout(function(){b.dismiss()},1e3*this.__internal.delay)}return this},setContent:function(b){return"string"==typeof b?(g(this.element),this.element.innerHTML=b):b instanceof a.HTMLElement&&this.element.firstChild!==b&&(g(this.element),this.element.appendChild(b)),this},dismissOthers:function(){return t.dismissAll(this),this}})}var k,l,m=[],n={base:"alertify-notifier",message:"ajs-message",top:"ajs-top",right:"ajs-right",bottom:"ajs-bottom",left:"ajs-left",visible:"ajs-visible",hidden:"ajs-hidden"};return{setting:function(a,b){if(d(this),"undefined"==typeof b)return this.__internal[a];switch(a){case"position":this.__internal.position=b,h(this);break;case"delay":this.__internal.delay=b}return this},set:function(a,b){return this.setting(a,b),this},get:function(a){return this.setting(a)},create:function(a,b){d(this);var c=document.createElement("div");return c.className=n.message+("string"==typeof a&&""!==a?" ajs-"+a:""),i(c,b)},dismissAll:function(a){for(var b=m.slice(0),c=0;c<b.length;c+=1){var d=b[c];(void 0===a||a!==d)&&d.dismiss()}}}}(),u=new l;u.dialog("alert",function(){return{main:function(a,b,c){var d,e,f;switch(arguments.length){case 1:e=a;break;case 2:"function"==typeof b?(e=a,f=b):(d=a,e=b);break;case 3:d=a,e=b,f=c}return this.set("title",d),this.set("message",e),this.set("onok",f),this},setup:function(){return{buttons:[{text:u.defaults.glossary.ok,key:m.ESC,invokeOnClose:!0,className:u.defaults.theme.ok}],focus:{element:0,select:!1},options:{maximizable:!1,resizable:!1}}},build:function(){},prepare:function(){},setMessage:function(a){this.setContent(a)},settings:{message:void 0,onok:void 0,label:void 0},settingUpdated:function(a,b,c){switch(a){case"message":this.setMessage(c);break;case"label":this.__internal.buttons[0].element&&(this.__internal.buttons[0].element.innerHTML=c)}},callback:function(a){if("function"==typeof this.get("onok")){var b=this.get("onok").call(this,a);"undefined"!=typeof b&&(a.cancel=!b)}}}}),u.dialog("confirm",function(){function a(a){null!==c.timer&&(clearInterval(c.timer),c.timer=null,a.__internal.buttons[c.index].element.innerHTML=c.text)}function b(b,d,e){a(b),c.duration=e,c.index=d,c.text=b.__internal.buttons[d].element.innerHTML,c.timer=setInterval(j(b,c.task),1e3),c.task(null,b)}var c={timer:null,index:null,text:null,duration:null,task:function(b,d){if(d.isOpen()){if(d.__internal.buttons[c.index].element.innerHTML=c.text+" (&#8207;"+c.duration+"&#8207;) ",c.duration-=1,-1===c.duration){a(d);var e=d.__internal.buttons[c.index],f=k(c.index,e);"function"==typeof d.callback&&d.callback.apply(d,[f]),f.close!==!1&&d.close()}}else a(d)}};return{main:function(a,b,c,d){var e,f,g,h;switch(arguments.length){case 1:f=a;break;
case 2:f=a,g=b;break;case 3:f=a,g=b,h=c;break;case 4:e=a,f=b,g=c,h=d}return this.set("title",e),this.set("message",f),this.set("onok",g),this.set("oncancel",h),this},setup:function(){return{buttons:[{text:u.defaults.glossary.ok,key:m.ENTER,className:u.defaults.theme.ok},{text:u.defaults.glossary.cancel,key:m.ESC,invokeOnClose:!0,className:u.defaults.theme.cancel}],focus:{element:0,select:!1},options:{maximizable:!1,resizable:!1}}},build:function(){},prepare:function(){},setMessage:function(a){this.setContent(a)},settings:{message:null,labels:null,onok:null,oncancel:null,defaultFocus:null,reverseButtons:null},settingUpdated:function(a,b,c){switch(a){case"message":this.setMessage(c);break;case"labels":"ok"in c&&this.__internal.buttons[0].element&&(this.__internal.buttons[0].text=c.ok,this.__internal.buttons[0].element.innerHTML=c.ok),"cancel"in c&&this.__internal.buttons[1].element&&(this.__internal.buttons[1].text=c.cancel,this.__internal.buttons[1].element.innerHTML=c.cancel);break;case"reverseButtons":c===!0?this.elements.buttons.primary.appendChild(this.__internal.buttons[0].element):this.elements.buttons.primary.appendChild(this.__internal.buttons[1].element);break;case"defaultFocus":this.__internal.focus.element="ok"===c?0:1}},callback:function(b){a(this);var c;switch(b.index){case 0:"function"==typeof this.get("onok")&&(c=this.get("onok").call(this,b),"undefined"!=typeof c&&(b.cancel=!c));break;case 1:"function"==typeof this.get("oncancel")&&(c=this.get("oncancel").call(this,b),"undefined"!=typeof c&&(b.cancel=!c))}},autoOk:function(a){return b(this,0,a),this},autoCancel:function(a){return b(this,1,a),this}}}),u.dialog("prompt",function(){var b=document.createElement("INPUT"),c=document.createElement("P");return{main:function(a,b,c,d,e){var f,g,h,i,j;switch(arguments.length){case 1:g=a;break;case 2:g=a,h=b;break;case 3:g=a,h=b,i=c;break;case 4:g=a,h=b,i=c,j=d;break;case 5:f=a,g=b,h=c,i=d,j=e}return this.set("title",f),this.set("message",g),this.set("value",h),this.set("onok",i),this.set("oncancel",j),this},setup:function(){return{buttons:[{text:u.defaults.glossary.ok,key:m.ENTER,className:u.defaults.theme.ok},{text:u.defaults.glossary.cancel,key:m.ESC,invokeOnClose:!0,className:u.defaults.theme.cancel}],focus:{element:b,select:!0},options:{maximizable:!1,resizable:!1}}},build:function(){b.className=u.defaults.theme.input,b.setAttribute("type","text"),b.value=this.get("value"),this.elements.content.appendChild(c),this.elements.content.appendChild(b)},prepare:function(){},setMessage:function(b){"string"==typeof b?(g(c),c.innerHTML=b):b instanceof a.HTMLElement&&c.firstChild!==b&&(g(c),c.appendChild(b))},settings:{message:void 0,labels:void 0,onok:void 0,oncancel:void 0,value:"",type:"text",reverseButtons:void 0},settingUpdated:function(a,c,d){switch(a){case"message":this.setMessage(d);break;case"value":b.value=d;break;case"type":switch(d){case"text":case"color":case"date":case"datetime-local":case"email":case"month":case"number":case"password":case"search":case"tel":case"time":case"week":b.type=d;break;default:b.type="text"}break;case"labels":d.ok&&this.__internal.buttons[0].element&&(this.__internal.buttons[0].element.innerHTML=d.ok),d.cancel&&this.__internal.buttons[1].element&&(this.__internal.buttons[1].element.innerHTML=d.cancel);break;case"reverseButtons":d===!0?this.elements.buttons.primary.appendChild(this.__internal.buttons[0].element):this.elements.buttons.primary.appendChild(this.__internal.buttons[1].element)}},callback:function(a){var c;switch(a.index){case 0:this.settings.value=b.value,"function"==typeof this.get("onok")&&(c=this.get("onok").call(this,a,this.settings.value),"undefined"!=typeof c&&(a.cancel=!c));break;case 1:"function"==typeof this.get("oncancel")&&(c=this.get("oncancel").call(this,a),"undefined"!=typeof c&&(a.cancel=!c))}}}}),"object"==typeof module&&"object"==typeof module.exports?module.exports=u:"function"==typeof define&&define.amd?define([],function(){return u}):a.alertify||(a.alertify=u)}("undefined"!=typeof window?window:this);