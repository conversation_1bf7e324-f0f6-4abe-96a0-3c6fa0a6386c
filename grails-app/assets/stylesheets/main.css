/* CSS crunched with Crunch - http://crunchapp.net/ */

html,
body {
    min-width: 1000px;
    /* min-height: 500px */
}
body {
    padding-right: 50px
}
body.index-page {
    padding-right: 0;
}
html,
body,
textarea,
input,
select,
option,
.form-control,
.btn {
    font-size: 13px
}
h1 {
    font-size: 250%;
    color: #777777
}
a,
.scroller {
    color: #2396d7;
    text-decoration: none !important
}
a:hover,
.scroller:hover {
    color: #02a2fc
}
pre {
    overflow: auto;
    word-wrap: normal;
    white-space: pre
}
pre span {
    color: #ff0000
}
#body-gradient {
    height: 500px;
    background-color: #e4ddcb;
    margin-bottom: -500px
}
#body-bgcolor {
    height: 500px;
    background-color: black;
    margin-bottom: -500px
}
#user-bar {
    padding: 5px 10px;
    text-align: right
}
#user-bar>ul {
    display: inline-block;
    list-style: none;
    margin: 0px;
    padding: 5px 0px 0px
}
#user-bar>ul>li {
    float: left;
    padding: 5px;
    border-left: 1px solid #ffffff;
    font-size: 90%
}
#user-bar>ul>li:first-child {
    border-left: none !important
}
#user-bar>ul>li>a {
    padding: 5px 15px
}
#user-bar>ul>li>a:hover {
    -moz-border-radius: 5px;
    border-radius: 5px;
    background-color: #eee
}
#user-bar>ul>li>a>.secondary-label {
    -moz-border-radius: 10px;
    border-radius: 10px;
    padding: 0px 4px;
    border: 1px solid #2396d7;
    background-color: #ffffff
}
#logo-bar {
    height: 107px;
    padding: 10px 15px 10px 28px
}
#logo-bar>.logo {
    text-align: center;
    float: left;
    padding-right: 15px
}
#logo-bar>.logo>span {
    font-size: 60px
}
#logo-bar>.logo>img {
    height: 85px
}
#logo-bar>.location {
    width: 300px;
    height: 85px;
    float: left;
    padding: 20px 15px 0px;
    vertical-align: middle;
    color: #888888;
    font-size: 95%
}
#logo-bar>.location .fa {
    width: 20px;
    text-align: center
}
#logo-bar>.location #logo-bar>.search {
    width: 300px;
    float: right;
    height: 85px;
    padding-top: 50px
}
#logo-bar>.search>.search-bar {
    padding: 10px;
    background-color: #eeeeee;
    -moz-border-radius: 10px;
    border-radius: 10px;
    float: right
}
#logo-bar>.search>.search-bar input[type=text] {
    width: 350px
}
#logo-bar>.search>.search-bar .input-group-btn {
    width: auto!important
}
#breadcrumbs {
    padding: 0px 15px;
    font-size: 90%;
    background-color: #dddddd
}
#breadcrumbs>a,
#breadcrumbs>span {
    display: inline-block;
    margin: 5px 5px 5px 5px;
    padding: 3px 5px
}
#breadcrumbs>span {
    color: #eeeeee
}
#breadcrumbs a:hover,
#breadcrumbs .active {
    -moz-border-radius: 5px;
    border-radius: 5px;
    background-color: #eeeeee
}
#breadcrumbs #menu-btn {
    display: inline-block;
    padding: 3px 5px;
    margin-right: 10px
}
#breadcrumbs .menu-btn-container {
    border-right: 2px solid #eeeeee
}
#menu {
    height: 250px;
    background-color: #eeeeee;
    width: 100%;
    position: absolute;
    z-index: 1000
}
#menu .filter {
    width: 400px;
    padding: 20px 25px 10px
}
#menu .container {
    overflow-y: auto;
    height: 170px;
    margin: 0px;
    width: 100%
}
#menu-content {
    -moz-column-count: 5;
    -webkit-column-count: 5;
    column-count: 5;
    list-style: none;
    margin: 0px;
    padding: 10px 25px
}
#menu-content li {
    list-style: none;
    font-size: 90%;
    font-weight: bold;
    margin-bottom: 6px
}
#menu-content li a span.fa {
    padding-right: 5px
}
#menu-content>li>ul {
    padding-left: 15px
}
#menu-content>li>ul>li {
    font-size: 90%;
    font-weight: normal;
    margin-bottom: 3px
}
#menu-content>li>ul>li:first-child {
    margin-top: 3px
}
#page-content {
    padding-bottom: 30px;
    clear: both
}
#page-content h1 {
    padding: 10px 25px
}
#main-content {
    width: 70%;
    float: left;
    padding: 10px 10px 10px 25px;
    min-height: 300px;
}
#main-actions {
    width: 280px;
    padding: 10px 10px 10px 10px;
    color: #ffffff;
    position: absolute;
    right: 0px
}
#main-actions #sidebar {
    width: 260px;
    padding-right: 15px
}
#main-actions .title {
    -moz-border-radius: 5px 5px 0px 0px;
    border-radius: 5px 5px 0px 0px;
    background-color: #777878;
    padding: 5px 10px;
    font-weight: bold
}
#main-actions .content {
    -moz-border-radius: 0px 0px 5px 5px;
    border-radius: 0px 0px 5px 5px;
    background-color: #eeeeee;
    min-height: 140px
}
#main-actions ul,
#main-actions ul li {
    list-style: none;
    background-color: #2396d7;
    padding: 0px;
    margin: 0px
}
#main-actions ul li {
    border-bottom: 1px solid #ffffff
}
#main-actions ul li:first-child {
    border-top: 1px solid #ffffff
}
#main-actions ul li a,
#main-actions ul li input,
#main-actions ul li button {
    display: block;
    padding: 4px 10px;
    color: #ffffff;
    width: 100%;
    text-align: left;
    border: none;
    background-color: transparent;
    font-size: 90%
}
#main-actions ul li:hover {
    color: #ffffff;
    background-color: #02a2fc
}
#main-actions ul li .disabled,
#main-actions ul li *:disabled {
    background-color: #dddddd !important
}
.form-group {
    padding: 4px 0px;
    clear: both
}
.form-horizontal .form-group {
    margin: 0px !important
}
.control-label {
    font-weight: normal
}
.form-group>.control-label {
    padding-top: 7px;
    padding-left: 0px;
    padding-right: 10px !important;
    font-weight: bold;
    width: 25%;
    float: left
}
.form-control {
    height: 32px;
}
.form-horizontal .form-buttons {
    clear: both;
    margin-bottom: 0px;
    padding-left: 5px;
    padding-right: 5px;
    margin-left: 25% !important;
    border-bottom: none
}
.form-group input[type='radio'] {
    margin-top: 0px;
    line-height: 32px;
    vertical-align: middle
}
.col-xs-1,
.col-sm-1,
.col-md-1,
.col-lg-1,
.col-xs-2,
.col-sm-2,
.col-md-2,
.col-lg-2,
.col-xs-3,
.col-sm-3,
.col-md-3,
.col-lg-3,
.col-xs-4,
.col-sm-4,
.col-md-4,
.col-lg-4,
.col-xs-5,
.col-sm-5,
.col-md-5,
.col-lg-5,
.col-xs-6,
.col-sm-6,
.col-md-6,
.col-lg-6,
.col-xs-7,
.col-sm-7,
.col-md-7,
.col-lg-7,
.col-xs-8,
.col-sm-8,
.col-md-8,
.col-lg-8,
.col-xs-9,
.col-sm-9,
.col-md-9,
.col-lg-9,
.col-xs-10,
.col-sm-10,
.col-md-10,
.col-lg-10,
.col-xs-11,
.col-sm-11,
.col-md-11,
.col-lg-11,
.col-xs-12,
.col-sm-12,
.col-md-12,
.col-lg-12,
.input-group[class*=col-] {
    padding-left: 5px;
    padding-right: 5px
}
.breaker {
    width: 100%;
    float: none;
    clear: both;
    height: 1px
}
.nav-tab-container {
    overflow: hidden;
    margin: -1px;
    margin-left: 20px;
    margin-right: 20px
}
.nav-tab-scrollers {
    margin-top: -37px;
    margin-bottom: 36px
}
.nav-tab-scrollers .disabled {
    color: #dddddd !important
}
.nav-tab-scrollers .scroller {
    display: block;
    padding: 10px 5px;
    cursor: pointer
}
.nav-tab-scrollers .scroll-left {
    float: left
}
.nav-tab-scrollers .scroll-right {
    float: right
}
.nav-tabs {
    border-bottom: 0px;
    width: 10000px
}
.nav-tabs li {
    margin-right: 5px
}
.nav-tabs li a {
    background-color: #eeeeee;
    border-bottom: 1px solid #dddddd
}
.nav-tabs li a:hover {
    background-color: #02a2fc;
    color: #ffffff
}
.nav-tabs .active {
    border-bottom: 1px solid #ffffff
}
.tab-content {
    padding: 20px;
    background: #ffffff;
    border: 1px solid #dddddd;
    -moz-border-radius: 4px;
    border-radius: 4px
}
.btn {
    border-color: #cccccc
}
.btn-primary {
    background-color: #2396d7;
    border-color: #cccccc
}
.btn-primary:hover {
    background-color: #02a2fc;
    border-color: #cccccc;
    color: #fff;
}
.multi-field-template {
    display: none
}
.box {
    -moz-border-radius: 5px;
    border-radius: 5px;
    border: 1px solid #cccccc;
    padding: 10px;
    margin-bottom: 10px
}
.icon {
    margin-right: 5px
}
.bg-white {
    background-color: #ffffff
}
.bg-yellow {
    background-color: #dbdd08
}
.required-mark {
    display: block;
    clear: both;
    margin-left: 25%;
    text-align: left;
    padding-left: 5px
}
.required-mark span {
    font-size: 70%;
    text-align: right;
    padding: 2px 10px;
    color: #ffffff;
    -moz-border-radius: 5px;
    border-radius: 5px;
    font-weight: bold;
    background-color: #ff0000
}
.error {
    border: 1px solid #ff0000
}
.property-list {
    list-style-type: none;
    padding-left: 0
}
.property-list li {
    line-height: 14px;
    margin-bottom: 10px
}
.property-list .property-label {
    display: inline-block;
    width: 160px;
    font-weight: bold;
    line-height: 16px;
    vertical-align: top
}
.property-list .property-value {
    display: inline-block;
    line-height: 16px;
    max-width: 70%;
    vertical-align: top
}
.box-container {
    background: #fff;
    border: 1px solid #ccc;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    padding: 12px
}
#jstree {
    max-width: 200px
}
#jstree a {
    white-space: normal !important;
    height: auto;
    padding: 1px 2px
}
.table {
    background: #fff
}
.right {
    float: right
}
.left {
    float: left
}
#sidebar_menu {
    background: #2396d7;
    width: 50px;
    height: 100%;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 2
}
.sidebar_nav {
    list-style: none;
    padding: 0
}
.sidebar_header {
    background: #10243e;
    padding: 15px 0;
    display: block;
    min-height: 46px
}
.sidebar_header h2 {
    color: #a5bed3;
    display: none;
    font-size: 14px;
    margin: 0 0 0 15px;
    text-transform: uppercase
}
.nav-trigger {
    position: absolute;
    clip: rect(0, 0, 0, 0)
}
label[for="nav-trigger"] {
    position: fixed;
    top: 15px;
    left: 15px;
    z-index: 2;
    width: 30px;
    height: 30px;
    cursor: pointer;
    background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' version='1.1' x='0px' y='0px' width='30px' height='30px' viewBox='0 0 30 30' enable-background='new 0 0 30 30' xml:space='preserve'><rect width='20' height='3' style='fill:rgb(165,190,211);'/><rect y='14' width='20' height='3' style='fill:rgb(165,190,211);'/><rect y='7' width='20' height='3' style='fill:rgb(165,190,211);'/></svg>");
    background-size: contain
}
.nav-trigger:checked+label {
    left: 160px
}
.nav-trigger:checked~.bodywrap {
    left: 200px;
    border-left: 1px solid #d1d1d1;
    box-shadow: -3px 0 10px rgba(0, 0, 0, 0.1)
}
.nav-trigger+label,
.bodywrap {
    transition: left 0.3s
}
.nav_item {
    font-size: 13px;
    font-weight: bold;
    border-bottom: 1px solid #59a1d6;
    position: relative
}
.nav_item:hover {
    background: #327fc0
}
.nav_item:hover .nav-icon {
    opacity: 1
}
.nav_item:hover>ul {
    display: inline
}
.nav_item a,
.nav_item a:link {
    color: #fff;
    display: block;
    padding: 10px 15px;
    white-space: nowrap;
    overflow: hidden
}
.nav_item>ul {
    display: none;
    list-style-type: none;
    background: #307ebf;
    padding: 7px 0;
    box-shadow: 3px 1px 2px rgba(0, 0, 0, 0.2);
    width: auto;
    position: absolute;
    left: 100%;
    top: 0;
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    -webkit-column-count: 2;
    -moz-column-count: 2;
    column-count: 2
}
.nav_item>ul li a,
.nav_item>ul li a:link {
    padding: 4px 15px 4px 10px;
    font-size: 12px;
    font-weight: normal
}
.nav_item>ul li a:hover {
    background: #1b6bae
}
.search_results {
    background: #fff;
    border: 1px solid #ddd;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
    display: none;
    max-height: 400px;
    width: 100%;
    overflow-y: auto;
    position: absolute;
    z-index: 1000
}
.search_results>ul {
    margin: 0;
    padding: 0
}
.search_results>ul>li {
    display: none
}
.search_results>ul>li a,
.search_results>ul>li a:link {
    color: #0d74ae;
    font-weight: normal;
    padding: 3px 10px;
    display: block
}
.search_results>ul>li a:hover {
    background: #f1f1f1
}
#no-results {
    display: none
}
.close_search {
    border: none;
    background: transparent;
    float: right;
    margin-right: 6px;
    margin-top: -24px;
    opacity: 0.2;
    padding: 0;
    display: none
}
.close_search:hover {
    opacity: .5
}
#menu-search {
    padding-left: 30px
}
#input-search-icon {
    opacity: .5;
    position: absolute;
    top: 9px;
    left: 14px
}
.menu_search_wrapper {
    padding: 7px 5px 5px
}
.search_form {
    position: relative
}
.section-container {
    border: 1px solid #e9e9e9;
    margin: 0 15px 25px 0;
    background: #fff
}
.section-container .infowrap {
    padding: 10px 15px
}
.section-container .section-header {
    color: #111;
    font-size: 14px;
    font-weight: normal;
    border: none;
    background: #e9e9e9;
    margin-bottom: 0;
    margin-top: 0;
    padding: 8px 15px
}
.section-container .dl-horizontal dt {
    text-align: left
}
body {
    overflow-x: hidden
}
.bodywrap {
    min-width: 100%;
    background-color: #fff;
    position: relative;
    top: 0;
    bottom: 100%;
    z-index: 1;
    min-height: 100vh
}
.bodywrap.withsidebar {
    left: 50px
}
.nav-icon {
    height: 23px;
    margin-right: 12px;
    vertical-align: middle;
    width: 23px;
    display: inline-block;
    opacity: .7
}
.icon-custinfo {
    background: url(icon-custinfo.png) no-repeat
}
.icon-deposit {
    background: url(icon-deposit.png) no-repeat
}
.icon-loan {
    background: url(icon-loan.png) no-repeat
}
.icon-tellertxn {
    background: url(icon-tellertxn.png) no-repeat
}
.icon-ledger {
    background: url(icon-ledger.png) no-repeat
}
.icon-sysad {
    background: url(icon-sysad.png) no-repeat
}
.icon-config {
    background: url(icon-config.png) no-repeat
}
.icon-audit {
    background: url(icon-audit.png) no-repeat
}
.icon-depositconfig {
    background: url(icon-depositconfig.png) no-repeat
}
.icon-pdf,
.icon-xls {
    display: inline-block;
    height: 17px;
    margin-right: 5px;
    vertical-align: middle;
    width: 17px
}
.icon-pdf {
    background: url(icon-pdf.png) no-repeat
}
.icon-xls {
    background: url(icon-xls.png) no-repeat
}
.graycontainer {
    background: #f9f9f9;
    padding: 12px
}
.paginateButtons {
    background: #fff url(shadow.jpg) bottom repeat-x;
    border: 1px solid #ccc;
    border-top: 0;
    color: #666;
    font-size: 10px;
    overflow: hidden;
    padding: 10px 3px;
}
.paginateButtons a {
    background: #fff;
    border: 1px solid #ccc;
    border-color: #ccc #aaa #aaa #ccc;
    color: #666;
    margin: 0 3px;
    padding: 2px 6px;
}
.paginateButtons span {
    padding: 2px 3px;
}




/* added by akdsauli */
.bg-black {
    background-color: black !important;
}
.bg-gray {
    background-color: #eaeaec !important;
    padding: 10px 20px 10px 10px
}
.bodywrap.loginbottom {
    min-width: 100%;
    background-color: black;
    position: relative;
    top: 0;
    bottom: 100%;
    z-index: 1;
    min-height: 100vh
}
.btn-primary-login {
    background-color: #000000;
    border-color: #cccccc;
    color: #ffffff;
    width: 290px;
    height: 40px;
    position: absolute;
    top:0px;
    bottom: 20px;
    left: 5px;
    right: 160px;
    padding-top: 10px;
    /* padding-left: 20px; */
    margin: auto;
}
.form-box {
    width: 360px;
    margin: 90px auto 0 auto;
    position: absolute;
    top:20px;
    bottom: 0px;
    left: 0;
    right: 0;
}

.form-box-msg {
    width: 360px;
    margin: auto;
    position: absolute;
    top:10px;
    bottom: 0px;
    left: 0;
    right: 0;
}

.form-box .header {
    -webkit-border-top-left-radius: 4px;
    -webkit-border-top-right-radius: 4px;
    -webkit-border-bottom-right-radius: 0;
    -webkit-border-bottom-left-radius: 0;
    -moz-border-radius-topleft: 4px;
    -moz-border-radius-topright: 4px;
    -moz-border-radius-bottomright: 0;
    -moz-border-radius-bottomleft: 0;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    background-color: #fdf056;
    box-shadow: inset 0px -3px 0px rgba(0, 0, 0, 0.2);
    /* padding: 20px 10px; */
    text-align: center;
    font-size: 26px;
    font-weight: 300;
    color: #fff;
}

.banner {
  display: block;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  /* background-image: url('../images/expresso.png') no-repeat; */
  width: 100%; /* Width of new image */
  height: 100%; /* Height of new image */
  /* margin-left: 30px; */
}
.form-box .footer {
    -webkit-border-top-left-radius: 0;
    -webkit-border-top-right-radius: 0;
    -webkit-border-bottom-right-radius: 4px;
    -webkit-border-bottom-left-radius: 4px;
    -moz-border-radius-topleft: 0;
    -moz-border-radius-topright: 0;
    -moz-border-radius-bottomright: 4px;
    -moz-border-radius-bottomleft: 4px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    height: 80px
}

.form-box .body, .form-box .footer {
    padding: 10px 20px;
    background: blue;
    color: #444;
}
.form-control1 {
    -webkit-border-radius: 0px !important;
    -moz-border-radius: 0px !important;
    border-radius: 0px !important;
    box-shadow: none;
}
.form-control1 {
    display: block;
    width: 100%;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.428571429;
    color: #555;
    vertical-align: middle;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);
    -webkit-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
}
.btn-block {
    display: block;
    width: 100%;
    padding-right: 0;
    padding-left: 0;
}
.form-group1 {
    margin-top: 20px;
    margin-bottom: 15px;
}
.form-box .footer {
    -webkit-border-top-left-radius: 0;
    -webkit-border-top-right-radius: 0;
    -webkit-border-bottom-right-radius: 4px;
    -webkit-border-bottom-left-radius: 4px;
    -moz-border-radius-topleft: 0;
    -moz-border-radius-topright: 0;
    -moz-border-radius-bottomright: 4px;
    -moz-border-radius-bottomleft: 4px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
}
.form-box .body, .form-box .footer {
    padding: 10px 20px;
    background: #fff;
    color: #444;
}

*, *:before, *:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

div {
    display: block;
}

.btn1 {
    font-weight: 500;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    border: 1px solid transparent;
    -webkit-box-shadow: inset 0px -2px 0px 0px rgba(0, 0, 0, 0.09);
    -moz-box-shadow: inset 0px -2px 0px 0px rgba(0, 0, 0, 0.09);
    box-shadow: inset 0px -1px 0px 0px rgba(0, 0, 0, 0.09);
}
.bg-black {
    background-color: black !important;
    color: #f9f9f9 !important;
}

.btn-block {
    display: block;
    width: 100%;
    padding-right: 0;
    padding-left: 0;
}
.btn1 {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.428571429;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
}
.box-bodylogin {
    height: 100%;
    width: 100%;
}
