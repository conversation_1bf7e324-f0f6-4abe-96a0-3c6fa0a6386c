/**
 * Select2 Bootstrap 3 CSS v1.4.3
 * Tested with Bootstrap v3.3.1 and Select2 v3.3.2, v3.4.1-v3.4.5, v3.5.1, v3.5.2, master
 * in latest Chrome, Safari, Firefox, Opera (Mac) and IE8-IE11
 * MIT License
 */
/**
 * Reset Bootstrap 3 .form-control styles which - if applied to the
 * original <select>-element the Select2-plugin may be run against -
 * are copied to the .select2-container.
 *
 * 1. Overwrite .select2-container's original display:inline-block
 *    with Bootstrap 3's default for .form-control, display:block;
 *    courtesy of @juristr (@see https://github.com/fk/select2-bootstrap-css/pull/1)
 */
.select2-container.form-control {
  background: transparent;
  box-shadow: none;
  border: none;
  display: block;
  /* 1 */
  margin: 0;
  padding: 0;
}

/**
 * Adjust Select2 inputs to fit Bootstrap 3 default .form-control appearance.
 */
.select2-container .select2-choices .select2-search-field input,
.select2-container .select2-choice,
.select2-container .select2-choices {
  background: none;
  padding: 0;
  border-color: #cccccc;
  border-radius: 4px;
  color: #555555;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  background-color: white;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.select2-search input {
  border-color: #cccccc;
  border-radius: 4px;
  color: #555555;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  background-color: white;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.select2-container .select2-choices .select2-search-field input {
  -webkit-box-shadow: none;
  box-shadow: none;
}

/**
 * Adjust Select2 input heights to match the Bootstrap default.
 */
.select2-container .select2-choice {
  height: 34px;
  line-height: 1.42857;
}

/**
 * Address Multi Select2's height which - depending on how many elements have been selected -
 * may grown higher than their initial size.
 */
.select2-container.select2-container-multi.form-control {
  height: auto;
}

/**
 * Address Bootstrap 3 control sizing classes
 * @see http://getbootstrap.com/css/#forms-control-sizes
 */
.select2-container.input-sm .select2-choice,
.input-group-sm .select2-container .select2-choice {
  height: 30px;
  line-height: 1.5;
  border-radius: 3px;
}

.select2-container.input-lg .select2-choice,
.input-group-lg .select2-container .select2-choice {
  height: 46px;
  line-height: 1.33;
  border-radius: 6px;
}

.select2-container-multi .select2-choices .select2-search-field input {
  height: 32px;
}

.select2-container-multi.input-sm .select2-choices .select2-search-field input,
.input-group-sm .select2-container-multi .select2-choices .select2-search-field input {
  height: 28px;
}

.select2-container-multi.input-lg .select2-choices .select2-search-field input,
.input-group-lg .select2-container-multi .select2-choices .select2-search-field input {
  height: 44px;
}

/**
 * Adjust height and line-height for .select2-search-field amd multi-select Select2 widgets.
 *
 * 1. Class repetition to address missing .select2-chosen in Select2 < 3.3.2.
 */
.select2-container-multi .select2-choices .select2-search-field input {
  margin: 0;
}

.select2-chosen,
.select2-choice > span:first-child,
.select2-container .select2-choices .select2-search-field input {
  padding: 6px 12px;
}

.input-sm .select2-chosen,
.input-group-sm .select2-chosen,
.input-sm .select2-choice > span:first-child,
.input-group-sm .select2-choice > span:first-child,
.input-sm .select2-choices .select2-search-field input,
.input-group-sm .select2-choices .select2-search-field input {
  padding: 5px 10px;
}

.input-lg .select2-chosen,
.input-group-lg .select2-chosen,
.input-lg .select2-choice > span:first-child,
.input-group-lg .select2-choice > span:first-child,
.input-lg .select2-choices .select2-search-field input,
.input-group-lg .select2-choices .select2-search-field input {
  padding: 10px 16px;
}

.select2-container-multi .select2-choices .select2-search-choice {
  margin-top: 5px;
  margin-bottom: 3px;
}

.select2-container-multi.input-sm .select2-choices .select2-search-choice,
.input-group-sm .select2-container-multi .select2-choices .select2-search-choice {
  margin-top: 3px;
  margin-bottom: 2px;
}

.select2-container-multi.input-lg .select2-choices .select2-search-choice,
.input-group-lg .select2-container-multi .select2-choices .select2-search-choice {
  line-height: 24px;
}

/**
 * Adjust the single Select2's dropdown arrow button appearance.
 *
 * 1. For Select2 v.3.3.2.
 */
.select2-container .select2-choice .select2-arrow,
.select2-container .select2-choice div {
  border-left: none;
  background: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}

.select2-dropdown-open .select2-choice .select2-arrow,
.select2-dropdown-open .select2-choice div {
  border-left-color: transparent;
  background: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}

/**
 * Adjust the dropdown arrow button icon position for the single-select Select2 elements
 * to make it line up vertically now that we increased the height of .select2-container.
 *
 * 1. Class repetition to address missing .select2-chosen in Select2 v.3.3.2.
 */
.select2-container .select2-choice .select2-arrow b,
.select2-container .select2-choice div b {
  background-position: 0 3px;
}

.select2-dropdown-open .select2-choice .select2-arrow b,
.select2-dropdown-open .select2-choice div b {
  background-position: -18px 3px;
}

.select2-container.input-sm .select2-choice .select2-arrow b,
.input-group-sm .select2-container .select2-choice .select2-arrow b,
.select2-container.input-sm .select2-choice div b,
.input-group-sm .select2-container .select2-choice div b {
  background-position: 0 1px;
}

.select2-dropdown-open.input-sm .select2-choice .select2-arrow b,
.input-group-sm .select2-dropdown-open .select2-choice .select2-arrow b,
.select2-dropdown-open.input-sm .select2-choice div b,
.input-group-sm .select2-dropdown-open .select2-choice div b {
  background-position: -18px 1px;
}

.select2-container.input-lg .select2-choice .select2-arrow b,
.input-group-lg .select2-container .select2-choice .select2-arrow b,
.select2-container.input-lg .select2-choice div b,
.input-group-lg .select2-container .select2-choice div b {
  background-position: 0 9px;
}

.select2-dropdown-open.input-lg .select2-choice .select2-arrow b,
.input-group-lg .select2-dropdown-open .select2-choice .select2-arrow b,
.select2-dropdown-open.input-lg .select2-choice div b,
.input-group-lg .select2-dropdown-open .select2-choice div b {
  background-position: -18px 9px;
}

/**
 * Address Bootstrap's validation states and change Select2's border colors and focus states.
 * Apply .has-warning, .has-danger or .has-succes to #select2-drop to match Bootstraps' colors.
 */
.has-warning .select2-choice,
.has-warning .select2-choices {
  border-color: #8a6d3b;
}
.has-warning .select2-container-active .select2-choice,
.has-warning .select2-container-multi.select2-container-active .select2-choices {
  border-color: #66512c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
}
.has-warning.select2-drop-active {
  border-color: #66512c;
}
.has-warning.select2-drop-active.select2-drop.select2-drop-above {
  border-top-color: #66512c;
}

.has-error .select2-choice,
.has-error .select2-choices {
  border-color: #a94442;
}
.has-error .select2-container-active .select2-choice,
.has-error .select2-container-multi.select2-container-active .select2-choices {
  border-color: #843534;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
}
.has-error.select2-drop-active {
  border-color: #843534;
}
.has-error.select2-drop-active.select2-drop.select2-drop-above {
  border-top-color: #843534;
}

.has-success .select2-choice,
.has-success .select2-choices {
  border-color: #3c763d;
}
.has-success .select2-container-active .select2-choice,
.has-success .select2-container-multi.select2-container-active .select2-choices {
  border-color: #2b542c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
}
.has-success.select2-drop-active {
  border-color: #2b542c;
}
.has-success.select2-drop-active.select2-drop.select2-drop-above {
  border-top-color: #2b542c;
}

/**
 * Make Select2's active-styles - applied to .select2-container when the widget receives focus -
 * fit Bootstrap 3's .form-element:focus appearance.
 */
.select2-container-active .select2-choice,
.select2-container-multi.select2-container-active .select2-choices {
  border-color: #66afe9;
  outline: none;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
  -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

.select2-drop-active {
  border-color: #66afe9;
}

.select2-drop-auto-width,
.select2-drop.select2-drop-above.select2-drop-active {
  border-top-color: #66afe9;
}

/**
 * Select2 widgets in Bootstrap Input Groups
 *
 * When Select2 widgets are combined with other elements using Bootstrap 3's
 * "Input Group" component, we don't want specific edges of the Select2 container
 * to have a border-radius.
 *
 * In Bootstrap 2, input groups required a markup where these style adjustments
 * could be bound to a CSS-class identifying if the additional elements are appended,
 * prepended or both.
 *
 * Bootstrap 3 doesn't rely on these classes anymore, so we have to use our own.
 * Use .select2-bootstrap-prepend and .select2-bootstrap-append on a Bootstrap 3 .input-group
 * to let the contained Select2 widget know which edges should not be rounded as they are
 * directly followed by another element.
 *
 * @see http://getbootstrap.com/components/#input-groups
 */
.input-group.select2-bootstrap-prepend [class^="select2-choice"] {
  border-bottom-left-radius: 0 !important;
  border-top-left-radius: 0 !important;
}

.input-group.select2-bootstrap-append [class^="select2-choice"] {
  border-bottom-right-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

.select2-dropdown-open [class^="select2-choice"] {
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.select2-dropdown-open.select2-drop-above [class^="select2-choice"] {
  border-top-right-radius: 0 !important;
  border-top-left-radius: 0 !important;
  border-bottom-right-radius: 4px !important;
  border-bottom-left-radius: 4px !important;
  background: white;
  filter: none;
}
.input-group.select2-bootstrap-prepend .select2-dropdown-open.select2-drop-above [class^="select2-choice"] {
  border-bottom-left-radius: 0 !important;
  border-top-left-radius: 0 !important;
}
.input-group.select2-bootstrap-append .select2-dropdown-open.select2-drop-above [class^="select2-choice"] {
  border-bottom-right-radius: 0 !important;
  border-top-right-radius: 0 !important;
}
.input-group.input-group-sm.select2-bootstrap-prepend .select2-dropdown-open.select2-drop-above [class^="select2-choice"] {
  border-bottom-right-radius: 3px !important;
}
.input-group.input-group-lg.select2-bootstrap-prepend .select2-dropdown-open.select2-drop-above [class^="select2-choice"] {
  border-bottom-right-radius: 6px !important;
}
.input-group.input-group-sm.select2-bootstrap-append .select2-dropdown-open.select2-drop-above [class^="select2-choice"] {
  border-bottom-left-radius: 3px !important;
}
.input-group.input-group-lg.select2-bootstrap-append .select2-dropdown-open.select2-drop-above [class^="select2-choice"] {
  border-bottom-left-radius: 6px !important;
}

/**
 * Adjust Select2's choices hover and selected styles to match Bootstrap 3's default dropdown styles.
 */
.select2-results .select2-highlighted {
  color: white;
  background-color: #337ab7;
}

/**
 * Adjust alignment of Bootstrap 3 buttons in Bootstrap 3 Input Groups to address
 * Multi Select2's height which - depending on how many elements have been selected -
 * may grown higher than their initial size.
 */
.select2-bootstrap-append .select2-container-multiple,
.select2-bootstrap-append .input-group-btn,
.select2-bootstrap-append .input-group-btn .btn,
.select2-bootstrap-prepend .select2-container-multiple,
.select2-bootstrap-prepend .input-group-btn,
.select2-bootstrap-prepend .input-group-btn .btn {
  vertical-align: top;
}

/**
 * Make Multi Select2's choices match Bootstrap 3's default button styles.
 */
.select2-container-multi .select2-choices .select2-search-choice {
  color: #555555;
  background: white;
  border-color: #cccccc;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  -webkit-box-shadow: none;
  box-shadow: none;
}

.select2-container-multi .select2-choices .select2-search-choice-focus {
  background: #ebebeb;
  border-color: #adadad;
  color: #333333;
  -webkit-box-shadow: none;
  box-shadow: none;
}

/**
 * Address Multi Select2's choice close-button vertical alignment.
 */
.select2-search-choice-close {
  margin-top: -7px;
  top: 50%;
}

/**
 * Adjust the single Select2's clear button position (used to reset the select box
 * back to the placeholder value and visible once a selection is made
 * activated by Select2's "allowClear" option).
 */
.select2-container .select2-choice abbr {
  top: 50%;
}

/**
 * Adjust "no results" and "selection limit" messages to make use
 * of Bootstrap 3's default "Alert" style.
 *
 * @see http://getbootstrap.com/components/#alerts-default
 */
.select2-results .select2-no-results,
.select2-results .select2-searching,
.select2-results .select2-selection-limit {
  background-color: #fcf8e3;
  color: #8a6d3b;
}

/**
 * Address disabled Select2 styles.
 *
 * 1. For Select2 v.3.3.2.
 * 2. Revert border-left:0 inherited from Select2's CSS to prevent the arrow
 *    from jumping when switching from disabled to enabled state and vice versa.
 */
.select2-container.select2-container-disabled .select2-choice,
.select2-container.select2-container-disabled .select2-choices {
  cursor: not-allowed;
  background-color: #eeeeee;
  border-color: #cccccc;
}
.select2-container.select2-container-disabled .select2-choice .select2-arrow,
.select2-container.select2-container-disabled .select2-choice div,
.select2-container.select2-container-disabled .select2-choices .select2-arrow,
.select2-container.select2-container-disabled .select2-choices div {
  background-color: transparent;
  border-left: 1px solid transparent;
  /* 2 */
}

/**
 * Address Select2's loading indicator position - which should not stick
 * to the right edge of Select2's search input.
 *
 * 1. in .select2-search input
 * 2. in Multi Select2's .select2-search-field input
 * 3. in the status-message of infinite-scroll with remote data (@see http://ivaynberg.github.io/select2/#infinite)
 *
 * These styles alter Select2's default background-position of 100%
 * and supply the new background-position syntax to browsers which support it:
 *
 * 1. Android, Safari < 6/Mobile, IE<9: change to a relative background-position of 99%
 * 2. Chrome 25+, Firefox 13+, IE 9+, Opera 10.5+: use the new CSS3-background-position syntax
 *
 * @see http://www.w3.org/TR/css3-background/#background-position
 *
 * @todo Since both Select2 and Bootstrap 3 only support IE8 and above,
 * we could use the :after-pseudo-element to display the loading indicator.
 * Alternatively, we could supply an altered loading indicator image which already
 * contains an offset to the right.
 */
.select2-search input.select2-active,
.select2-container-multi .select2-choices .select2-search-field input.select2-active,
.select2-more-results.select2-active {
  background-position: 99%;
  /* 4 */
  background-position: right 4px center;
  /* 5 */
}

/**
 * To support Select2 pre v3.4.2 in combination with Bootstrap v3.2.0,
 * ensure that .select2-offscreen width, height and position can not be overwritten.
 *
 * This adresses changes in Bootstrap somewhere after the initial v3.0.0 which -
 * in combination with Select2's pre-v3.4.2 CSS missing the "!important" after
 * the following rules - allow Bootstrap to overwrite the latter, which results in
 * the original <select> element Select2 is replacing not be properly being hidden
 * when used in a "Bootstrap Input Group with Addon".
 **/
.select2-offscreen,
.select2-offscreen:focus {
  width: 1px !important;
  height: 1px !important;
  position: absolute !important;
}