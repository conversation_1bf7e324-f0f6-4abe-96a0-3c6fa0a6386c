
.sidebar{
      z-index: 999999;
      position: fixed;
      top: -1px;
      bottom: -1px;
      padding:0px;
      width: auto;
      background-color: #fff;
      -webkit-background-clip: padding-box;
      background-clip: padding-box;
      border: 1px solid #ccc;
      border: 1px solid rgba(0, 0, 0, .15);
      -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
      box-shadow: 0 6px 12px rgba(0, 0, 0, .175);

}

.sidebar > .row > .col-xs-12, .sidebar > .row > .col-sm-12, .sidebar > .row > .col-md-12, .sidebar > .row > .col-lg-12{
      padding:0px;
      position: absolute;
      bottom: 0px;
      top: 0px;
      overflow: auto;           
}

.sidebar >.row{
      margin:0px;
}

.sidebar {
      width: auto;
}
.sidebar.sidebar-left{
      left:0px;
      right:20px;
}

.sidebar.sidebar-right{
      right:0px;
      left:20px;
}

@media (min-width: 400px) {

}
@media (min-width: 528px) {
      .sidebar {
            width: 300px;
      }
      .sidebar.sidebar-left{
            left:0px;
            right:auto;
      }

      .sidebar.sidebar-right{
            right:0px;
            left:auto;
      }
}
@media (min-width: 768px) {
      .sidebar {
            width: 230px;
      }
      .sidebar.sidebar-left{
            left:0px;
            right:auto;
      }

      .sidebar.sidebar-right{
            right:0px;
            left:auto;
      }
}
@media (min-width: 992px) {
      .sidebar {
            width: 230px;
      }
      .sidebar.sidebar-left{
            left:0px;
            right:auto;
      }

      .sidebar.sidebar-right{
            right:0px;
            left:auto;
      }
}
@media (min-width: 1200px) {
      .sidebar {
            width: 230px;
      }
      .sidebar.sidebar-left{
            left:0px;
            right:auto;
      }

      .sidebar.sidebar-right{
            right:0px;
            left:auto;
      }
}
.toggler{
      -webkit-background-clip: padding-box;
      background-clip: padding-box;
      border: 1px solid #ccc;
      border: 1px solid rgba(0, 0, 0, .15);
      width: 20px;
      height: 48px;
      position: absolute;
      top: 45%;
      cursor: pointer;
}
.sidebar-left > .toggler{
      -webkit-border-top-right-radius: 4px;
      -webkit-border-bottom-right-radius: 4px;
      -moz-border-radius-topright: 4px;
      -moz-border-radius-bottomright: 4px;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      border-left: 1px solid #fff;
      -webkit-box-shadow: 1px 0px 8px rgba(0, 0, 0, .175);
      box-shadow: 1px 0px 8px rgba(0, 0, 0, .175);
      right: -20px;
} 

.sidebar-left > .toggler > span{
      margin:15px 2px;
}

.sidebar-left > .toggler > .glyphicon-chevron-right{
      display:none;
}

.sidebar-right > .toggler{
      -webkit-border-top-left-radius: 4px;
      -webkit-border-bottom-left-radius: 4px;
      -moz-border-radius-topleft: 4px;
      -moz-border-radius-bottomleft: 4px;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      border-right: 1px solid #fff;
      -webkit-box-shadow: -1px 0px 8px rgba(0, 0, 0, .175);
      box-shadow: -1px 0px 8px rgba(0, 0, 0, .175);      		
      left: -20px;
} 

.sidebar-right > .toggler > span{
      margin:15px 2px;
}

.sidebar-right > .toggler > .glyphicon-chevron-left{
      display:none;
}

.sidebar .submenu{
      display: none;
      position: fixed;
} 

/*Native BootStrap Hack*/
.sidebar .list-group-item:last-child, .sidebar .list-group-item:first-child{
      border-radius:0px;
}

.sidebar .list-group{
      margin-bottom:0px;
}
.sidebar .list-group-item{
      padding: 5px;
      border: 1px solid #DDD;
      border-left: 0px;
      border-right: 0px;
      margin-bottom: 0px;
      margin-top: -1px;
}


  .user{
    padding:5px;
    margin-bottom: 5px;
  }


