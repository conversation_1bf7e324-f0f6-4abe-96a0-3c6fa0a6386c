/*
    Component: Buttons
-------------------------
*/

.btn {
    font-weight: 500;
    .border-radius(@btn-border-radius);
    border: 1px solid transparent;
    -webkit-box-shadow: inset 0px -2px 0px 0px rgba(0,0,0,0.09);
    -moz-box-shadow: inset 0px -2px 0px 0px rgba(0,0,0,0.09);
    box-shadow: inset 0px -1px 0px 0px rgba(0,0,0,0.09);
    //Button color variations 
    &.btn-default {
        background-color: #2396d7;
        color: #666;
        border-color: #ddd;
        border-bottom-color: #ddd;
        &:hover, &:active, &.hover {
            background-color: #f4f4f4!important;
        }
        &.btn-flat {
            border-bottom-color: darken(#e6e7e8, 5%);
        }
    }
    &.btn-primary {
        background-color: @light-blue;
        border-color: darken(@light-blue, 5%);
        &:hover, &:active, &.hover {
            background-color: darken(@light-blue, 5%);
        }
    }
    &.btn-success {
        background-color: @green;
        border-color: darken(@green, 5%);
        &:hover, &:active, &.hover {
            background-color: darken(@green, 5%);
        }
    }
    &.btn-info {
        background-color: @aqua;
        border-color: darken(@aqua, 5%);
        &:hover, &:active, &.hover {
            background-color: darken(@aqua, 5%);
        }
    }
    &.btn-danger {
        background-color: @red;
        border-color: darken(@red, 5%);
        &:hover, &:active, &.hover {
            background-color: darken(@red, 5%);
        }
    }
    &.btn-warning {
        background-color: @yellow;
        border-color: darken(@yellow, 5%);
        &:hover, &:active, &.hover {
            background-color: darken(@yellow, 5%);
        }
    }
    
    // Flat buttons 
    &.btn-flat {
        .border-radius(0);
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
        border-width: 1px;
    }

    // Active state 
    &:active {
        -webkit-box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
        -moz-box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
        box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
    }

    &:focus {
        outline: none;
    }

    // input file btn 
    &.btn-file {
        position: relative;
        width: 120px;
        height: 35px;
        overflow: hidden;
        > input[type='file'] {
            display: block !important;
            width: 100% !important;
            height: 35px !important;
            opacity: 0 !important;
            position: absolute;
            top: -10px;
            cursor: pointer;
        }
    }

    // Application buttons 
    &.btn-app {
        position: relative;
        padding: 15px 5px;
        margin: 0 0 10px 10px;
        min-width: 80px;
        height: 60px;
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none; 
        .border-radius(0);
        text-align: center;
        color: #666;
        border: 1px solid #ddd;
        background-color: #fafafa;
        font-size: 12px;
        //Icons within the btn
        > .fa, > .glyphicon, > .ion {
            font-size: 20px;   
            display: block;
        }

        &:hover {
            background: #f4f4f4;
            color: #444;
            border-color: #aaa;
        }

        &:active, &:focus {
            -webkit-box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
            -moz-box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
            box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
        }

        //The bandge
        > .badge {
            position: absolute;
            top: -3px;
            right: -10px;
            font-size: 10px;
            font-weight: 400;
        }
    }

    //Social buttons
    &.btn-social-old {
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
        opacity: 0.9;
        //Only font-awesome provides social icons
        padding: 0;
        > .fa {            
            padding: 10px 0;
            width: 40px;
        }
        > .fa + span {
            border-left: 1px solid rgba(255, 255,255, 0.3);            
        }
        span {
            padding: 10px;
        }

        &:hover {
            opacity: 1;
        }
    }
    
    &.btn-circle {
        width: 30px;
        height: 30px;
        line-height: 30px;
        padding: 0;
        .border-radius(50%);
    }

}