/*
    Component: Sidebar
--------------------------
*/

.sidebar {
    margin-bottom: 5px;
    // remove shadow from form 
    .sidebar-form {
        input:focus {
            -webkit-box-shadow: none;
            -moz-box-shadow: none;
            box-shadow: none;
            border-color: transparent!important;
        }
    }
    // Sidebar menu 
    .sidebar-menu {
        list-style: none;
        margin: 0;
        padding: 0;
        > li {
            margin: 0;
            padding: 0;
            > a {
                padding: 12px 5px 12px 15px;
                display: block;
                > .fa, > .glyphicon, > .ion {
                    width: 20px;
                }
            }
        }

        // Tree view menu 
        .treeview-menu {
            display: none;
            list-style: none;
            padding:0;
            margin:0;
            > li {
                margin: 0;
                > a {
                    padding: 5px 5px 5px 15px;
                    display: block;
                    font-size: 14px;
                    margin: 0px 0px;
                    > .fa, > .glyphicon, > .ion {
                        width: 20px;
                    }
                }
            }
        }
    }
}

//Sidebar user panel
.user-panel {
    padding: 10px;
    .clearfix();
    > .image > img {
        width: 45px;
        height: 45px;        
    }
    > .info {
        font-weight: 600;
        padding: 5px 5px 5px 15px;
        font-size: 14px;
        line-height: 1;
    }
    > .info > p {
        margin-bottom: 9px;
    }
    > .info > a {
        text-decoration: none;
        padding-right: 5px;
        margin-top: 3px;
        font-size: 11px;
        font-weight: normal;
        > .fa, > .ion, > .glyphicon {
            margin-right: 3px;
        }
    }
}

/*
 * Off Canvas
 * --------------------------------------------------
 *  Gives us the push menu effect
 */
@media screen and (max-width: @screen-md) {

    .relative {
        position: relative;
    }

    .row-offcanvas-right
    .sidebar-offcanvas {
        right: -@left-side-width;
    }

    .row-offcanvas-left
    .sidebar-offcanvas {
        left: -@left-side-width;
    }

    .row-offcanvas-right.active {
        right: @left-side-width;
    }

    .row-offcanvas-left.active {
        left: @left-side-width;
    }

    .sidebar-offcanvas {
        left: 0;
    }

    body.fixed {
        .sidebar-offcanvas {
            margin-top: 50px;
            left: -@left-side-width;
        }
        .row-offcanvas-left.active {
            .navbar {
                left: @left-side-width!important;
                right: 0;

            }
            .sidebar-offcanvas {
                left: 0px;
            }

        }
    }
}