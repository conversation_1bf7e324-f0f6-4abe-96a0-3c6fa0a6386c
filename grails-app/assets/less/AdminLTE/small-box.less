/*
    Component: Small boxes
*/

.small-box {
    position: relative;
    display: block;
    .border-radius(2px);
    margin-bottom: 15px;
    // content wrapper
    > .inner {
        padding: 10px;
    }

    > .small-box-footer {
        position: relative;
        text-align: center;
        padding: 3px 0;
        color: #fff;
        color: rgba(255, 255, 255, 0.8);
        display: block;
        z-index: 10;
        background: rgba(0,0,0,0.1);
        text-decoration: none;
        &:hover {
            color: #fff;
            background: rgba(0,0,0,0.15);
        }
    }

    h3 {
        font-size: 38px;
        font-weight: bold;
        margin: 0 0 10px 0;
        white-space: nowrap;
        padding: 0;

    }

    p {
        font-size: 15px;
        > small {
            display: block;
            color: #f9f9f9;
            font-size: 13px;
            margin-top: 5px;
        }
    }

    h3, p {
        z-index: 5px;
    }

    // the icon
    .icon {
        position: absolute;
        top: auto;
        bottom: 5px;
        right: 5px;
        z-index: 0;
        font-size: 90px;
        color: rgba(0, 0, 0, 0.15);
    }

    // Small box hover state
    &:hover {
        text-decoration: none;
        color: #f9f9f9;
        // Animate icons on small box hover
        .icon {
            animation-name: tansformAnimation;
            animation-duration:.5s;
            animation-iteration-count: 1;
            animation-timing-function: ease;
            animation-fill-mode: forwards;

            -webkit-animation-name: tansformAnimation;
            -webkit-animation-duration:.5s;
            -webkit-animation-iteration-count: 1;
            -webkit-animation-timing-function: ease;
            -webkit-animation-fill-mode: forwards;

            -moz-animation-name: tansformAnimation;
            -moz-animation-duration:.5s;
            -moz-animation-iteration-count: 1;
            -moz-animation-timing-function: ease;
            -moz-animation-fill-mode: forwards;
        }
    }
}

// Transform: rotate -10 degrees within 500 milliseconds
@keyframes tansformAnimation {
    from {
        font-size: 90px;
    }
    to {
        font-size: 100px;
    }
}
@-webkit-keyframes tansformAnimation {
    from {
        font-size: 90px;
    }
    to {
        font-size: 100px;
    }
}

@media screen and (max-width: @screen-xs) {
    // No need for icons on very small devices
    .small-box {
        text-align: center;
        .icon {
            display: none;
        }
        p {
            font-size: 12px;
        }
    }

}