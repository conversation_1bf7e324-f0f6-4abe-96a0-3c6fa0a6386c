package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql
import org.icbs.deposit.StandingOrder
import org.icbs.lov.StandingOrderStatus
import org.icbs.lov.StandingOrderFrequency

/**
 * DepositStandingOrderController - Handles deposit standing order operations
 * 
 * This controller manages standing order operations including:
 * - Standing order creation and management
 * - Standing order execution and scheduling
 * - Standing order inquiry and tracking
 * - Standing order cancellation and modification
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositStandingOrderController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * View standing order management page
     * @param id The deposit account ID
     */
    def viewStandingOrder() {
        try {
            log.info("Viewing standing orders for deposit ID: ${params.id}")
            
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                if (depositInstance.branch == UserMaster.get(session.user_id).branch) {
                    render(view:'standingOrder/view', model:[depositInstance:depositInstance])
                } else {
                    flash.message = 'Standing Order functions not allowed for other branch account'
                    render(view:"inquiry/depositInquiry", model:[depositInstance:depositInstance])
                }
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error viewing standing orders: ${e.message}", e)
            flash.message = "Error loading standing order page |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Show standing order form via AJAX
     * @return JSON response with standing order grid
     */
    def showStandingOrderFormAjax() {
        try {
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                render(template:"standingOrder/viewGrid", model:[depositInstance:depositInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error showing standing order form: ${e.message}", e)
            render([error: "Unable to load standing order form"] as JSON)
        }
    }

    /**
     * Create new standing order via AJAX
     * @return JSON response with create form
     */
    def createStandingOrderAjax() {
        try {
            log.info("Creating standing order with params: ${params}")
            
            if(params.id) {
                def standingOrderInstance = new StandingOrder()
                standingOrderInstance.fromAccount = Deposit.read(params.id)
                standingOrderInstance.createdBy = UserMaster.get(session.user_id)
                standingOrderInstance.createdDate = new Date()
                
                if(!standingOrderInstance.fromAccount) {
                    notFound()
                    return
                }
                
                render(template:'standingOrder/create', model:[standingOrderInstance:standingOrderInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error creating standing order: ${e.message}", e)
            render([error: "Unable to create standing order"] as JSON)
        }
    }

    /**
     * Edit standing order via AJAX
     * @return JSON response with edit form
     */
    def editStandingOrderAjax() {
        try {
            if(params.id) {
                def standingOrderInstance = StandingOrder.read(params.id)
                if(!standingOrderInstance) {
                    notFound()
                    return
                }
                
                render(template:'standingOrder/edit', model:[standingOrderInstance:standingOrderInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error editing standing order: ${e.message}", e)
            render([error: "Unable to edit standing order"] as JSON)
        }
    }

    /**
     * Save new standing order via AJAX
     * @return JSON response with save result
     */
    @Transactional
    def saveStandingOrderAjax() {
        try {
            log.info("Saving standing order with params: ${params}")
            
            def standingOrderInstance = new StandingOrder()
            bindData(standingOrderInstance, params)
            
            if (!standingOrderInstance.validate()) {
                render(template:'standingOrder/create', model:[standingOrderInstance:standingOrderInstance]) as JSON
                return
            }
            
            // Validate standing order business rules
            if (!validateStandingOrder(standingOrderInstance)) {
                render(template:'standingOrder/create', model:[standingOrderInstance:standingOrderInstance]) as JSON
                return
            }
            
            standingOrderInstance.status = StandingOrderStatus.read(1) // Active
            standingOrderInstance.createdBy = UserMaster.get(session.user_id)
            standingOrderInstance.createdDate = new Date()
            standingOrderInstance.nextExecutionDate = calculateNextExecutionDate(standingOrderInstance)
            standingOrderInstance.save(flush: true, failOnError: true)
            
            // Log the standing order creation
            AuditLogService.insert('080', 'DEP00530', 
                "Standing order created for account ${standingOrderInstance.fromAccount.acctNo} - Amount: ${standingOrderInstance.amount}", 
                'deposit', null, null, null, standingOrderInstance.fromAccount.id)
            
            flash.message = "Standing Order Successfully Created"
            render(template:'standingOrder/create', 
                model:[standingOrderInstance:new StandingOrder(fromAccount:standingOrderInstance.fromAccount)]) as JSON
            
        } catch (Exception e) {
            log.error("Error saving standing order: ${e.message}", e)
            render([error: "Unable to save standing order: ${e.message}"] as JSON)
        }
    }

    /**
     * Update standing order via AJAX
     * @return JSON response with update result
     */
    @Transactional
    def updateStandingOrderAjax() {
        try {
            def standingOrderInstance = StandingOrder.get(params.id)
            if (!standingOrderInstance) {
                notFound()
                return
            }
            
            bindData(standingOrderInstance, params)
            
            if (!standingOrderInstance.validate()) {
                render(template:'standingOrder/edit', model:[standingOrderInstance:standingOrderInstance]) as JSON
                return
            }
            
            // Validate updated standing order
            if (!validateStandingOrder(standingOrderInstance)) {
                render(template:'standingOrder/edit', model:[standingOrderInstance:standingOrderInstance]) as JSON
                return
            }
            
            // Recalculate next execution date if frequency or start date changed
            if (standingOrderInstance.isDirty('frequency') || standingOrderInstance.isDirty('startDate')) {
                standingOrderInstance.nextExecutionDate = calculateNextExecutionDate(standingOrderInstance)
            }
            
            standingOrderInstance.save(flush: true, failOnError: true)
            
            // Log the standing order update
            AuditLogService.insert('080', 'DEP00531', 
                "Standing order updated for account ${standingOrderInstance.fromAccount.acctNo}", 
                'deposit', null, null, null, standingOrderInstance.fromAccount.id)
            
            flash.message = "Standing Order Successfully Updated"
            render(template:'standingOrder/edit', model:[standingOrderInstance:standingOrderInstance]) as JSON
            
        } catch (Exception e) {
            log.error("Error updating standing order: ${e.message}", e)
            render([error: "Unable to update standing order: ${e.message}"] as JSON)
        }
    }

    /**
     * Cancel standing order
     */
    @Transactional
    def cancelStandingOrder() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Standing Order ID is required"
                ] as JSON)
                return
            }
            
            def standingOrderInstance = StandingOrder.get(params.id)
            if (!standingOrderInstance) {
                render([
                    success: false,
                    error: "Standing Order not found"
                ] as JSON)
                return
            }
            
            if (standingOrderInstance.status.id != 1) {
                render([
                    success: false,
                    error: "Standing Order is not active"
                ] as JSON)
                return
            }
            
            // Update standing order status
            standingOrderInstance.status = StandingOrderStatus.read(3) // Cancelled
            standingOrderInstance.cancelledBy = UserMaster.get(session.user_id)
            standingOrderInstance.cancelledDate = new Date()
            standingOrderInstance.cancellationReason = params.cancellationReason ?: 'Manual Cancellation'
            standingOrderInstance.save(flush: true, failOnError: true)
            
            // Log the standing order cancellation
            AuditLogService.insert('080', 'DEP00532', 
                "Standing order cancelled for account ${standingOrderInstance.fromAccount.acctNo}", 
                'deposit', null, null, null, standingOrderInstance.fromAccount.id)
            
            render([
                success: true,
                message: "Standing Order cancelled successfully"
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error cancelling standing order: ${e.message}", e)
            render([
                success: false,
                error: "Unable to cancel standing order: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Execute standing order manually
     */
    @Transactional
    def executeStandingOrder() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Standing Order ID is required"
                ] as JSON)
                return
            }
            
            def standingOrderInstance = StandingOrder.get(params.id)
            if (!standingOrderInstance) {
                render([
                    success: false,
                    error: "Standing Order not found"
                ] as JSON)
                return
            }
            
            if (standingOrderInstance.status.id != 1) {
                render([
                    success: false,
                    error: "Standing Order is not active"
                ] as JSON)
                return
            }
            
            // Execute the standing order
            def executionResult = executeStandingOrderTransaction(standingOrderInstance)
            
            if (executionResult.success) {
                // Update next execution date
                standingOrderInstance.lastExecutionDate = new Date()
                standingOrderInstance.nextExecutionDate = calculateNextExecutionDate(standingOrderInstance)
                standingOrderInstance.executionCount = (standingOrderInstance.executionCount ?: 0) + 1
                standingOrderInstance.save(flush: true, failOnError: true)
                
                // Log the execution
                AuditLogService.insert('080', 'DEP00533', 
                    "Standing order executed for account ${standingOrderInstance.fromAccount.acctNo} - Amount: ${standingOrderInstance.amount}", 
                    'deposit', null, null, null, standingOrderInstance.fromAccount.id)
                
                render([
                    success: true,
                    message: "Standing Order executed successfully",
                    transactionId: executionResult.transactionId
                ] as JSON)
            } else {
                render([
                    success: false,
                    error: executionResult.message
                ] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error executing standing order: ${e.message}", e)
            render([
                success: false,
                error: "Unable to execute standing order: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get standing order information
     * @return JSON response with standing order details
     */
    def getStandingOrderInfo() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Standing Order ID is required"
                ] as JSON)
                return
            }
            
            def standingOrderInstance = StandingOrder.get(params.id)
            if (!standingOrderInstance) {
                render([
                    success: false,
                    error: "Standing Order not found"
                ] as JSON)
                return
            }
            
            def standingOrderInfo = [
                id: standingOrderInstance.id,
                amount: standingOrderInstance.amount,
                frequency: standingOrderInstance.frequency?.description,
                status: standingOrderInstance.status?.description,
                fromAccount: standingOrderInstance.fromAccount?.acctNo,
                toAccount: standingOrderInstance.toAccount?.acctNo,
                beneficiaryName: standingOrderInstance.beneficiaryName,
                startDate: standingOrderInstance.startDate?.format('yyyy-MM-dd'),
                endDate: standingOrderInstance.endDate?.format('yyyy-MM-dd'),
                nextExecutionDate: standingOrderInstance.nextExecutionDate?.format('yyyy-MM-dd'),
                lastExecutionDate: standingOrderInstance.lastExecutionDate?.format('yyyy-MM-dd'),
                executionCount: standingOrderInstance.executionCount ?: 0,
                createdDate: standingOrderInstance.createdDate?.format('yyyy-MM-dd'),
                createdBy: standingOrderInstance.createdBy?.username,
                description: standingOrderInstance.description
            ]
            
            render([
                success: true,
                standingOrderInfo: standingOrderInfo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting standing order info: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get standing order information: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get standing orders for account
     * @return JSON response with account standing orders
     */
    def getAccountStandingOrders() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def standingOrders = StandingOrder.findAllByFromAccount(deposit, [sort: 'createdDate', order: 'desc'])
            
            def standingOrderData = standingOrders.collect { so ->
                [
                    id: so.id,
                    amount: so.amount,
                    frequency: so.frequency?.description,
                    status: so.status?.description,
                    toAccount: so.toAccount?.acctNo,
                    beneficiaryName: so.beneficiaryName,
                    nextExecutionDate: so.nextExecutionDate?.format('yyyy-MM-dd'),
                    lastExecutionDate: so.lastExecutionDate?.format('yyyy-MM-dd'),
                    executionCount: so.executionCount ?: 0,
                    isActive: so.status?.id == 1,
                    canExecute: so.status?.id == 1 && so.nextExecutionDate <= new Date()
                ]
            }
            
            def activeCount = standingOrders.count { it.status?.id == 1 }
            
            render([
                success: true,
                standingOrders: standingOrderData,
                activeCount: activeCount,
                totalCount: standingOrders.size(),
                accountNo: deposit.acctNo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting account standing orders: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get account standing orders: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate standing order business rules
     * @param standingOrder The standing order to validate
     * @return true if valid
     */
    private boolean validateStandingOrder(StandingOrder standingOrder) {
        try {
            // Check minimum amount
            if (standingOrder.amount <= 0) {
                standingOrder.errors.rejectValue('amount', 'standingOrder.amount.invalid')
                return false
            }
            
            // Check start date
            if (standingOrder.startDate < new Date().clearTime()) {
                standingOrder.errors.rejectValue('startDate', 'standingOrder.startDate.past')
                return false
            }
            
            // Check end date
            if (standingOrder.endDate && standingOrder.endDate <= standingOrder.startDate) {
                standingOrder.errors.rejectValue('endDate', 'standingOrder.endDate.invalid')
                return false
            }
            
            // Check account balance
            if (standingOrder.amount > standingOrder.fromAccount.availableBalAmt) {
                standingOrder.errors.rejectValue('amount', 'standingOrder.amount.exceeds.balance')
                return false
            }
            
            return true
            
        } catch (Exception e) {
            log.error("Error validating standing order: ${e.message}", e)
            return false
        }
    }

    /**
     * Calculate next execution date
     * @param standingOrder The standing order
     * @return Next execution date
     */
    private Date calculateNextExecutionDate(StandingOrder standingOrder) {
        try {
            def calendar = Calendar.getInstance()
            calendar.setTime(standingOrder.lastExecutionDate ?: standingOrder.startDate)
            
            switch (standingOrder.frequency?.code) {
                case 'DAILY':
                    calendar.add(Calendar.DAY_OF_MONTH, 1)
                    break
                case 'WEEKLY':
                    calendar.add(Calendar.WEEK_OF_YEAR, 1)
                    break
                case 'MONTHLY':
                    calendar.add(Calendar.MONTH, 1)
                    break
                case 'QUARTERLY':
                    calendar.add(Calendar.MONTH, 3)
                    break
                case 'YEARLY':
                    calendar.add(Calendar.YEAR, 1)
                    break
                default:
                    calendar.add(Calendar.MONTH, 1)
            }
            
            return calendar.getTime()
            
        } catch (Exception e) {
            log.error("Error calculating next execution date: ${e.message}", e)
            return new Date() + 30 // Default to 30 days
        }
    }

    /**
     * Execute standing order transaction
     * @param standingOrder The standing order to execute
     * @return Execution result
     */
    private Map executeStandingOrderTransaction(StandingOrder standingOrder) {
        try {
            // Check available balance
            if (standingOrder.amount > standingOrder.fromAccount.availableBalAmt) {
                return [success: false, message: "Insufficient funds"]
            }
            
            // Create debit transaction
            def debitTxn = createStandingOrderTransaction(standingOrder, 'DEBIT')
            
            // Create credit transaction if internal transfer
            if (standingOrder.toAccount) {
                def creditTxn = createStandingOrderTransaction(standingOrder, 'CREDIT')
            }
            
            return [success: true, transactionId: debitTxn.id, message: "Standing order executed successfully"]
            
        } catch (Exception e) {
            log.error("Error executing standing order transaction: ${e.message}", e)
            return [success: false, message: "Transaction execution failed: ${e.message}"]
        }
    }

    /**
     * Create standing order transaction
     * @param standingOrder The standing order
     * @param type Transaction type (DEBIT/CREDIT)
     * @return Created transaction
     */
    private TxnFile createStandingOrderTransaction(StandingOrder standingOrder, String type) {
        try {
            def account = (type == 'DEBIT') ? standingOrder.fromAccount : standingOrder.toAccount
            def txnType = TxnType.findByCode(type == 'DEBIT' ? 'DR' : 'CR')
            
            def txn = new TxnFile(
                acctNo: account.acctNo,
                branch: account.branch,
                currency: account.product.currency,
                depAcct: account,
                status: ConfigItemStatus.get(2),
                txnAmt: standingOrder.amount,
                txnCode: 'STORD',
                txnDate: account.branch.runDate,
                txnDescription: "Standing Order ${type}",
                txnParticulars: standingOrder.description ?: "Standing Order Transfer",
                txnRef: "SO-${standingOrder.id}-${type}",
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: txnType,
                txnTemplate: TxnTemplate.findByCode('STORD')
            )
            txn.save(flush: true, failOnError: true)
            
            // Update account balance
            if (type == 'DEBIT') {
                account.ledgerBalAmt -= standingOrder.amount
                account.availableBalAmt -= standingOrder.amount
            } else {
                account.ledgerBalAmt += standingOrder.amount
                account.availableBalAmt += standingOrder.amount
            }
            account.save(flush: true, failOnError: true)
            
            // Create GL breakdown
            GlTransactionService.saveTxnBreakdown(txn.id)
            
            return txn
            
        } catch (Exception e) {
            log.error("Error creating standing order transaction: ${e.message}", e)
            throw e
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Standing Order not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
