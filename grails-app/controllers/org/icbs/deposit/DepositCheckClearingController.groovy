package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql
import org.icbs.tellering.TxnCOCI
import org.icbs.lov.CheckStatus
import org.icbs.lov.TxnCheckStatus

/**
 * DepositCheckClearingController - Handles deposit check clearing operations
 * 
 * This controller manages check clearing operations including:
 * - Check deposit clearing processing
 * - Check status updates and tracking
 * - Uncleared check management
 * - Check clearing reports
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositCheckClearingController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * View check clearing management page
     * @param id The deposit account ID
     */
    def viewCheckClearing() {
        try {
            log.info("Viewing check clearing for deposit ID: ${params.id}")
            
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                if (depositInstance.branch == UserMaster.get(session.user_id).branch) {
                    render(view:'checkClearing/view', model:[depositInstance:depositInstance])
                } else {
                    flash.message = 'Check clearing functions not allowed for other branch account'
                    render(view:"inquiry/depositInquiry", model:[depositInstance:depositInstance])
                }
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error viewing check clearing: ${e.message}", e)
            flash.message = "Error loading check clearing page |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Get uncleared checks for account
     * @return JSON response with uncleared checks
     */
    def getUnclearedChecks() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def unclearedChecks = TxnCOCI.createCriteria().list {
                eq("depAcct", deposit)
                'in'("checkStatus", [CheckStatus.read(1), CheckStatus.read(2), CheckStatus.read(3)]) // Pending, Processing, Hold
                order("depositDate", "desc")
            }
            
            def checkData = unclearedChecks.collect { check ->
                [
                    id: check.id,
                    checkNo: check.checkNo,
                    checkAmt: check.checkAmt,
                    depositDate: check.depositDate?.format('yyyy-MM-dd'),
                    clearingDate: check.clearingDate?.format('yyyy-MM-dd'),
                    checkStatus: check.checkStatus?.description,
                    bankName: check.bankName,
                    branchName: check.branchName,
                    drawerName: check.drawerName,
                    daysOutstanding: calculateDaysOutstanding(check.depositDate),
                    canClear: check.checkStatus?.id in [1, 2],
                    canReturn: check.checkStatus?.id in [1, 2, 3]
                ]
            }
            
            def totalAmount = unclearedChecks.sum { it.checkAmt } ?: 0.0
            
            render([
                success: true,
                unclearedChecks: checkData,
                totalAmount: totalAmount,
                checkCount: unclearedChecks.size(),
                accountNo: deposit.acctNo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting uncleared checks: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get uncleared checks: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Clear specific check
     */
    @Transactional
    def clearCheck() {
        try {
            if (!params.checkId) {
                render([
                    success: false,
                    error: "Check ID is required"
                ] as JSON)
                return
            }
            
            def check = TxnCOCI.get(params.checkId)
            if (!check) {
                render([
                    success: false,
                    error: "Check not found"
                ] as JSON)
                return
            }
            
            if (check.checkStatus.id !in [1, 2]) {
                render([
                    success: false,
                    error: "Check cannot be cleared in current status"
                ] as JSON)
                return
            }
            
            // Clear the check
            def clearingResult = processCheckClearing(check)
            
            if (clearingResult.success) {
                // Log the check clearing
                AuditLogService.insert('080', 'DEP00600', 
                    "Check cleared for account ${check.depAcct.acctNo} - Check: ${check.checkNo}, Amount: ${check.checkAmt}", 
                    'deposit', null, null, null, check.depAcct.id)
                
                render([
                    success: true,
                    message: "Check cleared successfully",
                    checkNo: check.checkNo,
                    amount: check.checkAmt
                ] as JSON)
            } else {
                render([
                    success: false,
                    error: clearingResult.message
                ] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error clearing check: ${e.message}", e)
            render([
                success: false,
                error: "Unable to clear check: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Return check (dishonor)
     */
    @Transactional
    def returnCheck() {
        try {
            if (!params.checkId || !params.returnReason) {
                render([
                    success: false,
                    error: "Check ID and return reason are required"
                ] as JSON)
                return
            }
            
            def check = TxnCOCI.get(params.checkId)
            if (!check) {
                render([
                    success: false,
                    error: "Check not found"
                ] as JSON)
                return
            }
            
            if (check.checkStatus.id !in [1, 2, 3]) {
                render([
                    success: false,
                    error: "Check cannot be returned in current status"
                ] as JSON)
                return
            }
            
            // Return the check
            def returnResult = processCheckReturn(check, params.returnReason)
            
            if (returnResult.success) {
                // Log the check return
                AuditLogService.insert('080', 'DEP00601', 
                    "Check returned for account ${check.depAcct.acctNo} - Check: ${check.checkNo}, Reason: ${params.returnReason}", 
                    'deposit', null, null, null, check.depAcct.id)
                
                render([
                    success: true,
                    message: "Check returned successfully",
                    checkNo: check.checkNo,
                    amount: check.checkAmt
                ] as JSON)
            } else {
                render([
                    success: false,
                    error: returnResult.message
                ] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error returning check: ${e.message}", e)
            render([
                success: false,
                error: "Unable to return check: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Bulk clear checks
     */
    @Transactional
    def bulkClearChecks() {
        try {
            if (!params.checkIds) {
                render([
                    success: false,
                    error: "Check IDs are required"
                ] as JSON)
                return
            }
            
            def checkIds = params.checkIds.split(',').collect { it.toLong() }
            def clearedCount = 0
            def failedCount = 0
            def totalAmount = 0.0
            
            for (checkId in checkIds) {
                try {
                    def check = TxnCOCI.get(checkId)
                    if (check && check.checkStatus.id in [1, 2]) {
                        def clearingResult = processCheckClearing(check)
                        if (clearingResult.success) {
                            clearedCount++
                            totalAmount += check.checkAmt
                        } else {
                            failedCount++
                        }
                    } else {
                        failedCount++
                    }
                } catch (Exception e) {
                    log.error("Error clearing check ${checkId}: ${e.message}", e)
                    failedCount++
                }
            }
            
            // Log the bulk clearing
            AuditLogService.insert('080', 'DEP00602', 
                "Bulk check clearing completed - Cleared: ${clearedCount}, Failed: ${failedCount}, Amount: ${totalAmount}", 
                'deposit', null, null, null, null)
            
            render([
                success: true,
                message: "Bulk clearing completed",
                clearedCount: clearedCount,
                failedCount: failedCount,
                totalAmount: totalAmount
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error in bulk clear checks: ${e.message}", e)
            render([
                success: false,
                error: "Unable to perform bulk clearing: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get check clearing summary
     * @return JSON response with clearing summary
     */
    def getCheckClearingSummary() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def summary = [:]
            
            // Get counts by status
            summary.pendingCount = TxnCOCI.countByDepAcctAndCheckStatus(deposit, CheckStatus.read(1))
            summary.processingCount = TxnCOCI.countByDepAcctAndCheckStatus(deposit, CheckStatus.read(2))
            summary.holdCount = TxnCOCI.countByDepAcctAndCheckStatus(deposit, CheckStatus.read(3))
            summary.clearedCount = TxnCOCI.countByDepAcctAndCheckStatus(deposit, CheckStatus.read(5))
            summary.returnedCount = TxnCOCI.countByDepAcctAndCheckStatus(deposit, CheckStatus.read(4))
            
            // Get amounts by status
            summary.pendingAmount = getTotalAmountByStatus(deposit, CheckStatus.read(1))
            summary.processingAmount = getTotalAmountByStatus(deposit, CheckStatus.read(2))
            summary.holdAmount = getTotalAmountByStatus(deposit, CheckStatus.read(3))
            summary.clearedAmount = getTotalAmountByStatus(deposit, CheckStatus.read(5))
            summary.returnedAmount = getTotalAmountByStatus(deposit, CheckStatus.read(4))
            
            // Calculate totals
            summary.totalUnclearedCount = summary.pendingCount + summary.processingCount + summary.holdCount
            summary.totalUnclearedAmount = summary.pendingAmount + summary.processingAmount + summary.holdAmount
            
            summary.accountNo = deposit.acctNo
            summary.currentBalance = deposit.ledgerBalAmt
            summary.availableBalance = deposit.availableBalAmt
            summary.unclearedCheckBalance = deposit.unclearedCheckBalAmt
            
            render([
                success: true,
                summary: summary
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting check clearing summary: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get clearing summary: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Process check clearing
     * @param check The check to clear
     * @return Clearing result
     */
    private Map processCheckClearing(TxnCOCI check) {
        try {
            def deposit = check.depAcct
            
            // Update check status
            check.checkStatus = CheckStatus.read(5) // Cleared
            check.cleared = 'TRUE'
            check.clearedDate = new Date()
            check.clearedBy = UserMaster.get(session.user_id)
            check.save(flush: true, failOnError: true)
            
            // Update deposit balances
            deposit.availableBalAmt += check.checkAmt
            deposit.unclearedCheckBalAmt -= check.checkAmt
            deposit.save(flush: true, failOnError: true)
            
            // Create clearing transaction
            def txn = new TxnFile(
                acctNo: deposit.acctNo,
                branch: deposit.branch,
                currency: deposit.product.currency,
                depAcct: deposit,
                status: ConfigItemStatus.get(2),
                txnAmt: check.checkAmt,
                txnCode: 'CHKCL',
                txnDate: deposit.branch.runDate,
                txnDescription: 'Check Cleared',
                txnParticulars: "Check ${check.checkNo} cleared",
                txnRef: "CHK-CLR-${check.id}",
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: TxnType.findByCode('MEMO'),
                txnTemplate: TxnTemplate.findByCode('CHKCL')
            )
            txn.save(flush: true, failOnError: true)
            
            return [success: true, transactionId: txn.id, message: "Check cleared successfully"]
            
        } catch (Exception e) {
            log.error("Error processing check clearing: ${e.message}", e)
            return [success: false, message: "Check clearing failed: ${e.message}"]
        }
    }

    /**
     * Process check return
     * @param check The check to return
     * @param returnReason The reason for return
     * @return Return result
     */
    private Map processCheckReturn(TxnCOCI check, String returnReason) {
        try {
            def deposit = check.depAcct
            
            // Update check status
            check.checkStatus = CheckStatus.read(4) // Returned
            check.returnReason = returnReason
            check.returnedDate = new Date()
            check.returnedBy = UserMaster.get(session.user_id)
            check.save(flush: true, failOnError: true)
            
            // Update deposit balances
            deposit.ledgerBalAmt -= check.checkAmt
            deposit.unclearedCheckBalAmt -= check.checkAmt
            deposit.save(flush: true, failOnError: true)
            
            // Create return transaction
            def txn = new TxnFile(
                acctNo: deposit.acctNo,
                branch: deposit.branch,
                currency: deposit.product.currency,
                depAcct: deposit,
                status: ConfigItemStatus.get(2),
                txnAmt: check.checkAmt,
                txnCode: 'CHKRT',
                txnDate: deposit.branch.runDate,
                txnDescription: 'Check Returned',
                txnParticulars: "Check ${check.checkNo} returned - ${returnReason}",
                txnRef: "CHK-RET-${check.id}",
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: TxnType.findByCode('DR'),
                txnTemplate: TxnTemplate.findByCode('CHKRT')
            )
            txn.save(flush: true, failOnError: true)
            
            // Create GL breakdown
            GlTransactionService.saveTxnBreakdown(txn.id)
            
            return [success: true, transactionId: txn.id, message: "Check returned successfully"]
            
        } catch (Exception e) {
            log.error("Error processing check return: ${e.message}", e)
            return [success: false, message: "Check return failed: ${e.message}"]
        }
    }

    /**
     * Calculate days outstanding for check
     * @param depositDate The check deposit date
     * @return Number of days outstanding
     */
    private Integer calculateDaysOutstanding(Date depositDate) {
        try {
            if (!depositDate) return 0
            
            def today = new Date()
            return (today - depositDate).intValue()
            
        } catch (Exception e) {
            log.error("Error calculating days outstanding: ${e.message}", e)
            return 0
        }
    }

    /**
     * Get total amount by check status
     * @param deposit The deposit account
     * @param status The check status
     * @return Total amount for status
     */
    private Double getTotalAmountByStatus(Deposit deposit, CheckStatus status) {
        try {
            def checks = TxnCOCI.findAllByDepAcctAndCheckStatus(deposit, status)
            return checks.sum { it.checkAmt } ?: 0.0
            
        } catch (Exception e) {
            log.error("Error getting total amount by status: ${e.message}", e)
            return 0.0
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Check or deposit account not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
