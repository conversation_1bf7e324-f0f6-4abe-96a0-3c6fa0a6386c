package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql
import org.icbs.deposit.StopPaymentOrder
import org.icbs.lov.StopPaymentStatus

/**
 * DepositStopPaymentController - Handles deposit stop payment operations
 * 
 * This controller manages stop payment operations including:
 * - Stop payment order creation and management
 * - Stop payment order cancellation and modification
 * - Stop payment order inquiry and tracking
 * - Check stop payment processing
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositStopPaymentController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * View stop payment management page
     * @param id The deposit account ID
     */
    def viewStopPayment() {
        try {
            log.info("Viewing stop payments for deposit ID: ${params.id}")
            
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                if (depositInstance.branch == UserMaster.get(session.user_id).branch) {
                    render(view:'stopPayment/view', model:[depositInstance:depositInstance])
                } else {
                    flash.message = 'Stop Payment functions not allowed for other branch account'
                    render(view:"inquiry/depositInquiry", model:[depositInstance:depositInstance])
                }
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error viewing stop payments: ${e.message}", e)
            flash.message = "Error loading stop payment page |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Show stop payment form via AJAX
     * @return JSON response with stop payment grid
     */
    def showStopPaymentFormAjax() {
        try {
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                render(template:"stopPayment/viewGrid", model:[depositInstance:depositInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error showing stop payment form: ${e.message}", e)
            render([error: "Unable to load stop payment form"] as JSON)
        }
    }

    /**
     * Create new stop payment via AJAX
     * @return JSON response with create form
     */
    def createStopPaymentAjax() {
        try {
            log.info("Creating stop payment with params: ${params}")
            
            if(params.id) {
                def stopPaymentInstance = new StopPaymentOrder()
                stopPaymentInstance.deposit = Deposit.read(params.id)
                stopPaymentInstance.requestedBy = UserMaster.get(session.user_id)
                stopPaymentInstance.requestDate = new Date()
                
                if(!stopPaymentInstance.deposit) {
                    notFound()
                    return
                }
                
                render(template:'stopPayment/create', model:[stopPaymentInstance:stopPaymentInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error creating stop payment: ${e.message}", e)
            render([error: "Unable to create stop payment"] as JSON)
        }
    }

    /**
     * Edit stop payment via AJAX
     * @return JSON response with edit form
     */
    def editStopPaymentAjax() {
        try {
            if(params.id) {
                def stopPaymentInstance = StopPaymentOrder.read(params.id)
                if(!stopPaymentInstance) {
                    notFound()
                    return
                }
                
                render(template:'stopPayment/edit', model:[stopPaymentInstance:stopPaymentInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error editing stop payment: ${e.message}", e)
            render([error: "Unable to edit stop payment"] as JSON)
        }
    }

    /**
     * Save new stop payment via AJAX
     * @return JSON response with save result
     */
    @Transactional
    def saveStopPaymentAjax() {
        try {
            log.info("Saving stop payment with params: ${params}")
            
            def stopPaymentInstance = new StopPaymentOrder()
            bindData(stopPaymentInstance, params)
            
            if (!stopPaymentInstance.validate()) {
                render(template:'stopPayment/create', model:[stopPaymentInstance:stopPaymentInstance]) as JSON
                return
            }
            
            // Validate stop payment business rules
            if (!validateStopPayment(stopPaymentInstance)) {
                render(template:'stopPayment/create', model:[stopPaymentInstance:stopPaymentInstance]) as JSON
                return
            }
            
            stopPaymentInstance.status = StopPaymentStatus.read(1) // Active
            stopPaymentInstance.requestedBy = UserMaster.get(session.user_id)
            stopPaymentInstance.requestDate = new Date()
            stopPaymentInstance.save(flush: true, failOnError: true)
            
            // Create stop payment transaction
            createStopPaymentTransaction(stopPaymentInstance)
            
            // Log the stop payment creation
            AuditLogService.insert('080', 'DEP00540', 
                "Stop payment order created for account ${stopPaymentInstance.deposit.acctNo} - Check: ${stopPaymentInstance.checkNumber}", 
                'deposit', null, null, null, stopPaymentInstance.deposit.id)
            
            flash.message = "Stop Payment Order Successfully Created"
            render(template:'stopPayment/create', 
                model:[stopPaymentInstance:new StopPaymentOrder(deposit:stopPaymentInstance.deposit)]) as JSON
            
        } catch (Exception e) {
            log.error("Error saving stop payment: ${e.message}", e)
            render([error: "Unable to save stop payment: ${e.message}"] as JSON)
        }
    }

    /**
     * Update stop payment via AJAX
     * @return JSON response with update result
     */
    @Transactional
    def updateStopPaymentAjax() {
        try {
            def stopPaymentInstance = StopPaymentOrder.get(params.id)
            if (!stopPaymentInstance) {
                notFound()
                return
            }
            
            bindData(stopPaymentInstance, params)
            
            if (!stopPaymentInstance.validate()) {
                render(template:'stopPayment/edit', model:[stopPaymentInstance:stopPaymentInstance]) as JSON
                return
            }
            
            // Validate updated stop payment
            if (!validateStopPayment(stopPaymentInstance)) {
                render(template:'stopPayment/edit', model:[stopPaymentInstance:stopPaymentInstance]) as JSON
                return
            }
            
            stopPaymentInstance.save(flush: true, failOnError: true)
            
            // Log the stop payment update
            AuditLogService.insert('080', 'DEP00541', 
                "Stop payment order updated for account ${stopPaymentInstance.deposit.acctNo} - Check: ${stopPaymentInstance.checkNumber}", 
                'deposit', null, null, null, stopPaymentInstance.deposit.id)
            
            flash.message = "Stop Payment Order Successfully Updated"
            render(template:'stopPayment/edit', model:[stopPaymentInstance:stopPaymentInstance]) as JSON
            
        } catch (Exception e) {
            log.error("Error updating stop payment: ${e.message}", e)
            render([error: "Unable to update stop payment: ${e.message}"] as JSON)
        }
    }

    /**
     * Cancel stop payment order
     */
    @Transactional
    def cancelStopPayment() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Stop Payment Order ID is required"
                ] as JSON)
                return
            }
            
            def stopPaymentInstance = StopPaymentOrder.get(params.id)
            if (!stopPaymentInstance) {
                render([
                    success: false,
                    error: "Stop Payment Order not found"
                ] as JSON)
                return
            }
            
            if (stopPaymentInstance.status.id != 1) {
                render([
                    success: false,
                    error: "Stop Payment Order is not active"
                ] as JSON)
                return
            }
            
            // Update stop payment status
            stopPaymentInstance.status = StopPaymentStatus.read(3) // Cancelled
            stopPaymentInstance.cancelledBy = UserMaster.get(session.user_id)
            stopPaymentInstance.cancelledDate = new Date()
            stopPaymentInstance.cancellationReason = params.cancellationReason ?: 'Manual Cancellation'
            stopPaymentInstance.save(flush: true, failOnError: true)
            
            // Create cancellation transaction
            createStopPaymentCancellationTransaction(stopPaymentInstance)
            
            // Log the stop payment cancellation
            AuditLogService.insert('080', 'DEP00542', 
                "Stop payment order cancelled for account ${stopPaymentInstance.deposit.acctNo} - Check: ${stopPaymentInstance.checkNumber}", 
                'deposit', null, null, null, stopPaymentInstance.deposit.id)
            
            render([
                success: true,
                message: "Stop Payment Order cancelled successfully"
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error cancelling stop payment: ${e.message}", e)
            render([
                success: false,
                error: "Unable to cancel stop payment: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get stop payment information
     * @return JSON response with stop payment details
     */
    def getStopPaymentInfo() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Stop Payment Order ID is required"
                ] as JSON)
                return
            }
            
            def stopPaymentInstance = StopPaymentOrder.get(params.id)
            if (!stopPaymentInstance) {
                render([
                    success: false,
                    error: "Stop Payment Order not found"
                ] as JSON)
                return
            }
            
            def stopPaymentInfo = [
                id: stopPaymentInstance.id,
                checkNumber: stopPaymentInstance.checkNumber,
                checkAmount: stopPaymentInstance.checkAmount,
                payeeName: stopPaymentInstance.payeeName,
                status: stopPaymentInstance.status?.description,
                requestDate: stopPaymentInstance.requestDate?.format('yyyy-MM-dd'),
                requestedBy: stopPaymentInstance.requestedBy?.username,
                expiryDate: stopPaymentInstance.expiryDate?.format('yyyy-MM-dd'),
                cancelledDate: stopPaymentInstance.cancelledDate?.format('yyyy-MM-dd'),
                cancelledBy: stopPaymentInstance.cancelledBy?.username,
                cancellationReason: stopPaymentInstance.cancellationReason,
                reason: stopPaymentInstance.reason,
                accountNo: stopPaymentInstance.deposit?.acctNo,
                customerName: stopPaymentInstance.deposit?.customer?.displayName,
                fee: stopPaymentInstance.fee
            ]
            
            render([
                success: true,
                stopPaymentInfo: stopPaymentInfo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting stop payment info: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get stop payment information: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get stop payments for account
     * @return JSON response with account stop payments
     */
    def getAccountStopPayments() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def stopPayments = StopPaymentOrder.findAllByDeposit(deposit, [sort: 'requestDate', order: 'desc'])
            
            def stopPaymentData = stopPayments.collect { sp ->
                [
                    id: sp.id,
                    checkNumber: sp.checkNumber,
                    checkAmount: sp.checkAmount,
                    payeeName: sp.payeeName,
                    status: sp.status?.description,
                    requestDate: sp.requestDate?.format('yyyy-MM-dd'),
                    requestedBy: sp.requestedBy?.username,
                    expiryDate: sp.expiryDate?.format('yyyy-MM-dd'),
                    reason: sp.reason,
                    fee: sp.fee,
                    isActive: sp.status?.id == 1,
                    canCancel: sp.status?.id == 1
                ]
            }
            
            def activeCount = stopPayments.count { it.status?.id == 1 }
            
            render([
                success: true,
                stopPayments: stopPaymentData,
                activeCount: activeCount,
                totalCount: stopPayments.size(),
                accountNo: deposit.acctNo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting account stop payments: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get account stop payments: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Check if check number has stop payment
     * @return JSON response with stop payment status
     */
    def checkStopPaymentStatus() {
        try {
            if (!params.depositId || !params.checkNumber) {
                render([
                    success: false,
                    error: "Deposit ID and check number are required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.depositId)
            if (!deposit) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def stopPayment = StopPaymentOrder.findByDepositAndCheckNumberAndStatus(
                deposit, params.checkNumber, StopPaymentStatus.read(1))
            
            render([
                success: true,
                hasStopPayment: stopPayment != null,
                stopPaymentId: stopPayment?.id,
                stopPaymentInfo: stopPayment ? [
                    id: stopPayment.id,
                    checkAmount: stopPayment.checkAmount,
                    payeeName: stopPayment.payeeName,
                    requestDate: stopPayment.requestDate?.format('yyyy-MM-dd'),
                    reason: stopPayment.reason
                ] : null
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error checking stop payment status: ${e.message}", e)
            render([
                success: false,
                error: "Unable to check stop payment status: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate stop payment business rules
     * @param stopPayment The stop payment to validate
     * @return true if valid
     */
    private boolean validateStopPayment(StopPaymentOrder stopPayment) {
        try {
            // Check for duplicate stop payment on same check
            def existingStopPayment = StopPaymentOrder.findByDepositAndCheckNumberAndStatus(
                stopPayment.deposit, stopPayment.checkNumber, StopPaymentStatus.read(1))
            
            if (existingStopPayment && existingStopPayment.id != stopPayment.id) {
                stopPayment.errors.rejectValue('checkNumber', 'stopPayment.checkNumber.duplicate')
                return false
            }
            
            // Validate check number format
            if (!stopPayment.checkNumber?.matches(/^\d+$/)) {
                stopPayment.errors.rejectValue('checkNumber', 'stopPayment.checkNumber.invalid')
                return false
            }
            
            // Validate check amount
            if (stopPayment.checkAmount && stopPayment.checkAmount <= 0) {
                stopPayment.errors.rejectValue('checkAmount', 'stopPayment.checkAmount.invalid')
                return false
            }
            
            // Validate expiry date
            if (stopPayment.expiryDate && stopPayment.expiryDate <= new Date()) {
                stopPayment.errors.rejectValue('expiryDate', 'stopPayment.expiryDate.past')
                return false
            }
            
            return true
            
        } catch (Exception e) {
            log.error("Error validating stop payment: ${e.message}", e)
            return false
        }
    }

    /**
     * Create stop payment transaction
     * @param stopPayment The stop payment order
     */
    private void createStopPaymentTransaction(StopPaymentOrder stopPayment) {
        try {
            def fee = stopPayment.fee ?: 0.0
            
            if (fee > 0) {
                def txn = new TxnFile(
                    acctNo: stopPayment.deposit.acctNo,
                    branch: stopPayment.deposit.branch,
                    currency: stopPayment.deposit.product.currency,
                    depAcct: stopPayment.deposit,
                    status: ConfigItemStatus.get(2),
                    txnAmt: fee,
                    txnCode: 'STPMT',
                    txnDate: stopPayment.deposit.branch.runDate,
                    txnDescription: 'Stop Payment Fee',
                    txnParticulars: "Stop Payment on Check ${stopPayment.checkNumber}",
                    txnRef: "SPO-${stopPayment.id}",
                    txnTimestamp: new Date().toTimestamp(),
                    user: UserMaster.get(session.user_id),
                    txnType: TxnType.findByCode('DR'),
                    txnTemplate: TxnTemplate.findByCode('STPMT')
                )
                txn.save(flush: true, failOnError: true)
                
                // Update account balance
                stopPayment.deposit.ledgerBalAmt -= fee
                stopPayment.deposit.availableBalAmt -= fee
                stopPayment.deposit.save(flush: true, failOnError: true)
                
                // Create GL breakdown
                GlTransactionService.saveTxnBreakdown(txn.id)
            }
            
        } catch (Exception e) {
            log.error("Error creating stop payment transaction: ${e.message}", e)
            throw e
        }
    }

    /**
     * Create stop payment cancellation transaction
     * @param stopPayment The cancelled stop payment order
     */
    private void createStopPaymentCancellationTransaction(StopPaymentOrder stopPayment) {
        try {
            // Create memo transaction for cancellation
            def txn = new TxnFile(
                acctNo: stopPayment.deposit.acctNo,
                branch: stopPayment.deposit.branch,
                currency: stopPayment.deposit.product.currency,
                depAcct: stopPayment.deposit,
                status: ConfigItemStatus.get(2),
                txnAmt: 0.0,
                txnCode: 'MEMO',
                txnDate: stopPayment.deposit.branch.runDate,
                txnDescription: 'Stop Payment Cancelled',
                txnParticulars: "Stop Payment Cancelled on Check ${stopPayment.checkNumber}",
                txnRef: "SPO-CANCEL-${stopPayment.id}",
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: TxnType.findByCode('MEMO'),
                txnTemplate: TxnTemplate.findByCode('MEMO')
            )
            txn.save(flush: true, failOnError: true)
            
        } catch (Exception e) {
            log.error("Error creating stop payment cancellation transaction: ${e.message}", e)
            throw e
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Stop Payment Order not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
