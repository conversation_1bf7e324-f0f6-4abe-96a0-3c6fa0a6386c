package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql
import org.icbs.deposit.FundTransfer
import org.icbs.lov.TransferType
import org.icbs.lov.TransferStatus

/**
 * DepositFundTransferController - Handles deposit fund transfer operations
 * 
 * This controller manages fund transfer operations including:
 * - Internal fund transfers between accounts
 * - External fund transfers
 * - Transfer scheduling and execution
 * - Transfer inquiry and tracking
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositFundTransferController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * View fund transfer page
     * @param id The deposit account ID
     */
    def viewFundTransfer() {
        try {
            log.info("Viewing fund transfers for deposit ID: ${params.id}")
            
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                if (depositInstance.branch == UserMaster.get(session.user_id).branch) {
                    render(view:'fundTransfer/view', model:[depositInstance:depositInstance])
                } else {
                    flash.message = 'Fund transfer functions not allowed for other branch account'
                    render(view:"inquiry/depositInquiry", model:[depositInstance:depositInstance])
                }
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error viewing fund transfers: ${e.message}", e)
            flash.message = "Error loading fund transfer page |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Create new fund transfer
     * @return JSON response with create form
     */
    def createFundTransfer() {
        try {
            log.info("Creating fund transfer with params: ${params}")
            
            if(params.fromAccountId) {
                def transferInstance = new FundTransfer()
                transferInstance.fromAccount = Deposit.read(params.fromAccountId)
                transferInstance.createdBy = UserMaster.get(session.user_id)
                transferInstance.createdDate = new Date()
                
                if(!transferInstance.fromAccount) {
                    notFound()
                    return
                }
                
                render(view:'fundTransfer/create', model:[transferInstance:transferInstance])
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error creating fund transfer: ${e.message}", e)
            flash.message = "Error creating fund transfer |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Save new fund transfer
     */
    @Transactional
    def saveFundTransfer() {
        try {
            log.info("Saving fund transfer with params: ${params}")
            
            def transferInstance = new FundTransfer()
            bindData(transferInstance, params)
            
            if (!transferInstance.validate()) {
                render(view:'fundTransfer/create', model:[transferInstance:transferInstance])
                return
            }
            
            // Validate fund transfer business rules
            if (!validateFundTransfer(transferInstance)) {
                render(view:'fundTransfer/create', model:[transferInstance:transferInstance])
                return
            }
            
            transferInstance.status = TransferStatus.read(1) // Pending
            transferInstance.createdBy = UserMaster.get(session.user_id)
            transferInstance.createdDate = new Date()
            transferInstance.save(flush: true, failOnError: true)
            
            // Execute transfer if immediate
            if (transferInstance.transferType?.code == 'IMMEDIATE') {
                def executionResult = executeFundTransfer(transferInstance)
                if (!executionResult.success) {
                    flash.message = "Transfer created but execution failed: ${executionResult.message} |warning|alert"
                    render(view:'fundTransfer/show', model:[transferInstance:transferInstance])
                    return
                }
            }
            
            // Log the fund transfer creation
            AuditLogService.insert('080', 'DEP00580', 
                "Fund transfer created from account ${transferInstance.fromAccount.acctNo} - Amount: ${transferInstance.amount}", 
                'deposit', null, null, null, transferInstance.fromAccount.id)
            
            flash.message = "Fund Transfer Successfully Created"
            redirect(action: "showFundTransfer", id: transferInstance.id)
            
        } catch (Exception e) {
            log.error("Error saving fund transfer: ${e.message}", e)
            flash.message = "Error saving fund transfer: ${e.message} |error|alert"
            redirect(action: "createFundTransfer", params: params)
        }
    }

    /**
     * Show fund transfer details
     * @param transferInstance The fund transfer instance
     */
    def showFundTransfer(FundTransfer transferInstance) {
        try {
            if (transferInstance) {
                respond transferInstance
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error showing fund transfer: ${e.message}", e)
            flash.message = "Error loading fund transfer |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Execute fund transfer
     */
    @Transactional
    def executeFundTransfer() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Fund Transfer ID is required"
                ] as JSON)
                return
            }
            
            def transferInstance = FundTransfer.get(params.id)
            if (!transferInstance) {
                render([
                    success: false,
                    error: "Fund Transfer not found"
                ] as JSON)
                return
            }
            
            if (transferInstance.status.id != 1) {
                render([
                    success: false,
                    error: "Fund Transfer is not pending"
                ] as JSON)
                return
            }
            
            // Execute the transfer
            def executionResult = executeFundTransfer(transferInstance)
            
            if (executionResult.success) {
                render([
                    success: true,
                    message: "Fund Transfer executed successfully",
                    transactionId: executionResult.transactionId
                ] as JSON)
            } else {
                render([
                    success: false,
                    error: executionResult.message
                ] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error executing fund transfer: ${e.message}", e)
            render([
                success: false,
                error: "Unable to execute fund transfer: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Cancel fund transfer
     */
    @Transactional
    def cancelFundTransfer() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Fund Transfer ID is required"
                ] as JSON)
                return
            }
            
            def transferInstance = FundTransfer.get(params.id)
            if (!transferInstance) {
                render([
                    success: false,
                    error: "Fund Transfer not found"
                ] as JSON)
                return
            }
            
            if (transferInstance.status.id != 1) {
                render([
                    success: false,
                    error: "Fund Transfer is not pending"
                ] as JSON)
                return
            }
            
            // Update transfer status
            transferInstance.status = TransferStatus.read(4) // Cancelled
            transferInstance.cancelledBy = UserMaster.get(session.user_id)
            transferInstance.cancelledDate = new Date()
            transferInstance.cancellationReason = params.cancellationReason ?: 'Manual Cancellation'
            transferInstance.save(flush: true, failOnError: true)
            
            // Log the fund transfer cancellation
            AuditLogService.insert('080', 'DEP00582', 
                "Fund transfer cancelled from account ${transferInstance.fromAccount.acctNo}", 
                'deposit', null, null, null, transferInstance.fromAccount.id)
            
            render([
                success: true,
                message: "Fund Transfer cancelled successfully"
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error cancelling fund transfer: ${e.message}", e)
            render([
                success: false,
                error: "Unable to cancel fund transfer: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get fund transfer information
     * @return JSON response with transfer details
     */
    def getFundTransferInfo() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Fund Transfer ID is required"
                ] as JSON)
                return
            }
            
            def transferInstance = FundTransfer.get(params.id)
            if (!transferInstance) {
                render([
                    success: false,
                    error: "Fund Transfer not found"
                ] as JSON)
                return
            }
            
            def transferInfo = [
                id: transferInstance.id,
                amount: transferInstance.amount,
                transferType: transferInstance.transferType?.description,
                status: transferInstance.status?.description,
                fromAccount: transferInstance.fromAccount?.acctNo,
                toAccount: transferInstance.toAccount?.acctNo,
                beneficiaryName: transferInstance.beneficiaryName,
                description: transferInstance.description,
                createdDate: transferInstance.createdDate?.format('yyyy-MM-dd'),
                createdBy: transferInstance.createdBy?.username,
                executedDate: transferInstance.executedDate?.format('yyyy-MM-dd'),
                executedBy: transferInstance.executedBy?.username,
                transactionId: transferInstance.transactionId,
                fee: transferInstance.fee
            ]
            
            render([
                success: true,
                transferInfo: transferInfo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting fund transfer info: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get fund transfer information: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get fund transfers for account
     * @return JSON response with account fund transfers
     */
    def getAccountFundTransfers() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def transfers = FundTransfer.createCriteria().list {
                or {
                    eq("fromAccount", deposit)
                    eq("toAccount", deposit)
                }
                order("createdDate", "desc")
            }
            
            def transferData = transfers.collect { transfer ->
                [
                    id: transfer.id,
                    amount: transfer.amount,
                    transferType: transfer.transferType?.description,
                    status: transfer.status?.description,
                    fromAccount: transfer.fromAccount?.acctNo,
                    toAccount: transfer.toAccount?.acctNo,
                    beneficiaryName: transfer.beneficiaryName,
                    createdDate: transfer.createdDate?.format('yyyy-MM-dd'),
                    executedDate: transfer.executedDate?.format('yyyy-MM-dd'),
                    direction: transfer.fromAccount?.id == deposit.id ? 'OUT' : 'IN',
                    isPending: transfer.status?.id == 1,
                    canExecute: transfer.status?.id == 1,
                    canCancel: transfer.status?.id == 1
                ]
            }
            
            def pendingCount = transfers.count { it.status?.id == 1 }
            def completedCount = transfers.count { it.status?.id == 2 }
            
            render([
                success: true,
                transfers: transferData,
                pendingCount: pendingCount,
                completedCount: completedCount,
                totalCount: transfers.size(),
                accountNo: deposit.acctNo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting account fund transfers: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get account fund transfers: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate fund transfer business rules
     * @param transfer The fund transfer to validate
     * @return true if valid
     */
    private boolean validateFundTransfer(FundTransfer transfer) {
        try {
            // Check transfer amount
            if (transfer.amount <= 0) {
                transfer.errors.rejectValue('amount', 'fundTransfer.amount.invalid')
                return false
            }
            
            // Check available balance
            if (transfer.amount > transfer.fromAccount.availableBalAmt) {
                transfer.errors.rejectValue('amount', 'fundTransfer.amount.exceeds.balance')
                return false
            }
            
            // Check that from and to accounts are different
            if (transfer.toAccount && transfer.fromAccount.id == transfer.toAccount.id) {
                transfer.errors.rejectValue('toAccount', 'fundTransfer.toAccount.same')
                return false
            }
            
            // Check daily transfer limits
            if (!checkDailyTransferLimit(transfer)) {
                transfer.errors.rejectValue('amount', 'fundTransfer.amount.exceeds.daily.limit')
                return false
            }
            
            return true
            
        } catch (Exception e) {
            log.error("Error validating fund transfer: ${e.message}", e)
            return false
        }
    }

    /**
     * Execute fund transfer transaction
     * @param transfer The fund transfer to execute
     * @return Execution result
     */
    private Map executeFundTransfer(FundTransfer transfer) {
        try {
            // Check available balance again
            if (transfer.amount > transfer.fromAccount.availableBalAmt) {
                return [success: false, message: "Insufficient funds"]
            }
            
            // Create debit transaction
            def debitTxn = createTransferTransaction(transfer, 'DEBIT')
            
            // Create credit transaction if internal transfer
            if (transfer.toAccount) {
                def creditTxn = createTransferTransaction(transfer, 'CREDIT')
            }
            
            // Update transfer status
            transfer.status = TransferStatus.read(2) // Completed
            transfer.executedBy = UserMaster.get(session.user_id)
            transfer.executedDate = new Date()
            transfer.transactionId = debitTxn.id
            transfer.save(flush: true, failOnError: true)
            
            // Log the execution
            AuditLogService.insert('080', 'DEP00581', 
                "Fund transfer executed from account ${transfer.fromAccount.acctNo} - Amount: ${transfer.amount}", 
                'deposit', null, null, null, transfer.fromAccount.id)
            
            return [success: true, transactionId: debitTxn.id, message: "Fund transfer executed successfully"]
            
        } catch (Exception e) {
            log.error("Error executing fund transfer: ${e.message}", e)
            return [success: false, message: "Transfer execution failed: ${e.message}"]
        }
    }

    /**
     * Create fund transfer transaction
     * @param transfer The fund transfer
     * @param type Transaction type (DEBIT/CREDIT)
     * @return Created transaction
     */
    private TxnFile createTransferTransaction(FundTransfer transfer, String type) {
        try {
            def account = (type == 'DEBIT') ? transfer.fromAccount : transfer.toAccount
            def txnType = TxnType.findByCode(type == 'DEBIT' ? 'DR' : 'CR')
            
            def txn = new TxnFile(
                acctNo: account.acctNo,
                branch: account.branch,
                currency: account.product.currency,
                depAcct: account,
                status: ConfigItemStatus.get(2),
                txnAmt: transfer.amount,
                txnCode: 'XFER',
                txnDate: account.branch.runDate,
                txnDescription: "Fund Transfer ${type}",
                txnParticulars: transfer.description ?: "Fund Transfer",
                txnRef: "XFER-${transfer.id}-${type}",
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: txnType,
                txnTemplate: TxnTemplate.findByCode('XFER')
            )
            txn.save(flush: true, failOnError: true)
            
            // Update account balance
            if (type == 'DEBIT') {
                account.ledgerBalAmt -= transfer.amount
                account.availableBalAmt -= transfer.amount
            } else {
                account.ledgerBalAmt += transfer.amount
                account.availableBalAmt += transfer.amount
            }
            account.save(flush: true, failOnError: true)
            
            // Create GL breakdown
            GlTransactionService.saveTxnBreakdown(txn.id)
            
            return txn
            
        } catch (Exception e) {
            log.error("Error creating transfer transaction: ${e.message}", e)
            throw e
        }
    }

    /**
     * Check daily transfer limit
     * @param transfer The fund transfer
     * @return true if within limit
     */
    private boolean checkDailyTransferLimit(FundTransfer transfer) {
        try {
            def dailyLimit = Institution.findByParamCode('DEP.DAILY.TRANSFER.LIMIT')?.paramValue?.toDouble() ?: 100000.0
            
            def today = new Date().clearTime()
            def todayTransfers = FundTransfer.createCriteria().list {
                eq("fromAccount", transfer.fromAccount)
                ge("createdDate", today)
                eq("status", TransferStatus.read(2)) // Completed
            }
            
            def todayTotal = todayTransfers.sum { it.amount } ?: 0.0
            
            return (todayTotal + transfer.amount) <= dailyLimit
            
        } catch (Exception e) {
            log.error("Error checking daily transfer limit: ${e.message}", e)
            return true // Allow if check fails
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Fund Transfer not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
