package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql
import org.icbs.deposit.IssueCheckbook
import org.icbs.deposit.Checkbook
import org.icbs.lov.CheckbookStatus

/**
 * DepositCheckbookController - Handles deposit checkbook operations
 * 
 * This controller manages checkbook operations including:
 * - Checkbook issuance and management
 * - Checkbook status updates and tracking
 * - Check leaf management
 * - Checkbook inquiry and reporting
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositCheckbookController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * View checkbook management page
     * @param id The deposit account ID
     */
    def viewCheckbook() {
        try {
            log.info("Viewing checkbook for deposit ID: ${params.id}")
            
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                if (depositInstance.branch == UserMaster.get(session.user_id).branch) {
                    render(view:'checkbook/view', model:[depositInstance:depositInstance])
                } else {
                    flash.message = 'Checkbook functions not allowed for other branch account'
                    render(view:"inquiry/depositInquiry", model:[depositInstance:depositInstance])
                }
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error viewing checkbook: ${e.message}", e)
            flash.message = "Error loading checkbook page |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Show checkbook form via AJAX
     * @return JSON response with checkbook grid
     */
    def showCheckbookFormAjax() {
        try {
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                render(template:"checkbook/viewGrid", model:[depositInstance:depositInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error showing checkbook form: ${e.message}", e)
            render([error: "Unable to load checkbook form"] as JSON)
        }
    }

    /**
     * Create new checkbook via AJAX
     * @return JSON response with create form
     */
    def createCheckbookAjax() {
        try {
            log.info("Creating checkbook with params: ${params}")
            
            if(params.id) {
                def issueCheckbookInstance = new IssueCheckbook()
                issueCheckbookInstance.deposit = Deposit.read(params.id)
                issueCheckbookInstance.issuedBy = UserMaster.get(session.user_id)
                
                if(!issueCheckbookInstance.deposit) {
                    notFound()
                    return
                }
                
                render(template:'checkbook/create', model:[issueCheckbookInstance:issueCheckbookInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error creating checkbook: ${e.message}", e)
            render([error: "Unable to create checkbook"] as JSON)
        }
    }

    /**
     * Edit checkbook via AJAX
     * @return JSON response with edit form
     */
    def editCheckbookAjax() {
        try {
            if(params.id) {
                def issueCheckbookInstance = IssueCheckbook.read(params.id)
                if(!issueCheckbookInstance) {
                    notFound()
                    return
                }
                
                render(template:'checkbook/edit', model:[issueCheckbookInstance:issueCheckbookInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error editing checkbook: ${e.message}", e)
            render([error: "Unable to edit checkbook"] as JSON)
        }
    }

    /**
     * Save new checkbook via AJAX
     * @return JSON response with save result
     */
    @Transactional
    def saveCheckbookAjax() {
        try {
            log.info("Saving checkbook with params: ${params}")
            
            def result = depositService.saveCheckbook(params)
            
            if(!result.error) {
                // Log the checkbook issuance
                AuditLogService.insert('080', 'DEP00510', 
                    "Checkbook issued for account ${result.issueCheckbookInstance.deposit.acctNo}", 
                    'deposit', null, null, null, result.issueCheckbookInstance.deposit.id)
                
                flash.message = "Checkbook Successfully Issued"
                render(template:'checkbook/create', 
                    model:[issueCheckbookInstance:new IssueCheckbook(deposit:result.issueCheckbookInstance.deposit)]) as JSON
            } else {
                render(template:'checkbook/create', 
                    model:[issueCheckbookInstance:result.issueCheckbookInstance]) as JSON
            }
            
        } catch (Exception e) {
            log.error("Error saving checkbook: ${e.message}", e)
            render([error: "Unable to save checkbook: ${e.message}"] as JSON)
        }
    }

    /**
     * Update checkbook via AJAX
     * @return JSON response with update result
     */
    @Transactional
    def updateCheckbookAjax() {
        try {
            def result = depositService.updateCheckbook(params)
            
            if(!result.error) {
                // Log the checkbook update
                AuditLogService.insert('080', 'DEP00511', 
                    "Checkbook updated for account ${result.issueCheckbookInstance.deposit.acctNo}", 
                    'deposit', null, null, null, result.issueCheckbookInstance.deposit.id)
                
                flash.message = "Checkbook Successfully Updated"
                render(template:'checkbook/edit', model:[issueCheckbookInstance:result.issueCheckbookInstance]) as JSON
            } else {
                if(result.error.code == "default.not.found") {
                    notFound()
                    return
                }
                render(template:'checkbook/edit', model:[issueCheckbookInstance:result.issueCheckbookInstance.attach()]) as JSON
            }
            
        } catch (Exception e) {
            log.error("Error updating checkbook: ${e.message}", e)
            render([error: "Unable to update checkbook: ${e.message}"] as JSON)
        }
    }

    /**
     * Update checkbook status
     */
    @Transactional
    def updateCheckbookStatus() {
        try {
            log.info("Updating checkbook status with params: ${params}")
            
            def checkbookInstance = IssueCheckbook.get(params.checkbookId.toInteger())
            def checkbook = checkbookInstance.checkbook
            def deposit = checkbookInstance.deposit
            
            checkbook.status = CheckbookStatus.read(params.statusId.toInteger())
            checkbook.save(flush:true)
            
            // Handle cancelled checkbook
            if (checkbook.status == CheckbookStatus.read(4)) {
                checkbook.status = CheckbookStatus.read(1)
                checkbook.issueCheckbook = null
                checkbook.save(flush:true, failOnError:true)
                
                checkbookInstance.delete(flush:true)
            }
            
            def desc = "${deposit.acctNo} - Update Checkbook Status - ${checkbook.status}"
            AuditLogService.insert('080', 'DEP00610', desc, 'deposit', null, null, null, deposit.id)
            
            render([success: true, message: "Checkbook status updated successfully"] as JSON)
            
        } catch (Exception e) {
            log.error("Error updating checkbook status: ${e.message}", e)
            render([success: false, error: "Unable to update checkbook status: ${e.message}"] as JSON)
        }
    }

    /**
     * Get checkbook status list
     * @return JSON response with status options
     */
    def getCheckbookStatusList() {
        try {
            def db = new Sql(dataSource)
            def sql = "select id, description from checkbook_status where id <> 2"
            def results = db.rows(sql)
            
            render(results as JSON)
            
        } catch (Exception e) {
            log.error("Error getting checkbook status list: ${e.message}", e)
            render([error: "Unable to get status list"] as JSON)
        }
    }

    /**
     * Get checkbook information
     * @return JSON response with checkbook details
     */
    def getCheckbookInfo() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Checkbook ID is required"
                ] as JSON)
                return
            }
            
            def issueCheckbook = IssueCheckbook.get(params.id)
            if (!issueCheckbook) {
                render([
                    success: false,
                    error: "Checkbook not found"
                ] as JSON)
                return
            }
            
            def checkbookInfo = [
                id: issueCheckbook.id,
                checkbookNo: issueCheckbook.checkbook?.checkbookNo,
                status: issueCheckbook.checkbook?.status?.description,
                issuedDate: issueCheckbook.issuedDate?.format('yyyy-MM-dd'),
                issuedBy: issueCheckbook.issuedBy?.username,
                accountNo: issueCheckbook.deposit?.acctNo,
                customerName: issueCheckbook.deposit?.customer?.displayName,
                startCheckNo: issueCheckbook.checkbook?.startCheckNo,
                endCheckNo: issueCheckbook.checkbook?.endCheckNo,
                totalLeaves: issueCheckbook.checkbook?.totalLeaves,
                usedLeaves: issueCheckbook.checkbook?.usedLeaves,
                remainingLeaves: (issueCheckbook.checkbook?.totalLeaves ?: 0) - (issueCheckbook.checkbook?.usedLeaves ?: 0)
            ]
            
            render([
                success: true,
                checkbookInfo: checkbookInfo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting checkbook info: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get checkbook information: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Cancel checkbook
     */
    @Transactional
    def cancelCheckbook() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Checkbook ID is required"
                ] as JSON)
                return
            }
            
            def issueCheckbook = IssueCheckbook.get(params.id)
            if (!issueCheckbook) {
                render([
                    success: false,
                    error: "Checkbook not found"
                ] as JSON)
                return
            }
            
            def checkbook = issueCheckbook.checkbook
            def deposit = issueCheckbook.deposit
            
            // Mark checkbook as cancelled
            checkbook.status = CheckbookStatus.read(4) // Cancelled
            checkbook.save(flush:true, failOnError:true)
            
            // Log the cancellation
            AuditLogService.insert('080', 'DEP00612', 
                "Checkbook cancelled for account ${deposit.acctNo}", 
                'deposit', null, null, null, deposit.id)
            
            render([
                success: true,
                message: "Checkbook cancelled successfully"
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error cancelling checkbook: ${e.message}", e)
            render([
                success: false,
                error: "Unable to cancel checkbook: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get check leaf usage report
     * @return JSON response with check usage data
     */
    def getCheckLeafUsage() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def checkbooks = IssueCheckbook.findAllByDeposit(deposit)
            
            def checkbookUsage = checkbooks.collect { issueCheckbook ->
                def checkbook = issueCheckbook.checkbook
                [
                    checkbookNo: checkbook?.checkbookNo,
                    status: checkbook?.status?.description,
                    issuedDate: issueCheckbook.issuedDate?.format('yyyy-MM-dd'),
                    startCheckNo: checkbook?.startCheckNo,
                    endCheckNo: checkbook?.endCheckNo,
                    totalLeaves: checkbook?.totalLeaves ?: 0,
                    usedLeaves: checkbook?.usedLeaves ?: 0,
                    remainingLeaves: (checkbook?.totalLeaves ?: 0) - (checkbook?.usedLeaves ?: 0),
                    usagePercentage: checkbook?.totalLeaves ? 
                        ((checkbook?.usedLeaves ?: 0) * 100 / checkbook?.totalLeaves).round(2) : 0
                ]
            }
            
            render([
                success: true,
                checkbookUsage: checkbookUsage,
                accountNo: deposit.acctNo,
                totalCheckbooks: checkbooks.size()
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting check leaf usage: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get check leaf usage: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Stop payment on check
     */
    @Transactional
    def stopPayment() {
        try {
            if (!params.checkNo || !params.depositId) {
                render([
                    success: false,
                    error: "Check number and deposit ID are required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.depositId)
            if (!deposit) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            // Create stop payment record
            def stopPaymentResult = depositService.createStopPayment(deposit, params.checkNo, params.reason)
            
            if (stopPaymentResult.success) {
                // Log the stop payment
                AuditLogService.insert('080', 'DEP00620', 
                    "Stop payment placed on check ${params.checkNo} for account ${deposit.acctNo}", 
                    'deposit', null, null, null, deposit.id)
                
                render([
                    success: true,
                    message: "Stop payment placed successfully"
                ] as JSON)
            } else {
                render([
                    success: false,
                    error: stopPaymentResult.message
                ] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error placing stop payment: ${e.message}", e)
            render([
                success: false,
                error: "Unable to place stop payment: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get available checkbooks for account
     * @return JSON response with available checkbooks
     */
    def getAvailableCheckbooks() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def availableCheckbooks = Checkbook.createCriteria().list {
                eq("status", CheckbookStatus.read(1)) // Available
                isNull("issueCheckbook")
                eq("branch", deposit.branch)
            }
            
            def checkbookOptions = availableCheckbooks.collect { checkbook ->
                [
                    id: checkbook.id,
                    checkbookNo: checkbook.checkbookNo,
                    startCheckNo: checkbook.startCheckNo,
                    endCheckNo: checkbook.endCheckNo,
                    totalLeaves: checkbook.totalLeaves
                ]
            }
            
            render([
                success: true,
                availableCheckbooks: checkbookOptions,
                count: availableCheckbooks.size()
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting available checkbooks: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get available checkbooks: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Checkbook not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
