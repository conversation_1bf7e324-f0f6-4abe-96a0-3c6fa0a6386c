package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql
import org.icbs.deposit.Rollover
import org.icbs.lov.RolloverStatus
import org.icbs.lov.RolloverType

/**
 * DepositRolloverController - Handles deposit rollover operations
 * 
 * This controller manages rollover operations including:
 * - Rollover creation and management
 * - Rollover execution and processing
 * - Rollover inquiry and tracking
 * - Maturity rollover handling
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositRolloverController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * View rollover management page
     * @param id The deposit account ID
     */
    def viewRollover() {
        try {
            log.info("Viewing rollovers for deposit ID: ${params.id}")
            
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                // Check if this is a time deposit account
                if (depositInstance.type.id != 3) {
                    flash.message = 'Rollover functions only available for Time Deposit accounts'
                    render(view:"inquiry/depositInquiry", model:[depositInstance:depositInstance])
                    return
                }
                
                if (depositInstance.branch == UserMaster.get(session.user_id).branch) {
                    render(view:'rollover/view', model:[depositInstance:depositInstance])
                } else {
                    flash.message = 'Rollover functions not allowed for other branch account'
                    render(view:"inquiry/depositInquiry", model:[depositInstance:depositInstance])
                }
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error viewing rollovers: ${e.message}", e)
            flash.message = "Error loading rollover page |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Create new rollover
     * @return JSON response with create form
     */
    def createRollover() {
        try {
            log.info("Creating rollover with params: ${params}")
            
            if(params.depositId) {
                def rolloverInstance = new Rollover()
                rolloverInstance.deposit = Deposit.read(params.depositId)
                rolloverInstance.createdBy = UserMaster.get(session.user_id)
                rolloverInstance.createdDate = new Date()
                
                if(!rolloverInstance.deposit) {
                    notFound()
                    return
                }
                
                render(view:'rollover/create', model:[rolloverInstance:rolloverInstance])
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error creating rollover: ${e.message}", e)
            flash.message = "Error creating rollover |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Save new rollover
     */
    @Transactional
    def saveRollover() {
        try {
            log.info("Saving rollover with params: ${params}")
            
            def rolloverInstance = new Rollover()
            bindData(rolloverInstance, params)
            
            if (!rolloverInstance.validate()) {
                render(view:'rollover/create', model:[rolloverInstance:rolloverInstance])
                return
            }
            
            // Validate rollover business rules
            if (!validateRollover(rolloverInstance)) {
                render(view:'rollover/create', model:[rolloverInstance:rolloverInstance])
                return
            }
            
            rolloverInstance.status = RolloverStatus.read(1) // Active
            rolloverInstance.createdBy = UserMaster.get(session.user_id)
            rolloverInstance.createdDate = new Date()
            rolloverInstance.save(flush: true, failOnError: true)
            
            // Update deposit current rollover
            def deposit = rolloverInstance.deposit
            deposit.currentRollover = rolloverInstance
            deposit.maturityDate = rolloverInstance.endDate
            deposit.save(flush: true, failOnError: true)
            
            // Log the rollover creation
            AuditLogService.insert('080', 'DEP00610', 
                "Rollover created for account ${deposit.acctNo} - Term: ${rolloverInstance.term} days", 
                'deposit', null, null, null, deposit.id)
            
            flash.message = "Rollover Successfully Created"
            redirect(action: "showRollover", id: rolloverInstance.id)
            
        } catch (Exception e) {
            log.error("Error saving rollover: ${e.message}", e)
            flash.message = "Error saving rollover: ${e.message} |error|alert"
            redirect(action: "createRollover", params: params)
        }
    }

    /**
     * Show rollover details
     * @param rolloverInstance The rollover instance
     */
    def showRollover(Rollover rolloverInstance) {
        try {
            if (rolloverInstance) {
                respond rolloverInstance
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error showing rollover: ${e.message}", e)
            flash.message = "Error loading rollover |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Process rollover maturity
     */
    @Transactional
    def processRolloverMaturity() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Rollover ID is required"
                ] as JSON)
                return
            }
            
            def rolloverInstance = Rollover.get(params.id)
            if (!rolloverInstance) {
                render([
                    success: false,
                    error: "Rollover not found"
                ] as JSON)
                return
            }
            
            if (rolloverInstance.status.id != 1) {
                render([
                    success: false,
                    error: "Rollover is not active"
                ] as JSON)
                return
            }
            
            // Check if rollover has matured
            if (rolloverInstance.endDate > new Date()) {
                render([
                    success: false,
                    error: "Rollover has not yet matured"
                ] as JSON)
                return
            }
            
            // Process maturity
            def maturityResult = processMaturity(rolloverInstance)
            
            if (maturityResult.success) {
                render([
                    success: true,
                    message: "Rollover maturity processed successfully",
                    action: maturityResult.action,
                    newRolloverId: maturityResult.newRolloverId
                ] as JSON)
            } else {
                render([
                    success: false,
                    error: maturityResult.message
                ] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error processing rollover maturity: ${e.message}", e)
            render([
                success: false,
                error: "Unable to process rollover maturity: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Renew rollover
     */
    @Transactional
    def renewRollover() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Rollover ID is required"
                ] as JSON)
                return
            }
            
            def rolloverInstance = Rollover.get(params.id)
            if (!rolloverInstance) {
                render([
                    success: false,
                    error: "Rollover not found"
                ] as JSON)
                return
            }
            
            def newTerm = params.newTerm ? params.newTerm.toInteger() : rolloverInstance.term
            def newRate = params.newRate ? params.newRate.toDouble() : rolloverInstance.interestRate
            
            // Create new rollover
            def renewalResult = createRenewalRollover(rolloverInstance, newTerm, newRate)
            
            if (renewalResult.success) {
                // Log the rollover renewal
                AuditLogService.insert('080', 'DEP00611', 
                    "Rollover renewed for account ${rolloverInstance.deposit.acctNo} - New Term: ${newTerm} days", 
                    'deposit', null, null, null, rolloverInstance.deposit.id)
                
                render([
                    success: true,
                    message: "Rollover renewed successfully",
                    newRolloverId: renewalResult.newRolloverId
                ] as JSON)
            } else {
                render([
                    success: false,
                    error: renewalResult.message
                ] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error renewing rollover: ${e.message}", e)
            render([
                success: false,
                error: "Unable to renew rollover: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get rollover information
     * @return JSON response with rollover details
     */
    def getRolloverInfo() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Rollover ID is required"
                ] as JSON)
                return
            }
            
            def rolloverInstance = Rollover.get(params.id)
            if (!rolloverInstance) {
                render([
                    success: false,
                    error: "Rollover not found"
                ] as JSON)
                return
            }
            
            def rolloverInfo = [
                id: rolloverInstance.id,
                term: rolloverInstance.term,
                interestRate: rolloverInstance.interestRate,
                startDate: rolloverInstance.startDate?.format('yyyy-MM-dd'),
                endDate: rolloverInstance.endDate?.format('yyyy-MM-dd'),
                status: rolloverInstance.status?.description,
                rolloverType: rolloverInstance.rolloverType?.description,
                principalAmount: rolloverInstance.principalAmount,
                maturityAmount: rolloverInstance.maturityAmount,
                createdDate: rolloverInstance.createdDate?.format('yyyy-MM-dd'),
                createdBy: rolloverInstance.createdBy?.username,
                accountNo: rolloverInstance.deposit?.acctNo,
                customerName: rolloverInstance.deposit?.customer?.displayName,
                daysToMaturity: calculateDaysToMaturity(rolloverInstance.endDate),
                isMatured: rolloverInstance.endDate <= new Date(),
                canRenew: rolloverInstance.status?.id == 1
            ]
            
            render([
                success: true,
                rolloverInfo: rolloverInfo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting rollover info: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get rollover information: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get rollovers for account
     * @return JSON response with account rollovers
     */
    def getAccountRollovers() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def rollovers = Rollover.findAllByDeposit(deposit, [sort: 'startDate', order: 'desc'])
            
            def rolloverData = rollovers.collect { rollover ->
                [
                    id: rollover.id,
                    term: rollover.term,
                    interestRate: rollover.interestRate,
                    startDate: rollover.startDate?.format('yyyy-MM-dd'),
                    endDate: rollover.endDate?.format('yyyy-MM-dd'),
                    status: rollover.status?.description,
                    principalAmount: rollover.principalAmount,
                    maturityAmount: rollover.maturityAmount,
                    daysToMaturity: calculateDaysToMaturity(rollover.endDate),
                    isActive: rollover.status?.id == 1,
                    isMatured: rollover.endDate <= new Date(),
                    isCurrent: deposit.currentRollover?.id == rollover.id
                ]
            }
            
            def activeCount = rollovers.count { it.status?.id == 1 }
            def maturedCount = rollovers.count { it.status?.id == 3 }
            
            render([
                success: true,
                rollovers: rolloverData,
                activeCount: activeCount,
                maturedCount: maturedCount,
                totalCount: rollovers.size(),
                accountNo: deposit.acctNo,
                currentRollover: deposit.currentRollover?.id
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting account rollovers: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get account rollovers: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate rollover business rules
     * @param rollover The rollover to validate
     * @return true if valid
     */
    private boolean validateRollover(Rollover rollover) {
        try {
            def deposit = rollover.deposit
            
            // Check if deposit is a time deposit
            if (deposit.type.id != 3) {
                rollover.errors.rejectValue('deposit', 'rollover.deposit.not.time.deposit')
                return false
            }
            
            // Check term
            if (rollover.term <= 0) {
                rollover.errors.rejectValue('term', 'rollover.term.invalid')
                return false
            }
            
            // Check dates
            if (rollover.endDate <= rollover.startDate) {
                rollover.errors.rejectValue('endDate', 'rollover.endDate.invalid')
                return false
            }
            
            // Check interest rate
            if (rollover.interestRate < 0) {
                rollover.errors.rejectValue('interestRate', 'rollover.interestRate.invalid')
                return false
            }
            
            return true
            
        } catch (Exception e) {
            log.error("Error validating rollover: ${e.message}", e)
            return false
        }
    }

    /**
     * Process rollover maturity
     * @param rollover The rollover to process
     * @return Maturity processing result
     */
    private Map processMaturity(Rollover rollover) {
        try {
            def deposit = rollover.deposit
            
            // Mark current rollover as matured
            rollover.status = RolloverStatus.read(3) // Matured
            rollover.maturedDate = new Date()
            rollover.save(flush: true, failOnError: true)
            
            // Check rollover instructions
            if (rollover.rolloverType?.code == 'AUTO_RENEW') {
                // Create new rollover automatically
                def renewalResult = createRenewalRollover(rollover, rollover.term, rollover.interestRate)
                if (renewalResult.success) {
                    return [
                        success: true, 
                        action: 'AUTO_RENEWED',
                        newRolloverId: renewalResult.newRolloverId,
                        message: "Rollover automatically renewed"
                    ]
                }
            } else if (rollover.rolloverType?.code == 'PRINCIPAL_ONLY') {
                // Renew principal only, pay out interest
                def interestAmount = calculateInterestAmount(rollover)
                payoutInterest(deposit, interestAmount)
                
                def renewalResult = createRenewalRollover(rollover, rollover.term, rollover.interestRate, rollover.principalAmount)
                if (renewalResult.success) {
                    return [
                        success: true, 
                        action: 'PRINCIPAL_RENEWED',
                        newRolloverId: renewalResult.newRolloverId,
                        interestPaid: interestAmount,
                        message: "Principal renewed, interest paid out"
                    ]
                }
            } else {
                // Manual processing required
                deposit.status = DepositStatus.read(8) // Matured
                deposit.save(flush: true, failOnError: true)
                
                return [
                    success: true, 
                    action: 'MANUAL_REQUIRED',
                    message: "Rollover matured - manual processing required"
                ]
            }
            
            return [success: false, message: "Maturity processing failed"]
            
        } catch (Exception e) {
            log.error("Error processing maturity: ${e.message}", e)
            return [success: false, message: "Maturity processing failed: ${e.message}"]
        }
    }

    /**
     * Create renewal rollover
     * @param originalRollover The original rollover
     * @param newTerm The new term
     * @param newRate The new interest rate
     * @param principalAmount The principal amount (optional)
     * @return Renewal result
     */
    private Map createRenewalRollover(Rollover originalRollover, Integer newTerm, Double newRate, Double principalAmount = null) {
        try {
            def deposit = originalRollover.deposit
            def startDate = new Date()
            def endDate = calculateEndDate(startDate, newTerm)
            
            def newRollover = new Rollover(
                deposit: deposit,
                term: newTerm,
                interestRate: newRate,
                startDate: startDate,
                endDate: endDate,
                principalAmount: principalAmount ?: deposit.ledgerBalAmt,
                rolloverType: originalRollover.rolloverType,
                status: RolloverStatus.read(1), // Active
                createdBy: UserMaster.get(session.user_id),
                createdDate: new Date()
            )
            newRollover.save(flush: true, failOnError: true)
            
            // Update deposit
            deposit.currentRollover = newRollover
            deposit.maturityDate = endDate
            deposit.save(flush: true, failOnError: true)
            
            return [success: true, newRolloverId: newRollover.id]
            
        } catch (Exception e) {
            log.error("Error creating renewal rollover: ${e.message}", e)
            return [success: false, message: "Renewal creation failed: ${e.message}"]
        }
    }

    /**
     * Calculate end date based on start date and term
     * @param startDate The start date
     * @param term The term in days
     * @return Calculated end date
     */
    private Date calculateEndDate(Date startDate, Integer term) {
        try {
            def calendar = Calendar.getInstance()
            calendar.setTime(startDate)
            calendar.add(Calendar.DAY_OF_MONTH, term)
            return calendar.getTime()
        } catch (Exception e) {
            log.error("Error calculating end date: ${e.message}", e)
            return startDate + term
        }
    }

    /**
     * Calculate days to maturity
     * @param maturityDate The maturity date
     * @return Days to maturity
     */
    private Integer calculateDaysToMaturity(Date maturityDate) {
        try {
            if (!maturityDate) return 0
            
            def today = new Date()
            def days = (maturityDate - today).intValue()
            return days > 0 ? days : 0
            
        } catch (Exception e) {
            log.error("Error calculating days to maturity: ${e.message}", e)
            return 0
        }
    }

    /**
     * Calculate interest amount for rollover
     * @param rollover The rollover
     * @return Calculated interest amount
     */
    private Double calculateInterestAmount(Rollover rollover) {
        try {
            def principal = rollover.principalAmount
            def rate = rollover.interestRate / 100
            def term = rollover.term
            
            return (principal * rate * term) / 365
            
        } catch (Exception e) {
            log.error("Error calculating interest amount: ${e.message}", e)
            return 0.0
        }
    }

    /**
     * Payout interest to deposit account
     * @param deposit The deposit account
     * @param interestAmount The interest amount to pay
     */
    private void payoutInterest(Deposit deposit, Double interestAmount) {
        try {
            if (interestAmount > 0) {
                // Create interest payout transaction
                def txn = new TxnFile(
                    acctNo: deposit.acctNo,
                    branch: deposit.branch,
                    currency: deposit.product.currency,
                    depAcct: deposit,
                    status: ConfigItemStatus.get(2),
                    txnAmt: interestAmount,
                    txnCode: 'INTPY',
                    txnDate: deposit.branch.runDate,
                    txnDescription: 'Interest Payout',
                    txnParticulars: "Rollover interest payout",
                    txnRef: "INT-PAYOUT-${deposit.id}",
                    txnTimestamp: new Date().toTimestamp(),
                    user: UserMaster.get(session.user_id),
                    txnType: TxnType.findByCode('DR'),
                    txnTemplate: TxnTemplate.findByCode('INTPY')
                )
                txn.save(flush: true, failOnError: true)
                
                // Update account balance
                deposit.ledgerBalAmt -= interestAmount
                deposit.availableBalAmt -= interestAmount
                deposit.save(flush: true, failOnError: true)
                
                // Create GL breakdown
                GlTransactionService.saveTxnBreakdown(txn.id)
            }
            
        } catch (Exception e) {
            log.error("Error paying out interest: ${e.message}", e)
            throw e
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Rollover not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
