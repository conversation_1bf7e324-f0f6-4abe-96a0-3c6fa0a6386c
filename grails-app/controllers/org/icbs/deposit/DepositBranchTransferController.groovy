package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql

/**
 * DepositBranchTransferController - Handles deposit branch transfer operations
 * 
 * This controller manages branch transfer operations including:
 * - Account branch transfer processing
 * - Transfer validation and approval
 * - Transfer history tracking
 * - Inter-branch account management
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositBranchTransferController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * View branch transfer page
     * @param id The deposit account ID
     */
    def viewBranchTransfer() {
        try {
            log.info("Viewing branch transfer for deposit ID: ${params.id}")
            
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                render(view:'branchTransfer/view', model:[depositInstance:depositInstance])
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error viewing branch transfer: ${e.message}", e)
            flash.message = "Error loading branch transfer page |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Initiate branch transfer
     */
    @Transactional
    def initiateBranchTransfer() {
        try {
            if (!params.id || !params.targetBranchId) {
                render([
                    success: false,
                    error: "Deposit ID and target branch are required"
                ] as JSON)
                return
            }
            
            def depositInstance = Deposit.get(params.id)
            if (!depositInstance) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def targetBranch = Branch.get(params.targetBranchId)
            if (!targetBranch) {
                render([
                    success: false,
                    error: "Target branch not found"
                ] as JSON)
                return
            }
            
            // Validate branch transfer
            def validationResult = validateBranchTransfer(depositInstance, targetBranch)
            if (!validationResult.canTransfer) {
                render([
                    success: false,
                    error: validationResult.reason
                ] as JSON)
                return
            }
            
            // Process branch transfer
            def transferResult = processBranchTransfer(depositInstance, targetBranch, params.transferReason)
            
            if (transferResult.success) {
                // Log the branch transfer
                AuditLogService.insert('080', 'DEP00630', 
                    "Branch transfer completed for account ${depositInstance.acctNo} from ${depositInstance.branch.name} to ${targetBranch.name}", 
                    'deposit', null, null, null, depositInstance.id)
                
                render([
                    success: true,
                    message: "Branch transfer completed successfully",
                    oldBranch: transferResult.oldBranch,
                    newBranch: transferResult.newBranch,
                    transactionId: transferResult.transactionId
                ] as JSON)
            } else {
                render([
                    success: false,
                    error: transferResult.message
                ] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error initiating branch transfer: ${e.message}", e)
            render([
                success: false,
                error: "Unable to initiate branch transfer: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get available branches for transfer
     * @return JSON response with available branches
     */
    def getAvailableBranches() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def depositInstance = Deposit.get(params.id)
            if (!depositInstance) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def currentBranch = depositInstance.branch
            
            // Get all active branches except current branch
            def availableBranches = Branch.createCriteria().list {
                eq("status", ConfigItemStatus.read(2)) // Active
                ne("id", currentBranch.id)
                order("name", "asc")
            }
            
            def branchData = availableBranches.collect { branch ->
                [
                    id: branch.id,
                    code: branch.code,
                    name: branch.name,
                    address: branch.address,
                    canTransfer: validateBranchForTransfer(depositInstance, branch)
                ]
            }
            
            render([
                success: true,
                availableBranches: branchData,
                currentBranch: [
                    id: currentBranch.id,
                    code: currentBranch.code,
                    name: currentBranch.name
                ]
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting available branches: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get available branches: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get branch transfer history
     * @return JSON response with transfer history
     */
    def getBranchTransferHistory() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def depositInstance = Deposit.get(params.id)
            if (!depositInstance) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            // Get branch transfer transactions
            def transferTxns = TxnFile.createCriteria().list {
                eq("depAcct", depositInstance)
                eq("txnCode", "BRXFR")
                order("txnDate", "desc")
                order("txnTimestamp", "desc")
            }
            
            def historyData = transferTxns.collect { txn ->
                [
                    id: txn.id,
                    txnDate: txn.txnDate?.format('yyyy-MM-dd'),
                    description: txn.txnDescription,
                    particulars: txn.txnParticulars,
                    user: txn.user?.username,
                    branch: txn.branch?.name,
                    timestamp: txn.txnTimestamp?.format('yyyy-MM-dd HH:mm:ss')
                ]
            }
            
            render([
                success: true,
                transferHistory: historyData,
                currentBranch: depositInstance.branch?.name,
                accountNo: depositInstance.acctNo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting branch transfer history: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get transfer history: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get branch transfer summary
     * @return JSON response with transfer summary
     */
    def getBranchTransferSummary() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def depositInstance = Deposit.get(params.id)
            if (!depositInstance) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def summary = [
                accountNo: depositInstance.acctNo,
                customerName: depositInstance.customer?.displayName,
                currentBranch: depositInstance.branch?.name,
                accountType: depositInstance.type?.description,
                status: depositInstance.status?.description,
                balance: depositInstance.ledgerBalAmt,
                dateOpened: depositInstance.dateOpened?.format('yyyy-MM-dd'),
                canTransfer: canAccountBeTransferred(depositInstance),
                transferCount: getTotalTransferCount(depositInstance),
                lastTransferDate: getLastTransferDate(depositInstance)
            ]
            
            render([
                success: true,
                summary: summary
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting branch transfer summary: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get transfer summary: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate branch transfer
     * @param deposit The deposit account
     * @param targetBranch The target branch
     * @return Validation result
     */
    private Map validateBranchTransfer(Deposit deposit, Branch targetBranch) {
        try {
            // Check if account is active
            if (deposit.status.id != 2) {
                return [canTransfer: false, reason: "Account is not active"]
            }
            
            // Check if target branch is different from current
            if (deposit.branch.id == targetBranch.id) {
                return [canTransfer: false, reason: "Target branch is same as current branch"]
            }
            
            // Check if target branch is active
            if (targetBranch.status.id != 2) {
                return [canTransfer: false, reason: "Target branch is not active"]
            }
            
            // Check for pending transactions
            def pendingTxns = TxnFile.countByDepAcctAndStatus(deposit, ConfigItemStatus.read(1))
            if (pendingTxns > 0) {
                return [canTransfer: false, reason: "Account has pending transactions"]
            }
            
            // Check for active holds
            if (deposit.holdBalAmt > 0) {
                return [canTransfer: false, reason: "Account has active holds"]
            }
            
            // Check for uncleared checks
            if (deposit.unclearedCheckBalAmt > 0) {
                return [canTransfer: false, reason: "Account has uncleared checks"]
            }
            
            return [canTransfer: true, reason: "Account can be transferred"]
            
        } catch (Exception e) {
            log.error("Error validating branch transfer: ${e.message}", e)
            return [canTransfer: false, reason: "Validation failed: ${e.message}"]
        }
    }

    /**
     * Validate branch for transfer
     * @param deposit The deposit account
     * @param branch The branch to validate
     * @return true if branch is valid for transfer
     */
    private boolean validateBranchForTransfer(Deposit deposit, Branch branch) {
        try {
            return branch.status.id == 2 && branch.id != deposit.branch.id
        } catch (Exception e) {
            log.error("Error validating branch for transfer: ${e.message}", e)
            return false
        }
    }

    /**
     * Process branch transfer
     * @param deposit The deposit account
     * @param targetBranch The target branch
     * @param transferReason The reason for transfer
     * @return Transfer result
     */
    private Map processBranchTransfer(Deposit deposit, Branch targetBranch, String transferReason) {
        try {
            def oldBranch = deposit.branch
            
            // Update deposit branch
            deposit.branch = targetBranch
            deposit.save(flush: true, failOnError: true)
            
            // Create branch transfer transaction
            def txn = new TxnFile(
                acctNo: deposit.acctNo,
                branch: targetBranch,
                currency: deposit.product.currency,
                depAcct: deposit,
                status: ConfigItemStatus.get(2),
                txnAmt: 0.0,
                txnCode: 'BRXFR',
                txnDate: targetBranch.runDate,
                txnDescription: "Branch Transfer",
                txnParticulars: "Transferred from ${oldBranch.name} to ${targetBranch.name} - ${transferReason ?: 'Branch transfer'}",
                txnRef: "BRXFR-${deposit.id}",
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: TxnType.findByCode('MEMO'),
                txnTemplate: TxnTemplate.findByCode('BRXFR')
            )
            txn.save(flush: true, failOnError: true)
            
            return [
                success: true, 
                oldBranch: oldBranch.name,
                newBranch: targetBranch.name,
                transactionId: txn.id,
                message: "Branch transfer completed successfully"
            ]
            
        } catch (Exception e) {
            log.error("Error processing branch transfer: ${e.message}", e)
            return [success: false, message: "Branch transfer failed: ${e.message}"]
        }
    }

    /**
     * Check if account can be transferred
     * @param deposit The deposit account
     * @return true if account can be transferred
     */
    private boolean canAccountBeTransferred(Deposit deposit) {
        try {
            return deposit.status.id == 2 && // Active
                   deposit.holdBalAmt == 0 && // No holds
                   deposit.unclearedCheckBalAmt == 0 && // No uncleared checks
                   TxnFile.countByDepAcctAndStatus(deposit, ConfigItemStatus.read(1)) == 0 // No pending transactions
        } catch (Exception e) {
            log.error("Error checking if account can be transferred: ${e.message}", e)
            return false
        }
    }

    /**
     * Get total transfer count for account
     * @param deposit The deposit account
     * @return Total number of transfers
     */
    private Integer getTotalTransferCount(Deposit deposit) {
        try {
            return TxnFile.countByDepAcctAndTxnCode(deposit, 'BRXFR')
        } catch (Exception e) {
            log.error("Error getting total transfer count: ${e.message}", e)
            return 0
        }
    }

    /**
     * Get last transfer date for account
     * @param deposit The deposit account
     * @return Last transfer date
     */
    private String getLastTransferDate(Deposit deposit) {
        try {
            def lastTransfer = TxnFile.createCriteria().get {
                eq("depAcct", deposit)
                eq("txnCode", "BRXFR")
                projections {
                    max("txnDate")
                }
            }
            
            return lastTransfer?.format('yyyy-MM-dd') ?: 'Never'
        } catch (Exception e) {
            log.error("Error getting last transfer date: ${e.message}", e)
            return 'Unknown'
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Deposit account not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
