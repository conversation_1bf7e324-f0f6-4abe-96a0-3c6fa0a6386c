package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql

/**
 * DepositUtilityController - Handles deposit utility and helper operations
 * 
 * This controller manages utility operations including:
 * - Logging and audit functions
 * - Helper methods and utilities
 * - Data validation and cleanup
 * - System maintenance operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositUtilityController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * Get deposit lookup data
     * @return JSON response with lookup data
     */
    def getLookupData() {
        try {
            def lookupData = [:]
            
            // Deposit types
            lookupData.depositTypes = DepositType.list().collect { type ->
                [id: type.id, code: type.code, description: type.description]
            }
            
            // Deposit statuses
            lookupData.depositStatuses = DepositStatus.list().collect { status ->
                [id: status.id, code: status.code, description: status.description]
            }
            
            // Branches
            lookupData.branches = Branch.findAllByStatus(ConfigItemStatus.read(2)).collect { branch ->
                [id: branch.id, code: branch.code, name: branch.name]
            }
            
            // Products
            lookupData.products = Product.findAllByStatus(ConfigItemStatus.read(2)).collect { product ->
                [id: product.id, code: product.code, name: product.name]
            }
            
            // Transaction types
            lookupData.transactionTypes = TxnType.list().collect { type ->
                [id: type.id, code: type.code, description: type.description]
            }
            
            render([
                success: true,
                lookupData: lookupData
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting lookup data: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get lookup data: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate account number format
     * @return JSON response with validation result
     */
    def validateAccountNumber() {
        try {
            if (!params.accountNo) {
                render([
                    success: false,
                    error: "Account number is required"
                ] as JSON)
                return
            }
            
            def accountNo = params.accountNo.toString().trim()
            
            // Basic format validation
            def isValid = accountNo.matches(/^\d{10,16}$/) // 10-16 digits
            
            // Check if account exists
            def existingAccount = null
            if (isValid) {
                existingAccount = Deposit.findByAcctNo(accountNo)
            }
            
            render([
                success: true,
                validation: [
                    accountNo: accountNo,
                    isValidFormat: isValid,
                    exists: existingAccount != null,
                    accountInfo: existingAccount ? [
                        id: existingAccount.id,
                        customerName: existingAccount.customer?.displayName,
                        status: existingAccount.status?.description,
                        balance: existingAccount.ledgerBalAmt
                    ] : null
                ]
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error validating account number: ${e.message}", e)
            render([
                success: false,
                error: "Unable to validate account number: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get system configuration
     * @return JSON response with system config
     */
    def getSystemConfig() {
        try {
            def config = [:]
            
            // Get key system parameters
            config.dailyTransferLimit = Institution.findByParamCode('DEP.DAILY.TRANSFER.LIMIT')?.paramValue?.toDouble() ?: 100000.0
            config.maxHoldDays = Institution.findByParamCode('DEP.MAX.HOLD.DAYS')?.paramValue?.toInteger() ?: 30
            config.dormantDays = Institution.findByParamCode('DEP.DORMANT.DAYS')?.paramValue?.toInteger() ?: 365
            config.minBalance = Institution.findByParamCode('DEP.MIN.BALANCE')?.paramValue?.toDouble() ?: 100.0
            config.maxDailyWithdrawal = Institution.findByParamCode('DEP.MAX.DAILY.WITHDRAWAL')?.paramValue?.toDouble() ?: 50000.0
            
            // System status
            config.systemDate = new Date().format('yyyy-MM-dd')
            config.businessDate = Branch.get(1)?.runDate?.format('yyyy-MM-dd')
            config.systemStatus = 'ONLINE'
            
            render([
                success: true,
                config: config
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting system config: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get system config: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Generate account number
     * @return JSON response with generated account number
     */
    def generateAccountNumber() {
        try {
            def branchCode = params.branchCode ?: UserMaster.get(session.user_id)?.branch?.code ?: '001'
            def productCode = params.productCode ?: '01'
            
            def accountNumber = generateUniqueAccountNumber(branchCode, productCode)
            
            render([
                success: true,
                accountNumber: accountNumber,
                branchCode: branchCode,
                productCode: productCode
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error generating account number: ${e.message}", e)
            render([
                success: false,
                error: "Unable to generate account number: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Log user activity
     */
    def logActivity() {
        try {
            if (!params.activity || !params.module) {
                render([
                    success: false,
                    error: "Activity and module are required"
                ] as JSON)
                return
            }
            
            AuditLogService.insert(
                params.module.toString(),
                params.subModule ?: 'GENERAL',
                params.activity.toString(),
                params.entityType ?: 'system',
                params.oldValue,
                params.newValue,
                params.fieldName,
                params.entityId?.toLong()
            )
            
            render([
                success: true,
                message: "Activity logged successfully"
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error logging activity: ${e.message}", e)
            render([
                success: false,
                error: "Unable to log activity: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get deposit statistics
     * @return JSON response with statistics
     */
    def getDepositStatistics() {
        try {
            def stats = [:]
            
            // Account counts by status
            stats.totalAccounts = Deposit.count()
            stats.activeAccounts = Deposit.countByStatus(DepositStatus.read(2))
            stats.dormantAccounts = Deposit.countByStatus(DepositStatus.read(5))
            stats.closedAccounts = Deposit.countByStatus(DepositStatus.read(7))
            
            // Account counts by type
            stats.savingsAccounts = Deposit.countByType(DepositType.read(1))
            stats.currentAccounts = Deposit.countByType(DepositType.read(2))
            stats.timeDepositAccounts = Deposit.countByType(DepositType.read(3))
            
            // Balance statistics
            def totalBalance = Deposit.createCriteria().get {
                projections {
                    sum("ledgerBalAmt")
                }
            } ?: 0.0
            
            def avgBalance = totalBalance / (stats.totalAccounts ?: 1)
            
            stats.totalBalance = totalBalance
            stats.averageBalance = avgBalance.round(2)
            
            // Recent activity
            def today = new Date().clearTime()
            stats.todayTransactions = TxnFile.countByTxnDateGreaterThanEquals(today)
            stats.todayNewAccounts = Deposit.countByDateOpenedGreaterThanEquals(today)
            
            render([
                success: true,
                statistics: stats,
                generatedAt: new Date().format('yyyy-MM-dd HH:mm:ss')
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting deposit statistics: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get statistics: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Cleanup old data
     */
    @Transactional
    def cleanupOldData() {
        try {
            def daysToKeep = params.daysToKeep?.toInteger() ?: 365
            def cutoffDate = new Date() - daysToKeep
            
            def cleanupResults = [:]
            
            // Cleanup old audit logs (if enabled)
            if (params.cleanupAuditLogs == 'true') {
                // Implementation would depend on audit log structure
                cleanupResults.auditLogsDeleted = 0
            }
            
            // Cleanup old transaction files (closed accounts only)
            if (params.cleanupTransactions == 'true') {
                def oldTxns = TxnFile.createCriteria().list {
                    lt("txnDate", cutoffDate)
                    depAcct {
                        eq("status", DepositStatus.read(7)) // Closed
                    }
                }
                
                def deletedCount = 0
                oldTxns.each { txn ->
                    try {
                        txn.delete(flush: true)
                        deletedCount++
                    } catch (Exception e) {
                        log.warn("Could not delete transaction ${txn.id}: ${e.message}")
                    }
                }
                
                cleanupResults.transactionsDeleted = deletedCount
            }
            
            // Log the cleanup
            AuditLogService.insert('080', 'CLEANUP', 
                "Data cleanup completed - ${cleanupResults}", 
                'system', null, null, null, null)
            
            render([
                success: true,
                message: "Data cleanup completed",
                results: cleanupResults
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error during data cleanup: ${e.message}", e)
            render([
                success: false,
                error: "Data cleanup failed: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Export deposit data
     * @return JSON response with export status
     */
    def exportDepositData() {
        try {
            def exportType = params.exportType ?: 'CSV'
            def dateFrom = params.dateFrom ? Date.parse("yyyy-MM-dd", params.dateFrom) : new Date() - 30
            def dateTo = params.dateTo ? Date.parse("yyyy-MM-dd", params.dateTo) : new Date()
            
            // Get deposits in date range
            def deposits = Deposit.createCriteria().list {
                between("dateOpened", dateFrom, dateTo)
                order("dateOpened", "desc")
            }
            
            def exportData = deposits.collect { deposit ->
                [
                    accountNo: deposit.acctNo,
                    customerName: deposit.customer?.displayName,
                    accountType: deposit.type?.description,
                    status: deposit.status?.description,
                    balance: deposit.ledgerBalAmt,
                    dateOpened: deposit.dateOpened?.format('yyyy-MM-dd'),
                    branch: deposit.branch?.name
                ]
            }
            
            // For now, return JSON data
            // In a real implementation, you would generate CSV/Excel files
            render([
                success: true,
                message: "Export completed",
                exportType: exportType,
                recordCount: exportData.size(),
                data: exportData
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error exporting deposit data: ${e.message}", e)
            render([
                success: false,
                error: "Export failed: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Generate unique account number
     * @param branchCode The branch code
     * @param productCode The product code
     * @return Generated account number
     */
    private String generateUniqueAccountNumber(String branchCode, String productCode) {
        try {
            def maxAttempts = 100
            def attempt = 0
            
            while (attempt < maxAttempts) {
                def sequence = String.format("%06d", new Random().nextInt(999999))
                def checkDigit = calculateCheckDigit(branchCode + productCode + sequence)
                def accountNumber = branchCode + productCode + sequence + checkDigit
                
                // Check if account number already exists
                if (!Deposit.findByAcctNo(accountNumber)) {
                    return accountNumber
                }
                
                attempt++
            }
            
            throw new RuntimeException("Unable to generate unique account number after ${maxAttempts} attempts")
            
        } catch (Exception e) {
            log.error("Error generating unique account number: ${e.message}", e)
            throw e
        }
    }

    /**
     * Calculate check digit for account number
     * @param accountBase The account number base
     * @return Check digit
     */
    private String calculateCheckDigit(String accountBase) {
        try {
            def sum = 0
            def multiplier = 2
            
            // Calculate weighted sum from right to left
            for (int i = accountBase.length() - 1; i >= 0; i--) {
                def digit = Integer.parseInt(accountBase.charAt(i).toString())
                sum += digit * multiplier
                multiplier = multiplier == 2 ? 1 : 2
            }
            
            def checkDigit = (10 - (sum % 10)) % 10
            return checkDigit.toString()
            
        } catch (Exception e) {
            log.error("Error calculating check digit: ${e.message}", e)
            return "0"
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Resource not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
