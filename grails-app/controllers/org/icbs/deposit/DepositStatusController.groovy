package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql

/**
 * DepositStatusController - Handles deposit account status management
 * 
 * This controller manages status operations including:
 * - Account status changes and validation
 * - Status history tracking
 * - Dormant account management
 * - Account closure processing
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositStatusController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * View status management page
     * @param id The deposit account ID
     */
    def viewStatus() {
        try {
            log.info("Viewing status for deposit ID: ${params.id}")
            
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                if (depositInstance.branch == UserMaster.get(session.user_id).branch) {
                    render(view:'status/view', model:[depositInstance:depositInstance])
                } else {
                    flash.message = 'Status functions not allowed for other branch account'
                    render(view:"inquiry/depositInquiry", model:[depositInstance:depositInstance])
                }
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error viewing status: ${e.message}", e)
            flash.message = "Error loading status page |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Change account status
     */
    @Transactional
    def changeStatus() {
        try {
            if (!params.id || !params.newStatusId) {
                render([
                    success: false,
                    error: "Deposit ID and new status are required"
                ] as JSON)
                return
            }
            
            def depositInstance = Deposit.get(params.id)
            if (!depositInstance) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def newStatus = DepositStatus.get(params.newStatusId)
            if (!newStatus) {
                render([
                    success: false,
                    error: "Invalid status"
                ] as JSON)
                return
            }
            
            def oldStatus = depositInstance.status
            
            // Validate status change
            if (!validateStatusChange(depositInstance, newStatus)) {
                render([
                    success: false,
                    error: "Invalid status change"
                ] as JSON)
                return
            }
            
            // Process status change
            def changeResult = processStatusChange(depositInstance, oldStatus, newStatus, params.reason)
            
            if (changeResult.success) {
                render([
                    success: true,
                    message: "Status changed successfully",
                    oldStatus: oldStatus.description,
                    newStatus: newStatus.description,
                    transactionId: changeResult.transactionId
                ] as JSON)
            } else {
                render([
                    success: false,
                    error: changeResult.message
                ] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error changing status: ${e.message}", e)
            render([
                success: false,
                error: "Unable to change status: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Close account
     */
    @Transactional
    def closeAccount() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def depositInstance = Deposit.get(params.id)
            if (!depositInstance) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            // Validate account can be closed
            def validationResult = validateAccountClosure(depositInstance)
            if (!validationResult.canClose) {
                render([
                    success: false,
                    error: validationResult.reason
                ] as JSON)
                return
            }
            
            // Process account closure
            def closureResult = processAccountClosure(depositInstance, params.closureReason)
            
            if (closureResult.success) {
                // Log the account closure
                AuditLogService.insert('080', 'DEP00620', 
                    "Account closed: ${depositInstance.acctNo} - Reason: ${params.closureReason}", 
                    'deposit', null, null, null, depositInstance.id)
                
                render([
                    success: true,
                    message: "Account closed successfully",
                    finalBalance: closureResult.finalBalance,
                    transactionId: closureResult.transactionId
                ] as JSON)
            } else {
                render([
                    success: false,
                    error: closureResult.message
                ] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error closing account: ${e.message}", e)
            render([
                success: false,
                error: "Unable to close account: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Reactivate dormant account
     */
    @Transactional
    def reactivateAccount() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def depositInstance = Deposit.get(params.id)
            if (!depositInstance) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            if (depositInstance.status.id != 5) { // Dormant
                render([
                    success: false,
                    error: "Account is not dormant"
                ] as JSON)
                return
            }
            
            // Reactivate account
            def oldStatus = depositInstance.status
            depositInstance.status = DepositStatus.read(3) // Re-activated
            depositInstance.statusChangeDate = new Date()
            depositInstance.save(flush: true, failOnError: true)
            
            // Create reactivation transaction
            def txnResult = createStatusChangeTransaction(depositInstance, oldStatus, depositInstance.status, 'Account Reactivation')
            
            // Log the reactivation
            AuditLogService.insert('080', 'DEP00621', 
                "Account reactivated: ${depositInstance.acctNo}", 
                'deposit', null, null, null, depositInstance.id)
            
            render([
                success: true,
                message: "Account reactivated successfully",
                transactionId: txnResult.transactionId
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error reactivating account: ${e.message}", e)
            render([
                success: false,
                error: "Unable to reactivate account: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get status change history
     * @return JSON response with status history
     */
    def getStatusHistory() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def depositInstance = Deposit.get(params.id)
            if (!depositInstance) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            // Get status change transactions
            def statusTxns = TxnFile.createCriteria().list {
                eq("depAcct", depositInstance)
                'in'("txnCode", ["STCHG", "CLOSE", "REACT"])
                order("txnDate", "desc")
                order("txnTimestamp", "desc")
            }
            
            def historyData = statusTxns.collect { txn ->
                [
                    id: txn.id,
                    txnDate: txn.txnDate?.format('yyyy-MM-dd'),
                    txnCode: txn.txnCode,
                    description: txn.txnDescription,
                    particulars: txn.txnParticulars,
                    user: txn.user?.username,
                    timestamp: txn.txnTimestamp?.format('yyyy-MM-dd HH:mm:ss')
                ]
            }
            
            render([
                success: true,
                statusHistory: historyData,
                currentStatus: depositInstance.status?.description,
                statusChangeDate: depositInstance.statusChangeDate?.format('yyyy-MM-dd'),
                accountNo: depositInstance.acctNo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting status history: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get status history: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get available status transitions
     * @return JSON response with available statuses
     */
    def getAvailableStatuses() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def depositInstance = Deposit.get(params.id)
            if (!depositInstance) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def currentStatusId = depositInstance.status.id
            def availableStatuses = getValidStatusTransitions(currentStatusId)
            
            def statusData = availableStatuses.collect { status ->
                [
                    id: status.id,
                    code: status.code,
                    description: status.description,
                    requiresReason: status.id in [5, 6, 7] // Dormant, Frozen, Closed
                ]
            }
            
            render([
                success: true,
                availableStatuses: statusData,
                currentStatus: [
                    id: depositInstance.status.id,
                    description: depositInstance.status.description
                ]
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting available statuses: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get available statuses: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate status change
     * @param deposit The deposit account
     * @param newStatus The new status
     * @return true if valid
     */
    private boolean validateStatusChange(Deposit deposit, DepositStatus newStatus) {
        try {
            def currentStatusId = deposit.status.id
            def newStatusId = newStatus.id
            
            // Define valid status transitions
            def validTransitions = [
                1: [2], // Pending -> Active
                2: [5, 6, 7], // Active -> Dormant, Frozen, Closed
                3: [2, 5, 6], // Re-activated -> Active, Dormant, Frozen
                4: [2, 5, 6], // Re-opened -> Active, Dormant, Frozen
                5: [3, 6, 7], // Dormant -> Re-activated, Frozen, Closed
                6: [2, 3, 4, 5, 7] // Frozen -> Active, Re-activated, Re-opened, Dormant, Closed
            ]
            
            return validTransitions[currentStatusId]?.contains(newStatusId) ?: false
            
        } catch (Exception e) {
            log.error("Error validating status change: ${e.message}", e)
            return false
        }
    }

    /**
     * Get valid status transitions for current status
     * @param currentStatusId The current status ID
     * @return List of valid statuses
     */
    private List<DepositStatus> getValidStatusTransitions(Integer currentStatusId) {
        try {
            def validTransitions = [
                1: [2], // Pending -> Active
                2: [5, 6, 7], // Active -> Dormant, Frozen, Closed
                3: [2, 5, 6], // Re-activated -> Active, Dormant, Frozen
                4: [2, 5, 6], // Re-opened -> Active, Dormant, Frozen
                5: [3, 6, 7], // Dormant -> Re-activated, Frozen, Closed
                6: [2, 3, 4, 5, 7] // Frozen -> Active, Re-activated, Re-opened, Dormant, Closed
            ]
            
            def validStatusIds = validTransitions[currentStatusId] ?: []
            return DepositStatus.findAllByIdInList(validStatusIds)
            
        } catch (Exception e) {
            log.error("Error getting valid status transitions: ${e.message}", e)
            return []
        }
    }

    /**
     * Process status change
     * @param deposit The deposit account
     * @param oldStatus The old status
     * @param newStatus The new status
     * @param reason The reason for change
     * @return Processing result
     */
    private Map processStatusChange(Deposit deposit, DepositStatus oldStatus, DepositStatus newStatus, String reason) {
        try {
            // Update deposit status
            deposit.status = newStatus
            deposit.statusChangeDate = new Date()
            deposit.save(flush: true, failOnError: true)
            
            // Create status change transaction
            def txnResult = createStatusChangeTransaction(deposit, oldStatus, newStatus, reason)
            
            // Log the status change
            AuditLogService.insert('080', 'DEP00622', 
                "Status changed for account ${deposit.acctNo} from ${oldStatus.description} to ${newStatus.description}", 
                'deposit', null, null, null, deposit.id)
            
            return [success: true, transactionId: txnResult.transactionId, message: "Status changed successfully"]
            
        } catch (Exception e) {
            log.error("Error processing status change: ${e.message}", e)
            return [success: false, message: "Status change failed: ${e.message}"]
        }
    }

    /**
     * Validate account closure
     * @param deposit The deposit account
     * @return Validation result
     */
    private Map validateAccountClosure(Deposit deposit) {
        try {
            // Check if account has balance
            if (deposit.ledgerBalAmt != 0) {
                return [canClose: false, reason: "Account has non-zero balance"]
            }
            
            // Check for pending transactions
            def pendingTxns = TxnFile.countByDepAcctAndStatus(deposit, ConfigItemStatus.read(1))
            if (pendingTxns > 0) {
                return [canClose: false, reason: "Account has pending transactions"]
            }
            
            // Check for active holds
            if (deposit.holdBalAmt > 0) {
                return [canClose: false, reason: "Account has active holds"]
            }
            
            // Check for uncleared checks
            if (deposit.unclearedCheckBalAmt > 0) {
                return [canClose: false, reason: "Account has uncleared checks"]
            }
            
            return [canClose: true, reason: "Account can be closed"]
            
        } catch (Exception e) {
            log.error("Error validating account closure: ${e.message}", e)
            return [canClose: false, reason: "Validation failed: ${e.message}"]
        }
    }

    /**
     * Process account closure
     * @param deposit The deposit account
     * @param closureReason The reason for closure
     * @return Closure result
     */
    private Map processAccountClosure(Deposit deposit, String closureReason) {
        try {
            def finalBalance = deposit.ledgerBalAmt
            
            // Update deposit status
            deposit.status = DepositStatus.read(7) // Closed
            deposit.statusChangeDate = new Date()
            deposit.closureDate = new Date()
            deposit.closureReason = closureReason
            deposit.save(flush: true, failOnError: true)
            
            // Create closure transaction
            def txn = new TxnFile(
                acctNo: deposit.acctNo,
                branch: deposit.branch,
                currency: deposit.product.currency,
                depAcct: deposit,
                status: ConfigItemStatus.get(2),
                txnAmt: finalBalance.abs(),
                txnCode: 'CLOSE',
                txnDate: deposit.branch.runDate,
                txnDescription: 'Account Closure',
                txnParticulars: closureReason ?: 'Account closed',
                txnRef: "CLOSE-${deposit.id}",
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: TxnType.findByCode('MEMO'),
                txnTemplate: TxnTemplate.findByCode('CLOSE')
            )
            txn.save(flush: true, failOnError: true)
            
            return [success: true, finalBalance: finalBalance, transactionId: txn.id, message: "Account closed successfully"]
            
        } catch (Exception e) {
            log.error("Error processing account closure: ${e.message}", e)
            return [success: false, message: "Account closure failed: ${e.message}"]
        }
    }

    /**
     * Create status change transaction
     * @param deposit The deposit account
     * @param oldStatus The old status
     * @param newStatus The new status
     * @param reason The reason for change
     * @return Transaction result
     */
    private Map createStatusChangeTransaction(Deposit deposit, DepositStatus oldStatus, DepositStatus newStatus, String reason) {
        try {
            def txn = new TxnFile(
                acctNo: deposit.acctNo,
                branch: deposit.branch,
                currency: deposit.product.currency,
                depAcct: deposit,
                status: ConfigItemStatus.get(2),
                txnAmt: 0.0,
                txnCode: 'STCHG',
                txnDate: deposit.branch.runDate,
                txnDescription: "Status Change: ${oldStatus.description} to ${newStatus.description}",
                txnParticulars: reason ?: 'Status change',
                txnRef: "STCHG-${deposit.id}-${newStatus.id}",
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: TxnType.findByCode('MEMO'),
                txnTemplate: TxnTemplate.findByCode('STCHG')
            )
            txn.save(flush: true, failOnError: true)
            
            return [success: true, transactionId: txn.id]
            
        } catch (Exception e) {
            log.error("Error creating status change transaction: ${e.message}", e)
            return [success: false, message: "Transaction creation failed: ${e.message}"]
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Deposit account not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
