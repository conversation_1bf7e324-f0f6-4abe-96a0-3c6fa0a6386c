package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql
import org.icbs.deposit.Memo
import org.icbs.lov.MemoType
import org.icbs.lov.MemoStatus

/**
 * DepositMemoController - Handles deposit memo operations
 * 
 * This controller manages memo operations including:
 * - Memo creation and management
 * - Memo posting and reversal
 * - Memo inquiry and tracking
 * - Memo type management
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositMemoController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * View memo management page
     * @param id The deposit account ID
     */
    def viewMemo() {
        try {
            log.info("Viewing memos for deposit ID: ${params.id}")
            
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                if (depositInstance.branch == UserMaster.get(session.user_id).branch) {
                    render(view:'memo/view', model:[depositInstance:depositInstance])
                } else {
                    flash.message = 'Memo functions not allowed for other branch account'
                    render(view:"inquiry/depositInquiry", model:[depositInstance:depositInstance])
                }
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error viewing memos: ${e.message}", e)
            flash.message = "Error loading memo page |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Show memo form via AJAX
     * @return JSON response with memo grid
     */
    def showMemoFormAjax() {
        try {
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                render(template:"memo/viewGrid", model:[depositInstance:depositInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error showing memo form: ${e.message}", e)
            render([error: "Unable to load memo form"] as JSON)
        }
    }

    /**
     * Create new memo via AJAX
     * @return JSON response with create form
     */
    def createMemoAjax() {
        try {
            log.info("Creating memo with params: ${params}")
            
            if(params.id) {
                def memoInstance = new Memo()
                memoInstance.deposit = Deposit.read(params.id)
                memoInstance.createdBy = UserMaster.get(session.user_id)
                memoInstance.createdDate = new Date()
                
                if(!memoInstance.deposit) {
                    notFound()
                    return
                }
                
                render(template:'memo/create', model:[memoInstance:memoInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error creating memo: ${e.message}", e)
            render([error: "Unable to create memo"] as JSON)
        }
    }

    /**
     * Edit memo via AJAX
     * @return JSON response with edit form
     */
    def editMemoAjax() {
        try {
            if(params.id) {
                def memoInstance = Memo.read(params.id)
                if(!memoInstance) {
                    notFound()
                    return
                }
                
                render(template:'memo/edit', model:[memoInstance:memoInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error editing memo: ${e.message}", e)
            render([error: "Unable to edit memo"] as JSON)
        }
    }

    /**
     * Save new memo via AJAX
     * @return JSON response with save result
     */
    @Transactional
    def saveMemoAjax() {
        try {
            log.info("Saving memo with params: ${params}")
            
            def memoInstance = new Memo()
            bindData(memoInstance, params)
            
            if (!memoInstance.validate()) {
                render(template:'memo/create', model:[memoInstance:memoInstance]) as JSON
                return
            }
            
            // Validate memo business rules
            if (!validateMemo(memoInstance)) {
                render(template:'memo/create', model:[memoInstance:memoInstance]) as JSON
                return
            }
            
            memoInstance.status = MemoStatus.read(1) // Pending
            memoInstance.createdBy = UserMaster.get(session.user_id)
            memoInstance.createdDate = new Date()
            memoInstance.save(flush: true, failOnError: true)
            
            // Log the memo creation
            AuditLogService.insert('080', 'DEP00550', 
                "Memo created for account ${memoInstance.deposit.acctNo} - Type: ${memoInstance.memoType?.description}", 
                'deposit', null, null, null, memoInstance.deposit.id)
            
            flash.message = "Memo Successfully Created"
            render(template:'memo/create', 
                model:[memoInstance:new Memo(deposit:memoInstance.deposit)]) as JSON
            
        } catch (Exception e) {
            log.error("Error saving memo: ${e.message}", e)
            render([error: "Unable to save memo: ${e.message}"] as JSON)
        }
    }

    /**
     * Update memo via AJAX
     * @return JSON response with update result
     */
    @Transactional
    def updateMemoAjax() {
        try {
            def memoInstance = Memo.get(params.id)
            if (!memoInstance) {
                notFound()
                return
            }
            
            // Only allow updates if memo is pending
            if (memoInstance.status.id != 1) {
                flash.message = "Cannot update memo that is not pending"
                render(template:'memo/edit', model:[memoInstance:memoInstance]) as JSON
                return
            }
            
            bindData(memoInstance, params)
            
            if (!memoInstance.validate()) {
                render(template:'memo/edit', model:[memoInstance:memoInstance]) as JSON
                return
            }
            
            // Validate updated memo
            if (!validateMemo(memoInstance)) {
                render(template:'memo/edit', model:[memoInstance:memoInstance]) as JSON
                return
            }
            
            memoInstance.save(flush: true, failOnError: true)
            
            // Log the memo update
            AuditLogService.insert('080', 'DEP00551', 
                "Memo updated for account ${memoInstance.deposit.acctNo} - Type: ${memoInstance.memoType?.description}", 
                'deposit', null, null, null, memoInstance.deposit.id)
            
            flash.message = "Memo Successfully Updated"
            render(template:'memo/edit', model:[memoInstance:memoInstance]) as JSON
            
        } catch (Exception e) {
            log.error("Error updating memo: ${e.message}", e)
            render([error: "Unable to update memo: ${e.message}"] as JSON)
        }
    }

    /**
     * Post memo
     */
    @Transactional
    def postMemo() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Memo ID is required"
                ] as JSON)
                return
            }
            
            def memoInstance = Memo.get(params.id)
            if (!memoInstance) {
                render([
                    success: false,
                    error: "Memo not found"
                ] as JSON)
                return
            }
            
            if (memoInstance.status.id != 1) {
                render([
                    success: false,
                    error: "Memo is not pending"
                ] as JSON)
                return
            }
            
            // Post the memo
            def postResult = postMemoTransaction(memoInstance)
            
            if (postResult.success) {
                // Update memo status
                memoInstance.status = MemoStatus.read(2) // Posted
                memoInstance.postedBy = UserMaster.get(session.user_id)
                memoInstance.postedDate = new Date()
                memoInstance.transactionId = postResult.transactionId
                memoInstance.save(flush: true, failOnError: true)
                
                // Log the memo posting
                AuditLogService.insert('080', 'DEP00552', 
                    "Memo posted for account ${memoInstance.deposit.acctNo} - Amount: ${memoInstance.amount}", 
                    'deposit', null, null, null, memoInstance.deposit.id)
                
                render([
                    success: true,
                    message: "Memo posted successfully",
                    transactionId: postResult.transactionId
                ] as JSON)
            } else {
                render([
                    success: false,
                    error: postResult.message
                ] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error posting memo: ${e.message}", e)
            render([
                success: false,
                error: "Unable to post memo: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Reverse memo
     */
    @Transactional
    def reverseMemo() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Memo ID is required"
                ] as JSON)
                return
            }
            
            def memoInstance = Memo.get(params.id)
            if (!memoInstance) {
                render([
                    success: false,
                    error: "Memo not found"
                ] as JSON)
                return
            }
            
            if (memoInstance.status.id != 2) {
                render([
                    success: false,
                    error: "Memo is not posted"
                ] as JSON)
                return
            }
            
            // Reverse the memo
            def reverseResult = reverseMemoTransaction(memoInstance)
            
            if (reverseResult.success) {
                // Update memo status
                memoInstance.status = MemoStatus.read(4) // Reversed
                memoInstance.reversedBy = UserMaster.get(session.user_id)
                memoInstance.reversedDate = new Date()
                memoInstance.reversalReason = params.reversalReason ?: 'Manual Reversal'
                memoInstance.reversalTransactionId = reverseResult.transactionId
                memoInstance.save(flush: true, failOnError: true)
                
                // Log the memo reversal
                AuditLogService.insert('080', 'DEP00553', 
                    "Memo reversed for account ${memoInstance.deposit.acctNo} - Amount: ${memoInstance.amount}", 
                    'deposit', null, null, null, memoInstance.deposit.id)
                
                render([
                    success: true,
                    message: "Memo reversed successfully",
                    transactionId: reverseResult.transactionId
                ] as JSON)
            } else {
                render([
                    success: false,
                    error: reverseResult.message
                ] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error reversing memo: ${e.message}", e)
            render([
                success: false,
                error: "Unable to reverse memo: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get memo information
     * @return JSON response with memo details
     */
    def getMemoInfo() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Memo ID is required"
                ] as JSON)
                return
            }
            
            def memoInstance = Memo.get(params.id)
            if (!memoInstance) {
                render([
                    success: false,
                    error: "Memo not found"
                ] as JSON)
                return
            }
            
            def memoInfo = [
                id: memoInstance.id,
                amount: memoInstance.amount,
                memoType: memoInstance.memoType?.description,
                status: memoInstance.status?.description,
                description: memoInstance.description,
                createdDate: memoInstance.createdDate?.format('yyyy-MM-dd'),
                createdBy: memoInstance.createdBy?.username,
                postedDate: memoInstance.postedDate?.format('yyyy-MM-dd'),
                postedBy: memoInstance.postedBy?.username,
                reversedDate: memoInstance.reversedDate?.format('yyyy-MM-dd'),
                reversedBy: memoInstance.reversedBy?.username,
                reversalReason: memoInstance.reversalReason,
                transactionId: memoInstance.transactionId,
                reversalTransactionId: memoInstance.reversalTransactionId,
                accountNo: memoInstance.deposit?.acctNo,
                customerName: memoInstance.deposit?.customer?.displayName
            ]
            
            render([
                success: true,
                memoInfo: memoInfo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting memo info: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get memo information: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get memos for account
     * @return JSON response with account memos
     */
    def getAccountMemos() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def memos = Memo.findAllByDeposit(deposit, [sort: 'createdDate', order: 'desc'])
            
            def memoData = memos.collect { memo ->
                [
                    id: memo.id,
                    amount: memo.amount,
                    memoType: memo.memoType?.description,
                    status: memo.status?.description,
                    description: memo.description,
                    createdDate: memo.createdDate?.format('yyyy-MM-dd'),
                    createdBy: memo.createdBy?.username,
                    postedDate: memo.postedDate?.format('yyyy-MM-dd'),
                    isPending: memo.status?.id == 1,
                    isPosted: memo.status?.id == 2,
                    canPost: memo.status?.id == 1,
                    canReverse: memo.status?.id == 2
                ]
            }
            
            def pendingCount = memos.count { it.status?.id == 1 }
            def postedCount = memos.count { it.status?.id == 2 }
            
            render([
                success: true,
                memos: memoData,
                pendingCount: pendingCount,
                postedCount: postedCount,
                totalCount: memos.size(),
                accountNo: deposit.acctNo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting account memos: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get account memos: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get memo types
     * @return JSON response with memo types
     */
    def getMemoTypes() {
        try {
            def memoTypes = MemoType.list()
            
            def memoTypeData = memoTypes.collect { memoType ->
                [
                    id: memoType.id,
                    code: memoType.code,
                    description: memoType.description,
                    isDebit: memoType.isDebit,
                    isActive: memoType.status?.id == 2
                ]
            }
            
            render([
                success: true,
                memoTypes: memoTypeData
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting memo types: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get memo types: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate memo business rules
     * @param memo The memo to validate
     * @return true if valid
     */
    private boolean validateMemo(Memo memo) {
        try {
            // Validate amount
            if (memo.amount <= 0) {
                memo.errors.rejectValue('amount', 'memo.amount.invalid')
                return false
            }
            
            // Check available balance for debit memos
            if (memo.memoType?.isDebit && memo.amount > memo.deposit.availableBalAmt) {
                memo.errors.rejectValue('amount', 'memo.amount.exceeds.balance')
                return false
            }
            
            return true
            
        } catch (Exception e) {
            log.error("Error validating memo: ${e.message}", e)
            return false
        }
    }

    /**
     * Post memo transaction
     * @param memo The memo to post
     * @return Post result
     */
    private Map postMemoTransaction(Memo memo) {
        try {
            def txnType = memo.memoType?.isDebit ? TxnType.findByCode('DR') : TxnType.findByCode('CR')
            
            def txn = new TxnFile(
                acctNo: memo.deposit.acctNo,
                branch: memo.deposit.branch,
                currency: memo.deposit.product.currency,
                depAcct: memo.deposit,
                status: ConfigItemStatus.get(2),
                txnAmt: memo.amount,
                txnCode: 'MEMO',
                txnDate: memo.deposit.branch.runDate,
                txnDescription: "Memo ${memo.memoType?.description}",
                txnParticulars: memo.description,
                txnRef: "MEMO-${memo.id}",
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: txnType,
                txnTemplate: TxnTemplate.findByCode('MEMO')
            )
            txn.save(flush: true, failOnError: true)
            
            // Update account balance
            if (memo.memoType?.isDebit) {
                memo.deposit.ledgerBalAmt -= memo.amount
                memo.deposit.availableBalAmt -= memo.amount
            } else {
                memo.deposit.ledgerBalAmt += memo.amount
                memo.deposit.availableBalAmt += memo.amount
            }
            memo.deposit.save(flush: true, failOnError: true)
            
            // Create GL breakdown
            GlTransactionService.saveTxnBreakdown(txn.id)
            
            return [success: true, transactionId: txn.id, message: "Memo posted successfully"]
            
        } catch (Exception e) {
            log.error("Error posting memo transaction: ${e.message}", e)
            return [success: false, message: "Transaction posting failed: ${e.message}"]
        }
    }

    /**
     * Reverse memo transaction
     * @param memo The memo to reverse
     * @return Reverse result
     */
    private Map reverseMemoTransaction(Memo memo) {
        try {
            // Create reversal transaction with opposite type
            def txnType = memo.memoType?.isDebit ? TxnType.findByCode('CR') : TxnType.findByCode('DR')
            
            def txn = new TxnFile(
                acctNo: memo.deposit.acctNo,
                branch: memo.deposit.branch,
                currency: memo.deposit.product.currency,
                depAcct: memo.deposit,
                status: ConfigItemStatus.get(2),
                txnAmt: memo.amount,
                txnCode: 'MEMO',
                txnDate: memo.deposit.branch.runDate,
                txnDescription: "Memo Reversal ${memo.memoType?.description}",
                txnParticulars: "Reversal of Memo ${memo.id}",
                txnRef: "MEMO-REV-${memo.id}",
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: txnType,
                txnTemplate: TxnTemplate.findByCode('MEMO')
            )
            txn.save(flush: true, failOnError: true)
            
            // Reverse account balance changes
            if (memo.memoType?.isDebit) {
                memo.deposit.ledgerBalAmt += memo.amount
                memo.deposit.availableBalAmt += memo.amount
            } else {
                memo.deposit.ledgerBalAmt -= memo.amount
                memo.deposit.availableBalAmt -= memo.amount
            }
            memo.deposit.save(flush: true, failOnError: true)
            
            // Create GL breakdown
            GlTransactionService.saveTxnBreakdown(txn.id)
            
            return [success: true, transactionId: txn.id, message: "Memo reversed successfully"]
            
        } catch (Exception e) {
            log.error("Error reversing memo transaction: ${e.message}", e)
            return [success: false, message: "Transaction reversal failed: ${e.message}"]
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Memo not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
