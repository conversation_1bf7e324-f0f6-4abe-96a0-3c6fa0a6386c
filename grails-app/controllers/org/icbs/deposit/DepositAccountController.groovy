package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql

/**
 * DepositAccountController - Handles deposit account creation and management
 * 
 * This controller manages deposit account operations including:
 * - Deposit account creation and setup
 * - Account editing and updates
 * - Account status management
 * - Account validation and business rules
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositAccountController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * Display deposit account creation form
     * @param cmd Deposit initial command object
     */
    def create(depositInitialCommand cmd) {
        try {
            log.info("Creating deposit account with params: ${params}")
            
            if(params.customerFromCIFPage != null) {
                cmd.customer = Customer.read(params.customerFromCIFPage)
            } else {
                if(params.customer?.id) {
                    cmd.customer = Customer.read(params.customer.id)
                }
            }
            
            if(params.firstCreate != null) {
                if(cmd.hasErrors()) {
                    render(view:'create', model:['depositInstance': cmd, 'firstCreate':true])
                } else {
                    render(view:'create', model:['depositInstance': new Deposit(params)])
                }
            } else {
                cmd.clearErrors()
                render(view:'create', model:['depositInstance': cmd, 'firstCreate':true])
            }
            
        } catch (Exception e) {
            log.error("Error creating deposit account: ${e.message}", e)
            flash.message = "Error creating deposit account |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Save new deposit account
     */
    @Transactional
    def save() {
        try {
            log.info("Saving deposit account with params: ${params}")
            
            def depositInstance = new Deposit()
            bindData(depositInstance, params)
            
            if (depositInstance == null) {
                notFound()
                return
            }
            
            // Clean up signatories
            if(depositInstance?.signatories) {
                depositInstance.signatories.removeAll([null])
            }
            
            // Validate deposit instance
            if (!depositInstance.validate()) {
                respond depositInstance.errors, view:'create'
                return
            }
            
            // Set interest rate for CA accounts
            if (depositInstance?.type?.id == 2 && depositInstance?.depositInterestScheme.isGraduated == false) {
                depositInstance?.interestRate = depositInstance?.depositInterestScheme.interestRate
            }
            
            // Validate interest rate for TD accounts
            if (depositInstance?.type?.id == 3 && depositInstance?.depositInterestScheme.isGraduated == false) {
                if (!depositInstance?.interestRate) {
                    flash.message = 'Interest Rate cannot be null'
                    respond depositInstance.errors, view:'create'
                    return
                }
            }
            
            // Check min/max rates for changeable rate schemes
            if (depositInstance?.depositInterestScheme?.canChangeInterestRate == true) {
                if (depositInstance?.interestRate > depositInstance?.depositInterestScheme?.maxInterestRate) {
                    flash.message = 'Interest Rate cannot be greater than max interest rate'
                    respond depositInstance.errors, view:'create'
                    return
                }
                if (depositInstance?.interestRate < depositInstance?.depositInterestScheme?.minInterestRate) {
                    flash.message = 'Interest Rate cannot be less than min interest rate'
                    respond depositInstance.errors, view:'create'
                    return
                }
            }
            
            // Handle rollover for TD accounts
            def currentRollover = depositInstance.currentRollover
            depositInstance.currentRollover = null
            depositInstance.branch = UserMaster.get(session.user_id)?.branch
            depositInstance.createdBy = session.user_id
            depositInstance.save(flush:true)
            
            if(depositInstance?.type?.id == 3) {
                depositInstance.maturityDate = currentRollover.endDate
                depositInstance.save(flush:true)
                
                if (depositInstance.depositInterestScheme.fdMonthlyTransfer == true) {
                    def nextDate = depositService.getNextDate(currentRollover.startDate, currentRollover.startDate, depositInstance.maturityDate)
                    currentRollover.endDate = nextDate
                }
                
                depositInstance.addToRollovers(currentRollover)
                depositInstance.currentRollover = currentRollover
            }
            
            // Generate account number
            depositService.buildAcctNo(depositInstance)
            
            // Log the creation
            AuditLogService.insert('080', 'DEP00200', "saveDeposit ${depositInstance.id}", 'deposit', null, null, null, depositInstance.id)
            
            flash.message = "Account created successfully |success|alert"
            redirect(action: "depositInquiry", id: depositInstance.id)
            
        } catch (Exception e) {
            log.error("Error saving deposit account: ${e.message}", e)
            flash.message = "Error saving deposit account: ${e.message} |error|alert"
            redirect(action: "create")
        }
    }

    /**
     * Display deposit account for editing
     * @param depositInstance The deposit instance to edit
     */
    def edit(Deposit depositInstance) {
        try {
            def module = Module.findByCode("DEP00300")
            if (RoleModuleService.hasPermission(module) == false) {
                redirect(action:"index")
                return
            }
            
            respond depositInstance
            
        } catch (Exception e) {
            log.error("Error loading deposit for edit: ${e.message}", e)
            flash.message = "Error loading deposit account |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Update deposit account
     * @param depositInstance The deposit instance to update
     */
    @Transactional
    def update(Deposit depositInstance) {
        try {
            if (depositInstance == null) {
                notFound()
                return
            }
            
            log.info("Updating deposit account ${depositInstance.id} with params: ${params}")
            
            // Handle signatories
            if(depositInstance.signatories) {
                depositInstance.signatories.removeAll([null])
                def signatoryDelete = depositInstance.signatories.findAll{(it.deleted)}
                
                if(signatoryDelete) {
                    for (Signatory signatory : signatoryDelete) {
                        signatory?.status = ConfigItemStatus.get(3)
                    }
                }
            }
            
            if (!depositInstance.validate()) {
                respond depositInstance.errors, view:'edit'
                return
            }
            
            // Handle TD account updates
            if (depositInstance.type.id == 3) {
                if (depositInstance.depositInterestScheme.fdMonthlyTransfer == true) {
                    params.rollOverEndDate = params.rollOverEndDate ? new Date().parse("MM/dd/yyyy", params.rollOverEndDate) : null
                    depositInstance.currentRollover.endDate = params.rollOverEndDate
                    
                    if (depositInstance.currentRollover.endDate <= depositInstance.branch.runDate) {
                        flash.message = 'Invalid Rollover End Date |error|alert'
                        respond depositInstance, view:'edit'
                        return
                    }
                    
                    params.maturityDate = params.maturityDate ? new Date().parse("MM/dd/yyyy", params.maturityDate) : null
                    depositInstance.maturityDate = params.maturityDate
                    
                    if (depositInstance.maturityDate <= depositInstance.branch.runDate) {
                        flash.message = 'Invalid new maturity date |error|alert'
                        respond depositInstance, view:'edit'
                        return
                    }
                    
                    depositInstance.currentRollover.save(flush: true, failOnError: true)
                    depositInstance.save(flush: true, failOnError: true)
                } else {
                    params.maturityDate = params.maturityDate ? new Date().parse("MM/dd/yyyy", params.maturityDate) : null
                    depositInstance.maturityDate = params.maturityDate
                    
                    if (depositInstance.maturityDate <= depositInstance.branch.runDate) {
                        flash.message = 'Invalid new maturity date |error|alert'
                        respond depositInstance, view:'edit'
                        return
                    }
                    
                    depositInstance.currentRollover.endDate = depositInstance.maturityDate
                    depositInstance.currentRollover.save(flush: true, failOnError: true)
                    depositInstance.save(flush: true, failOnError: true)
                }
            }
            
            // Handle status changes
            if (depositInstance.isDirty('status')) {
                if (!validateStatusChange(depositInstance)) {
                    return
                }
                
                processStatusChange(depositInstance)
            }
            
            // Handle GL link changes
            if(params.oldGlinkId.toString() != params.glLink.id.toString()) {
                processGLLinkChange(depositInstance)
            }
            
            depositInstance.save(flush: true, failOnError: true)
            
            // Log the update
            AuditLogService.insert('080', 'DEP00300', "updateDeposit ${depositInstance.id}", 'deposit', null, null, null, depositInstance.id)
            
            flash.message = "Account updated successfully |success|alert"
            respond depositInstance, view:'edit'
            
        } catch (Exception e) {
            log.error("Error updating deposit account: ${e.message}", e)
            flash.message = "Error updating deposit account: ${e.message} |error|alert"
            respond depositInstance.errors, view:'edit'
        }
    }

    /**
     * Show deposit account details
     * @param depositInstance The deposit instance to show
     */
    def show(Deposit depositInstance) {
        try {
            respond depositInstance
        } catch (Exception e) {
            log.error("Error showing deposit account: ${e.message}", e)
            flash.message = "Error loading deposit account |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Validate status change rules
     * @param depositInstance The deposit instance
     * @return true if status change is valid
     */
    private boolean validateStatusChange(Deposit depositInstance) {
        try {
            def oldStatus = depositInstance.getPersistentValue('status').id
            def newStatus = depositInstance.status.id
            
            // Define valid status transitions
            def validTransitions = [
                1: [2], // Pending -> Active
                2: [5, 6, 7], // Active -> Dormant, Frozen, Closed
                3: [5, 6], // Re-activated -> Dormant, Frozen
                4: [5, 6], // Re-opened -> Dormant, Frozen
                5: [3, 6, 7], // Dormant -> Re-activated, Frozen, Closed
                6: [2, 3, 4, 5, 7] // Frozen -> Active, Re-activated, Re-opened, Dormant, Closed
            ]
            
            if (!validTransitions[oldStatus]?.contains(newStatus)) {
                flash.message = "Invalid status change from ${DepositStatus.get(oldStatus).description} to ${DepositStatus.get(newStatus).description} |error|alert"
                depositInstance.status = DepositStatus.get(oldStatus)
                respond depositInstance.errors, view:'edit'
                return false
            }
            
            return true
            
        } catch (Exception e) {
            log.error("Error validating status change: ${e.message}", e)
            return false
        }
    }

    /**
     * Process status change transactions
     * @param depositInstance The deposit instance
     */
    private void processStatusChange(Deposit depositInstance) {
        try {
            def currentDate = Branch.get(1).runDate
            
            // Handle dormant status changes
            if (depositInstance.getPersistentValue('status').id == 5 && depositInstance.status.id == 3) {
                createStatusChangeTransaction(depositInstance, 'DEP.40080', 'Reactivate Dormant Account', 'Transfer to Re-activated status')
            }
            
            if (depositInstance.status.id == 5) {
                createStatusChangeTransaction(depositInstance, 'DEP.40080', 'Transfer to Dormant Account', 'Transfer to Dormant status')
            }
            
            depositInstance.statusChangeDate = currentDate
            
        } catch (Exception e) {
            log.error("Error processing status change: ${e.message}", e)
            throw e
        }
    }

    /**
     * Create status change transaction
     * @param depositInstance The deposit instance
     * @param paramCode The parameter code for transaction template
     * @param description Transaction description
     * @param particulars Transaction particulars
     */
    private void createStatusChangeTransaction(Deposit depositInstance, String paramCode, String description, String particulars) {
        try {
            def txnTemplateId = Institution.findByParamCode(paramCode).paramValue.toInteger()
            def txnTemplate = TxnTemplate.get(txnTemplateId)
            
            def txn = new TxnFile(
                acctNo: depositInstance.acctNo,
                branch: depositInstance.branch,
                currency: depositInstance.product.currency,
                depAcct: depositInstance,
                status: ConfigItemStatus.get(2),
                txnAmt: depositInstance.ledgerBalAmt,
                txnCode: txnTemplate.code,
                txnDate: depositInstance.branch.runDate,
                txnDescription: description,
                txnParticulars: particulars,
                txnRef: "${depositInstance.branch.runDate} - Status Change",
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: txnTemplate.txnType,
                txnTemplate: txnTemplate
            )
            txn.save(flush: true, failOnError: true)
            
            // Create ledger entry
            def ledger = new TxnDepositAcctLedger(
                acct: depositInstance,
                acctNo: depositInstance.acctNo,
                bal: depositInstance.ledgerBalAmt,
                branch: depositInstance.branch,
                creditAmt: depositInstance.ledgerBalAmt,
                debitAmt: depositInstance.ledgerBalAmt,
                currency: depositInstance.product.currency,
                status: depositInstance.status,
                txnDate: txn.txnDate,
                txnFile: txn,
                user: UserMaster.get(session.user_id),
                txnRef: txn.txnRef,
                txnType: txn.txnType
            )
            ledger.save(flush: true, failOnError: true)
            
            // Create GL breakdown
            GlTransactionService.saveTxnBreakdown(txn.id)
            
        } catch (Exception e) {
            log.error("Error creating status change transaction: ${e.message}", e)
            throw e
        }
    }

    /**
     * Process GL link change
     * @param depositInstance The deposit instance
     */
    private void processGLLinkChange(Deposit depositInstance) {
        try {
            def glLinkTxnId = Institution.findByParamCode('GEN.10241').paramValue.toInteger()
            def txnTemplate = TxnTemplate.get(glLinkTxnId)
            
            def txn = new TxnFile(
                acctNo: depositInstance.acctNo,
                branch: depositInstance.branch,
                currency: depositInstance.product.currency,
                depAcct: depositInstance,
                status: ConfigItemStatus.get(2),
                txnAmt: depositInstance.ledgerBalAmt,
                txnCode: txnTemplate.code,
                txnDate: depositInstance.branch.runDate,
                txnDescription: 'Deposit Account Re-Classification',
                txnParticulars: params.glLink.id.toInteger(),
                txnRef: params.oldGlinkId.toInteger(),
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: txnTemplate.txnType,
                txnTemplate: txnTemplate
            )
            txn.save(flush: true, failOnError: true)
            
            // Create ledger entry
            def ledger = new TxnDepositAcctLedger(
                acct: depositInstance,
                acctNo: depositInstance.acctNo,
                bal: depositInstance.ledgerBalAmt,
                branch: depositInstance.branch,
                creditAmt: depositInstance.ledgerBalAmt,
                debitAmt: depositInstance.ledgerBalAmt,
                currency: depositInstance.product.currency,
                status: depositInstance.status,
                txnDate: txn.txnDate,
                txnFile: txn,
                user: UserMaster.get(session.user_id),
                txnRef: txn.txnRef,
                txnType: txn.txnType
            )
            ledger.save(flush: true, failOnError: true)
            
            // Create GL breakdown
            GlTransactionService.saveTxnBreakdown(txn.id)
            
        } catch (Exception e) {
            log.error("Error processing GL link change: ${e.message}", e)
            throw e
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Deposit account not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
