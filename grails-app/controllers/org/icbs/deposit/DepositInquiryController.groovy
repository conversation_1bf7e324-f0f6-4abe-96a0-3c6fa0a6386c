package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql
import org.icbs.tellering.TxnCOCI

/**
 * DepositInquiryController - Handles deposit account inquiry and search operations
 * 
 * This controller manages deposit inquiry operations including:
 * - Deposit account search and lookup
 * - Account information display
 * - Transaction history viewing
 * - Account balance inquiries
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositInquiryController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * Display deposit inquiry page
     * @param depositInstance The deposit instance to display
     */
    def depositInquiry(Deposit depositInstance) {
        try {
            if (depositInstance) {
                // Update passbook number if available
                updatePassbookNumber(depositInstance)
                
                respond depositInstance
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error in deposit inquiry: ${e.message}", e)
            flash.message = "Error loading deposit inquiry |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Print deposit inquiry report
     */
    def printDepositInquiry() {
        try {
            if(params.id) {
                def depositInstance = Deposit.get(params.id)
                respond depositInstance
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error printing deposit inquiry: ${e.message}", e)
            flash.message = "Error printing deposit inquiry |error|alert"
            redirect(action: "depositInquiry", id: params.id)
        }
    }

    /**
     * Display deposit view more information page
     */
    def depositViewMoreInformation() {
        try {
            def module = Module.findByCode("DEP00401")
            if (RoleModuleService.hasPermission(module) == false) {
                redirect(action:"index")
                return
            }
            
            if(params.id) {
                def depositInstance = Deposit.get(params.id)
                
                // Update passbook number
                updatePassbookNumber(depositInstance)
                
                respond depositInstance
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error in deposit view more information: ${e.message}", e)
            flash.message = "Error loading deposit information |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * View uncleared deposit details
     */
    def viewUnclearedDeposit() {
        try {
            if(params.id) {
                def txnCociInstanceId = TxnCOCI.get(params.id)
                log.debug("TxnCOCI Instance: ${txnCociInstanceId}")
                
                render(view: "viewUnclearedDeposit", model: [txnCociInstanceId: txnCociInstanceId])
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error viewing uncleared deposit: ${e.message}", e)
            flash.message = "Error loading uncleared deposit |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * View transaction details
     * @param txnFileInstance The transaction file instance
     */
    def viewTxnDetails(TxnFile txnFileInstance) {
        try {
            if (txnFileInstance) {
                def glEntries = TxnBreakdown.findAllByTxnFile(txnFileInstance)
                render(view: '/deposit/viewTxnDetails.gsp', model: [
                    glEntries: glEntries,
                    txnFileInstance: txnFileInstance
                ])
            } else {
                render(view: '/txnLog/index')
            }
            
        } catch (Exception e) {
            log.error("Error viewing transaction details: ${e.message}", e)
            flash.message = "Error loading transaction details |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Search deposits by criteria
     * @return JSON response with search results
     */
    def searchDeposits() {
        try {
            def searchCriteria = [:]
            
            if (params.accountNo) {
                searchCriteria.acctNo = params.accountNo
            }
            if (params.customerId) {
                searchCriteria.customer = Customer.get(params.customerId)
            }
            if (params.branchId) {
                searchCriteria.branch = Branch.get(params.branchId)
            }
            if (params.statusId) {
                searchCriteria.status = DepositStatus.get(params.statusId)
            }
            if (params.typeId) {
                searchCriteria.type = DepositType.get(params.typeId)
            }
            
            def deposits = Deposit.createCriteria().list(params) {
                searchCriteria.each { key, value ->
                    if (value != null) {
                        eq(key, value)
                    }
                }
                order("acctNo", "asc")
            }
            
            def searchResults = deposits.collect { deposit ->
                [
                    id: deposit.id,
                    acctNo: deposit.acctNo,
                    customerName: deposit.customer?.displayName,
                    branchName: deposit.branch?.name,
                    status: deposit.status?.description,
                    type: deposit.type?.description,
                    ledgerBalance: deposit.ledgerBalAmt,
                    availableBalance: deposit.availableBalAmt,
                    dateOpened: deposit.dateOpened?.format('yyyy-MM-dd')
                ]
            }
            
            render([
                success: true,
                deposits: searchResults,
                totalCount: deposits.totalCount
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error searching deposits: ${e.message}", e)
            render([
                success: false,
                error: "Search failed: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get deposit account summary
     * @return JSON response with account summary
     */
    def getAccountSummary() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Account ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Account not found"
                ] as JSON)
                return
            }
            
            def summary = [
                accountNo: deposit.acctNo,
                customerName: deposit.customer?.displayName,
                accountType: deposit.type?.description,
                status: deposit.status?.description,
                branch: deposit.branch?.name,
                currency: deposit.product?.currency?.code,
                ledgerBalance: deposit.ledgerBalAmt,
                availableBalance: deposit.availableBalAmt,
                holdBalance: deposit.holdBalAmt,
                unclearedBalance: deposit.unclearedCheckBalAmt,
                interestRate: deposit.interestRate,
                dateOpened: deposit.dateOpened?.format('yyyy-MM-dd'),
                lastTransactionDate: deposit.lastTxnDate?.format('yyyy-MM-dd'),
                maturityDate: deposit.maturityDate?.format('yyyy-MM-dd')
            ]
            
            render([
                success: true,
                summary: summary
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting account summary: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get account summary: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get recent transactions for account
     * @return JSON response with recent transactions
     */
    def getRecentTransactions() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Account ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Account not found"
                ] as JSON)
                return
            }
            
            def maxResults = params.max ? params.max.toInteger() : 10
            
            def transactions = TxnFile.createCriteria().list(max: maxResults) {
                eq("depAcct", deposit)
                order("txnDate", "desc")
                order("txnTimestamp", "desc")
            }
            
            def transactionData = transactions.collect { txn ->
                [
                    id: txn.id,
                    txnDate: txn.txnDate?.format('yyyy-MM-dd'),
                    txnCode: txn.txnCode,
                    description: txn.txnDescription,
                    amount: txn.txnAmt,
                    type: txn.txnType?.description,
                    reference: txn.txnRef,
                    balance: getTransactionBalance(txn)
                ]
            }
            
            render([
                success: true,
                transactions: transactionData,
                accountNo: deposit.acctNo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting recent transactions: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get recent transactions: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get account balance history
     * @return JSON response with balance history
     */
    def getBalanceHistory() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Account ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Account not found"
                ] as JSON)
                return
            }
            
            def startDate = params.startDate ? Date.parse("yyyy-MM-dd", params.startDate) : new Date() - 30
            def endDate = params.endDate ? Date.parse("yyyy-MM-dd", params.endDate) : new Date()
            
            def ledgerEntries = TxnDepositAcctLedger.createCriteria().list {
                eq("acct", deposit)
                between("txnDate", startDate, endDate)
                order("txnDate", "asc")
            }
            
            def balanceHistory = ledgerEntries.collect { entry ->
                [
                    date: entry.txnDate?.format('yyyy-MM-dd'),
                    balance: entry.bal,
                    debitAmount: entry.debitAmt,
                    creditAmount: entry.creditAmt,
                    reference: entry.txnRef
                ]
            }
            
            render([
                success: true,
                balanceHistory: balanceHistory,
                accountNo: deposit.acctNo,
                period: [
                    startDate: startDate.format('yyyy-MM-dd'),
                    endDate: endDate.format('yyyy-MM-dd')
                ]
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting balance history: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get balance history: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Update passbook number for deposit instance
     * @param depositInstance The deposit instance to update
     */
    private void updatePassbookNumber(Deposit depositInstance) {
        try {
            def db = new Sql(dataSource)
            def passbookList = db.rows("""
                select (select passbook_no from passbook where issue_passbook_id = A.id) 
                from issue_passbook A 
                where passbooks_idx = (select max(passbooks_idx) from issue_passbook) 
                and deposit_id = ?
            """, [depositInstance.id])
            
            if (passbookList.size() > 0) {
                def asString = passbookList.join(", ")
                def listPassNo = asString.substring((asString.indexOf(':') + 1), (asString.length() - 1))
                BigInteger intListPassNo = new BigInteger(listPassNo)
                depositInstance.passBookNo = intListPassNo
            }
            
        } catch (Exception e) {
            log.error("Error updating passbook number: ${e.message}", e)
        }
    }

    /**
     * Get transaction balance from ledger
     * @param txn The transaction file
     * @return The balance after transaction
     */
    private Double getTransactionBalance(TxnFile txn) {
        try {
            def ledgerEntry = TxnDepositAcctLedger.findByTxnFile(txn)
            return ledgerEntry?.bal ?: 0.0
        } catch (Exception e) {
            log.error("Error getting transaction balance: ${e.message}", e)
            return 0.0
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Deposit account not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
