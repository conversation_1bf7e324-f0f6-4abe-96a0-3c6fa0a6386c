package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql
import org.icbs.deposit.DepositInterestScheme
import org.icbs.deposit.InterestRateHistory

/**
 * DepositInterestController - Handles deposit interest rate operations
 * 
 * This controller manages interest rate operations including:
 * - Interest rate maintenance and updates
 * - Interest calculation and posting
 * - Interest rate history tracking
 * - Interest scheme management
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositInterestController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * View interest rate management page
     * @param id The deposit account ID
     */
    def viewInterestRate() {
        try {
            log.info("Viewing interest rates for deposit ID: ${params.id}")
            
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                if (depositInstance.branch == UserMaster.get(session.user_id).branch) {
                    render(view:'interest/view', model:[depositInstance:depositInstance])
                } else {
                    flash.message = 'Interest rate functions not allowed for other branch account'
                    render(view:"inquiry/depositInquiry", model:[depositInstance:depositInstance])
                }
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error viewing interest rates: ${e.message}", e)
            flash.message = "Error loading interest rate page |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Update interest rate for deposit account
     */
    @Transactional
    def updateInterestRate() {
        try {
            if (!params.id || !params.newInterestRate) {
                render([
                    success: false,
                    error: "Deposit ID and new interest rate are required"
                ] as JSON)
                return
            }
            
            def depositInstance = Deposit.get(params.id)
            if (!depositInstance) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def newRate = params.newInterestRate.toDouble()
            def oldRate = depositInstance.interestRate
            
            // Validate interest rate change
            if (!validateInterestRateChange(depositInstance, newRate)) {
                render([
                    success: false,
                    error: "Interest rate change validation failed"
                ] as JSON)
                return
            }
            
            // Create interest rate history record
            createInterestRateHistory(depositInstance, oldRate, newRate)
            
            // Update deposit interest rate
            depositInstance.interestRate = newRate
            depositInstance.save(flush: true, failOnError: true)
            
            // Log the interest rate change
            AuditLogService.insert('080', 'DEP00570', 
                "Interest rate updated for account ${depositInstance.acctNo} - Old: ${oldRate}%, New: ${newRate}%", 
                'deposit', null, null, null, depositInstance.id)
            
            render([
                success: true,
                message: "Interest rate updated successfully",
                oldRate: oldRate,
                newRate: newRate
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error updating interest rate: ${e.message}", e)
            render([
                success: false,
                error: "Unable to update interest rate: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Calculate interest for deposit account
     */
    def calculateInterest() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def depositInstance = Deposit.get(params.id)
            if (!depositInstance) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def startDate = params.startDate ? Date.parse("yyyy-MM-dd", params.startDate) : null
            def endDate = params.endDate ? Date.parse("yyyy-MM-dd", params.endDate) : new Date()
            
            def interestCalculation = calculateInterestAmount(depositInstance, startDate, endDate)
            
            render([
                success: true,
                interestCalculation: interestCalculation
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error calculating interest: ${e.message}", e)
            render([
                success: false,
                error: "Unable to calculate interest: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Post interest to deposit account
     */
    @Transactional
    def postInterest() {
        try {
            if (!params.id || !params.interestAmount) {
                render([
                    success: false,
                    error: "Deposit ID and interest amount are required"
                ] as JSON)
                return
            }
            
            def depositInstance = Deposit.get(params.id)
            if (!depositInstance) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def interestAmount = params.interestAmount.toDouble()
            
            if (interestAmount <= 0) {
                render([
                    success: false,
                    error: "Interest amount must be greater than zero"
                ] as JSON)
                return
            }
            
            // Create interest posting transaction
            def txnResult = createInterestTransaction(depositInstance, interestAmount)
            
            if (txnResult.success) {
                // Log the interest posting
                AuditLogService.insert('080', 'DEP00571', 
                    "Interest posted for account ${depositInstance.acctNo} - Amount: ${interestAmount}", 
                    'deposit', null, null, null, depositInstance.id)
                
                render([
                    success: true,
                    message: "Interest posted successfully",
                    transactionId: txnResult.transactionId,
                    amount: interestAmount
                ] as JSON)
            } else {
                render([
                    success: false,
                    error: txnResult.message
                ] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error posting interest: ${e.message}", e)
            render([
                success: false,
                error: "Unable to post interest: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get interest rate history for account
     * @return JSON response with interest rate history
     */
    def getInterestRateHistory() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def depositInstance = Deposit.get(params.id)
            if (!depositInstance) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def rateHistory = InterestRateHistory.findAllByDeposit(depositInstance, [sort: 'effectiveDate', order: 'desc'])
            
            def historyData = rateHistory.collect { history ->
                [
                    id: history.id,
                    effectiveDate: history.effectiveDate?.format('yyyy-MM-dd'),
                    oldRate: history.oldRate,
                    newRate: history.newRate,
                    changedBy: history.changedBy?.username,
                    changeReason: history.changeReason,
                    approvedBy: history.approvedBy?.username
                ]
            }
            
            render([
                success: true,
                rateHistory: historyData,
                currentRate: depositInstance.interestRate,
                accountNo: depositInstance.acctNo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting interest rate history: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get interest rate history: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get interest calculation details
     * @return JSON response with calculation breakdown
     */
    def getInterestCalculationDetails() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def depositInstance = Deposit.get(params.id)
            if (!depositInstance) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def calculationDetails = [
                accountNo: depositInstance.acctNo,
                currentBalance: depositInstance.ledgerBalAmt,
                interestRate: depositInstance.interestRate,
                interestScheme: depositInstance.depositInterestScheme?.name,
                paymentMode: depositInstance.depositInterestScheme?.interestPaymentMode?.description,
                lastInterestDate: depositInstance.lastInterestDate?.format('yyyy-MM-dd'),
                accruedInterest: depositInstance.accruedInterest ?: 0.0,
                yearToDateInterest: getYearToDateInterest(depositInstance),
                monthToDateInterest: getMonthToDateInterest(depositInstance)
            ]
            
            render([
                success: true,
                calculationDetails: calculationDetails
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting interest calculation details: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get calculation details: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get available interest schemes
     * @return JSON response with interest schemes
     */
    def getInterestSchemes() {
        try {
            def depositType = params.depositTypeId ? DepositType.get(params.depositTypeId) : null
            
            def schemes = DepositInterestScheme.createCriteria().list {
                eq("status", ConfigItemStatus.read(2)) // Active
                if (depositType) {
                    eq("depositType", depositType)
                }
                order("name", "asc")
            }
            
            def schemeData = schemes.collect { scheme ->
                [
                    id: scheme.id,
                    name: scheme.name,
                    description: scheme.description,
                    interestRate: scheme.interestRate,
                    minInterestRate: scheme.minInterestRate,
                    maxInterestRate: scheme.maxInterestRate,
                    canChangeInterestRate: scheme.canChangeInterestRate,
                    isGraduated: scheme.isGraduated,
                    paymentMode: scheme.interestPaymentMode?.description
                ]
            }
            
            render([
                success: true,
                interestSchemes: schemeData
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting interest schemes: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get interest schemes: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate interest rate change
     * @param deposit The deposit account
     * @param newRate The new interest rate
     * @return true if valid
     */
    private boolean validateInterestRateChange(Deposit deposit, Double newRate) {
        try {
            def scheme = deposit.depositInterestScheme
            
            // Check if rate changes are allowed
            if (!scheme.canChangeInterestRate) {
                return false
            }
            
            // Check rate limits
            if (newRate < scheme.minInterestRate || newRate > scheme.maxInterestRate) {
                return false
            }
            
            // Check if rate is different from current
            if (newRate == deposit.interestRate) {
                return false
            }
            
            return true
            
        } catch (Exception e) {
            log.error("Error validating interest rate change: ${e.message}", e)
            return false
        }
    }

    /**
     * Create interest rate history record
     * @param deposit The deposit account
     * @param oldRate The old interest rate
     * @param newRate The new interest rate
     */
    private void createInterestRateHistory(Deposit deposit, Double oldRate, Double newRate) {
        try {
            def history = new InterestRateHistory(
                deposit: deposit,
                effectiveDate: new Date(),
                oldRate: oldRate,
                newRate: newRate,
                changedBy: UserMaster.get(session.user_id),
                changeReason: params.changeReason ?: 'Rate adjustment'
            )
            history.save(flush: true, failOnError: true)
            
        } catch (Exception e) {
            log.error("Error creating interest rate history: ${e.message}", e)
            throw e
        }
    }

    /**
     * Calculate interest amount for period
     * @param deposit The deposit account
     * @param startDate Start date for calculation
     * @param endDate End date for calculation
     * @return Interest calculation details
     */
    private Map calculateInterestAmount(Deposit deposit, Date startDate, Date endDate) {
        try {
            def principal = deposit.ledgerBalAmt
            def rate = deposit.interestRate / 100 // Convert percentage to decimal
            def days = endDate - (startDate ?: deposit.lastInterestDate ?: deposit.dateOpened)
            def daysInYear = 365
            
            def interestAmount = (principal * rate * days) / daysInYear
            
            return [
                principal: principal,
                interestRate: deposit.interestRate,
                startDate: startDate?.format('yyyy-MM-dd'),
                endDate: endDate.format('yyyy-MM-dd'),
                days: days,
                interestAmount: interestAmount.round(2),
                calculationMethod: 'Simple Interest (365 days)'
            ]
            
        } catch (Exception e) {
            log.error("Error calculating interest amount: ${e.message}", e)
            return [error: "Calculation failed: ${e.message}"]
        }
    }

    /**
     * Create interest posting transaction
     * @param deposit The deposit account
     * @param interestAmount The interest amount to post
     * @return Transaction result
     */
    private Map createInterestTransaction(Deposit deposit, Double interestAmount) {
        try {
            def txn = new TxnFile(
                acctNo: deposit.acctNo,
                branch: deposit.branch,
                currency: deposit.product.currency,
                depAcct: deposit,
                status: ConfigItemStatus.get(2),
                txnAmt: interestAmount,
                txnCode: 'INTCR',
                txnDate: deposit.branch.runDate,
                txnDescription: 'Interest Credit',
                txnParticulars: "Interest posting @ ${deposit.interestRate}%",
                txnRef: "INT-${deposit.id}-${new Date().format('yyyyMMdd')}",
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: TxnType.findByCode('CR'),
                txnTemplate: TxnTemplate.findByCode('INTCR')
            )
            txn.save(flush: true, failOnError: true)
            
            // Update account balance
            deposit.ledgerBalAmt += interestAmount
            deposit.availableBalAmt += interestAmount
            deposit.lastInterestDate = new Date()
            deposit.save(flush: true, failOnError: true)
            
            // Create GL breakdown
            GlTransactionService.saveTxnBreakdown(txn.id)
            
            return [success: true, transactionId: txn.id, message: "Interest posted successfully"]
            
        } catch (Exception e) {
            log.error("Error creating interest transaction: ${e.message}", e)
            return [success: false, message: "Transaction creation failed: ${e.message}"]
        }
    }

    /**
     * Get year-to-date interest for account
     * @param deposit The deposit account
     * @return Year-to-date interest amount
     */
    private Double getYearToDateInterest(Deposit deposit) {
        try {
            def startOfYear = new Date().clearTime()
            startOfYear.set(dayOfYear: 1)
            
            def interestTxns = TxnFile.createCriteria().list {
                eq("depAcct", deposit)
                eq("txnCode", "INTCR")
                ge("txnDate", startOfYear)
            }
            
            return interestTxns.sum { it.txnAmt } ?: 0.0
            
        } catch (Exception e) {
            log.error("Error getting year-to-date interest: ${e.message}", e)
            return 0.0
        }
    }

    /**
     * Get month-to-date interest for account
     * @param deposit The deposit account
     * @return Month-to-date interest amount
     */
    private Double getMonthToDateInterest(Deposit deposit) {
        try {
            def startOfMonth = new Date().clearTime()
            startOfMonth.set(date: 1)
            
            def interestTxns = TxnFile.createCriteria().list {
                eq("depAcct", deposit)
                eq("txnCode", "INTCR")
                ge("txnDate", startOfMonth)
            }
            
            return interestTxns.sum { it.txnAmt } ?: 0.0
            
        } catch (Exception e) {
            log.error("Error getting month-to-date interest: ${e.message}", e)
            return 0.0
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Deposit account not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
