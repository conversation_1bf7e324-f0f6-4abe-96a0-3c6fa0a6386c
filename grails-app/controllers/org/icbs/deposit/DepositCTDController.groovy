package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql
import org.icbs.deposit.CTDCertificate
import org.icbs.lov.CTDStatus

/**
 * DepositCTDController - Handles Certificate of Time Deposit operations
 * 
 * This controller manages CTD operations including:
 * - CTD certificate creation and management
 * - CTD maturity processing
 * - CTD inquiry and tracking
 * - CTD certificate printing
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositCTDController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * View CTD management page
     * @param id The deposit account ID
     */
    def viewCTD() {
        try {
            log.info("Viewing CTD for deposit ID: ${params.id}")
            
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                // Check if this is a time deposit account
                if (depositInstance.type.id != 3) {
                    flash.message = 'CTD functions only available for Time Deposit accounts'
                    render(view:"inquiry/depositInquiry", model:[depositInstance:depositInstance])
                    return
                }
                
                if (depositInstance.branch == UserMaster.get(session.user_id).branch) {
                    render(view:'ctd/view', model:[depositInstance:depositInstance])
                } else {
                    flash.message = 'CTD functions not allowed for other branch account'
                    render(view:"inquiry/depositInquiry", model:[depositInstance:depositInstance])
                }
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error viewing CTD: ${e.message}", e)
            flash.message = "Error loading CTD page |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Create new CTD certificate
     * @return JSON response with create form
     */
    def createCTD() {
        try {
            log.info("Creating CTD certificate with params: ${params}")
            
            if(params.depositId) {
                def ctdInstance = new CTDCertificate()
                ctdInstance.deposit = Deposit.read(params.depositId)
                ctdInstance.issuedBy = UserMaster.get(session.user_id)
                ctdInstance.issuedDate = new Date()
                
                if(!ctdInstance.deposit) {
                    notFound()
                    return
                }
                
                render(view:'ctd/create', model:[ctdInstance:ctdInstance])
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error creating CTD certificate: ${e.message}", e)
            flash.message = "Error creating CTD certificate |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Save new CTD certificate
     */
    @Transactional
    def saveCTD() {
        try {
            log.info("Saving CTD certificate with params: ${params}")
            
            def ctdInstance = new CTDCertificate()
            bindData(ctdInstance, params)
            
            if (!ctdInstance.validate()) {
                render(view:'ctd/create', model:[ctdInstance:ctdInstance])
                return
            }
            
            // Validate CTD certificate business rules
            if (!validateCTDCertificate(ctdInstance)) {
                render(view:'ctd/create', model:[ctdInstance:ctdInstance])
                return
            }
            
            // Generate certificate number
            ctdInstance.certificateNumber = generateCertificateNumber()
            ctdInstance.status = CTDStatus.read(1) // Active
            ctdInstance.issuedBy = UserMaster.get(session.user_id)
            ctdInstance.issuedDate = new Date()
            ctdInstance.save(flush: true, failOnError: true)
            
            // Log the CTD certificate creation
            AuditLogService.insert('080', 'DEP00590', 
                "CTD certificate created for account ${ctdInstance.deposit.acctNo} - Certificate: ${ctdInstance.certificateNumber}", 
                'deposit', null, null, null, ctdInstance.deposit.id)
            
            flash.message = "CTD Certificate Successfully Created"
            redirect(action: "showCTD", id: ctdInstance.id)
            
        } catch (Exception e) {
            log.error("Error saving CTD certificate: ${e.message}", e)
            flash.message = "Error saving CTD certificate: ${e.message} |error|alert"
            redirect(action: "createCTD", params: params)
        }
    }

    /**
     * Show CTD certificate details
     * @param ctdInstance The CTD certificate instance
     */
    def showCTD(CTDCertificate ctdInstance) {
        try {
            if (ctdInstance) {
                respond ctdInstance
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error showing CTD certificate: ${e.message}", e)
            flash.message = "Error loading CTD certificate |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Print CTD certificate
     */
    def printCTD() {
        try {
            log.info("Printing CTD certificate with params: ${params}")
            
            if (!params.id) {
                flash.message = "CTD Certificate ID is required |error|alert"
                redirect(controller: "deposit", action: "index")
                return
            }
            
            def ctdInstance = CTDCertificate.get(params.id)
            if (!ctdInstance) {
                notFound()
                return
            }
            
            params._name = "CTD Certificate"
            params._format = "PDF"
            params._file = "CTDCertificate.jasper"
            params.ctdId = ctdInstance.id.toString()
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [:])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            response.outputStream << bytes
            response.outputStream.flush()
            
        } catch (Exception e) {
            log.error("Error printing CTD certificate: ${e.message}", e)
            flash.message = "Error printing CTD certificate |error|alert"
            redirect(action: "showCTD", id: params.id)
        }
    }

    /**
     * Process CTD maturity
     */
    @Transactional
    def processCTDMaturity() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "CTD Certificate ID is required"
                ] as JSON)
                return
            }
            
            def ctdInstance = CTDCertificate.get(params.id)
            if (!ctdInstance) {
                render([
                    success: false,
                    error: "CTD Certificate not found"
                ] as JSON)
                return
            }
            
            if (ctdInstance.status.id != 1) {
                render([
                    success: false,
                    error: "CTD Certificate is not active"
                ] as JSON)
                return
            }
            
            def deposit = ctdInstance.deposit
            
            // Check if deposit has matured
            if (deposit.maturityDate > new Date()) {
                render([
                    success: false,
                    error: "Deposit has not yet matured"
                ] as JSON)
                return
            }
            
            // Process maturity
            def maturityResult = processMaturity(ctdInstance)
            
            if (maturityResult.success) {
                // Update CTD status
                ctdInstance.status = CTDStatus.read(3) // Matured
                ctdInstance.maturedDate = new Date()
                ctdInstance.maturedBy = UserMaster.get(session.user_id)
                ctdInstance.save(flush: true, failOnError: true)
                
                // Log the maturity processing
                AuditLogService.insert('080', 'DEP00591', 
                    "CTD maturity processed for account ${deposit.acctNo} - Certificate: ${ctdInstance.certificateNumber}", 
                    'deposit', null, null, null, deposit.id)
                
                render([
                    success: true,
                    message: "CTD maturity processed successfully",
                    maturityAmount: maturityResult.maturityAmount
                ] as JSON)
            } else {
                render([
                    success: false,
                    error: maturityResult.message
                ] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error processing CTD maturity: ${e.message}", e)
            render([
                success: false,
                error: "Unable to process CTD maturity: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get CTD certificate information
     * @return JSON response with CTD details
     */
    def getCTDInfo() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "CTD Certificate ID is required"
                ] as JSON)
                return
            }
            
            def ctdInstance = CTDCertificate.get(params.id)
            if (!ctdInstance) {
                render([
                    success: false,
                    error: "CTD Certificate not found"
                ] as JSON)
                return
            }
            
            def deposit = ctdInstance.deposit
            
            def ctdInfo = [
                id: ctdInstance.id,
                certificateNumber: ctdInstance.certificateNumber,
                status: ctdInstance.status?.description,
                principalAmount: deposit.ledgerBalAmt,
                interestRate: deposit.interestRate,
                term: deposit.currentRollover?.term,
                issuedDate: ctdInstance.issuedDate?.format('yyyy-MM-dd'),
                maturityDate: deposit.maturityDate?.format('yyyy-MM-dd'),
                maturedDate: ctdInstance.maturedDate?.format('yyyy-MM-dd'),
                accountNo: deposit.acctNo,
                customerName: deposit.customer?.displayName,
                isMatured: deposit.maturityDate <= new Date(),
                canProcessMaturity: ctdInstance.status?.id == 1 && deposit.maturityDate <= new Date()
            ]
            
            render([
                success: true,
                ctdInfo: ctdInfo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting CTD info: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get CTD information: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get CTD certificates for account
     * @return JSON response with account CTD certificates
     */
    def getAccountCTDs() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def ctds = CTDCertificate.findAllByDeposit(deposit, [sort: 'issuedDate', order: 'desc'])
            
            def ctdData = ctds.collect { ctd ->
                [
                    id: ctd.id,
                    certificateNumber: ctd.certificateNumber,
                    status: ctd.status?.description,
                    issuedDate: ctd.issuedDate?.format('yyyy-MM-dd'),
                    maturityDate: deposit.maturityDate?.format('yyyy-MM-dd'),
                    maturedDate: ctd.maturedDate?.format('yyyy-MM-dd'),
                    principalAmount: deposit.ledgerBalAmt,
                    interestRate: deposit.interestRate,
                    isActive: ctd.status?.id == 1,
                    isMatured: ctd.status?.id == 3,
                    canProcessMaturity: ctd.status?.id == 1 && deposit.maturityDate <= new Date()
                ]
            }
            
            def activeCount = ctds.count { it.status?.id == 1 }
            def maturedCount = ctds.count { it.status?.id == 3 }
            
            render([
                success: true,
                ctds: ctdData,
                activeCount: activeCount,
                maturedCount: maturedCount,
                totalCount: ctds.size(),
                accountNo: deposit.acctNo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting account CTDs: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get account CTDs: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate CTD certificate business rules
     * @param ctd The CTD certificate to validate
     * @return true if valid
     */
    private boolean validateCTDCertificate(CTDCertificate ctd) {
        try {
            def deposit = ctd.deposit
            
            // Check if deposit is a time deposit
            if (deposit.type.id != 3) {
                ctd.errors.rejectValue('deposit', 'ctd.deposit.not.time.deposit')
                return false
            }
            
            // Check if deposit is active
            if (deposit.status.id != 2) {
                ctd.errors.rejectValue('deposit', 'ctd.deposit.not.active')
                return false
            }
            
            // Check if certificate already exists for this deposit
            def existingCTD = CTDCertificate.findByDepositAndStatus(deposit, CTDStatus.read(1))
            if (existingCTD && existingCTD.id != ctd.id) {
                ctd.errors.rejectValue('deposit', 'ctd.certificate.already.exists')
                return false
            }
            
            return true
            
        } catch (Exception e) {
            log.error("Error validating CTD certificate: ${e.message}", e)
            return false
        }
    }

    /**
     * Generate certificate number
     * @return Generated certificate number
     */
    private String generateCertificateNumber() {
        try {
            def branch = UserMaster.get(session.user_id).branch
            def year = new Date().format('yyyy')
            def sequence = getNextCertificateSequence()
            
            return "CTD${branch.code}${year}${sequence.toString().padLeft(6, '0')}"
            
        } catch (Exception e) {
            log.error("Error generating certificate number: ${e.message}", e)
            return "CTD${new Date().format('yyyyMMddHHmmss')}"
        }
    }

    /**
     * Get next certificate sequence number
     * @return Next sequence number
     */
    private Integer getNextCertificateSequence() {
        try {
            def lastCertificate = CTDCertificate.createCriteria().get {
                projections {
                    max("id")
                }
            }
            
            return (lastCertificate ?: 0) + 1
            
        } catch (Exception e) {
            log.error("Error getting certificate sequence: ${e.message}", e)
            return 1
        }
    }

    /**
     * Process CTD maturity
     * @param ctd The CTD certificate
     * @return Maturity processing result
     */
    private Map processMaturity(CTDCertificate ctd) {
        try {
            def deposit = ctd.deposit
            def principal = deposit.ledgerBalAmt
            def interestAmount = calculateMaturityInterest(deposit)
            def maturityAmount = principal + interestAmount
            
            // Create maturity transaction
            def txn = new TxnFile(
                acctNo: deposit.acctNo,
                branch: deposit.branch,
                currency: deposit.product.currency,
                depAcct: deposit,
                status: ConfigItemStatus.get(2),
                txnAmt: maturityAmount,
                txnCode: 'CTDMAT',
                txnDate: deposit.branch.runDate,
                txnDescription: 'CTD Maturity',
                txnParticulars: "CTD Certificate ${ctd.certificateNumber} maturity",
                txnRef: "CTD-MAT-${ctd.id}",
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: TxnType.findByCode('CR'),
                txnTemplate: TxnTemplate.findByCode('CTDMAT')
            )
            txn.save(flush: true, failOnError: true)
            
            // Update deposit status to matured
            deposit.status = DepositStatus.read(8) // Matured
            deposit.save(flush: true, failOnError: true)
            
            // Create GL breakdown
            GlTransactionService.saveTxnBreakdown(txn.id)
            
            return [
                success: true, 
                maturityAmount: maturityAmount, 
                interestAmount: interestAmount,
                transactionId: txn.id,
                message: "CTD maturity processed successfully"
            ]
            
        } catch (Exception e) {
            log.error("Error processing maturity: ${e.message}", e)
            return [success: false, message: "Maturity processing failed: ${e.message}"]
        }
    }

    /**
     * Calculate maturity interest
     * @param deposit The deposit account
     * @return Calculated interest amount
     */
    private Double calculateMaturityInterest(Deposit deposit) {
        try {
            def principal = deposit.ledgerBalAmt
            def rate = deposit.interestRate / 100
            def term = deposit.currentRollover?.term ?: 365
            
            // Simple interest calculation
            def interestAmount = (principal * rate * term) / 365
            
            return interestAmount.round(2)
            
        } catch (Exception e) {
            log.error("Error calculating maturity interest: ${e.message}", e)
            return 0.0
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "CTD Certificate not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
