package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql
import org.icbs.deposit.IssuePassbook
import org.icbs.deposit.Passbook
import org.icbs.lov.PassbookStatus

/**
 * DepositPassbookController - Handles deposit passbook operations
 * 
 * This controller manages passbook operations including:
 * - Passbook issuance and management
 * - Passbook printing and cover generation
 * - Passbook status updates
 * - Passbook inquiry and tracking
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositPassbookController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * View passbook management page
     * @param id The deposit account ID
     */
    def viewPassbook() {
        try {
            log.info("Viewing passbook for deposit ID: ${params.id}")
            
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                if (depositInstance.branch == UserMaster.get(session.user_id).branch) {
                    render(view:'passbook/view', model:[depositInstance:depositInstance])
                } else {
                    flash.message = 'Passbook functions not allowed for other branch account'
                    render(view:"inquiry/depositInquiry", model:[depositInstance:depositInstance])
                }
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error viewing passbook: ${e.message}", e)
            flash.message = "Error loading passbook page |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Show passbook form via AJAX
     * @return JSON response with passbook grid
     */
    def showPassbookFormAjax() {
        try {
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                render(template:"passbook/viewGrid", model:[depositInstance:depositInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error showing passbook form: ${e.message}", e)
            render([error: "Unable to load passbook form"] as JSON)
        }
    }

    /**
     * Create new passbook via AJAX
     * @return JSON response with create form
     */
    def createPassbookAjax() {
        try {
            log.info("Creating passbook with params: ${params}")
            
            if(params.id) {
                def issuePassbookInstance = new IssuePassbook()
                issuePassbookInstance.deposit = Deposit.read(params.id)
                issuePassbookInstance.issuedBy = UserMaster.get(session.user_id)
                
                if(!issuePassbookInstance.deposit) {
                    notFound()
                    return
                }
                
                render(template:'passbook/create', model:[issuePassbookInstance:issuePassbookInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error creating passbook: ${e.message}", e)
            render([error: "Unable to create passbook"] as JSON)
        }
    }

    /**
     * Edit passbook via AJAX
     * @return JSON response with edit form
     */
    def editPassbookAjax() {
        try {
            if(params.id) {
                def issuePassbookInstance = IssuePassbook.read(params.id)
                if(!issuePassbookInstance) {
                    notFound()
                    return
                }
                
                render(template:'passbook/edit', model:[issuePassbookInstance:issuePassbookInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error editing passbook: ${e.message}", e)
            render([error: "Unable to edit passbook"] as JSON)
        }
    }

    /**
     * Save new passbook via AJAX
     * @return JSON response with save result
     */
    @Transactional
    def savePassbookAjax() {
        try {
            log.info("Saving passbook with params: ${params}")
            
            def result = depositService.savePassbook(params)
            
            if(!result.error) {
                // Log the passbook issuance
                AuditLogService.insert('080', 'DEP00500', 
                    "Passbook issued for account ${result.issuePassbookInstance.deposit.acctNo}", 
                    'deposit', null, null, null, result.issuePassbookInstance.deposit.id)
                
                flash.message = "Passbook Successfully Issued"
                render(template:'passbook/create', 
                    model:[issuePassbookInstance:new IssuePassbook(deposit:result.issuePassbookInstance.deposit)]) as JSON
            } else {
                render(template:'passbook/create', 
                    model:[issuePassbookInstance:result.issuePassbookInstance]) as JSON
            }
            
        } catch (Exception e) {
            log.error("Error saving passbook: ${e.message}", e)
            render([error: "Unable to save passbook: ${e.message}"] as JSON)
        }
    }

    /**
     * Update passbook via AJAX
     * @return JSON response with update result
     */
    @Transactional
    def updatePassbookAjax() {
        try {
            def result = depositService.updatePassbook(params)
            
            if(!result.error) {
                // Log the passbook update
                AuditLogService.insert('080', 'DEP00501', 
                    "Passbook updated for account ${result.issuePassbookInstance.deposit.acctNo}", 
                    'deposit', null, null, null, result.issuePassbookInstance.deposit.id)
                
                flash.message = "Passbook Successfully Updated"
                render(template:'passbook/edit', model:[issuePassbookInstance:result.issuePassbookInstance]) as JSON
            } else {
                if(result.error.code == "default.not.found") {
                    notFound()
                    return
                }
                render(template:'passbook/edit', model:[issuePassbookInstance:result.issuePassbookInstance.attach()]) as JSON
            }
            
        } catch (Exception e) {
            log.error("Error updating passbook: ${e.message}", e)
            render([error: "Unable to update passbook: ${e.message}"] as JSON)
        }
    }

    /**
     * Print passbook
     */
    def printPassbook() {
        try {
            log.info("Printing passbook with params: ${params}")
            
            def list = Customer.list(fetch:[branch:"eager"])
            chain(controller:'jasper', action:'index', model:[data:list], params:params)
            
        } catch (Exception e) {
            log.error("Error printing passbook: ${e.message}", e)
            flash.message = "Error printing passbook |error|alert"
            redirect(action: "viewPassbook", id: params.id)
        }
    }

    /**
     * Print passbook cover
     */
    def printPassbookCover() {
        try {
            log.info("Printing passbook cover with params: ${params}")
            
            params._name = "Passbook Cover"
            params._format = "PDF"
            params._file = "PassbookCover.jasper"
            params.pbID = params.passbookNo.toString()
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [:])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            response.outputStream << bytes
            response.outputStream.flush()
            
        } catch (Exception e) {
            log.error("Error printing passbook cover: ${e.message}", e)
            flash.message = "Error printing passbook cover |error|alert"
            redirect(action: "viewPassbook", id: params.id)
        }
    }

    /**
     * Update passbook status
     */
    @Transactional
    def updatePassbookStatus() {
        try {
            log.info("Updating passbook status with params: ${params}")
            
            def passbookInstance = IssuePassbook.get(params.txnid.toInteger())
            def passbook = passbookInstance.passbook
            def deposit = passbookInstance.deposit
            
            passbook.status = PassbookStatus.read(params.txn.toInteger())
            passbook.save(flush:true)
            
            // Handle cancelled passbook
            if (passbook.status == PassbookStatus.read(4)) {
                passbook.status = PassbookStatus.read(1)
                passbook.issuePassbook = null
                passbook.save(flush:true, failOnError:true)
                
                passbookInstance.delete(flush:true)
            }
            
            def desc = "${deposit.acctNo} - Update Passbook Status - ${passbook.status}"
            AuditLogService.insert('080', 'DEP00600', desc, 'deposit', null, null, null, deposit.id)
            
            render([success: true, message: "Passbook status updated successfully"] as JSON)
            
        } catch (Exception e) {
            log.error("Error updating passbook status: ${e.message}", e)
            render([success: false, error: "Unable to update passbook status: ${e.message}"] as JSON)
        }
    }

    /**
     * Get passbook status list
     * @return JSON response with status options
     */
    def getPassbookStatusList() {
        try {
            def db = new Sql(dataSource)
            def sql = "select id, description from passbook_status where id <> 2"
            def results = db.rows(sql)
            
            render(results as JSON)
            
        } catch (Exception e) {
            log.error("Error getting passbook status list: ${e.message}", e)
            render([error: "Unable to get status list"] as JSON)
        }
    }

    /**
     * Get passbook information
     * @return JSON response with passbook details
     */
    def getPassbookInfo() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Passbook ID is required"
                ] as JSON)
                return
            }
            
            def issuePassbook = IssuePassbook.get(params.id)
            if (!issuePassbook) {
                render([
                    success: false,
                    error: "Passbook not found"
                ] as JSON)
                return
            }
            
            def passbookInfo = [
                id: issuePassbook.id,
                passbookNo: issuePassbook.passbook?.passbookNo,
                status: issuePassbook.passbook?.status?.description,
                issuedDate: issuePassbook.issuedDate?.format('yyyy-MM-dd'),
                issuedBy: issuePassbook.issuedBy?.username,
                accountNo: issuePassbook.deposit?.acctNo,
                customerName: issuePassbook.deposit?.customer?.displayName,
                lastPrintedDate: issuePassbook.lastPrintedDate?.format('yyyy-MM-dd'),
                lastPrintedLine: issuePassbook.lastPrintedLine
            ]
            
            render([
                success: true,
                passbookInfo: passbookInfo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting passbook info: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get passbook information: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Cancel passbook
     */
    @Transactional
    def cancelPassbook() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Passbook ID is required"
                ] as JSON)
                return
            }
            
            def issuePassbook = IssuePassbook.get(params.id)
            if (!issuePassbook) {
                render([
                    success: false,
                    error: "Passbook not found"
                ] as JSON)
                return
            }
            
            def passbook = issuePassbook.passbook
            def deposit = issuePassbook.deposit
            
            // Mark passbook as cancelled
            passbook.status = PassbookStatus.read(4) // Cancelled
            passbook.save(flush:true, failOnError:true)
            
            // Log the cancellation
            AuditLogService.insert('080', 'DEP00602', 
                "Passbook cancelled for account ${deposit.acctNo}", 
                'deposit', null, null, null, deposit.id)
            
            render([
                success: true,
                message: "Passbook cancelled successfully"
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error cancelling passbook: ${e.message}", e)
            render([
                success: false,
                error: "Unable to cancel passbook: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get passbook transaction history
     * @return JSON response with transaction history
     */
    def getPassbookTransactionHistory() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def maxResults = params.max ? params.max.toInteger() : 20
            
            def transactions = TxnFile.createCriteria().list(max: maxResults) {
                eq("depAcct", deposit)
                order("txnDate", "desc")
                order("txnTimestamp", "desc")
            }
            
            def transactionHistory = transactions.collect { txn ->
                [
                    txnDate: txn.txnDate?.format('yyyy-MM-dd'),
                    description: txn.txnDescription,
                    debitAmount: txn.txnType?.code == 'DR' ? txn.txnAmt : 0.0,
                    creditAmount: txn.txnType?.code == 'CR' ? txn.txnAmt : 0.0,
                    balance: getTransactionBalance(txn, deposit),
                    reference: txn.txnRef
                ]
            }
            
            render([
                success: true,
                transactions: transactionHistory,
                accountNo: deposit.acctNo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting passbook transaction history: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get transaction history: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get transaction balance
     * @param txn The transaction
     * @param deposit The deposit account
     * @return The balance after transaction
     */
    private Double getTransactionBalance(TxnFile txn, Deposit deposit) {
        try {
            def ledgerEntry = TxnDepositAcctLedger.findByTxnFileAndAcct(txn, deposit)
            return ledgerEntry?.bal ?: 0.0
        } catch (Exception e) {
            log.error("Error getting transaction balance: ${e.message}", e)
            return 0.0
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Passbook not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
