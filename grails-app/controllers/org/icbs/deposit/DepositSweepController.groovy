package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql
import org.icbs.deposit.SweepArrangement
import org.icbs.lov.SweepType
import org.icbs.lov.SweepStatus

/**
 * DepositSweepController - Handles deposit sweep operations
 * 
 * This controller manages sweep operations including:
 * - Sweep arrangement creation and management
 * - Sweep execution and scheduling
 * - Sweep inquiry and tracking
 * - Sweep configuration management
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositSweepController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * View sweep management page
     * @param id The deposit account ID
     */
    def viewSweep() {
        try {
            log.info("Viewing sweep arrangements for deposit ID: ${params.id}")
            
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                if (depositInstance.branch == UserMaster.get(session.user_id).branch) {
                    render(view:'sweep/view', model:[depositInstance:depositInstance])
                } else {
                    flash.message = 'Sweep functions not allowed for other branch account'
                    render(view:"inquiry/depositInquiry", model:[depositInstance:depositInstance])
                }
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error viewing sweep arrangements: ${e.message}", e)
            flash.message = "Error loading sweep page |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Show sweep form via AJAX
     * @return JSON response with sweep grid
     */
    def showSweepFormAjax() {
        try {
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                render(template:"sweep/viewGrid", model:[depositInstance:depositInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error showing sweep form: ${e.message}", e)
            render([error: "Unable to load sweep form"] as JSON)
        }
    }

    /**
     * Create new sweep arrangement via AJAX
     * @return JSON response with create form
     */
    def createSweepAjax() {
        try {
            log.info("Creating sweep arrangement with params: ${params}")
            
            if(params.id) {
                def sweepInstance = new SweepArrangement()
                sweepInstance.sourceAccount = Deposit.read(params.id)
                sweepInstance.createdBy = UserMaster.get(session.user_id)
                sweepInstance.createdDate = new Date()
                
                if(!sweepInstance.sourceAccount) {
                    notFound()
                    return
                }
                
                render(template:'sweep/create', model:[sweepInstance:sweepInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error creating sweep arrangement: ${e.message}", e)
            render([error: "Unable to create sweep arrangement"] as JSON)
        }
    }

    /**
     * Edit sweep arrangement via AJAX
     * @return JSON response with edit form
     */
    def editSweepAjax() {
        try {
            if(params.id) {
                def sweepInstance = SweepArrangement.read(params.id)
                if(!sweepInstance) {
                    notFound()
                    return
                }
                
                render(template:'sweep/edit', model:[sweepInstance:sweepInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error editing sweep arrangement: ${e.message}", e)
            render([error: "Unable to edit sweep arrangement"] as JSON)
        }
    }

    /**
     * Save new sweep arrangement via AJAX
     * @return JSON response with save result
     */
    @Transactional
    def saveSweepAjax() {
        try {
            log.info("Saving sweep arrangement with params: ${params}")
            
            def sweepInstance = new SweepArrangement()
            bindData(sweepInstance, params)
            
            if (!sweepInstance.validate()) {
                render(template:'sweep/create', model:[sweepInstance:sweepInstance]) as JSON
                return
            }
            
            // Validate sweep arrangement business rules
            if (!validateSweepArrangement(sweepInstance)) {
                render(template:'sweep/create', model:[sweepInstance:sweepInstance]) as JSON
                return
            }
            
            sweepInstance.status = SweepStatus.read(1) // Active
            sweepInstance.createdBy = UserMaster.get(session.user_id)
            sweepInstance.createdDate = new Date()
            sweepInstance.save(flush: true, failOnError: true)
            
            // Log the sweep arrangement creation
            AuditLogService.insert('080', 'DEP00560', 
                "Sweep arrangement created for account ${sweepInstance.sourceAccount.acctNo}", 
                'deposit', null, null, null, sweepInstance.sourceAccount.id)
            
            flash.message = "Sweep Arrangement Successfully Created"
            render(template:'sweep/create', 
                model:[sweepInstance:new SweepArrangement(sourceAccount:sweepInstance.sourceAccount)]) as JSON
            
        } catch (Exception e) {
            log.error("Error saving sweep arrangement: ${e.message}", e)
            render([error: "Unable to save sweep arrangement: ${e.message}"] as JSON)
        }
    }

    /**
     * Update sweep arrangement via AJAX
     * @return JSON response with update result
     */
    @Transactional
    def updateSweepAjax() {
        try {
            def sweepInstance = SweepArrangement.get(params.id)
            if (!sweepInstance) {
                notFound()
                return
            }
            
            bindData(sweepInstance, params)
            
            if (!sweepInstance.validate()) {
                render(template:'sweep/edit', model:[sweepInstance:sweepInstance]) as JSON
                return
            }
            
            // Validate updated sweep arrangement
            if (!validateSweepArrangement(sweepInstance)) {
                render(template:'sweep/edit', model:[sweepInstance:sweepInstance]) as JSON
                return
            }
            
            sweepInstance.save(flush: true, failOnError: true)
            
            // Log the sweep arrangement update
            AuditLogService.insert('080', 'DEP00561', 
                "Sweep arrangement updated for account ${sweepInstance.sourceAccount.acctNo}", 
                'deposit', null, null, null, sweepInstance.sourceAccount.id)
            
            flash.message = "Sweep Arrangement Successfully Updated"
            render(template:'sweep/edit', model:[sweepInstance:sweepInstance]) as JSON
            
        } catch (Exception e) {
            log.error("Error updating sweep arrangement: ${e.message}", e)
            render([error: "Unable to update sweep arrangement: ${e.message}"] as JSON)
        }
    }

    /**
     * Execute sweep arrangement manually
     */
    @Transactional
    def executeSweep() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Sweep Arrangement ID is required"
                ] as JSON)
                return
            }
            
            def sweepInstance = SweepArrangement.get(params.id)
            if (!sweepInstance) {
                render([
                    success: false,
                    error: "Sweep Arrangement not found"
                ] as JSON)
                return
            }
            
            if (sweepInstance.status.id != 1) {
                render([
                    success: false,
                    error: "Sweep Arrangement is not active"
                ] as JSON)
                return
            }
            
            // Execute the sweep
            def executionResult = executeSweepTransaction(sweepInstance)
            
            if (executionResult.success) {
                // Update last execution date
                sweepInstance.lastExecutionDate = new Date()
                sweepInstance.executionCount = (sweepInstance.executionCount ?: 0) + 1
                sweepInstance.save(flush: true, failOnError: true)
                
                // Log the execution
                AuditLogService.insert('080', 'DEP00562', 
                    "Sweep executed for account ${sweepInstance.sourceAccount.acctNo} - Amount: ${executionResult.amount}", 
                    'deposit', null, null, null, sweepInstance.sourceAccount.id)
                
                render([
                    success: true,
                    message: "Sweep executed successfully",
                    amount: executionResult.amount,
                    transactionId: executionResult.transactionId
                ] as JSON)
            } else {
                render([
                    success: false,
                    error: executionResult.message
                ] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error executing sweep: ${e.message}", e)
            render([
                success: false,
                error: "Unable to execute sweep: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Cancel sweep arrangement
     */
    @Transactional
    def cancelSweep() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Sweep Arrangement ID is required"
                ] as JSON)
                return
            }
            
            def sweepInstance = SweepArrangement.get(params.id)
            if (!sweepInstance) {
                render([
                    success: false,
                    error: "Sweep Arrangement not found"
                ] as JSON)
                return
            }
            
            if (sweepInstance.status.id != 1) {
                render([
                    success: false,
                    error: "Sweep Arrangement is not active"
                ] as JSON)
                return
            }
            
            // Update sweep status
            sweepInstance.status = SweepStatus.read(3) // Cancelled
            sweepInstance.cancelledBy = UserMaster.get(session.user_id)
            sweepInstance.cancelledDate = new Date()
            sweepInstance.cancellationReason = params.cancellationReason ?: 'Manual Cancellation'
            sweepInstance.save(flush: true, failOnError: true)
            
            // Log the sweep cancellation
            AuditLogService.insert('080', 'DEP00563', 
                "Sweep arrangement cancelled for account ${sweepInstance.sourceAccount.acctNo}", 
                'deposit', null, null, null, sweepInstance.sourceAccount.id)
            
            render([
                success: true,
                message: "Sweep Arrangement cancelled successfully"
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error cancelling sweep: ${e.message}", e)
            render([
                success: false,
                error: "Unable to cancel sweep: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get sweep arrangement information
     * @return JSON response with sweep details
     */
    def getSweepInfo() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Sweep Arrangement ID is required"
                ] as JSON)
                return
            }
            
            def sweepInstance = SweepArrangement.get(params.id)
            if (!sweepInstance) {
                render([
                    success: false,
                    error: "Sweep Arrangement not found"
                ] as JSON)
                return
            }
            
            def sweepInfo = [
                id: sweepInstance.id,
                sweepType: sweepInstance.sweepType?.description,
                status: sweepInstance.status?.description,
                sourceAccount: sweepInstance.sourceAccount?.acctNo,
                targetAccount: sweepInstance.targetAccount?.acctNo,
                minimumBalance: sweepInstance.minimumBalance,
                maximumBalance: sweepInstance.maximumBalance,
                sweepAmount: sweepInstance.sweepAmount,
                frequency: sweepInstance.frequency,
                createdDate: sweepInstance.createdDate?.format('yyyy-MM-dd'),
                createdBy: sweepInstance.createdBy?.username,
                lastExecutionDate: sweepInstance.lastExecutionDate?.format('yyyy-MM-dd'),
                executionCount: sweepInstance.executionCount ?: 0,
                isActive: sweepInstance.status?.id == 1
            ]
            
            render([
                success: true,
                sweepInfo: sweepInfo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting sweep info: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get sweep information: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get sweep arrangements for account
     * @return JSON response with account sweep arrangements
     */
    def getAccountSweeps() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def sweeps = SweepArrangement.findAllBySourceAccount(deposit, [sort: 'createdDate', order: 'desc'])
            
            def sweepData = sweeps.collect { sweep ->
                [
                    id: sweep.id,
                    sweepType: sweep.sweepType?.description,
                    status: sweep.status?.description,
                    targetAccount: sweep.targetAccount?.acctNo,
                    minimumBalance: sweep.minimumBalance,
                    maximumBalance: sweep.maximumBalance,
                    sweepAmount: sweep.sweepAmount,
                    lastExecutionDate: sweep.lastExecutionDate?.format('yyyy-MM-dd'),
                    executionCount: sweep.executionCount ?: 0,
                    isActive: sweep.status?.id == 1,
                    canExecute: sweep.status?.id == 1
                ]
            }
            
            def activeCount = sweeps.count { it.status?.id == 1 }
            
            render([
                success: true,
                sweeps: sweepData,
                activeCount: activeCount,
                totalCount: sweeps.size(),
                accountNo: deposit.acctNo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting account sweeps: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get account sweeps: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate sweep arrangement business rules
     * @param sweep The sweep arrangement to validate
     * @return true if valid
     */
    private boolean validateSweepArrangement(SweepArrangement sweep) {
        try {
            // Check minimum balance
            if (sweep.minimumBalance < 0) {
                sweep.errors.rejectValue('minimumBalance', 'sweep.minimumBalance.invalid')
                return false
            }
            
            // Check maximum balance
            if (sweep.maximumBalance && sweep.maximumBalance <= sweep.minimumBalance) {
                sweep.errors.rejectValue('maximumBalance', 'sweep.maximumBalance.invalid')
                return false
            }
            
            // Check sweep amount
            if (sweep.sweepAmount && sweep.sweepAmount <= 0) {
                sweep.errors.rejectValue('sweepAmount', 'sweep.sweepAmount.invalid')
                return false
            }
            
            // Check target account
            if (!sweep.targetAccount) {
                sweep.errors.rejectValue('targetAccount', 'sweep.targetAccount.required')
                return false
            }
            
            // Check that source and target accounts are different
            if (sweep.sourceAccount.id == sweep.targetAccount.id) {
                sweep.errors.rejectValue('targetAccount', 'sweep.targetAccount.same')
                return false
            }
            
            return true
            
        } catch (Exception e) {
            log.error("Error validating sweep arrangement: ${e.message}", e)
            return false
        }
    }

    /**
     * Execute sweep transaction
     * @param sweep The sweep arrangement to execute
     * @return Execution result
     */
    private Map executeSweepTransaction(SweepArrangement sweep) {
        try {
            def sourceAccount = sweep.sourceAccount
            def targetAccount = sweep.targetAccount
            
            // Calculate sweep amount
            def sweepAmount = calculateSweepAmount(sweep)
            
            if (sweepAmount <= 0) {
                return [success: false, message: "No sweep required - balance within limits"]
            }
            
            // Check available balance
            if (sweepAmount > sourceAccount.availableBalAmt) {
                return [success: false, message: "Insufficient funds for sweep"]
            }
            
            // Create debit transaction (source account)
            def debitTxn = createSweepTransaction(sweep, 'DEBIT', sweepAmount)
            
            // Create credit transaction (target account)
            def creditTxn = createSweepTransaction(sweep, 'CREDIT', sweepAmount)
            
            return [
                success: true, 
                amount: sweepAmount, 
                transactionId: debitTxn.id, 
                message: "Sweep executed successfully"
            ]
            
        } catch (Exception e) {
            log.error("Error executing sweep transaction: ${e.message}", e)
            return [success: false, message: "Sweep execution failed: ${e.message}"]
        }
    }

    /**
     * Calculate sweep amount based on arrangement rules
     * @param sweep The sweep arrangement
     * @return Amount to sweep
     */
    private Double calculateSweepAmount(SweepArrangement sweep) {
        try {
            def sourceBalance = sweep.sourceAccount.availableBalAmt
            
            if (sweep.sweepType?.code == 'EXCESS') {
                // Sweep excess over maximum balance
                if (sweep.maximumBalance && sourceBalance > sweep.maximumBalance) {
                    return sourceBalance - sweep.maximumBalance
                }
            } else if (sweep.sweepType?.code == 'DEFICIT') {
                // Sweep to maintain minimum balance
                if (sourceBalance < sweep.minimumBalance) {
                    return sweep.minimumBalance - sourceBalance
                }
            } else if (sweep.sweepType?.code == 'FIXED') {
                // Sweep fixed amount
                if (sweep.sweepAmount && sourceBalance >= sweep.sweepAmount) {
                    return sweep.sweepAmount
                }
            }
            
            return 0.0
            
        } catch (Exception e) {
            log.error("Error calculating sweep amount: ${e.message}", e)
            return 0.0
        }
    }

    /**
     * Create sweep transaction
     * @param sweep The sweep arrangement
     * @param type Transaction type (DEBIT/CREDIT)
     * @param amount Transaction amount
     * @return Created transaction
     */
    private TxnFile createSweepTransaction(SweepArrangement sweep, String type, Double amount) {
        try {
            def account = (type == 'DEBIT') ? sweep.sourceAccount : sweep.targetAccount
            def txnType = TxnType.findByCode(type == 'DEBIT' ? 'DR' : 'CR')
            
            def txn = new TxnFile(
                acctNo: account.acctNo,
                branch: account.branch,
                currency: account.product.currency,
                depAcct: account,
                status: ConfigItemStatus.get(2),
                txnAmt: amount,
                txnCode: 'SWEEP',
                txnDate: account.branch.runDate,
                txnDescription: "Sweep ${type}",
                txnParticulars: "Sweep Transfer - ${sweep.sweepType?.description}",
                txnRef: "SWEEP-${sweep.id}-${type}",
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: txnType,
                txnTemplate: TxnTemplate.findByCode('SWEEP')
            )
            txn.save(flush: true, failOnError: true)
            
            // Update account balance
            if (type == 'DEBIT') {
                account.ledgerBalAmt -= amount
                account.availableBalAmt -= amount
            } else {
                account.ledgerBalAmt += amount
                account.availableBalAmt += amount
            }
            account.save(flush: true, failOnError: true)
            
            // Create GL breakdown
            GlTransactionService.saveTxnBreakdown(txn.id)
            
            return txn
            
        } catch (Exception e) {
            log.error("Error creating sweep transaction: ${e.message}", e)
            throw e
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Sweep Arrangement not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
