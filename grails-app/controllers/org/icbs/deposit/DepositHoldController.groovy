package org.icbs.deposit

import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositType
import org.icbs.lov.DepositStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Module
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.icbs.gl.TxnGlLink
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.admin.Institution
import org.icbs.admin.ProductTxn
import org.icbs.lov.TxnType
import org.icbs.lov.InterestPaymentMode
import static java.util.Calendar.*
import groovy.sql.Sql
import org.icbs.deposit.Hold
import org.icbs.lov.HoldType
import org.icbs.lov.HoldStatus

/**
 * DepositHoldController - Handles deposit hold operations
 * 
 * This controller manages hold operations including:
 * - Hold placement and management
 * - Hold release and expiration
 * - Hold inquiry and tracking
 * - Hold type management
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DepositHoldController {
    
    // Service Dependencies
    def jasperService
    def depositService
    def DepositPeriodicOpsService
    def AuditLogService
    def dataSource
    def txnFileID
    def GlTransactionService
    def RoleModuleService
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * View hold management page
     * @param id The deposit account ID
     */
    def viewHold() {
        try {
            log.info("Viewing holds for deposit ID: ${params.id}")
            
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                if (depositInstance.branch == UserMaster.get(session.user_id).branch) {
                    render(view:'hold/view', model:[depositInstance:depositInstance])
                } else {
                    flash.message = 'Hold functions not allowed for other branch account'
                    render(view:"inquiry/depositInquiry", model:[depositInstance:depositInstance])
                }
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error viewing holds: ${e.message}", e)
            flash.message = "Error loading hold page |error|alert"
            redirect(controller: "deposit", action: "index")
        }
    }

    /**
     * Show hold form via AJAX
     * @return JSON response with hold grid
     */
    def showHoldFormAjax() {
        try {
            if(params.id) {
                def depositInstance = Deposit.read(params.id)
                if(!depositInstance) {
                    notFound()
                    return
                }
                
                render(template:"hold/viewGrid", model:[depositInstance:depositInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error showing hold form: ${e.message}", e)
            render([error: "Unable to load hold form"] as JSON)
        }
    }

    /**
     * Create new hold via AJAX
     * @return JSON response with create form
     */
    def createHoldAjax() {
        try {
            log.info("Creating hold with params: ${params}")
            
            if(params.id) {
                def holdInstance = new Hold()
                holdInstance.deposit = Deposit.read(params.id)
                holdInstance.placedBy = UserMaster.get(session.user_id)
                holdInstance.placedDate = new Date()
                
                if(!holdInstance.deposit) {
                    notFound()
                    return
                }
                
                render(template:'hold/create', model:[holdInstance:holdInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error creating hold: ${e.message}", e)
            render([error: "Unable to create hold"] as JSON)
        }
    }

    /**
     * Edit hold via AJAX
     * @return JSON response with edit form
     */
    def editHoldAjax() {
        try {
            if(params.id) {
                def holdInstance = Hold.read(params.id)
                if(!holdInstance) {
                    notFound()
                    return
                }
                
                render(template:'hold/edit', model:[holdInstance:holdInstance]) as JSON
            } else {
                notFound()
            }
            
        } catch (Exception e) {
            log.error("Error editing hold: ${e.message}", e)
            render([error: "Unable to edit hold"] as JSON)
        }
    }

    /**
     * Save new hold via AJAX
     * @return JSON response with save result
     */
    @Transactional
    def saveHoldAjax() {
        try {
            log.info("Saving hold with params: ${params}")
            
            def holdInstance = new Hold()
            bindData(holdInstance, params)
            
            if (!holdInstance.validate()) {
                render(template:'hold/create', model:[holdInstance:holdInstance]) as JSON
                return
            }
            
            // Validate hold amount against available balance
            def deposit = holdInstance.deposit
            if (holdInstance.holdAmount > deposit.availableBalAmt) {
                holdInstance.errors.rejectValue('holdAmount', 'hold.amount.exceeds.available')
                render(template:'hold/create', model:[holdInstance:holdInstance]) as JSON
                return
            }
            
            holdInstance.status = HoldStatus.read(1) // Active
            holdInstance.placedBy = UserMaster.get(session.user_id)
            holdInstance.placedDate = new Date()
            holdInstance.save(flush: true, failOnError: true)
            
            // Update deposit hold balance
            deposit.holdBalAmt = (deposit.holdBalAmt ?: 0.0) + holdInstance.holdAmount
            deposit.availableBalAmt = deposit.ledgerBalAmt - deposit.holdBalAmt
            deposit.save(flush: true, failOnError: true)
            
            // Create hold transaction
            createHoldTransaction(holdInstance, 'PLACE')
            
            // Log the hold placement
            AuditLogService.insert('080', 'DEP00520', 
                "Hold placed on account ${deposit.acctNo} - Amount: ${holdInstance.holdAmount}", 
                'deposit', null, null, null, deposit.id)
            
            flash.message = "Hold Successfully Placed"
            render(template:'hold/create', 
                model:[holdInstance:new Hold(deposit:deposit)]) as JSON
            
        } catch (Exception e) {
            log.error("Error saving hold: ${e.message}", e)
            render([error: "Unable to save hold: ${e.message}"] as JSON)
        }
    }

    /**
     * Update hold via AJAX
     * @return JSON response with update result
     */
    @Transactional
    def updateHoldAjax() {
        try {
            def holdInstance = Hold.get(params.id)
            if (!holdInstance) {
                notFound()
                return
            }
            
            def originalAmount = holdInstance.holdAmount
            bindData(holdInstance, params)
            
            if (!holdInstance.validate()) {
                render(template:'hold/edit', model:[holdInstance:holdInstance]) as JSON
                return
            }
            
            // Validate updated hold amount
            def deposit = holdInstance.deposit
            def availableForHold = deposit.availableBalAmt + originalAmount
            
            if (holdInstance.holdAmount > availableForHold) {
                holdInstance.errors.rejectValue('holdAmount', 'hold.amount.exceeds.available')
                render(template:'hold/edit', model:[holdInstance:holdInstance]) as JSON
                return
            }
            
            holdInstance.save(flush: true, failOnError: true)
            
            // Update deposit hold balance
            def amountDifference = holdInstance.holdAmount - originalAmount
            deposit.holdBalAmt = (deposit.holdBalAmt ?: 0.0) + amountDifference
            deposit.availableBalAmt = deposit.ledgerBalAmt - deposit.holdBalAmt
            deposit.save(flush: true, failOnError: true)
            
            // Create adjustment transaction if amount changed
            if (amountDifference != 0) {
                createHoldTransaction(holdInstance, 'ADJUST', amountDifference)
            }
            
            // Log the hold update
            AuditLogService.insert('080', 'DEP00521', 
                "Hold updated on account ${deposit.acctNo} - New Amount: ${holdInstance.holdAmount}", 
                'deposit', null, null, null, deposit.id)
            
            flash.message = "Hold Successfully Updated"
            render(template:'hold/edit', model:[holdInstance:holdInstance]) as JSON
            
        } catch (Exception e) {
            log.error("Error updating hold: ${e.message}", e)
            render([error: "Unable to update hold: ${e.message}"] as JSON)
        }
    }

    /**
     * Release hold
     */
    @Transactional
    def releaseHold() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Hold ID is required"
                ] as JSON)
                return
            }
            
            def holdInstance = Hold.get(params.id)
            if (!holdInstance) {
                render([
                    success: false,
                    error: "Hold not found"
                ] as JSON)
                return
            }
            
            if (holdInstance.status.id != 1) {
                render([
                    success: false,
                    error: "Hold is not active"
                ] as JSON)
                return
            }
            
            def deposit = holdInstance.deposit
            
            // Update hold status
            holdInstance.status = HoldStatus.read(3) // Released
            holdInstance.releasedBy = UserMaster.get(session.user_id)
            holdInstance.releasedDate = new Date()
            holdInstance.releaseReason = params.releaseReason ?: 'Manual Release'
            holdInstance.save(flush: true, failOnError: true)
            
            // Update deposit hold balance
            deposit.holdBalAmt = (deposit.holdBalAmt ?: 0.0) - holdInstance.holdAmount
            deposit.availableBalAmt = deposit.ledgerBalAmt - deposit.holdBalAmt
            deposit.save(flush: true, failOnError: true)
            
            // Create release transaction
            createHoldTransaction(holdInstance, 'RELEASE')
            
            // Log the hold release
            AuditLogService.insert('080', 'DEP00522', 
                "Hold released on account ${deposit.acctNo} - Amount: ${holdInstance.holdAmount}", 
                'deposit', null, null, null, deposit.id)
            
            render([
                success: true,
                message: "Hold released successfully"
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error releasing hold: ${e.message}", e)
            render([
                success: false,
                error: "Unable to release hold: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get hold information
     * @return JSON response with hold details
     */
    def getHoldInfo() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Hold ID is required"
                ] as JSON)
                return
            }
            
            def holdInstance = Hold.get(params.id)
            if (!holdInstance) {
                render([
                    success: false,
                    error: "Hold not found"
                ] as JSON)
                return
            }
            
            def holdInfo = [
                id: holdInstance.id,
                holdAmount: holdInstance.holdAmount,
                holdType: holdInstance.holdType?.description,
                status: holdInstance.status?.description,
                placedDate: holdInstance.placedDate?.format('yyyy-MM-dd'),
                placedBy: holdInstance.placedBy?.username,
                expiryDate: holdInstance.expiryDate?.format('yyyy-MM-dd'),
                releasedDate: holdInstance.releasedDate?.format('yyyy-MM-dd'),
                releasedBy: holdInstance.releasedBy?.username,
                releaseReason: holdInstance.releaseReason,
                reason: holdInstance.reason,
                accountNo: holdInstance.deposit?.acctNo,
                customerName: holdInstance.deposit?.customer?.displayName
            ]
            
            render([
                success: true,
                holdInfo: holdInfo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting hold info: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get hold information: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get holds for account
     * @return JSON response with account holds
     */
    def getAccountHolds() {
        try {
            if (!params.id) {
                render([
                    success: false,
                    error: "Deposit ID is required"
                ] as JSON)
                return
            }
            
            def deposit = Deposit.get(params.id)
            if (!deposit) {
                render([
                    success: false,
                    error: "Deposit account not found"
                ] as JSON)
                return
            }
            
            def holds = Hold.findAllByDeposit(deposit, [sort: 'placedDate', order: 'desc'])
            
            def holdData = holds.collect { hold ->
                [
                    id: hold.id,
                    holdAmount: hold.holdAmount,
                    holdType: hold.holdType?.description,
                    status: hold.status?.description,
                    placedDate: hold.placedDate?.format('yyyy-MM-dd'),
                    placedBy: hold.placedBy?.username,
                    expiryDate: hold.expiryDate?.format('yyyy-MM-dd'),
                    reason: hold.reason,
                    isActive: hold.status?.id == 1,
                    canRelease: hold.status?.id == 1
                ]
            }
            
            def totalActiveHolds = holds.findAll { it.status?.id == 1 }.sum { it.holdAmount } ?: 0.0
            
            render([
                success: true,
                holds: holdData,
                totalActiveHolds: totalActiveHolds,
                accountNo: deposit.acctNo
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting account holds: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get account holds: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get hold types
     * @return JSON response with hold types
     */
    def getHoldTypes() {
        try {
            def holdTypes = HoldType.list()
            
            def holdTypeData = holdTypes.collect { holdType ->
                [
                    id: holdType.id,
                    code: holdType.code,
                    description: holdType.description,
                    isActive: holdType.status?.id == 2
                ]
            }
            
            render([
                success: true,
                holdTypes: holdTypeData
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting hold types: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get hold types: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Create hold transaction
     * @param hold The hold instance
     * @param action The action (PLACE, RELEASE, ADJUST)
     * @param amount The amount (optional for adjustments)
     */
    private void createHoldTransaction(Hold hold, String action, Double amount = null) {
        try {
            def txnAmount = amount ?: hold.holdAmount
            def txnCode = getHoldTransactionCode(action)
            def description = getHoldTransactionDescription(action, hold)
            
            def txn = new TxnFile(
                acctNo: hold.deposit.acctNo,
                branch: hold.deposit.branch,
                currency: hold.deposit.product.currency,
                depAcct: hold.deposit,
                status: ConfigItemStatus.get(2),
                txnAmt: txnAmount,
                txnCode: txnCode,
                txnDate: hold.deposit.branch.runDate,
                txnDescription: description,
                txnParticulars: "Hold ${action} - ${hold.reason}",
                txnRef: "HOLD-${hold.id}-${action}",
                txnTimestamp: new Date().toTimestamp(),
                user: UserMaster.get(session.user_id),
                txnType: TxnType.findByCode('HOLD'),
                txnTemplate: TxnTemplate.findByCode(txnCode)
            )
            txn.save(flush: true, failOnError: true)
            
            // Create GL breakdown
            GlTransactionService.saveTxnBreakdown(txn.id)
            
        } catch (Exception e) {
            log.error("Error creating hold transaction: ${e.message}", e)
            throw e
        }
    }

    /**
     * Get transaction code for hold action
     * @param action The hold action
     * @return Transaction code
     */
    private String getHoldTransactionCode(String action) {
        switch (action) {
            case 'PLACE':
                return 'HLDPL'
            case 'RELEASE':
                return 'HLDRL'
            case 'ADJUST':
                return 'HLDAJ'
            default:
                return 'HLDPL'
        }
    }

    /**
     * Get transaction description for hold action
     * @param action The hold action
     * @param hold The hold instance
     * @return Transaction description
     */
    private String getHoldTransactionDescription(String action, Hold hold) {
        switch (action) {
            case 'PLACE':
                return "Hold Placed - ${hold.holdType?.description}"
            case 'RELEASE':
                return "Hold Released - ${hold.holdType?.description}"
            case 'ADJUST':
                return "Hold Adjusted - ${hold.holdType?.description}"
            default:
                return "Hold Transaction - ${hold.holdType?.description}"
        }
    }

    /**
     * Handle not found scenarios
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = "Hold not found |error|alert"
                redirect(controller: "deposit", action: "index")
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
