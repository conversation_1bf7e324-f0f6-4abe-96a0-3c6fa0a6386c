package org.icbs.api

/**
 * ARCHITECTURE MODERNIZATION: API Documentation Controller
 * Provides interactive API documentation using OpenAPI/Swagger specification
 */
class ApiDocumentationController {

    def index() {
        render(view: 'index', model: [
            apiVersion: 'v1',
            title: 'QwikBanka API Documentation',
            description: 'Modern REST API for QwikBanka Core Banking System'
        ])
    }

    /**
     * Generate OpenAPI 3.0 specification
     */
    def openapi() {
        def spec = [
            openapi: '3.0.3',
            info: [
                title: 'QwikBanka Core Banking API',
                description: 'Modern REST API for QwikBanka Core Banking System with comprehensive customer, deposit, and loan management capabilities.',
                version: '1.0.0',
                contact: [
                    name: 'QwikBanka API Support',
                    email: '<EMAIL>'
                ],
                license: [
                    name: 'Proprietary',
                    url: 'https://qwikbanka.com/license'
                ]
            ],
            servers: [
                [
                    url: 'https://api.qwikbanka.com/v1',
                    description: 'Production server'
                ],
                [
                    url: 'https://staging-api.qwikbanka.com/v1',
                    description: 'Staging server'
                ],
                [
                    url: 'http://localhost:8080/api/v1',
                    description: 'Development server'
                ]
            ],
            paths: getApiPaths(),
            components: getApiComponents(),
            security: [
                [
                    bearerAuth: []
                ]
            ]
        ]

        render(contentType: 'application/json') {
            spec
        }
    }

    /**
     * Get API paths specification
     */
    private Map getApiPaths() {
        return [
            '/customers': [
                get: [
                    summary: 'List customers',
                    description: 'Retrieve a paginated list of customers with optional filtering',
                    tags: ['Customers'],
                    parameters: [
                        [
                            name: 'branchId',
                            in: 'query',
                            description: 'Filter by branch ID',
                            required: false,
                            schema: [type: 'integer', format: 'int64']
                        ],
                        [
                            name: 'status',
                            in: 'query',
                            description: 'Filter by customer status',
                            required: false,
                            schema: [type: 'string', enum: ['ACTIVE', 'INACTIVE', 'PENDING_APPROVAL']]
                        ],
                        [
                            name: 'offset',
                            in: 'query',
                            description: 'Number of records to skip',
                            required: false,
                            schema: [type: 'integer', minimum: 0, default: 0]
                        ],
                        [
                            name: 'max',
                            in: 'query',
                            description: 'Maximum number of records to return',
                            required: false,
                            schema: [type: 'integer', minimum: 1, maximum: 100, default: 20]
                        ]
                    ],
                    responses: [
                        '200': [
                            description: 'Successful response',
                            content: [
                                'application/json': [
                                    schema: ['$ref': '#/components/schemas/CustomerListResponse']
                                ]
                            ]
                        ],
                        '400': [
                            description: 'Bad request',
                            content: [
                                'application/json': [
                                    schema: ['$ref': '#/components/schemas/ErrorResponse']
                                ]
                            ]
                        ],
                        '401': [
                            description: 'Unauthorized',
                            content: [
                                'application/json': [
                                    schema: ['$ref': '#/components/schemas/ErrorResponse']
                                ]
                            ]
                        ],
                        '500': [
                            description: 'Internal server error',
                            content: [
                                'application/json': [
                                    schema: ['$ref': '#/components/schemas/ErrorResponse']
                                ]
                            ]
                        ]
                    ]
                ],
                post: [
                    summary: 'Create customer',
                    description: 'Create a new customer in the system',
                    tags: ['Customers'],
                    requestBody: [
                        required: true,
                        content: [
                            'application/json': [
                                schema: ['$ref': '#/components/schemas/CreateCustomerRequest']
                            ]
                        ]
                    ],
                    responses: [
                        '201': [
                            description: 'Customer created successfully',
                            content: [
                                'application/json': [
                                    schema: ['$ref': '#/components/schemas/CustomerResponse']
                                ]
                            ]
                        ],
                        '400': [
                            description: 'Bad request - validation failed',
                            content: [
                                'application/json': [
                                    schema: ['$ref': '#/components/schemas/ErrorResponse']
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            '/customers/{id}': [
                get: [
                    summary: 'Get customer by ID',
                    description: 'Retrieve detailed information about a specific customer',
                    tags: ['Customers'],
                    parameters: [
                        [
                            name: 'id',
                            in: 'path',
                            description: 'Customer ID',
                            required: true,
                            schema: [type: 'integer', format: 'int64']
                        ]
                    ],
                    responses: [
                        '200': [
                            description: 'Successful response',
                            content: [
                                'application/json': [
                                    schema: ['$ref': '#/components/schemas/CustomerResponse']
                                ]
                            ]
                        ],
                        '404': [
                            description: 'Customer not found',
                            content: [
                                'application/json': [
                                    schema: ['$ref': '#/components/schemas/ErrorResponse']
                                ]
                            ]
                        ]
                    ]
                ],
                put: [
                    summary: 'Update customer',
                    description: 'Update an existing customer\'s information',
                    tags: ['Customers'],
                    parameters: [
                        [
                            name: 'id',
                            in: 'path',
                            description: 'Customer ID',
                            required: true,
                            schema: [type: 'integer', format: 'int64']
                        ]
                    ],
                    requestBody: [
                        required: true,
                        content: [
                            'application/json': [
                                schema: ['$ref': '#/components/schemas/UpdateCustomerRequest']
                            ]
                        ]
                    ],
                    responses: [
                        '200': [
                            description: 'Customer updated successfully',
                            content: [
                                'application/json': [
                                    schema: ['$ref': '#/components/schemas/CustomerResponse']
                                ]
                            ]
                        ],
                        '404': [
                            description: 'Customer not found'
                        ],
                        '400': [
                            description: 'Bad request - validation failed'
                        ]
                    ]
                ]
            ],
            '/customers/{id}/activate': [
                post: [
                    summary: 'Activate customer',
                    description: 'Activate a pending customer account',
                    tags: ['Customers'],
                    parameters: [
                        [
                            name: 'id',
                            in: 'path',
                            description: 'Customer ID',
                            required: true,
                            schema: [type: 'integer', format: 'int64']
                        ]
                    ],
                    responses: [
                        '200': [
                            description: 'Customer activated successfully',
                            content: [
                                'application/json': [
                                    schema: ['$ref': '#/components/schemas/CustomerResponse']
                                ]
                            ]
                        ],
                        '404': [
                            description: 'Customer not found'
                        ],
                        '400': [
                            description: 'Invalid customer state or business rule violation'
                        ]
                    ]
                ]
            ],
            '/customers/search': [
                get: [
                    summary: 'Search customers',
                    description: 'Search customers by name, customer number, or other criteria',
                    tags: ['Customers'],
                    parameters: [
                        [
                            name: 'q',
                            in: 'query',
                            description: 'Search term',
                            required: true,
                            schema: [type: 'string', minLength: 2]
                        ],
                        [
                            name: 'offset',
                            in: 'query',
                            description: 'Number of records to skip',
                            required: false,
                            schema: [type: 'integer', minimum: 0, default: 0]
                        ],
                        [
                            name: 'max',
                            in: 'query',
                            description: 'Maximum number of records to return',
                            required: false,
                            schema: [type: 'integer', minimum: 1, maximum: 100, default: 20]
                        ]
                    ],
                    responses: [
                        '200': [
                            description: 'Search results',
                            content: [
                                'application/json': [
                                    schema: ['$ref': '#/components/schemas/CustomerSearchResponse']
                                ]
                            ]
                        ],
                        '400': [
                            description: 'Invalid search term'
                        ]
                    ]
                ]
            ]
        ]
    }

    /**
     * Get API components (schemas, security schemes, etc.)
     */
    private Map getApiComponents() {
        return [
            schemas: [
                Customer: [
                    type: 'object',
                    properties: [
                        id: [type: 'integer', format: 'int64', description: 'Unique customer identifier'],
                        customerNumber: [type: 'string', description: 'Customer account number'],
                        firstName: [type: 'string', description: 'Customer first name'],
                        middleName: [type: 'string', description: 'Customer middle name'],
                        lastName: [type: 'string', description: 'Customer last name'],
                        displayName: [type: 'string', description: 'Full display name'],
                        email: [type: 'string', format: 'email', description: 'Customer email address'],
                        phoneNumber: [type: 'string', description: 'Customer phone number'],
                        birthDate: [type: 'string', format: 'date', description: 'Customer birth date'],
                        identificationNumber: [type: 'string', description: 'Government ID number'],
                        branch: ['$ref': '#/components/schemas/Branch'],
                        status: ['$ref': '#/components/schemas/Status'],
                        type: ['$ref': '#/components/schemas/CustomerType'],
                        dateCreated: [type: 'string', format: 'date-time'],
                        lastUpdated: [type: 'string', format: 'date-time']
                    ]
                ],
                Branch: [
                    type: 'object',
                    properties: [
                        id: [type: 'integer', format: 'int64'],
                        name: [type: 'string'],
                        code: [type: 'string']
                    ]
                ],
                Status: [
                    type: 'object',
                    properties: [
                        id: [type: 'integer', format: 'int64'],
                        name: [type: 'string']
                    ]
                ],
                CustomerType: [
                    type: 'object',
                    properties: [
                        id: [type: 'integer', format: 'int64'],
                        name: [type: 'string']
                    ]
                ],
                CreateCustomerRequest: [
                    type: 'object',
                    required: ['firstName', 'lastName', 'branchId', 'identificationNumber'],
                    properties: [
                        firstName: [type: 'string', minLength: 1, maxLength: 50],
                        middleName: [type: 'string', maxLength: 50],
                        lastName: [type: 'string', minLength: 1, maxLength: 50],
                        email: [type: 'string', format: 'email'],
                        phoneNumber: [type: 'string'],
                        birthDate: [type: 'string', format: 'date'],
                        identificationNumber: [type: 'string', minLength: 1],
                        branchId: [type: 'integer', format: 'int64'],
                        customerType: [type: 'string'],
                        taxExempt: [type: 'boolean', default: false]
                    ]
                ],
                UpdateCustomerRequest: [
                    type: 'object',
                    properties: [
                        firstName: [type: 'string', minLength: 1, maxLength: 50],
                        middleName: [type: 'string', maxLength: 50],
                        lastName: [type: 'string', minLength: 1, maxLength: 50],
                        email: [type: 'string', format: 'email'],
                        phoneNumber: [type: 'string']
                    ]
                ],
                CustomerListResponse: [
                    type: 'object',
                    properties: [
                        data: [
                            type: 'array',
                            items: ['$ref': '#/components/schemas/Customer']
                        ],
                        pagination: ['$ref': '#/components/schemas/Pagination'],
                        meta: ['$ref': '#/components/schemas/Meta']
                    ]
                ],
                CustomerResponse: [
                    type: 'object',
                    properties: [
                        data: ['$ref': '#/components/schemas/Customer'],
                        message: [type: 'string'],
                        meta: ['$ref': '#/components/schemas/Meta']
                    ]
                ],
                CustomerSearchResponse: [
                    type: 'object',
                    properties: [
                        data: [
                            type: 'array',
                            items: ['$ref': '#/components/schemas/Customer']
                        ],
                        pagination: ['$ref': '#/components/schemas/Pagination'],
                        meta: [
                            allOf: [
                                ['$ref': '#/components/schemas/Meta'],
                                [
                                    type: 'object',
                                    properties: [
                                        searchTerm: [type: 'string']
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                Pagination: [
                    type: 'object',
                    properties: [
                        offset: [type: 'integer'],
                        max: [type: 'integer'],
                        total: [type: 'integer'],
                        hasMore: [type: 'boolean']
                    ]
                ],
                Meta: [
                    type: 'object',
                    properties: [
                        timestamp: [type: 'string', format: 'date-time'],
                        version: [type: 'string']
                    ]
                ],
                ErrorResponse: [
                    type: 'object',
                    properties: [
                        error: [
                            type: 'object',
                            properties: [
                                status: [type: 'integer'],
                                message: [type: 'string'],
                                details: [type: 'string'],
                                timestamp: [type: 'string', format: 'date-time'],
                                path: [type: 'string']
                            ]
                        ]
                    ]
                ]
            ],
            securitySchemes: [
                bearerAuth: [
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT'
                ]
            ]
        ]
    }
}
