package org.icbs.api.v1

import grails.converters.JSON
import grails.gorm.transactions.Transactional
import org.springframework.http.HttpStatus
import org.icbs.cif.Customer
import org.icbs.domain.CustomerDomainService
import org.icbs.domain.CreateCustomerCommand
import org.icbs.domain.UpdateCustomerCommand

/**
 * ARCHITECTURE MODERNIZATION: REST API Controller for Customer Management
 * Provides modern RESTful API endpoints for customer operations
 */
class CustomerApiController {

    static responseFormats = ['json']
    static allowedMethods = [
        index: 'GET',
        show: 'GET', 
        save: 'POST',
        update: 'PUT',
        delete: 'DELETE',
        search: 'GET',
        activate: 'POST',
        deactivate: 'POST'
    ]

    def customerDomainService
    def optimizedCustomerService

    /**
     * GET /api/v1/customers
     * List customers with pagination and filtering
     */
    def index() {
        try {
            def params = request.JSON ?: params
            def branchId = params.branchId as Long
            def status = params.status
            def offset = (params.offset ?: 0) as Integer
            def max = Math.min((params.max ?: 20) as Integer, 100) // Limit max to 100
            
            def customers = optimizedCustomerService.findCustomersOptimized(branchId, status, offset, max)
            def totalCount = Customer.countByBranchIdAndStatus(branchId, status)
            
            def response = [
                data: customers.collect { customer ->
                    [
                        id: customer.id,
                        customerNumber: customer.customerId,
                        name: customer.displayName,
                        email: customer.email,
                        phoneNumber: customer.phoneNumber,
                        branch: [
                            id: customer.branch?.id,
                            name: customer.branch?.branchName
                        ],
                        status: [
                            id: customer.status?.id,
                            name: customer.status?.itemValue
                        ],
                        type: [
                            id: customer.type?.id,
                            name: customer.type?.itemValue
                        ],
                        dateCreated: customer.dateCreated,
                        lastUpdated: customer.lastUpdatedAt
                    ]
                },
                pagination: [
                    offset: offset,
                    max: max,
                    total: totalCount,
                    hasMore: (offset + max) < totalCount
                ],
                meta: [
                    timestamp: new Date(),
                    version: 'v1'
                ]
            ]
            
            render(status: HttpStatus.OK, contentType: 'application/json') {
                response
            }
            
        } catch (Exception e) {
            log.error("Error listing customers: ${e.message}", e)
            renderError(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve customers", e.message)
        }
    }

    /**
     * GET /api/v1/customers/{id}
     * Get customer by ID
     */
    def show(Long id) {
        try {
            def customer = optimizedCustomerService.findCustomerByIdOptimized(id)
            
            if (!customer) {
                renderError(HttpStatus.NOT_FOUND, "Customer not found", "Customer with ID ${id} does not exist")
                return
            }
            
            def response = [
                data: buildCustomerDetailResponse(customer),
                meta: [
                    timestamp: new Date(),
                    version: 'v1'
                ]
            ]
            
            render(status: HttpStatus.OK, contentType: 'application/json') {
                response
            }
            
        } catch (Exception e) {
            log.error("Error retrieving customer ${id}: ${e.message}", e)
            renderError(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to retrieve customer", e.message)
        }
    }

    /**
     * POST /api/v1/customers
     * Create new customer
     */
    @Transactional
    def save() {
        try {
            def requestData = request.JSON
            
            // Validate required fields
            def validationErrors = validateCustomerCreation(requestData)
            if (validationErrors) {
                renderError(HttpStatus.BAD_REQUEST, "Validation failed", validationErrors)
                return
            }
            
            // Create command object
            def command = new CreateCustomerCommand(
                firstName: requestData.firstName,
                middleName: requestData.middleName,
                lastName: requestData.lastName,
                birthDate: requestData.birthDate ? Date.parse('yyyy-MM-dd', requestData.birthDate) : null,
                identificationNumber: requestData.identificationNumber,
                email: requestData.email,
                phoneNumber: requestData.phoneNumber,
                branchId: requestData.branchId as Long,
                customerType: requestData.customerType,
                taxExempt: requestData.taxExempt as Boolean,
                createdBy: getCurrentUser()
            )
            
            // Create customer using domain service
            def customer = customerDomainService.createCustomer(command)
            
            def response = [
                data: buildCustomerDetailResponse(customer),
                message: "Customer created successfully",
                meta: [
                    timestamp: new Date(),
                    version: 'v1'
                ]
            ]
            
            render(status: HttpStatus.CREATED, contentType: 'application/json') {
                response
            }
            
        } catch (BusinessRuleViolationException e) {
            renderError(HttpStatus.BAD_REQUEST, "Business rule violation", e.message)
        } catch (Exception e) {
            log.error("Error creating customer: ${e.message}", e)
            renderError(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to create customer", e.message)
        }
    }

    /**
     * PUT /api/v1/customers/{id}
     * Update customer
     */
    @Transactional
    def update(Long id) {
        try {
            def requestData = request.JSON
            
            // Create command object
            def command = new UpdateCustomerCommand(
                customerId: id,
                firstName: requestData.firstName,
                middleName: requestData.middleName,
                lastName: requestData.lastName,
                email: requestData.email,
                phoneNumber: requestData.phoneNumber,
                updatedBy: getCurrentUser()
            )
            
            // Update customer using domain service
            def customer = customerDomainService.updateCustomer(command)
            
            def response = [
                data: buildCustomerDetailResponse(customer),
                message: "Customer updated successfully",
                meta: [
                    timestamp: new Date(),
                    version: 'v1'
                ]
            ]
            
            render(status: HttpStatus.OK, contentType: 'application/json') {
                response
            }
            
        } catch (CustomerNotFoundException e) {
            renderError(HttpStatus.NOT_FOUND, "Customer not found", e.message)
        } catch (BusinessRuleViolationException e) {
            renderError(HttpStatus.BAD_REQUEST, "Business rule violation", e.message)
        } catch (Exception e) {
            log.error("Error updating customer ${id}: ${e.message}", e)
            renderError(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to update customer", e.message)
        }
    }

    /**
     * POST /api/v1/customers/{id}/activate
     * Activate customer
     */
    @Transactional
    def activate(Long id) {
        try {
            def customer = customerDomainService.activateCustomer(id, getCurrentUser())
            
            def response = [
                data: buildCustomerDetailResponse(customer),
                message: "Customer activated successfully",
                meta: [
                    timestamp: new Date(),
                    version: 'v1'
                ]
            ]
            
            render(status: HttpStatus.OK, contentType: 'application/json') {
                response
            }
            
        } catch (CustomerNotFoundException e) {
            renderError(HttpStatus.NOT_FOUND, "Customer not found", e.message)
        } catch (InvalidCustomerStateException e) {
            renderError(HttpStatus.BAD_REQUEST, "Invalid customer state", e.message)
        } catch (BusinessRuleViolationException e) {
            renderError(HttpStatus.BAD_REQUEST, "Business rule violation", e.message)
        } catch (Exception e) {
            log.error("Error activating customer ${id}: ${e.message}", e)
            renderError(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to activate customer", e.message)
        }
    }

    /**
     * POST /api/v1/customers/{id}/deactivate
     * Deactivate customer
     */
    @Transactional
    def deactivate(Long id) {
        try {
            def requestData = request.JSON
            def reason = requestData.reason ?: "No reason provided"
            
            def customer = customerDomainService.deactivateCustomer(id, reason, getCurrentUser())
            
            def response = [
                data: buildCustomerDetailResponse(customer),
                message: "Customer deactivated successfully",
                meta: [
                    timestamp: new Date(),
                    version: 'v1'
                ]
            ]
            
            render(status: HttpStatus.OK, contentType: 'application/json') {
                response
            }
            
        } catch (CustomerNotFoundException e) {
            renderError(HttpStatus.NOT_FOUND, "Customer not found", e.message)
        } catch (BusinessRuleViolationException e) {
            renderError(HttpStatus.BAD_REQUEST, "Business rule violation", e.message)
        } catch (Exception e) {
            log.error("Error deactivating customer ${id}: ${e.message}", e)
            renderError(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to deactivate customer", e.message)
        }
    }

    /**
     * GET /api/v1/customers/search
     * Search customers
     */
    def search() {
        try {
            def searchTerm = params.q
            def offset = (params.offset ?: 0) as Integer
            def max = Math.min((params.max ?: 20) as Integer, 100)
            
            if (!searchTerm || searchTerm.trim().isEmpty()) {
                renderError(HttpStatus.BAD_REQUEST, "Invalid search term", "Search term is required")
                return
            }
            
            def searchResult = optimizedCustomerService.searchCustomersOptimized(searchTerm, offset, max)
            
            def response = [
                data: searchResult.customers.collect { customer ->
                    [
                        id: customer.id,
                        customerNumber: customer.customerId,
                        name: customer.displayName,
                        email: customer.email,
                        branch: customer.branch?.branchName,
                        status: customer.status?.itemValue
                    ]
                },
                pagination: [
                    offset: offset,
                    max: max,
                    total: searchResult.totalCount,
                    hasMore: searchResult.hasMore
                ],
                meta: [
                    searchTerm: searchTerm,
                    timestamp: new Date(),
                    version: 'v1'
                ]
            ]
            
            render(status: HttpStatus.OK, contentType: 'application/json') {
                response
            }
            
        } catch (Exception e) {
            log.error("Error searching customers: ${e.message}", e)
            renderError(HttpStatus.INTERNAL_SERVER_ERROR, "Search failed", e.message)
        }
    }

    /**
     * Build detailed customer response
     */
    private Map buildCustomerDetailResponse(Customer customer) {
        return [
            id: customer.id,
            customerNumber: customer.customerId,
            firstName: customer.name1,
            middleName: customer.name2,
            lastName: customer.name3,
            displayName: customer.displayName,
            email: customer.email,
            phoneNumber: customer.phoneNumber,
            birthDate: customer.birthDate?.format('yyyy-MM-dd'),
            identificationNumber: customer.identificationNumber,
            branch: [
                id: customer.branch?.id,
                name: customer.branch?.branchName,
                code: customer.branch?.branchCode
            ],
            status: [
                id: customer.status?.id,
                name: customer.status?.itemValue
            ],
            type: [
                id: customer.type?.id,
                name: customer.type?.itemValue
            ],
            addresses: customer.addresses?.collect { address ->
                [
                    id: address.id,
                    type: address.type?.itemValue,
                    address: address.address,
                    city: address.city,
                    province: address.province,
                    zipCode: address.zipCode
                ]
            },
            contacts: customer.contacts?.collect { contact ->
                [
                    id: contact.id,
                    type: contact.type?.itemValue,
                    value: contact.contactValue
                ]
            },
            dateCreated: customer.dateCreated,
            lastUpdated: customer.lastUpdatedAt,
            createdBy: customer.createdBy,
            lastUpdatedBy: customer.lastUpdatedBy
        ]
    }

    /**
     * Validate customer creation request
     */
    private List validateCustomerCreation(Map requestData) {
        def errors = []
        
        if (!requestData.firstName) {
            errors << "First name is required"
        }
        if (!requestData.lastName) {
            errors << "Last name is required"
        }
        if (!requestData.branchId) {
            errors << "Branch ID is required"
        }
        if (!requestData.identificationNumber) {
            errors << "Identification number is required"
        }
        
        return errors
    }

    /**
     * Render error response
     */
    private void renderError(HttpStatus status, String message, Object details = null) {
        def errorResponse = [
            error: [
                status: status.value(),
                message: message,
                details: details,
                timestamp: new Date(),
                path: request.requestURI
            ]
        ]
        
        render(status: status, contentType: 'application/json') {
            errorResponse
        }
    }

    /**
     * Get current user from session
     */
    private String getCurrentUser() {
        return session.userMaster?.username ?: 'system'
    }
}
