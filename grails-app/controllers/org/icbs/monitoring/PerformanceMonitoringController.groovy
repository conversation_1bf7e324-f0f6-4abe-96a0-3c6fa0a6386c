package org.icbs.monitoring

import grails.converters.JSON

/**
 * PERFORMANCE OPTIMIZATION: Controller for performance monitoring dashboard and APIs
 */
class PerformanceMonitoringController {

    def performanceMonitoringService
    def cacheManagementService

    /**
     * PERFORMANCE OPTIMIZATION: Main monitoring dashboard
     */
    def index() {
        try {
            def summary = performanceMonitoringService.getPerformanceSummary()
            def alerts = performanceMonitoringService.getPerformanceAlerts()
            
            render(view: 'index', model: [
                summary: summary,
                alerts: alerts,
                refreshInterval: 30000 // 30 seconds
            ])
            
        } catch (Exception e) {
            log.error("Error loading monitoring dashboard: ${e.message}", e)
            flash.error = "Error loading monitoring dashboard"
            render(view: 'error')
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get current metrics as JSON
     */
    def getCurrentMetrics() {
        try {
            def metrics = performanceMonitoringService.getCurrentMetrics()
            render(contentType: 'application/json') {
                metrics
            }
        } catch (Exception e) {
            log.error("Error getting current metrics: ${e.message}", e)
            render(status: 500, contentType: 'application/json') {
                [error: e.message]
            }
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get performance summary
     */
    def getSummary() {
        try {
            def summary = performanceMonitoringService.getPerformanceSummary()
            render(contentType: 'application/json') {
                summary
            }
        } catch (Exception e) {
            log.error("Error getting performance summary: ${e.message}", e)
            render(status: 500, contentType: 'application/json') {
                [error: e.message]
            }
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get performance trends
     */
    def getTrends() {
        try {
            def hours = params.hours ? params.hours as Integer : 24
            def trends = performanceMonitoringService.getPerformanceTrends(hours)
            
            render(contentType: 'application/json') {
                trends
            }
        } catch (Exception e) {
            log.error("Error getting performance trends: ${e.message}", e)
            render(status: 500, contentType: 'application/json') {
                [error: e.message]
            }
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get performance alerts
     */
    def getAlerts() {
        try {
            def alerts = performanceMonitoringService.getPerformanceAlerts()
            render(contentType: 'application/json') {
                [
                    alerts: alerts,
                    count: alerts.size(),
                    critical: alerts.count { it.type == 'CRITICAL' },
                    warning: alerts.count { it.type == 'WARNING' },
                    timestamp: new Date()
                ]
            }
        } catch (Exception e) {
            log.error("Error getting performance alerts: ${e.message}", e)
            render(status: 500, contentType: 'application/json') {
                [error: e.message]
            }
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Cache management dashboard
     */
    def cacheManagement() {
        try {
            def cacheStats = cacheManagementService.getCacheStatistics()
            def cacheSummary = cacheManagementService.getCacheSummary()
            def cacheHealth = cacheManagementService.getCacheHealth()
            
            render(view: 'cacheManagement', model: [
                cacheStats: cacheStats,
                cacheSummary: cacheSummary,
                cacheHealth: cacheHealth
            ])
            
        } catch (Exception e) {
            log.error("Error loading cache management dashboard: ${e.message}", e)
            flash.error = "Error loading cache management dashboard"
            render(view: 'error')
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get cache statistics
     */
    def getCacheStats() {
        try {
            def stats = cacheManagementService.getCacheStatistics()
            render(contentType: 'application/json') {
                stats
            }
        } catch (Exception e) {
            log.error("Error getting cache statistics: ${e.message}", e)
            render(status: 500, contentType: 'application/json') {
                [error: e.message]
            }
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Clear specific cache
     */
    def clearCache() {
        try {
            def cacheName = params.cacheName
            if (!cacheName) {
                render(status: 400, contentType: 'application/json') {
                    [error: 'Cache name is required']
                }
                return
            }
            
            cacheManagementService.clearCache(cacheName)
            
            render(contentType: 'application/json') {
                [
                    success: true,
                    message: "Cache '${cacheName}' cleared successfully",
                    timestamp: new Date()
                ]
            }
        } catch (Exception e) {
            log.error("Error clearing cache: ${e.message}", e)
            render(status: 500, contentType: 'application/json') {
                [error: e.message]
            }
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Clear all caches
     */
    def clearAllCaches() {
        try {
            cacheManagementService.clearAllCaches()
            
            render(contentType: 'application/json') {
                [
                    success: true,
                    message: "All caches cleared successfully",
                    timestamp: new Date()
                ]
            }
        } catch (Exception e) {
            log.error("Error clearing all caches: ${e.message}", e)
            render(status: 500, contentType: 'application/json') {
                [error: e.message]
            }
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Warm up caches
     */
    def warmUpCaches() {
        try {
            cacheManagementService.warmUpCaches()
            
            render(contentType: 'application/json') {
                [
                    success: true,
                    message: "Cache warm-up initiated successfully",
                    timestamp: new Date()
                ]
            }
        } catch (Exception e) {
            log.error("Error warming up caches: ${e.message}", e)
            render(status: 500, contentType: 'application/json') {
                [error: e.message]
            }
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Reset request metrics
     */
    def resetMetrics() {
        try {
            performanceMonitoringService.resetRequestMetrics()
            
            render(contentType: 'application/json') {
                [
                    success: true,
                    message: "Request metrics reset successfully",
                    timestamp: new Date()
                ]
            }
        } catch (Exception e) {
            log.error("Error resetting metrics: ${e.message}", e)
            render(status: 500, contentType: 'application/json') {
                [error: e.message]
            }
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Health check endpoint
     */
    def healthCheck() {
        try {
            def summary = performanceMonitoringService.getPerformanceSummary()
            def cacheHealth = cacheManagementService.getCacheHealth()
            
            def overallStatus = 'HEALTHY'
            if (summary.status == 'CRITICAL' || cacheHealth.status == 'ERROR') {
                overallStatus = 'CRITICAL'
            } else if (summary.status == 'WARNING' || cacheHealth.status == 'WARNING') {
                overallStatus = 'WARNING'
            }
            
            def healthData = [
                status: overallStatus,
                application: summary,
                cache: cacheHealth,
                timestamp: new Date(),
                uptime: java.lang.management.ManagementFactory.getRuntimeMXBean().uptime
            ]
            
            // Set appropriate HTTP status
            def httpStatus = 200
            if (overallStatus == 'CRITICAL') {
                httpStatus = 503 // Service Unavailable
            } else if (overallStatus == 'WARNING') {
                httpStatus = 200 // OK but with warnings
            }
            
            render(status: httpStatus, contentType: 'application/json') {
                healthData
            }
            
        } catch (Exception e) {
            log.error("Error in health check: ${e.message}", e)
            render(status: 500, contentType: 'application/json') {
                [
                    status: 'ERROR',
                    error: e.message,
                    timestamp: new Date()
                ]
            }
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Export performance data
     */
    def exportData() {
        try {
            def format = params.format ?: 'json'
            def hours = params.hours ? params.hours as Integer : 24
            
            def data = [
                summary: performanceMonitoringService.getPerformanceSummary(),
                trends: performanceMonitoringService.getPerformanceTrends(hours),
                alerts: performanceMonitoringService.getPerformanceAlerts(),
                cacheStats: cacheManagementService.getCacheStatistics(),
                exportTime: new Date()
            ]
            
            if (format.toLowerCase() == 'json') {
                response.setHeader("Content-Disposition", "attachment; filename=\"performance_data_${new Date().format('yyyyMMdd_HHmmss')}.json\"")
                render(contentType: 'application/json') {
                    data
                }
            } else {
                render(status: 400, contentType: 'application/json') {
                    [error: 'Unsupported format. Only JSON is currently supported.']
                }
            }
            
        } catch (Exception e) {
            log.error("Error exporting performance data: ${e.message}", e)
            render(status: 500, contentType: 'application/json') {
                [error: e.message]
            }
        }
    }
}
