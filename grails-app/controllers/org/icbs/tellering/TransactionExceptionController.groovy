package org.icbs.tellering

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnTellerCash
import org.icbs.tellering.TxnCashCheckBlotter
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.PolicyException
import org.icbs.lov.ConfigItemStatus
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import grails.converters.JSON
import groovy.sql.Sql
import javax.sql.DataSource

/**
 * REFACTORED: Transaction Exception Controller
 * Extracted from TelleringController.groovy (7,319 lines)
 * Handles all transaction exception and policy operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class TransactionExceptionController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def policyService
    def userMasterService
    def glTransactionService
    def auditLogService
    DataSource dataSource
    
    static allowedMethods = [
        takeAction: "POST",
        receiveTellerCashTransfer: "GET",
        saveTellerCashTransferReceive: "POST",
        receiveTellerCashCancel: "POST",
        viewPendingExceptions: "GET",
        processException: "POST"
    ]
    
    // =====================================================
    // POLICY EXCEPTION OPERATIONS
    // =====================================================
    
    /**
     * Take action on policy exception
     */
    def takeAction() {
        log.info("Taking action on policy exception for transaction: ${params.txnFileInstanceId}")
        
        try {
            def txnFileInstance = TxnFile.get(params.txnFileInstanceId)
            if (!txnFileInstance) {
                notFound()
                return
            }
            
            boolean isApproved = params.isApproved as boolean
            
            // Process policy action
            policyService.takeAction(txnFileInstance, ConfigItemStatus, 'txnFile', isApproved)
            
            // Audit logging
            auditLogService.insert('110', 'TEX01100', 
                "Policy action taken - Transaction: ${txnFileInstance.id}, Approved: ${isApproved}", 
                'TransactionExceptionController', null, null, 'tellering/takeAction', txnFileInstance.id)
            
            flash.message = isApproved ? 
                'Transaction approved successfully|success|alert' : 
                'Transaction rejected|warning|alert'
            
            redirect(controller: 'tellering', action: 'index')
            
        } catch (Exception e) {
            log.error("Error taking action on policy exception", e)
            flash.message = 'Error processing policy action|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * View pending teller cash transfer exceptions
     */
    def receiveTellerCashTransfer() {
        log.info("Viewing pending teller cash transfer exceptions")
        
        try {
            def sql = new Sql(dataSource)
            def query = """
                SELECT A.*, B.id as policy_id  
                FROM txn_file A 
                LEFT JOIN policy_exception B ON A.id = B.record_id  
                WHERE A.txn_type_id = 2 
                  AND A.status_id = 1 
                  AND A.id IN (
                    SELECT record_id 
                    FROM policy_exception 
                    WHERE date_of_action IS NULL 
                      AND requesting_user_id = ?
                  )
            """
            
            def sourceTxn = sql.rows(query, [session.user_id])
            
            // Audit logging
            auditLogService.insert('110', 'TEX01200', 
                "Pending cash transfer exceptions viewed - Count: ${sourceTxn.size()}", 
                'TransactionExceptionController', null, null, 'tellering/receiveTellerCashTransfer', session.user_id)
            
            render(view: '/tellering/receiveTellerCashTransfer', 
                model: [sourceTxn: sourceTxn])
            
        } catch (Exception e) {
            log.error("Error viewing pending cash transfer exceptions", e)
            flash.message = 'Error viewing pending exceptions|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * Save teller cash transfer receive transaction
     */
    def saveTellerCashTransferReceive(TxnTellerCash tc, TxnFile tf) {
        log.info("Processing teller cash transfer receive")
        
        try {
            if (tc.hasErrors() || tf.hasErrors()) {
                flash.message = 'Failed to Receive Teller Cash Transfer|error|alert'
                redirect(action: "receiveTellerCashTransfer")
                return
            }
            
            // Get source transaction and policy exception
            def sourceTf = TxnFile.get(params.txnTemplate)
            def pcx = PolicyException.findByRecordId(params.txnTemplate)
            
            if (!sourceTf || !pcx) {
                flash.message = 'Source transaction or policy exception not found|error|alert'
                redirect(action: "receiveTellerCashTransfer")
                return
            }
            
            // Process the cash transfer receive
            Map receiveResult = processCashTransferReceive(tc, tf, sourceTf, pcx)
            
            if (receiveResult.success) {
                // Update teller balance and GL
                userMasterService.updateTellerBalanceStatus(false)
                glTransactionService.saveTxnBreakdown(tf.id)
                
                // Update policy exception
                policyService.updateException('TxnFile', pcx.id as int, true)
                
                flash.message = 'Teller Cash Transfer (Receive - Success)|success|alert'
                
                // Audit logging
                auditLogService.insert('110', 'TEX01300', 
                    "Cash transfer received successfully - Amount: ${tf.txnAmt}", 
                    'TransactionExceptionController', null, null, 'tellering/saveTellerCashTransferReceive', tf.id)
                
                redirect(action: "receiveTellerCashTransfer")
            } else {
                flash.message = receiveResult.message + '|error|alert'
                redirect(action: "receiveTellerCashTransfer")
            }
            
        } catch (Exception e) {
            log.error("Error processing cash transfer receive", e)
            flash.message = 'Error processing cash transfer receive|error|alert'
            redirect(action: "receiveTellerCashTransfer")
        }
    }
    
    /**
     * Cancel/reject teller cash transfer
     */
    def receiveTellerCashCancel(TxnTellerCash tc) {
        log.info("Cancelling teller cash transfer")
        
        try {
            def tf = TxnFile.get(params.txnTemplate)
            def pcx = PolicyException.findByRecordId(params.txnTemplate)
            
            if (!tf || !pcx) {
                flash.message = 'Transaction or policy exception not found|error|alert'
                redirect(action: "receiveTellerCashTransfer")
                return
            }
            
            if (tc.hasErrors() || tf.hasErrors()) {
                flash.message = 'Failed to cancel teller cash transfer|error|alert'
                render(view: '/tellering/receiveTellerCashTransfer')
                return
            }
            
            // Update policy exception as rejected
            policyService.updateException('TxnFile', pcx.id as int, false)
            
            // Audit logging
            auditLogService.insert('110', 'TEX01400', 
                "Cash transfer cancelled/rejected - Transaction: ${tf.id}", 
                'TransactionExceptionController', null, null, 'tellering/receiveTellerCashCancel', tf.id)
            
            flash.message = 'Teller Cash Transfer (REJECTED)|warning|alert'
            redirect(controller: "tellering", action: "index")
            
        } catch (Exception e) {
            log.error("Error cancelling cash transfer", e)
            flash.message = 'Error cancelling cash transfer|error|alert'
            redirect(controller: "tellering", action: "index")
        }
    }
    
    /**
     * View all pending exceptions
     */
    def viewPendingExceptions() {
        log.info("Viewing all pending exceptions")
        
        try {
            Date fromDate = params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 7
            Date toDate = params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date()
            
            List<PolicyException> pendingExceptions = getPendingExceptions(fromDate, toDate)
            
            // Audit logging
            auditLogService.insert('110', 'TEX01500', 
                "Pending exceptions viewed - Count: ${pendingExceptions.size()}", 
                'TransactionExceptionController', null, null, 'tellering/viewPendingExceptions', session.user_id)
            
            render(view: '/tellering/exceptions/pendingExceptions', 
                model: [
                    pendingExceptions: pendingExceptions,
                    fromDate: fromDate,
                    toDate: toDate
                ])
            
        } catch (Exception e) {
            log.error("Error viewing pending exceptions", e)
            flash.message = 'Error viewing pending exceptions|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * Process specific exception
     */
    def processException() {
        log.info("Processing exception: ${params.exceptionId}")
        
        try {
            if (!params.exceptionId || !params.action) {
                render([success: false, error: 'Exception ID and action are required'] as JSON)
                return
            }
            
            def exception = PolicyException.get(params.exceptionId)
            if (!exception) {
                render([success: false, error: 'Exception not found'] as JSON)
                return
            }
            
            boolean isApproved = params.action == 'approve'
            String comments = params.comments ?: ''
            
            // Process the exception
            Map processResult = processExceptionAction(exception, isApproved, comments)
            
            if (processResult.success) {
                // Audit logging
                auditLogService.insert('110', 'TEX01600', 
                    "Exception processed - ID: ${exception.id}, Action: ${params.action}, Comments: ${comments}", 
                    'TransactionExceptionController', null, null, 'tellering/processException', exception.id)
                
                render([success: true, message: processResult.message] as JSON)
            } else {
                render([success: false, error: processResult.message] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error processing exception", e)
            render([success: false, error: 'Error processing exception'] as JSON)
        }
    }
    
    /**
     * Get exception statistics
     */
    def getExceptionStatistics() {
        log.debug("Getting exception statistics")
        
        try {
            Date fromDate = params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 30
            Date toDate = params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date()
            
            Map statistics = calculateExceptionStatistics(fromDate, toDate)
            
            render(statistics as JSON)
            
        } catch (Exception e) {
            log.error("Error getting exception statistics", e)
            render([error: 'Error getting exception statistics'] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Process cash transfer receive operation
     */
    private Map processCashTransferReceive(TxnTellerCash tc, TxnFile tf, TxnFile sourceTf, PolicyException pcx) {
        Map result = [success: false, message: '']
        
        try {
            def txnBranch = Branch.get(UserMaster.get(session.user_id).branchId)
            def txnUserMaster = sourceTf.user
            def tb = new TxnCashCheckBlotter()
            
            // Set transaction file details
            tf.txnDate = new Date()
            tf.status = ConfigItemStatus.get(2)
            tf.txnAmt = sourceTf.txnAmt
            tf.currency = sourceTf.currency
            tf.txnParticulars = "Cash Transfer Receive from ${sourceTf.user.username}"
            tf.txnRef = sourceTf.txnRef
            tf.user = UserMaster.get(session.user_id)
            tf.branch = txnBranch
            tf.save(flush: true, failOnError: true)
            
            // Create teller blotter entry
            tb.cashOutAmt = 0
            tb.cashInAmt = tf.txnAmt
            tb.checkInAmt = 0
            tb.checkOutAmt = 0
            tb.txnFile = tf
            tb.branch = txnBranch
            tb.currency = sourceTf.currency
            tb.user = UserMaster.get(session.user_id)
            tb.txnParticulars = tf.txnParticulars
            tb.save(flush: true, failOnError: true)
            
            result.success = true
            result.message = 'Cash transfer received successfully'
            
        } catch (Exception e) {
            log.error("Error processing cash transfer receive", e)
            result.message = 'Error processing cash transfer receive'
        }
        
        return result
    }
    
    /**
     * Get pending exceptions
     */
    private List<PolicyException> getPendingExceptions(Date fromDate, Date toDate) {
        try {
            return PolicyException.createCriteria().list {
                isNull("dateOfAction")
                between("dateCreated", fromDate, toDate)
                order("dateCreated", "desc")
                maxResults(500)
            }
        } catch (Exception e) {
            log.error("Error getting pending exceptions", e)
            return []
        }
    }
    
    /**
     * Process exception action
     */
    private Map processExceptionAction(PolicyException exception, boolean isApproved, String comments) {
        Map result = [success: false, message: '']
        
        try {
            // Update exception
            exception.dateOfAction = new Date()
            exception.isApproved = isApproved
            exception.actionComments = comments
            exception.actionBy = UserMaster.get(session.user_id)
            exception.save(flush: true, failOnError: true)
            
            // Update related transaction if applicable
            if (exception.entityType == 'txnFile' && exception.recordId) {
                def txnFile = TxnFile.get(exception.recordId.toLong())
                if (txnFile) {
                    if (isApproved) {
                        txnFile.status = ConfigItemStatus.get(2) // Approved
                    } else {
                        txnFile.status = ConfigItemStatus.get(4) // Rejected
                    }
                    txnFile.save(flush: true, failOnError: true)
                }
            }
            
            result.success = true
            result.message = isApproved ? 'Exception approved successfully' : 'Exception rejected successfully'
            
        } catch (Exception e) {
            log.error("Error processing exception action", e)
            result.message = 'Error processing exception action'
        }
        
        return result
    }
    
    /**
     * Calculate exception statistics
     */
    private Map calculateExceptionStatistics(Date fromDate, Date toDate) {
        Map statistics = [:]
        
        try {
            def sql = new Sql(dataSource)
            
            // Total exceptions
            def totalQuery = """
                SELECT COUNT(*) as total_count
                FROM policy_exception 
                WHERE date_created BETWEEN ? AND ?
            """
            def totalResult = sql.firstRow(totalQuery, [fromDate, toDate])
            statistics.totalExceptions = totalResult.total_count
            
            // Pending exceptions
            def pendingQuery = """
                SELECT COUNT(*) as pending_count
                FROM policy_exception 
                WHERE date_created BETWEEN ? AND ? 
                  AND date_of_action IS NULL
            """
            def pendingResult = sql.firstRow(pendingQuery, [fromDate, toDate])
            statistics.pendingExceptions = pendingResult.pending_count
            
            // Approved exceptions
            def approvedQuery = """
                SELECT COUNT(*) as approved_count
                FROM policy_exception 
                WHERE date_created BETWEEN ? AND ? 
                  AND is_approved = true
            """
            def approvedResult = sql.firstRow(approvedQuery, [fromDate, toDate])
            statistics.approvedExceptions = approvedResult.approved_count
            
            // Rejected exceptions
            def rejectedQuery = """
                SELECT COUNT(*) as rejected_count
                FROM policy_exception 
                WHERE date_created BETWEEN ? AND ? 
                  AND is_approved = false 
                  AND date_of_action IS NOT NULL
            """
            def rejectedResult = sql.firstRow(rejectedQuery, [fromDate, toDate])
            statistics.rejectedExceptions = rejectedResult.rejected_count
            
            // Exceptions by type
            def typeQuery = """
                SELECT exception_code, COUNT(*) as count
                FROM policy_exception 
                WHERE date_created BETWEEN ? AND ?
                GROUP BY exception_code
                ORDER BY count DESC
            """
            statistics.byType = sql.rows(typeQuery, [fromDate, toDate])
            
            // Exceptions by user
            def userQuery = """
                SELECT u.username, COUNT(pe.id) as count
                FROM policy_exception pe
                JOIN user_master u ON pe.requesting_user_id = u.id
                WHERE pe.date_created BETWEEN ? AND ?
                GROUP BY u.username
                ORDER BY count DESC
                LIMIT 10
            """
            statistics.byUser = sql.rows(userQuery, [fromDate, toDate])
            
        } catch (Exception e) {
            log.error("Error calculating exception statistics", e)
            statistics.error = "Error calculating exception statistics"
        }
        
        return statistics
    }
}
