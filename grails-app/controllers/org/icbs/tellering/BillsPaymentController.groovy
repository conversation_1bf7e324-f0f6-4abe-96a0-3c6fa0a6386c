package org.icbs.tellering

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.tellering.TxnBillsPayment
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnCOCI
import org.icbs.tellering.TxnCashCheckBlotter
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.Currency
import org.icbs.admin.TxnTemplate
import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import grails.converters.JSON

/**
 * REFACTORED: Bills Payment Controller
 * Extracted from TelleringController.groovy (7,319 lines)
 * Handles all bills payment operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class BillsPaymentController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def userMasterService
    def glTransactionService
    def auditLogService
    def policyService
    
    static allowedMethods = [
        saveTellerOtherCashReceiptBillsPaymentTxn: "POST",
        saveTellerOtherCheckReceiptBillsPaymentTxn: "POST",
        createTellerOtherCashReceiptBillsPaymentTxn: "GET",
        createTellerOtherCheckReceiptBillsPaymentTxn: "GET"
    ]
    
    // =====================================================
    // CASH BILLS PAYMENT OPERATIONS
    // =====================================================
    
    /**
     * Create teller other cash receipt bills payment transaction
     */
    def createTellerOtherCashReceiptBillsPaymentTxn() {
        log.info("Creating teller other cash receipt bills payment transaction")
        
        try {
            def user = UserMaster.get(session.user_id)
            if (!user.cash) {
                flash.message = 'Error! No cash account defined.|error'
                redirect(controller: 'tellering', action: 'index')
                return
            }
            
            def txnFileInstance = new TxnFile()
            def txnBillsPaymentInstance = new TxnBillsPayment()
            
            // Audit logging
            auditLogService.insert('110', 'BPY01100', 
                "Cash bills payment transaction creation initiated", 
                'BillsPaymentController', null, null, 'tellering/createTellerOtherCashReceiptBillsPaymentTxn', session.user_id)
            
            render(view: '/tellering/txnBillsPayment/create', 
                model: [txnBillsPaymentInstance: txnBillsPaymentInstance])
            
        } catch (Exception e) {
            log.error("Error creating cash bills payment transaction", e)
            flash.message = 'Error creating bills payment transaction|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * Save teller other cash receipt bills payment transaction
     */
    def saveTellerOtherCashReceiptBillsPaymentTxn(TxnBillsPayment tc, TxnFile tf) {
        log.info("Saving teller other cash receipt bills payment transaction")
        
        try {
            // Validation
            Map validationResult = unifiedValidationService.validateBillsPayment(params, tc, tf)
            if (!validationResult.isValid) {
                flash.message = validationResult.errors.join(', ') + '|error|alert'
                render(view: '/tellering/txnBillsPayment/create', model: [txnBillsPaymentInstance: tc])
                return
            }
            
            if (tc.hasErrors() || tf.hasErrors()) {
                flash.message = 'Failed to transfer.|error'
                render(view: '/tellering/txnBillsPayment/create', model: [txnBillsPaymentInstance: tc])
                return
            }
            
            // Process bills payment
            Map paymentResult = processCashBillsPayment(tc, tf, params)
            
            if (paymentResult.success) {
                // Update teller balance and GL
                userMasterService.updateTellerBalanceStatus(false)
                glTransactionService.saveTxnBreakdown(tf.id)
                
                flash.message = 'Transaction complete.|success'
                
                // Set session variables for reporting
                session["transactionFileId"] = tf.id.toInteger()
                session["map"] = "billspayment"
                
                // Audit logging
                auditLogService.insert('110', 'BPY01200', 
                    "Cash bills payment transaction completed successfully - Amount: ${tf.txnAmt}", 
                    'BillsPaymentController', null, null, 'tellering/saveTellerOtherCashReceiptBillsPaymentTxn', tf.id)
                
                redirect(controller: "tellering", action: "txnSuccess")
            } else {
                flash.message = paymentResult.message + '|error|alert'
                render(view: '/tellering/txnBillsPayment/create', model: [txnBillsPaymentInstance: tc])
            }
            
        } catch (Exception e) {
            log.error("Error saving cash bills payment transaction", e)
            flash.message = 'Error processing bills payment transaction|error|alert'
            render(view: '/tellering/txnBillsPayment/create', model: [txnBillsPaymentInstance: tc])
        }
    }
    
    // =====================================================
    // CHECK BILLS PAYMENT OPERATIONS
    // =====================================================
    
    /**
     * Create teller other check receipt bills payment transaction
     */
    def createTellerOtherCheckReceiptBillsPaymentTxn() {
        log.info("Creating teller other check receipt bills payment transaction")
        
        try {
            def user = UserMaster.get(session.user_id)
            if (!user.coci) {
                flash.message = 'Error! No COCI account defined.|error'
                redirect(controller: 'tellering', action: 'index')
                return
            }
            
            def txnFileInstance = new TxnFile()
            def txnCOCIInstance = new TxnCOCI()
            def txnCheckBillsPaymentInstance = new TxnBillsPayment()
            session["checks"] = []
            
            // Audit logging
            auditLogService.insert('110', 'BPY01300', 
                "Check bills payment transaction creation initiated", 
                'BillsPaymentController', null, null, 'tellering/createTellerOtherCheckReceiptBillsPaymentTxn', session.user_id)
            
            render(view: '/tellering/txnCheckBillsPayment/create', 
                model: [txnCheckBillsPaymentInstance: txnCheckBillsPaymentInstance])
            
        } catch (Exception e) {
            log.error("Error creating check bills payment transaction", e)
            flash.message = 'Error creating check bills payment transaction|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * Save teller other check receipt bills payment transaction
     */
    def saveTellerOtherCheckReceiptBillsPaymentTxn(TxnBillsPayment tc, TxnFile tf) {
        log.info("Saving teller other check receipt bills payment transaction")
        
        try {
            // Validation
            Map validationResult = unifiedValidationService.validateCheckBillsPayment(params, tc, tf)
            if (!validationResult.isValid) {
                flash.message = validationResult.errors.join(', ') + '|error|alert'
                redirect(action: "createTellerOtherCheckReceiptBillsPaymentTxn")
                return
            }
            
            if (tc.hasErrors() || tf.hasErrors()) {
                flash.message = 'Failed to transfer.|error'
                redirect(action: "createTellerOtherCheckReceiptBillsPaymentTxn")
                return
            }
            
            // Process check bills payment
            Map paymentResult = processCheckBillsPayment(tc, tf, params)
            
            if (paymentResult.success) {
                // Update teller balance and GL
                userMasterService.updateTellerBalanceStatus(false)
                glTransactionService.saveTxnBreakdown(tf.id)
                
                flash.message = 'Transaction complete.|success'
                
                // Set session variables for reporting
                session["transactionFileId"] = tf.id.toInteger()
                session["map"] = "billspayment"
                
                // Audit logging
                auditLogService.insert('110', 'BPY01400', 
                    "Check bills payment transaction completed successfully - Amount: ${tf.txnAmt}", 
                    'BillsPaymentController', null, null, 'tellering/saveTellerOtherCheckReceiptBillsPaymentTxn', tf.id)
                
                redirect(controller: "tellering", action: "txnSuccess")
            } else {
                flash.message = paymentResult.message + '|error|alert'
                redirect(action: "createTellerOtherCheckReceiptBillsPaymentTxn")
            }
            
        } catch (Exception e) {
            log.error("Error saving check bills payment transaction", e)
            flash.message = 'Error processing check bills payment transaction|error|alert'
            redirect(action: "createTellerOtherCheckReceiptBillsPaymentTxn")
        }
    }
    
    // =====================================================
    // BILLS PAYMENT VALIDATION
    // =====================================================
    
    /**
     * Validate bills payment details via AJAX
     */
    def validateBillsPayment() {
        log.debug("Validating bills payment details")
        
        try {
            Map validationResult = [isValid: true, errors: [], warnings: []]
            
            // Validate required fields
            if (!params.billsPaymentId) {
                validationResult.isValid = false
                validationResult.errors << "Bills payment ID is required"
            }
            
            if (!params.txnAmt || params.txnAmt.toDouble() <= 0) {
                validationResult.isValid = false
                validationResult.errors << "Valid transaction amount is required"
            }
            
            if (!params.billsMerchantId) {
                validationResult.isValid = false
                validationResult.errors << "Merchant ID is required"
            }
            
            // Additional business validations
            if (validationResult.isValid) {
                validationResult = performBillsPaymentBusinessValidation(params, validationResult)
            }
            
            render(validationResult as JSON)
            
        } catch (Exception e) {
            log.error("Error validating bills payment", e)
            render([isValid: false, errors: ['Error validating bills payment']] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Process cash bills payment transaction
     */
    private Map processCashBillsPayment(TxnBillsPayment tc, TxnFile tf, Map params) {
        Map result = [success: false, message: '']
        
        try {
            // Set transaction details
            tc.txnDate = new Date()
            tc.status = ConfigItemStatus.get(2)
            tc.branch = Branch.get(UserMaster.get(session.user_id).branchId)
            tc.currency = Currency.get(params.currency?.toInteger() ?: 1)
            
            // Set beneficiary if provided
            if (params.customer?.id) {
                tc.beneficiary = Customer.get(params.customer.id.toInteger())
            }
            
            // Save bills payment record
            tc.save(flush: true, failOnError: true)
            
            // Set transaction file details
            tf.txnId = tc.id
            tf.txnDate = new Date()
            tf.status = ConfigItemStatus.get(2)
            tf.beneficiary = tc.beneficiary
            tf.save(flush: true, failOnError: true)
            
            // Create teller blotter entry
            def tb = new TxnCashCheckBlotter(
                cashInAmt: tc.txnAmt,
                cashOutAmt: 0,
                checkInAmt: 0,
                checkOutAmt: 0,
                branch: tc.branch,
                currency: tc.currency,
                user: UserMaster.get(session.user_id),
                txnParticulars: tf.txnParticulars,
                txnFile: tf
            )
            tb.save(flush: true, failOnError: true)
            
            result.success = true
            result.message = 'Cash bills payment processed successfully'
            
        } catch (Exception e) {
            log.error("Error processing cash bills payment", e)
            result.message = 'Error processing cash bills payment'
        }
        
        return result
    }
    
    /**
     * Process check bills payment transaction
     */
    private Map processCheckBillsPayment(TxnBillsPayment tc, TxnFile tf, Map params) {
        Map result = [success: false, message: '']
        
        try {
            // Set transaction details
            tc.txnDate = new Date()
            tc.status = ConfigItemStatus.get(2)
            tc.branch = Branch.get(UserMaster.get(session.user_id).branchId)
            tc.currency = Currency.get(params.currency?.toInteger() ?: 1)
            
            // Set beneficiary if provided
            if (params.customer?.id) {
                tc.beneficiary = Customer.get(params.customer.id.toInteger())
            }
            
            // Process checks if any
            if (session["checks"]) {
                session["checks"].each { checkId ->
                    def tcoci = TxnCOCI.get(checkId)
                    if (tcoci) {
                        tc.addToChecks(tcoci)
                        tcoci.status = ConfigItemStatus.get(2)
                        tcoci.txnFile = tf
                        tcoci.save(flush: true)
                    }
                }
            }
            
            // Save bills payment record
            tc.save(flush: true, failOnError: true)
            
            // Set transaction file details
            tf.txnId = tc.id
            tf.txnDate = new Date()
            tf.status = ConfigItemStatus.get(2)
            tf.beneficiary = tc.beneficiary
            tf.save(flush: true, failOnError: true)
            
            // Create teller blotter entry
            def tb = new TxnCashCheckBlotter(
                cashInAmt: 0,
                cashOutAmt: 0,
                checkInAmt: tc.txnAmt,
                checkOutAmt: 0,
                branch: tc.branch,
                currency: tc.currency,
                user: UserMaster.get(session.user_id),
                txnParticulars: tf.txnParticulars,
                txnFile: tf
            )
            tb.save(flush: true, failOnError: true)
            
            result.success = true
            result.message = 'Check bills payment processed successfully'
            
        } catch (Exception e) {
            log.error("Error processing check bills payment", e)
            result.message = 'Error processing check bills payment'
        }
        
        return result
    }
    
    /**
     * Perform business validation for bills payment
     */
    private Map performBillsPaymentBusinessValidation(Map params, Map validationResult) {
        try {
            // Validate merchant ID format
            if (params.billsMerchantId && !params.billsMerchantId.matches(/^[A-Z0-9]{6,12}$/)) {
                validationResult.warnings << "Merchant ID format may be invalid"
            }
            
            // Validate amount limits
            def amount = params.txnAmt.toDouble()
            if (amount > 100000) {
                validationResult.warnings << "Large amount transaction - requires additional approval"
            }
            
            // Additional validations can be added here
            
        } catch (Exception e) {
            log.error("Error in bills payment business validation", e)
            validationResult.isValid = false
            validationResult.errors << "Error in business validation"
        }
        
        return validationResult
    }
}
