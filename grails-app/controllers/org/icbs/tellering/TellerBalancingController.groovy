package org.icbs.tellering

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.Currency
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnCashCheckBlotter
import org.icbs.tellering.TxnTellerCash
import org.icbs.lov.ConfigItemStatus
import groovy.sql.Sql

/**
 * TellerBalancingController - Handles teller balancing operations
 * 
 * This controller manages teller balancing operations including:
 * - Teller cash balancing
 * - End of day balancing
 * - Force balance operations
 * - Cash on hand calculations
 * - Teller balance confirmations
 * - Balance discrepancy handling
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class TellerBalancingController {
    
    // Service Dependencies
    def userMasterService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        confirmTellerBalance: "POST",
        forceBalancePerUser: "POST",
        forceBalanceAllUser: "POST",
        receiveTellerCashTransferSave: "POST",
        receiveTellerCashCancel: "POST"
    ]

    /**
     * View teller balancing page
     */
    def viewTellerBalancing() {
        def user = UserMaster.get(session.user_id)
        def branch = user.branch
        
        // Get teller balance information
        def tellerBalances = TxnTellerBalance.findAllByUser(user)
        def cashTransactions = getCashTransactionSummary(user, branch.runDate)
        def checkTransactions = getCheckTransactionSummary(user, branch.runDate)
        
        render(view:'/tellering/tellerBalancing/view', model: [
            user: user,
            branch: branch,
            tellerBalances: tellerBalances,
            cashTransactions: cashTransactions,
            checkTransactions: checkTransactions
        ])
    }

    /**
     * View teller cash transactions
     */
    def viewTellerCashTxn() {
        def user = UserMaster.get(session.user_id)
        def branch = user.branch
        
        def tbCash = TxnCashCheckBlotter.createCriteria().list() {
            and {
                eq("user", user)   
                eq("txnFile.txnDate", branch.runDate)
            }
            order("txnFile", "asc")
        }  
        
        render(view:'/tellering/tellerBalancing/viewTellerCashTxn', model: [
            tbCash: tbCash,
            user: user,
            branch: branch
        ]) 
    }

    /**
     * Confirm teller balance
     */
    @Transactional
    def confirmTellerBalance() {
        println("================ confirmTellerBalance ===================")
        
        def user = UserMaster.get(session.user_id)
        def branch = user.branch
        
        try {
            // Calculate expected balances
            def expectedBalances = calculateExpectedBalances(user, branch.runDate)
            def actualBalances = params.actualBalances // From form submission
            
            def isBalanced = true
            def discrepancies = []
            
            // Compare expected vs actual for each currency
            expectedBalances.each { currency, expected ->
                def actual = actualBalances[currency.toString()]?.toDouble() ?: 0
                def variance = Math.abs(expected - actual)
                
                if (variance > 0.01) { // Allow 1 cent variance
                    isBalanced = false
                    discrepancies.add([
                        currency: currency,
                        expected: expected,
                        actual: actual,
                        variance: variance
                    ])
                }
            }
            
            if (isBalanced) {
                // Mark teller as balanced
                user.isTellerBalanced = true
                user.save(flush: true)
                
                def description = "Teller ${user.username} confirmed balanced for ${branch.runDate.format('MM/dd/yyyy')}"
                auditLogService.insert('120', 'TLR00900', description, 'TellerBalancing', null, null, null, user.id)
                
                flash.message = "Teller balance confirmed successfully|success"
            } else {
                def description = "Teller ${user.username} balance discrepancy detected: ${discrepancies}"
                auditLogService.insert('120', 'TLR01000', description, 'TellerBalancing', null, null, null, user.id)
                
                flash.message = "Balance discrepancy detected. Please review and adjust.|error|alert"
            }
            
            render([
                balanced: isBalanced,
                discrepancies: discrepancies,
                message: flash.message
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error confirming teller balance", e)
            render([
                balanced: false,
                error: "Error confirming balance: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Force balance for specific user
     */
    @Transactional
    def forceBalancePerUser() {
        def json = request.JSON
        def userId = json.userId?.toInteger()
        
        if (!userId) {
            render([success: false, message: "User ID required"] as JSON)
            return
        }
        
        try {
            def targetUser = UserMaster.get(userId)
            if (!targetUser) {
                render([success: false, message: "User not found"] as JSON)
                return
            }
            
            targetUser.isTellerBalanced = true
            targetUser.save(flush: true)
            
            def currentUser = UserMaster.get(session.user_id)
            def description = "Force balance applied to user ${targetUser.username} by ${currentUser.username}"
            auditLogService.insert('120', 'TLR01100', description, 'TellerBalancing', null, null, null, targetUser.id)
            
            render([
                success: true,
                message: "User ${targetUser.username} force balanced successfully"
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error force balancing user", e)
            render([
                success: false,
                message: "Error force balancing user: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Force balance for all users
     */
    @Transactional
    def forceBalanceAllUser() {
        try {
            def userMasterInstanceList = UserMaster.createCriteria().list() {
                and {
                    eq("isTellerBalanced", false)
                    eq("configItemStatus", ConfigItemStatus.get(2))
                }
            }
            
            int balancedCount = 0
            
            if (userMasterInstanceList) {
                for (user in userMasterInstanceList) {
                    user.isTellerBalanced = true
                    user.save(flush: true)
                    
                    def currentUser = UserMaster.get(session.user_id)
                    def description = "Force balance all users - ${user.username} balanced by ${currentUser.username}"
                    auditLogService.insert('120', 'TLR01200', description, 'TellerBalancing', null, null, null, user.id)
                    
                    balancedCount++
                    log.info("Force balanced user: ${user.username}")
                }
            }
            
            render([
                success: true,
                message: "Force balanced ${balancedCount} users successfully",
                balancedCount: balancedCount
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error force balancing all users", e)
            render([
                success: false,
                message: "Error force balancing all users: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Receive teller cash transfer
     */
    def receiveTellerCashTransfer() {
        def user = UserMaster.get(session.user_id)
        
        // Get pending cash transfers for this user
        def pendingTransfers = TxnTellerCash.createCriteria().list() {
            and {
                eq("receivingUser", user)
                eq("txnFile.status", ConfigItemStatus.get(1)) // Pending
            }
            order("txnDate", "desc")
        }
        
        render(view:'/tellering/tellerBalancing/receiveCashTransfer', model: [
            pendingTransfers: pendingTransfers,
            user: user
        ])
    }

    /**
     * Save received teller cash transfer
     */
    @Transactional
    def receiveTellerCashTransferSave(TxnTellerCash tc) {
        def user = UserMaster.get(session.user_id)
        def transferRecord = TxnTellerCash.get(params.transferId)
        
        if (!transferRecord || transferRecord.receivingUser.id != user.id) {
            flash.message = "Invalid transfer record|error|alert"
            redirect(action: "receiveTellerCashTransfer")
            return
        }
        
        try {
            // Update transaction status to completed
            transferRecord.txnFile.status = ConfigItemStatus.get(2) // Posted
            transferRecord.txnFile.save(flush: true)
            
            // Update sender's balance
            def senderBalance = TxnTellerBalance.findByUserAndCurrency(
                transferRecord.user, 
                transferRecord.txnFile.currency
            )
            if (senderBalance) {
                senderBalance.cashOutAmt += transferRecord.netAmtPaidOut
                senderBalance.save(flush: true)
            }
            
            // Update receiver's balance
            def receiverBalance = TxnTellerBalance.findByUserAndCurrency(
                user, 
                transferRecord.txnFile.currency
            )
            if (!receiverBalance) {
                receiverBalance = new TxnTellerBalance(
                    user: user,
                    currency: transferRecord.txnFile.currency,
                    cashInAmt: 0,
                    cashOutAmt: 0,
                    checkInAmt: 0,
                    checkOutAmt: 0
                )
            }
            receiverBalance.cashInAmt += transferRecord.netAmtPaidOut
            receiverBalance.save(flush: true)
            
            def description = "Cash transfer received - From: ${transferRecord.user.username} Amount: ${transferRecord.netAmtPaidOut}"
            auditLogService.insert('120', 'TLR01300', description, 'TellerBalancing', null, null, null, transferRecord.txnFile.id)
            
            flash.message = "Cash transfer received successfully|success"
            redirect(action: "viewTellerBalancing")
            
        } catch (Exception e) {
            log.error("Error receiving cash transfer", e)
            flash.message = "Error receiving cash transfer: ${e.message}|error|alert"
            redirect(action: "receiveTellerCashTransfer")
        }
    }

    /**
     * Cancel received teller cash transfer
     */
    @Transactional
    def receiveTellerCashCancel(TxnTellerCash tc) {
        def user = UserMaster.get(session.user_id)
        def transferRecord = TxnTellerCash.get(params.transferId)
        
        if (!transferRecord || transferRecord.receivingUser.id != user.id) {
            flash.message = "Invalid transfer record|error|alert"
            redirect(action: "receiveTellerCashTransfer")
            return
        }
        
        try {
            // Update transaction status to cancelled
            transferRecord.txnFile.status = ConfigItemStatus.get(3) // Cancelled
            transferRecord.txnFile.save(flush: true)
            
            def description = "Cash transfer cancelled - From: ${transferRecord.user.username} Amount: ${transferRecord.netAmtPaidOut}"
            auditLogService.insert('120', 'TLR01400', description, 'TellerBalancing', null, null, null, transferRecord.txnFile.id)
            
            flash.message = "Cash transfer cancelled|info"
            redirect(action: "receiveTellerCashTransfer")
            
        } catch (Exception e) {
            log.error("Error cancelling cash transfer", e)
            flash.message = "Error cancelling cash transfer: ${e.message}|error|alert"
            redirect(action: "receiveTellerCashTransfer")
        }
    }

    // Helper methods
    private def calculateExpectedBalances(UserMaster user, Date txnDate) {
        def sql = new Sql(dataSource)
        def balances = [:]
        
        try {
            def results = sql.rows("""
                SELECT c.id as currency_id, c.code,
                       COALESCE(SUM(CASE WHEN tb.cash_in_amt IS NOT NULL THEN tb.cash_in_amt ELSE 0 END), 0) as cash_in,
                       COALESCE(SUM(CASE WHEN tb.cash_out_amt IS NOT NULL THEN tb.cash_out_amt ELSE 0 END), 0) as cash_out
                FROM currency c
                LEFT JOIN txn_teller_balance tb ON c.id = tb.currency_id AND tb.user_id = ?
                WHERE c.status_id = 2
                GROUP BY c.id, c.code
            """, [user.id])
            
            results.each { row ->
                def expectedBalance = row.cash_in - row.cash_out
                balances[row.currency_id] = expectedBalance
            }
            
        } catch (Exception e) {
            log.error("Error calculating expected balances", e)
        }
        
        return balances
    }

    private def getCashTransactionSummary(UserMaster user, Date txnDate) {
        def sql = new Sql(dataSource)
        
        try {
            return sql.firstRow("""
                SELECT 
                    COALESCE(SUM(CASE WHEN tt.description LIKE '%Deposit%' OR tt.description LIKE '%Receipt%' THEN tf.txn_amt ELSE 0 END), 0) as total_receipts,
                    COALESCE(SUM(CASE WHEN tt.description LIKE '%Withdrawal%' OR tt.description LIKE '%Payment%' THEN tf.txn_amt ELSE 0 END), 0) as total_payments,
                    COUNT(*) as transaction_count
                FROM txn_file tf
                JOIN txn_type tt ON tf.txn_type_id = tt.id
                WHERE tf.user_id = ? AND tf.txn_date = ? AND tf.status_id = 2
            """, [user.id, txnDate])
            
        } catch (Exception e) {
            log.error("Error getting cash transaction summary", e)
            return [total_receipts: 0, total_payments: 0, transaction_count: 0]
        }
    }

    private def getCheckTransactionSummary(UserMaster user, Date txnDate) {
        def sql = new Sql(dataSource)
        
        try {
            return sql.firstRow("""
                SELECT 
                    COALESCE(SUM(CASE WHEN tt.description LIKE '%Check%' AND tt.description LIKE '%Deposit%' THEN tf.txn_amt ELSE 0 END), 0) as check_deposits,
                    COALESCE(SUM(CASE WHEN tt.description LIKE '%Check%' AND tt.description LIKE '%Encash%' THEN tf.txn_amt ELSE 0 END), 0) as check_encashments,
                    COUNT(*) as check_count
                FROM txn_file tf
                JOIN txn_type tt ON tf.txn_type_id = tt.id
                WHERE tf.user_id = ? AND tf.txn_date = ? AND tf.status_id = 2
                AND tt.description LIKE '%Check%'
            """, [user.id, txnDate])
            
        } catch (Exception e) {
            log.error("Error getting check transaction summary", e)
            return [check_deposits: 0, check_encashments: 0, check_count: 0]
        }
    }
}
