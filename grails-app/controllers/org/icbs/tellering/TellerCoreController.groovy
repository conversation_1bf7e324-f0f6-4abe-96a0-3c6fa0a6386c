package org.icbs.tellering

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.PolicyException
import org.icbs.lov.ConfigItemStatus
import org.icbs.tellering.TxnFile
import org.icbs.cif.Customer
import org.icbs.loans.Loan
import org.icbs.deposit.Deposit

/**
 * TellerCoreController - Handles core teller operations
 * 
 * This controller manages core teller operations including:
 * - Main teller index and navigation
 * - Policy exception handling
 * - Customer details retrieval
 * - Account validation operations
 * - Core transaction utilities
 * - Session management
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class TellerCoreController {
    
    // Service Dependencies
    def policyService
    def userMasterService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        takeAction: "POST",
        search: "GET",
        showTxnAjax: "GET",
        showCustomerDetailsAjax: "GET"
    ]

    /**
     * Main teller index page
     */
    def index() { 
        // List of transactions
        session.map = null
        
        def user = UserMaster.get(session.user_id)
        def branch = user.branch
        
        render(view: '/tellering/index', model: [
            user: user,
            branch: branch,
            runDate: branch.runDate
        ])
    }

    /**
     * Handle policy exception actions
     */
    @Transactional
    def takeAction() {
        def txnFileInstance = TxnFile.get(params.txnFileInstanceId)
        if (txnFileInstance == null) {
            notFound()
            return
        }
        
        policyService.takeAction(txnFileInstance, ConfigItemStatus, 'txnFile', (boolean)params.isApproved)
        
        def description = "Policy action taken on transaction ${txnFileInstance.id}"
        auditLogService.insert('120', 'TLR00100', description, 'TellerCore', null, null, null, txnFileInstance.id)
        
        redirect(action: "index")
    }

    /**
     * Search transactions
     */
    def search(Integer max) {
        println params
        params.max = Math.min(max ?: 25, 100)
        
        if (params.sort == null) {
            params.sort = "id"
            params.order = "desc"
        }

        def user = UserMaster.get(session.user_id)
        def branch = user.branch
        
        if (params.query == null || params.query.trim() == "") {  
            // Show all transactions for current user and branch
            def txnList = TxnFile.createCriteria().list(params) {
                and {
                    eq("user", user)
                    eq("branch", branch)
                    eq("txnDate", branch.runDate)
                }
            }
            
            render(template:"search/searchTxn", model:[
                params: params, 
                domainInstanceList: txnList, 
                domainInstanceCount: txnList.totalCount
            ]) as JSON
        } else {    
            // Search with query
            def txnList = TxnFile.createCriteria().list(params) {
                and {
                    eq("user", user)
                    eq("branch", branch)
                    eq("txnDate", branch.runDate)
                    or {
                        ilike("txnRef", "%${params.query.trim()}%")
                        ilike("txnParticulars", "%${params.query.trim()}%")
                        ilike("acctNo", "%${params.query.trim()}%")
                        if (params.query.trim().isNumber()) {
                            eq("id", params.query.trim().toLong())
                        }
                    }
                }
            }
            
            render(template:"search/searchTxn", model:[
                params: params, 
                domainInstanceList: txnList, 
                domainInstanceCount: txnList.totalCount
            ]) as JSON
        }            
        return
    }

    /**
     * Show transaction details via AJAX
     */
    def showTxnAjax() {
        def txnFileInstance = null
        def senderInstance = null
        def beneficiaryInstance = null
        def txnTemplateInstance = null
        def indicator = ""

        if (params?.id) {
            txnFileInstance = TxnFile.get(params.id)
            
            if (txnFileInstance) {
                senderInstance = txnFileInstance.sender
                beneficiaryInstance = txnFileInstance.beneficiary
                txnTemplateInstance = txnFileInstance.txnTemplate
                
                // Determine transaction type indicator
                def txnTypeNo = txnFileInstance.txnType.id
                
                if (txnTypeNo >= 1 && txnTypeNo <= 55) {
                    indicator = "Deposit"
                } else if (txnTypeNo >= 56 && txnTypeNo <= 78) {
                    indicator = "Loan"
                } else if (txnTypeNo >= 79 && txnTypeNo <= 100) {
                    indicator = "Other"
                }

                render(template:"details/txnInfo", model:[
                    txnFileInstance: txnFileInstance, 
                    senderInstance: senderInstance, 
                    beneficiaryInstance: beneficiaryInstance, 
                    txnTemplateInstance: txnTemplateInstance, 
                    indicator: indicator
                ]) as JSON
                return
            } else {
                render(template:"details/txnInfo", model:[error: "Transaction not found"])
                return
            }
        }
        
        render(template:"details/txnInfo", model:[error: "No transaction ID provided"])
        return
    }

    /**
     * Show customer details via AJAX
     */
    def showCustomerDetailsAjax() {
        println " params " + params.field
        def customerInstance = null
        
        if (params?.field) {
            if (params.field.isNumber()) {
                customerInstance = Customer.get(params.field.toLong())
            } else {
                customerInstance = Customer.findByDisplayName(params.field)
            }
        }

        render(template:"details/customerInfo", model:[customerInstance: customerInstance]) as JSON
        return
    }

    /**
     * Change loan details for transaction
     */
    def changeLoanDetails() {
        def loanInstance = Loan.get(params.loan)
        def loanAccountNo = loanInstance?.accountNo
        def loanCustomer = loanInstance?.customer?.displayName
        def loanBalance = loanInstance?.balanceAmount
        def loanInterestBalance = loanInstance?.interestBalanceAmount
        def loanServiceChargeBalance = loanInstance?.serviceChargeBalanceAmount

        render(template:"details/loanInfo", model:[
            loanAccountNo: loanAccountNo,
            loanCustomer: loanCustomer, 
            loanBalance: loanBalance,
            loanInterestBalance: loanInterestBalance,
            loanServiceChargeBalance: loanServiceChargeBalance
        ]) as JSON
        return
    }

    /**
     * Change deposit details for transaction
     */
    def changeDepositDetails() {
        def userMasterId = UserMaster.get(session.user_id).id
        def depositInstance = Deposit.get(params.deposit)
        def depositAccountNo = depositInstance?.accountNo
        def depositCustomer = depositInstance?.customer?.displayName
        def depositBalance = depositInstance?.ledgerBalAmt
        def depositAvailableBalance = depositInstance?.availableBalAmt
        def depositType = depositInstance?.type?.description
        def depositStatus = depositInstance?.status?.description

        render(template:"details/depositInfo", model:[
            depositAccountNo: depositAccountNo,
            depositCustomer: depositCustomer,
            depositBalance: depositBalance,
            depositAvailableBalance: depositAvailableBalance,
            depositType: depositType,
            depositStatus: depositStatus
        ]) as JSON
        return
    }

    /**
     * Validate session and user permissions
     */
    def validateUserSession() {
        def user = UserMaster.get(session.user_id)
        if (!user) {
            render([valid: false, message: "Invalid user session"] as JSON)
            return
        }
        
        if (!user.isTellerBalanced) {
            render([valid: false, message: "Teller not balanced"] as JSON)
            return
        }
        
        render([valid: true, user: user.username] as JSON)
        return
    }

    /**
     * Get current branch information
     */
    def getBranchInfo() {
        def user = UserMaster.get(session.user_id)
        def branch = user?.branch
        
        if (branch) {
            render([
                branchId: branch.id,
                branchName: branch.name,
                branchCode: branch.code,
                runDate: branch.runDate.format("MM/dd/yyyy"),
                isEOD: branch.isEOD
            ] as JSON)
        } else {
            render([error: "Branch information not available"] as JSON)
        }
        return
    }

    /**
     * Check transaction limits
     */
    def checkTransactionLimits() {
        def user = UserMaster.get(session.user_id)
        def amount = params.amount?.toDouble() ?: 0
        def txnType = params.txnType
        
        def limits = [
            dailyLimit: user.dailyTransactionLimit ?: 0,
            singleLimit: user.singleTransactionLimit ?: 0,
            currentDailyTotal: getCurrentDailyTotal(user)
        ]
        
        def withinLimits = (amount <= limits.singleLimit) && 
                          ((limits.currentDailyTotal + amount) <= limits.dailyLimit)
        
        render([
            withinLimits: withinLimits,
            limits: limits,
            amount: amount
        ] as JSON)
        return
    }

    /**
     * Initialize session variables for transaction
     */
    def initializeTransactionSession() {
        session["checks"] = []
        session["map"] = params.transactionType ?: ""
        session["transactionFileId"] = null
        
        render([success: true, message: "Session initialized"] as JSON)
        return
    }

    // Helper methods
    private def getCurrentDailyTotal(UserMaster user) {
        def branch = user.branch
        def today = branch.runDate
        
        def total = TxnFile.createCriteria().get {
            projections {
                sum("txnAmt")
            }
            and {
                eq("user", user)
                eq("txnDate", today)
                eq("status", ConfigItemStatus.get(2)) // Posted
            }
        }
        
        return total ?: 0
    }

    protected def notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = message(code: 'default.not.found.message', args: ['Transaction', params.id])
                redirect action: "index", method: "GET"
            }
            '*'{ render status: NOT_FOUND }
        }
    }
}
