package org.icbs.tellering

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.tellering.TxnFile
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.Currency
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import grails.converters.JSON
import groovy.sql.Sql
import javax.sql.DataSource

/**
 * REFACTORED: Transaction Utility Controller
 * Extracted from TelleringController.groovy (7,319 lines)
 * Handles utility operations and helper functions with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class TransactionUtilityController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def auditLogService
    DataSource dataSource
    
    static allowedMethods = [
        txnSuccess: "GET",
        index: "GET",
        getTransactionReference: "GET",
        validateAmount: "POST",
        getCurrencyInfo: "GET"
    ]
    
    // =====================================================
    // UTILITY OPERATIONS
    // =====================================================
    
    /**
     * Transaction success page
     */
    def txnSuccess() {
        log.info("Displaying transaction success page")
        
        try {
            def transactionFileId = session["transactionFileId"]
            def mapType = session["map"]
            
            if (!transactionFileId) {
                flash.message = 'No transaction information found|warning|alert'
                redirect(controller: 'tellering', action: 'index')
                return
            }
            
            def txnFile = TxnFile.get(transactionFileId)
            if (!txnFile) {
                flash.message = 'Transaction not found|error|alert'
                redirect(controller: 'tellering', action: 'index')
                return
            }
            
            // Audit logging
            auditLogService.insert('110', 'TUT01100', 
                "Transaction success page displayed - Transaction: ${transactionFileId}", 
                'TransactionUtilityController', null, null, 'tellering/txnSuccess', transactionFileId)
            
            render(view: '/tellering/txnSuccess', 
                model: [
                    txnFile: txnFile,
                    mapType: mapType
                ])
            
        } catch (Exception e) {
            log.error("Error displaying transaction success page", e)
            flash.message = 'Error displaying transaction success page|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * Main tellering index page
     */
    def index() {
        log.info("Displaying tellering index page for user: ${session.user_id}")
        
        try {
            def user = UserMaster.get(session.user_id)
            if (!user) {
                flash.message = 'User session expired|error|alert'
                redirect(controller: 'login', action: 'auth')
                return
            }
            
            // Get user's daily transaction summary
            Map dailySummary = getDailyTransactionSummary(user)
            
            // Get pending items count
            Map pendingItems = getPendingItemsCount(user)
            
            // Audit logging
            auditLogService.insert('110', 'TUT01200', 
                "Tellering index page accessed", 
                'TransactionUtilityController', null, null, 'tellering/index', session.user_id)
            
            render(view: '/tellering/index', 
                model: [
                    user: user,
                    dailySummary: dailySummary,
                    pendingItems: pendingItems
                ])
            
        } catch (Exception e) {
            log.error("Error displaying tellering index page", e)
            flash.message = 'Error accessing tellering system|error|alert'
            redirect(controller: 'login', action: 'auth')
        }
    }
    
    /**
     * Generate transaction reference number
     */
    def getTransactionReference() {
        log.debug("Generating transaction reference number")
        
        try {
            String txnRef = generateTransactionReference()
            
            render([
                success: true,
                txnRef: txnRef,
                timestamp: new Date().time
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error generating transaction reference", e)
            render([success: false, error: 'Error generating transaction reference'] as JSON)
        }
    }
    
    /**
     * Validate transaction amount
     */
    def validateAmount() {
        log.debug("Validating transaction amount: ${params.amount}")
        
        try {
            if (!params.amount) {
                render([isValid: false, error: 'Amount is required'] as JSON)
                return
            }
            
            String amountStr = params.amount.toString().replace(',', '')
            
            try {
                BigDecimal amount = new BigDecimal(amountStr)
                
                Map validationResult = [isValid: true, warnings: []]
                
                // Basic validations
                if (amount <= 0) {
                    validationResult.isValid = false
                    validationResult.error = 'Amount must be greater than zero'
                } else if (amount > 10000000) {
                    validationResult.warnings << 'Large amount transaction'
                } else if (amount.scale() > 2) {
                    validationResult.warnings << 'Amount has more than 2 decimal places'
                }
                
                validationResult.formattedAmount = amount.setScale(2).toString()
                
                render(validationResult as JSON)
                
            } catch (NumberFormatException e) {
                render([isValid: false, error: 'Invalid amount format'] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error validating amount", e)
            render([isValid: false, error: 'Error validating amount'] as JSON)
        }
    }
    
    /**
     * Get currency information
     */
    def getCurrencyInfo() {
        log.debug("Getting currency information for: ${params.currencyId}")
        
        try {
            if (!params.currencyId) {
                render([error: 'Currency ID is required'] as JSON)
                return
            }
            
            def currency = Currency.get(params.currencyId)
            if (!currency) {
                render([error: 'Currency not found'] as JSON)
                return
            }
            
            Map currencyInfo = [
                id: currency.id,
                code: currency.code,
                name: currency.name,
                symbol: currency.symbol,
                decimalPlaces: currency.decimalPlaces ?: 2,
                exchangeRate: currency.exchangeRate ?: 1.0
            ]
            
            render(currencyInfo as JSON)
            
        } catch (Exception e) {
            log.error("Error getting currency information", e)
            render([error: 'Error getting currency information'] as JSON)
        }
    }
    
    /**
     * Get system status
     */
    def getSystemStatus() {
        log.debug("Getting system status")
        
        try {
            Map systemStatus = [
                isOnline: true,
                currentDate: new Date(),
                businessDate: Branch.get(1)?.runDate,
                systemTime: new Date().format('HH:mm:ss'),
                userCount: UserMaster.countByConfigItemStatus(org.icbs.lov.ConfigItemStatus.get(2))
            ]
            
            render(systemStatus as JSON)
            
        } catch (Exception e) {
            log.error("Error getting system status", e)
            render([error: 'Error getting system status'] as JSON)
        }
    }
    
    /**
     * Format amount for display
     */
    def formatAmount() {
        log.debug("Formatting amount: ${params.amount}")
        
        try {
            if (!params.amount) {
                render([error: 'Amount is required'] as JSON)
                return
            }
            
            BigDecimal amount = new BigDecimal(params.amount.toString().replace(',', ''))
            String currencyCode = params.currencyCode ?: 'USD'
            
            String formattedAmount = formatCurrency(amount, currencyCode)
            
            render([
                success: true,
                formattedAmount: formattedAmount,
                numericAmount: amount.toString()
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error formatting amount", e)
            render([error: 'Error formatting amount'] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Get daily transaction summary for user
     */
    private Map getDailyTransactionSummary(UserMaster user) {
        Map summary = [:]
        
        try {
            Date today = new Date()
            
            def sql = new Sql(dataSource)
            def query = """
                SELECT COUNT(*) as txn_count, 
                       COALESCE(SUM(txn_amt), 0) as total_amount,
                       COUNT(CASE WHEN status_id = 2 THEN 1 END) as completed_count,
                       COUNT(CASE WHEN status_id = 1 THEN 1 END) as pending_count
                FROM txn_file 
                WHERE user_id = ? AND DATE(txn_date) = CURRENT_DATE
            """
            
            def result = sql.firstRow(query, [user.id])
            
            summary.transactionCount = result.txn_count ?: 0
            summary.totalAmount = result.total_amount ?: 0
            summary.completedCount = result.completed_count ?: 0
            summary.pendingCount = result.pending_count ?: 0
            
        } catch (Exception e) {
            log.error("Error getting daily transaction summary", e)
            summary.error = "Error getting daily summary"
        }
        
        return summary
    }
    
    /**
     * Get pending items count for user
     */
    private Map getPendingItemsCount(UserMaster user) {
        Map pending = [:]
        
        try {
            def sql = new Sql(dataSource)
            
            // Pending approvals
            def approvalQuery = """
                SELECT COUNT(*) as approval_count
                FROM policy_exception pe
                WHERE pe.approving_user_id = ? 
                  AND pe.date_of_action IS NULL
            """
            def approvalResult = sql.firstRow(approvalQuery, [user.id])
            pending.approvals = approvalResult.approval_count ?: 0
            
            // Pending exceptions
            def exceptionQuery = """
                SELECT COUNT(*) as exception_count
                FROM policy_exception pe
                WHERE pe.requesting_user_id = ? 
                  AND pe.date_of_action IS NULL
            """
            def exceptionResult = sql.firstRow(exceptionQuery, [user.id])
            pending.exceptions = exceptionResult.exception_count ?: 0
            
            // Unbalanced status
            pending.isBalanced = user.isTellerBalanced ?: false
            
        } catch (Exception e) {
            log.error("Error getting pending items count", e)
            pending.error = "Error getting pending items"
        }
        
        return pending
    }
    
    /**
     * Generate unique transaction reference number
     */
    private String generateTransactionReference() {
        try {
            Date now = new Date()
            String dateStr = now.format('yyyyMMdd')
            String timeStr = now.format('HHmmss')
            String randomStr = String.format('%04d', new Random().nextInt(10000))
            
            return "TXN${dateStr}${timeStr}${randomStr}"
            
        } catch (Exception e) {
            log.error("Error generating transaction reference", e)
            return "TXN${System.currentTimeMillis()}"
        }
    }
    
    /**
     * Format currency amount
     */
    private String formatCurrency(BigDecimal amount, String currencyCode) {
        try {
            // Basic formatting - in a real system, this would use proper locale formatting
            return String.format("%,.2f %s", amount, currencyCode)
        } catch (Exception e) {
            log.error("Error formatting currency", e)
            return amount.toString()
        }
    }
}
