package org.icbs.tellering

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.TxnTemplate
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnTellerBalance
import org.icbs.deposit.Deposit
import org.icbs.deposit.Rollover
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositStatus
import org.icbs.lov.RolloverStatus
import org.icbs.lov.TxnType
import java.text.SimpleDateFormat

/**
 * TellerDepositController - Handles teller deposit operations
 * 
 * This controller manages teller deposit operations including:
 * - Cash deposit transactions
 * - Check deposit transactions
 * - Cash withdrawal transactions
 * - Fixed deposit operations
 * - Pre-termination transactions
 * - Interest withdrawal operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class TellerDepositController {
    
    // Service Dependencies
    def depositService
    def userMasterService
    def glTransactionService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        saveTellerCashDepositTxn: "POST",
        saveTellerCheckDepositTxn: "POST",
        saveTellerCashWithdrawalTxn: "POST",
        saveTellerFDInterestWithdrawalTxn: "POST",
        saveTellerFDPreTerminationTxn: "POST",
        validatePassbookBal: "POST"
    ]

    /**
     * Create teller cash deposit transaction
     */
    def createTellerCashDepositTxn() {
        session["map"] = 'deposit'
        def user = UserMaster.get(session.user_id)
        
        if (user.cash) {
            def txnFileInstance = new TxnFile()
            def txnCashDepositInstance = new TxnDepositAcctLedger()
            
            render(view:'/tellering/txnCashDeposit/create', model: [
                txnCashDepositInstance: txnCashDepositInstance
            ])       
        } else {
            flash.message = 'Error! No cash account defined|error|alert'
            redirect(action: "index", controller: "tellerCore")            
        }
    }

    /**
     * Save teller cash deposit transaction
     */
    @Transactional
    def saveTellerCashDepositTxn(TxnDepositAcctLedger tc, TxnFile tf) {
        println params // Error check
        
        def user = UserMaster.get(session.user_id)
        def branch = Branch.get(user.branchId)
        def depositAccount = Deposit.get(params.acct)
        
        // Validate transaction
        if (!depositAccount) {
            flash.message = 'Invalid deposit account|error|alert'
            render(view:'/tellering/txnCashDeposit/create', model: [
                txnCashDepositInstance: tc
            ])
            return
        }
        
        if (!tc.creditAmt || tc.creditAmt <= 0) {
            flash.message = 'Invalid deposit amount|error|alert'
            render(view:'/tellering/txnCashDeposit/create', model: [
                txnCashDepositInstance: tc
            ])
            return
        }

        // Check account status
        if (depositAccount.status.id != 4) { // Not active
            flash.message = 'Account is not active|error|alert'
            render(view:'/tellering/txnCashDeposit/create', model: [
                txnCashDepositInstance: tc
            ])
            return
        }

        // Create transaction file
        tf.txnCode = "CDEP"
        tf.txnDescription = "Cash Deposit"
        tf.txnDate = branch.runDate
        tf.currency = depositAccount.currency
        tf.txnAmt = tc.creditAmt
        tf.txnRef = params.txnRef ?: "Cash Deposit"
        tf.status = ConfigItemStatus.get(2)
        tf.branch = branch
        tf.acctNo = depositAccount.accountNo
        tf.txnTimestamp = new Date().toTimestamp()
        tf.txnParticulars = params.txnParticulars ?: "Cash deposit to account ${depositAccount.accountNo}"
        tf.txnType = TxnType.get(1) // Cash deposit type
        tf.txnTemplate = TxnTemplate.get(1)
        tf.user = user
        tf.beneficiary = depositAccount.customer
        tf.save(flush: true, failOnError: true)

        // Create deposit ledger entry
        tc.txnFile = tf
        tc.acct = depositAccount
        tc.txnDate = tf.txnDate
        tc.creditAmt = tc.creditAmt
        tc.debitAmt = 0
        tc.bal = depositAccount.ledgerBalAmt + tc.creditAmt
        tc.passbookBal = 0 // Will be updated when passbook is printed
        tc.txnRef = tf.txnRef
        tc.save(flush: true, failOnError: true)

        // Update deposit account balance
        depositAccount.ledgerBalAmt += tc.creditAmt
        depositAccount.availableBalAmt += tc.creditAmt
        depositAccount.save(flush: true)

        // Update teller cash balance
        def tellerBalance = TxnTellerBalance.findByUserAndCurrency(user, tf.currency)
        if (tellerBalance) {
            tellerBalance.cashInAmt += tc.creditAmt
            tellerBalance.save(flush: true)
        }

        // Update teller balance status
        userMasterService.updateTellerBalanceStatus(false)
        
        // Generate GL transactions
        glTransactionService.saveTxnBreakdown(tf.id)

        def description = "Cash deposit processed - Account: ${depositAccount.accountNo} Amount: ${tc.creditAmt}"
        auditLogService.insert('120', 'TLR00500', description, 'TellerDeposit', null, null, null, tf.id)
        
        session["transactionFileId"] = tf.id
        session["jrxmlTcId"] = tc.id
        session["type"] = depositAccount.type.id
        
        redirect(controller: "tellerCore", action: "txnSuccess")
    }

    /**
     * Create teller cash withdrawal transaction
     */
    def createTellerCashWithdrawalTxn() {
        session["map"] = 'deposit'
        def user = UserMaster.get(session.user_id)
        
        if (user.cash) {
            def txnFileInstance = new TxnFile()
            def txnCashWithdrawalInstance = new TxnDepositAcctLedger()
            
            render(view:'/tellering/txnCashWithdrawal/create', model: [
                txnCashWithdrawalInstance: txnCashWithdrawalInstance
            ])       
        } else {
            flash.message = 'Error! No cash account defined|error|alert'
            redirect(action: "index", controller: "tellerCore")            
        }
    }

    /**
     * Save teller cash withdrawal transaction
     */
    @Transactional
    def saveTellerCashWithdrawalTxn(TxnDepositAcctLedger tc, TxnFile tf) {
        println params // Error check
        
        def user = UserMaster.get(session.user_id)
        def branch = Branch.get(user.branchId)
        def depositAccount = Deposit.get(params.acct)
        
        // Validate transaction
        if (!depositAccount) {
            flash.message = 'Invalid deposit account|error|alert'
            render(view:'/tellering/txnCashWithdrawal/create', model: [
                txnCashWithdrawalInstance: tc
            ])
            return
        }
        
        if (!tc.debitAmt || tc.debitAmt <= 0) {
            flash.message = 'Invalid withdrawal amount|error|alert'
            render(view:'/tellering/txnCashWithdrawal/create', model: [
                txnCashWithdrawalInstance: tc
            ])
            return
        }

        // Check account status
        if (depositAccount.status.id != 4) { // Not active
            flash.message = 'Account is not active|error|alert'
            render(view:'/tellering/txnCashWithdrawal/create', model: [
                txnCashWithdrawalInstance: tc
            ])
            return
        }

        // Check sufficient balance
        if (depositAccount.availableBalAmt < tc.debitAmt) {
            flash.message = 'Insufficient account balance|error|alert'
            render(view:'/tellering/txnCashWithdrawal/create', model: [
                txnCashWithdrawalInstance: tc
            ])
            return
        }

        // Create transaction file
        tf.txnCode = "CWTH"
        tf.txnDescription = "Cash Withdrawal"
        tf.txnDate = branch.runDate
        tf.currency = depositAccount.currency
        tf.txnAmt = tc.debitAmt
        tf.txnRef = params.txnRef ?: "Cash Withdrawal"
        tf.status = ConfigItemStatus.get(2)
        tf.branch = branch
        tf.acctNo = depositAccount.accountNo
        tf.txnTimestamp = new Date().toTimestamp()
        tf.txnParticulars = params.txnParticulars ?: "Cash withdrawal from account ${depositAccount.accountNo}"
        tf.txnType = TxnType.get(2) // Cash withdrawal type
        tf.txnTemplate = TxnTemplate.get(2)
        tf.user = user
        tf.beneficiary = depositAccount.customer
        tf.save(flush: true, failOnError: true)

        // Create deposit ledger entry
        tc.txnFile = tf
        tc.acct = depositAccount
        tc.txnDate = tf.txnDate
        tc.debitAmt = tc.debitAmt
        tc.creditAmt = 0
        tc.bal = depositAccount.ledgerBalAmt - tc.debitAmt
        tc.passbookBal = 0 // Will be updated when passbook is printed
        tc.txnRef = tf.txnRef
        tc.save(flush: true, failOnError: true)

        // Update deposit account balance
        depositAccount.ledgerBalAmt -= tc.debitAmt
        depositAccount.availableBalAmt -= tc.debitAmt
        depositAccount.save(flush: true)

        // Update teller cash balance
        def tellerBalance = TxnTellerBalance.findByUserAndCurrency(user, tf.currency)
        if (tellerBalance) {
            tellerBalance.cashOutAmt += tc.debitAmt
            tellerBalance.save(flush: true)
        }

        // Update teller balance status
        userMasterService.updateTellerBalanceStatus(false)
        
        // Generate GL transactions
        glTransactionService.saveTxnBreakdown(tf.id)

        def description = "Cash withdrawal processed - Account: ${depositAccount.accountNo} Amount: ${tc.debitAmt}"
        auditLogService.insert('120', 'TLR00600', description, 'TellerDeposit', null, null, null, tf.id)
        
        session["transactionFileId"] = tf.id
        session["jrxmlTcId"] = tc.id
        session["type"] = depositAccount.type.id
        
        redirect(controller: "tellerCore", action: "txnSuccess")
    }

    /**
     * Validate passbook balance
     */
    def validatePassbookBal() {
        if (!params.acctNo || !params.passbookBal) {
            render([valid: false, message: "Missing account number or passbook balance"] as JSON)
            return
        }

        def depositAccount = Deposit.findByAccountNo(params.acctNo)
        if (!depositAccount) {
            render([valid: false, message: "Account not found"] as JSON)
            return
        }

        def passbookBalance = params.passbookBal.toDouble()
        def ledgerBalance = depositAccount.ledgerBalAmt

        // Allow small variance for rounding differences
        def variance = Math.abs(passbookBalance - ledgerBalance)
        def isValid = variance <= 0.01

        render([
            valid: isValid,
            passbookBalance: passbookBalance,
            ledgerBalance: ledgerBalance,
            variance: variance,
            message: isValid ? "Balance validated" : "Balance mismatch detected"
        ] as JSON)
        return
    }

    /**
     * Get deposit account details
     */
    def getDepositAccountDetails() {
        def accountNo = params.accountNo
        if (!accountNo) {
            render([error: "Account number required"] as JSON)
            return
        }

        def depositAccount = Deposit.findByAccountNo(accountNo)
        if (!depositAccount) {
            render([error: "Account not found"] as JSON)
            return
        }

        render([
            accountNo: depositAccount.accountNo,
            customerName: depositAccount.customer.displayName,
            accountType: depositAccount.type.description,
            currency: depositAccount.currency.code,
            ledgerBalance: depositAccount.ledgerBalAmt,
            availableBalance: depositAccount.availableBalAmt,
            status: depositAccount.status.description,
            dateOpened: depositAccount.dateOpened.format("MM/dd/yyyy"),
            maturityDate: depositAccount.maturityDate?.format("MM/dd/yyyy"),
            interestRate: depositAccount.interestRate
        ] as JSON)
        return
    }
}
