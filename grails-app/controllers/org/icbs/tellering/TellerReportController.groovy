package org.icbs.tellering

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnPassbookLine
import groovy.sql.Sql

/**
 * TellerReportController - Handles teller reporting operations
 * 
 * This controller manages teller reporting operations including:
 * - Transaction slip printing
 * - Validation slip printing
 * - Passbook transaction printing
 * - Transaction summary reports
 * - Teller activity reports
 * - Daily transaction reports
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional(readOnly = true)
class TellerReportController {
    
    // Service Dependencies
    def jasperService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        printValidationSlip: "GET",
        printTransactionSlip: "GET",
        printToPassbookTransactions: "GET"
    ]

    /**
     * Print validation slip
     */
    def printValidationSlip() {    
        println params.txnFile
        def newtid = params.txnFile.toInteger()
        session["transactionFileId"] = newtid
        
        try {
            params._name = "VALIDATION SLIP"
            params._format = "PDF"
            params._file = "validation_slip"
            params.id = newtid
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            response.setContentType('application/pdf')
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', 'inline; filename=validation_slip.pdf')
            response.setHeader('Expires', '0')
            response.setHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
            response.outputStream << bytes
            response.outputStream.flush()
            
            def description = "Validation slip printed for transaction ID: ${newtid}"
            auditLogService.insert('120', 'TLR01500', description, 'TellerReport', null, null, null, newtid)
            
        } catch(Exception e) {
            log.error("Error printing validation slip", e)
            flash.message = "Error generating validation slip: ${e.message}|error|alert"
            redirect(action: "index", controller: "tellerCore")
        }
    }

    /**
     * Print transaction slip
     */
    def printTransactionSlip() {    
        def newtid = session["transactionFileId"]
        println "Transaction File ID: " + newtid
        
        if (!newtid) {
            flash.message = "No transaction selected for printing|error|alert"
            redirect(action: "index", controller: "tellerCore")
            return
        }
        
        try {
            def txnFile = TxnFile.get(newtid)
            if (!txnFile) {
                flash.message = "Transaction not found|error|alert"
                redirect(action: "index", controller: "tellerCore")
                return
            }
            
            // Determine report file based on transaction type
            def indicator = ""
            def txnTypeNo = txnFile.txnType.id
            
            if (txnTypeNo >= 1 && txnTypeNo <= 55) {
                indicator = "Deposit"
                if (txnTypeNo >= 1 && txnTypeNo <= 10) {
                    params._file = "txn_deposit_slip"
                } else if (txnTypeNo >= 11 && txnTypeNo <= 20) {
                    params._file = "txn_withdrawal_slip"
                } else {
                    params._file = "txn_deposit_other"
                }
            } else if (txnTypeNo >= 56 && txnTypeNo <= 78) {
                indicator = "Loan"
                if ((txnTypeNo >= 56 && txnTypeNo <= 57) || (txnTypeNo >= 66 && txnTypeNo <= 69)) {
                    params._file = "txn_loan_disbursement"
                } else if ((txnTypeNo >= 58 && txnTypeNo <= 65) || (txnTypeNo >= 70 && txnTypeNo <= 71)) {
                    params._file = "txn_loan_payment"
                }
            } else {
                indicator = "Other"
                params._file = "txn_other_slip"
            }
            
            params._name = "TRANSACTION SLIP"
            params._format = "PDF"
            params.id = newtid
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            response.setContentType('application/pdf')
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', 'inline; filename=transaction_slip.pdf')
            response.setHeader('Expires', '0')
            response.setHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
            response.outputStream << bytes
            response.outputStream.flush()
            
            def description = "Transaction slip printed for transaction ID: ${newtid} (${indicator})"
            auditLogService.insert('120', 'TLR01600', description, 'TellerReport', null, null, null, newtid)
            
        } catch(Exception e) {
            log.error("Error printing transaction slip", e)
            flash.message = "Error generating transaction slip: ${e.message}|error|alert"
            redirect(action: "index", controller: "tellerCore")
        }
    }

    /**
     * Print passbook transactions
     */
    def printToPassbookTransactions() {  
        def pbPrintCode = params.pbPrintCode
        
        if (!pbPrintCode) {
            flash.message = "No passbook print code provided|error|alert"
            redirect(action: "index", controller: "tellerCore")
            return
        }
        
        try {
            params._name = "PASSBOOK TRANSACTIONS"
            params._format = "PDF"
            params._file = "passbook_transactions"
            params.pbPrintCode = pbPrintCode
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            response.setContentType('application/pdf')
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', 'inline; filename=passbook_transactions.pdf')
            response.setHeader('Expires', '0')
            response.setHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
            response.outputStream << bytes
            response.outputStream.flush()
            
            def description = "Passbook transactions printed - Print Code: ${pbPrintCode}"
            auditLogService.insert('120', 'TLR01700', description, 'TellerReport', null, null, null, null)
            
        } catch(Exception e) {
            log.error("Error printing passbook transactions", e)
            flash.message = "Error generating passbook transactions: ${e.message}|error|alert"
            redirect(action: "index", controller: "tellerCore")
        }
    }

    /**
     * View transaction summary
     */
    def viewTxnSummary() {
        def user = UserMaster.get(session.user_id)
        def branch = user.branch
        
        def summaryData = generateTransactionSummary(user, branch.runDate)
        
        render(view: '/tellering/txnSummaryView/viewSummary', model: [
            summaryData: summaryData,
            user: user,
            branch: branch,
            runDate: branch.runDate
        ])
    }

    /**
     * Generate daily teller report
     */
    def generateDailyTellerReport() {
        def user = UserMaster.get(session.user_id)
        def branch = user.branch
        def reportDate = params.reportDate ? Date.parse("MM/dd/yyyy", params.reportDate) : branch.runDate
        
        try {
            def reportData = generateDailyReportData(user, reportDate)
            
            params._name = "DAILY TELLER REPORT"
            params._format = "PDF"
            params._file = "daily_teller_report"
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), reportData)
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            response.setContentType('application/pdf')
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', 'inline; filename=daily_teller_report.pdf')
            response.outputStream << bytes
            response.outputStream.flush()
            
            def description = "Daily teller report generated for ${reportDate.format('MM/dd/yyyy')}"
            auditLogService.insert('120', 'TLR01800', description, 'TellerReport', null, null, null, null)
            
        } catch(Exception e) {
            log.error("Error generating daily teller report", e)
            flash.message = "Error generating daily report: ${e.message}|error|alert"
            redirect(action: "viewTxnSummary")
        }
    }

    /**
     * Generate teller activity report
     */
    def generateTellerActivityReport() {
        def fromDate = params.fromDate ? Date.parse("MM/dd/yyyy", params.fromDate) : new Date()
        def toDate = params.toDate ? Date.parse("MM/dd/yyyy", params.toDate) : new Date()
        def userId = params.userId ? params.userId.toInteger() : session.user_id
        
        try {
            def reportData = generateActivityReportData(userId, fromDate, toDate)
            
            params._name = "TELLER ACTIVITY REPORT"
            params._format = "PDF"
            params._file = "teller_activity_report"
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), reportData)
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            response.setContentType('application/pdf')
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', 'inline; filename=teller_activity_report.pdf')
            response.outputStream << bytes
            response.outputStream.flush()
            
            def description = "Teller activity report generated for user ${userId} from ${fromDate.format('MM/dd/yyyy')} to ${toDate.format('MM/dd/yyyy')}"
            auditLogService.insert('120', 'TLR01900', description, 'TellerReport', null, null, null, null)
            
        } catch(Exception e) {
            log.error("Error generating teller activity report", e)
            flash.message = "Error generating activity report: ${e.message}|error|alert"
            redirect(action: "viewTxnSummary")
        }
    }

    // Helper methods
    private def generateTransactionSummary(UserMaster user, Date txnDate) {
        def sql = new Sql(dataSource)
        def summary = [:]
        
        try {
            // Transaction counts by type
            summary.transactionCounts = sql.rows("""
                SELECT tt.description, COUNT(*) as count, SUM(tf.txn_amt) as total_amount
                FROM txn_file tf
                JOIN txn_type tt ON tf.txn_type_id = tt.id
                WHERE tf.user_id = ? AND tf.txn_date = ? AND tf.status_id = 2
                GROUP BY tt.id, tt.description
                ORDER BY count DESC
            """, [user.id, txnDate])
            
            // Cash summary
            summary.cashSummary = sql.firstRow("""
                SELECT 
                    SUM(CASE WHEN tt.description LIKE '%Deposit%' OR tt.description LIKE '%Receipt%' THEN tf.txn_amt ELSE 0 END) as cash_received,
                    SUM(CASE WHEN tt.description LIKE '%Withdrawal%' OR tt.description LIKE '%Payment%' THEN tf.txn_amt ELSE 0 END) as cash_paid,
                    COUNT(*) as total_transactions
                FROM txn_file tf
                JOIN txn_type tt ON tf.txn_type_id = tt.id
                WHERE tf.user_id = ? AND tf.txn_date = ? AND tf.status_id = 2
            """, [user.id, txnDate])
            
            // Check summary
            summary.checkSummary = sql.firstRow("""
                SELECT 
                    COUNT(CASE WHEN tt.description LIKE '%Check%' AND tt.description LIKE '%Deposit%' THEN 1 END) as checks_deposited,
                    COUNT(CASE WHEN tt.description LIKE '%Check%' AND tt.description LIKE '%Encash%' THEN 1 END) as checks_encashed,
                    SUM(CASE WHEN tt.description LIKE '%Check%' THEN tf.txn_amt ELSE 0 END) as total_check_amount
                FROM txn_file tf
                JOIN txn_type tt ON tf.txn_type_id = tt.id
                WHERE tf.user_id = ? AND tf.txn_date = ? AND tf.status_id = 2
            """, [user.id, txnDate])
            
        } catch (Exception e) {
            log.error("Error generating transaction summary", e)
            summary.error = "Error generating summary: ${e.message}"
        }
        
        return summary
    }

    private def generateDailyReportData(UserMaster user, Date reportDate) {
        def sql = new Sql(dataSource)
        def reportData = []
        
        try {
            def transactions = sql.rows("""
                SELECT tf.*, tt.description as txn_type_desc, c.display_name as customer_name
                FROM txn_file tf
                JOIN txn_type tt ON tf.txn_type_id = tt.id
                LEFT JOIN customer c ON tf.beneficiary_id = c.id
                WHERE tf.user_id = ? AND tf.txn_date = ? AND tf.status_id = 2
                ORDER BY tf.txn_timestamp
            """, [user.id, reportDate])
            
            reportData = transactions.collect { row ->
                [
                    txnTime: row.txn_timestamp,
                    txnType: row.txn_type_desc,
                    accountNo: row.acct_no,
                    customerName: row.customer_name,
                    amount: row.txn_amt,
                    reference: row.txn_ref,
                    particulars: row.txn_particulars
                ]
            }
            
        } catch (Exception e) {
            log.error("Error generating daily report data", e)
        }
        
        return reportData
    }

    private def generateActivityReportData(Integer userId, Date fromDate, Date toDate) {
        def sql = new Sql(dataSource)
        def reportData = [:]
        
        try {
            // Activity summary
            reportData.summary = sql.firstRow("""
                SELECT 
                    COUNT(*) as total_transactions,
                    SUM(tf.txn_amt) as total_amount,
                    COUNT(DISTINCT tf.txn_date) as active_days
                FROM txn_file tf
                WHERE tf.user_id = ? AND tf.txn_date BETWEEN ? AND ? AND tf.status_id = 2
            """, [userId, fromDate, toDate])
            
            // Daily breakdown
            reportData.dailyBreakdown = sql.rows("""
                SELECT 
                    tf.txn_date,
                    COUNT(*) as transaction_count,
                    SUM(tf.txn_amt) as daily_amount
                FROM txn_file tf
                WHERE tf.user_id = ? AND tf.txn_date BETWEEN ? AND ? AND tf.status_id = 2
                GROUP BY tf.txn_date
                ORDER BY tf.txn_date
            """, [userId, fromDate, toDate])
            
        } catch (Exception e) {
            log.error("Error generating activity report data", e)
        }
        
        return reportData
    }
}
