package org.icbs.tellering

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnCashCheckBlotter
import org.icbs.tellering.TxnTellerCash
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import grails.converters.JSON
import groovy.json.JsonBuilder
import groovy.sql.Sql
import javax.sql.DataSource

/**
 * REFACTORED: Transaction Report Controller
 * Extracted from TelleringController.groovy (7,319 lines)
 * Handles all transaction reporting operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class TransactionReportController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def jasperService
    def auditLogService
    DataSource dataSource
    
    static allowedMethods = [
        printValidationSlip: "GET",
        printTransactionSlip: "GET",
        printToPassbookTransactions: "GET",
        viewTxnSummary: "GET",
        receiveTellerCashJSON: "GET"
    ]
    
    // =====================================================
    // TRANSACTION SLIP PRINTING
    // =====================================================
    
    /**
     * Print validation slip for transaction
     */
    def printValidationSlip() {
        log.info("Printing validation slip for transaction: ${params.txnFile}")
        
        try {
            def transactionFileId = params.txnFile.toInteger()
            def mapType = session.map
            
            Map reportParams = [:]
            reportParams._format = "PDF"
            
            switch (mapType) {
                case "loan":
                    reportParams._name = "Validation Slip Loan"
                    reportParams._file = "ValidationSlipLoan.jasper"
                    reportParams.txnID = transactionFileId
                    break
                    
                case "loandisb":
                    reportParams._name = "Validation Slip Disb"
                    reportParams._file = "ValidationSlipLoan.jasper"
                    reportParams.txnID = transactionFileId
                    break
                    
                case "deposit":
                    reportParams._name = "Validation Slip Deposit"
                    reportParams._file = "ValidationSlipDeposit.jasper"
                    reportParams.TxnFileDepositid = transactionFileId
                    break
                    
                case "billspayment":
                    reportParams._name = "Validation Slip Bills Payment"
                    reportParams._file = "ValidationSlipBillsPayment.jasper"
                    reportParams.TxnFileBillsPaymentid = transactionFileId
                    break
                    
                default:
                    reportParams._name = "Validation Slip"
                    reportParams._file = "ValidationSlip.jasper"
                    reportParams.txnID = transactionFileId
            }
            
            // Generate report
            def reportDef = jasperService.buildReportDefinition(reportParams, request.getLocale(), [])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            // Set response headers
            response.setContentType('application/pdf')
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', 'inline; filename=validation_slip.pdf')
            response.setHeader('Expires', '0')
            response.setHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
            
            response.outputStream << bytes
            response.outputStream.flush()
            
            // Audit logging
            auditLogService.insert('110', 'TRP01100', 
                "Validation slip printed for transaction: ${transactionFileId}", 
                'TransactionReportController', null, null, 'tellering/printValidationSlip', transactionFileId)
            
        } catch (Exception e) {
            log.error("Error printing validation slip", e)
            response.sendError(500, "Error generating validation slip")
        }
    }
    
    /**
     * Print transaction slip
     */
    def printTransactionSlip() {
        log.info("Printing transaction slip for transaction: ${params.txnFile}")
        
        try {
            def transactionFileId = params.txnFile.toInteger()
            def mapType = session.map
            def prevTxnId = session.prevTxnDepAcctLedgerId
            def jrxmlTcId = session.jrxmlTcId
            
            Map reportParams = [:]
            reportParams._format = "PDF"
            
            switch (mapType) {
                case "loan":
                    reportParams._name = "Loan Transactions SLIP"
                    reportParams._file = "TransactionSlipLoan.jasper"
                    if (prevTxnId == null) {
                        reportParams.PrevTxnFileID = null
                        reportParams.LatesTxnFileID = transactionFileId
                        reportParams.TxnTemplateID = jrxmlTcId?.toInteger()
                    } else {
                        reportParams.PrevTxnFileID = prevTxnId.toInteger()
                        reportParams.LatesTxnFileID = transactionFileId
                        reportParams.TxnTemplateID = jrxmlTcId?.toInteger()
                    }
                    break
                    
                case "deposit":
                    reportParams._name = "Deposit Transaction SLIP"
                    reportParams._file = "TransactionSlipDeposit.jasper"
                    if (prevTxnId == null) {
                        reportParams.PrevTxnFileID = null
                        reportParams.LatesTxnFileID = transactionFileId
                        reportParams.TxnTemplateID = jrxmlTcId?.toInteger()
                    } else {
                        reportParams.PrevTxnFileID = prevTxnId.toInteger()
                        reportParams.LatesTxnFileID = transactionFileId
                        reportParams.TxnTemplateID = jrxmlTcId?.toInteger()
                    }
                    break
                    
                case "billspayment":
                    reportParams._name = "Other Transaction Bills Payment"
                    reportParams._file = "TransactionSlipBillsPayment.jasper"
                    if (prevTxnId == null) {
                        reportParams.txnBillsPaymentID1 = transactionFileId
                        reportParams.txnBillsPaymentID2 = transactionFileId
                    } else {
                        reportParams.txnBillsPaymentID1 = prevTxnId.toInteger()
                        reportParams.txnBillsPaymentID2 = transactionFileId
                    }
                    break
                    
                default:
                    reportParams._name = "Transaction SLIP"
                    reportParams._file = "TransactionSlip.jasper"
                    reportParams.txnID = transactionFileId
            }
            
            // Generate report
            def reportDef = jasperService.buildReportDefinition(reportParams, request.getLocale(), [])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            // Set response headers
            response.setContentType('application/pdf')
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', 'inline; filename=transaction_slip.pdf')
            response.setHeader('Expires', '0')
            response.setHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
            
            response.outputStream << bytes
            response.outputStream.flush()
            
            // Audit logging
            auditLogService.insert('110', 'TRP01200', 
                "Transaction slip printed for transaction: ${transactionFileId}", 
                'TransactionReportController', null, null, 'tellering/printTransactionSlip', transactionFileId)
            
        } catch (Exception e) {
            log.error("Error printing transaction slip", e)
            response.sendError(500, "Error generating transaction slip")
        }
    }
    
    /**
     * Print passbook transactions
     */
    def printToPassbookTransactions() {
        log.info("Printing passbook transactions")
        
        try {
            def pbPrintLine = params.pbPrintLine
            def accountType = session.type
            
            Map reportParams = [:]
            reportParams._format = "PDF"
            reportParams.pbId = pbPrintLine
            
            if (accountType == 1 || accountType == 4) {
                reportParams._name = "Passbook Transaction SA"
                reportParams._file = "PassbookTransactionSA5.jasper"
            } else if (accountType == 3) {
                reportParams._name = "Passbook Transaction FD"
                reportParams._file = "PassbookTransactionSA5.jasper"
            } else {
                reportParams._name = "Passbook Transaction"
                reportParams._file = "PassbookTransaction.jasper"
            }
            
            // Generate report
            def reportDef = jasperService.buildReportDefinition(reportParams, request.getLocale(), [])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            // Set response headers
            response.setContentType('application/pdf')
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', 'inline; filename=passbook_transactions.pdf')
            response.setHeader('Expires', '0')
            response.setHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
            
            response.outputStream << bytes
            response.outputStream.flush()
            
            // Audit logging
            auditLogService.insert('110', 'TRP01300', 
                "Passbook transactions printed - Print Code: ${pbPrintLine}", 
                'TransactionReportController', null, null, 'tellering/printToPassbookTransactions', session.user_id)
            
        } catch (Exception e) {
            log.error("Error printing passbook transactions", e)
            response.sendError(500, "Error generating passbook transactions")
        }
    }
    
    // =====================================================
    // TRANSACTION SUMMARY REPORTS
    // =====================================================
    
    /**
     * View transaction summary
     */
    def viewTxnSummary() {
        log.info("Viewing transaction summary")
        
        try {
            // Audit logging
            auditLogService.insert('110', 'TRP01400', 
                "Transaction summary view accessed", 
                'TransactionReportController', null, null, 'tellering/viewTxnSummary', session.user_id)
            
            render(view: '/tellering/txnSummaryView/viewSummary')
            
        } catch (Exception e) {
            log.error("Error viewing transaction summary", e)
            flash.message = 'Error accessing transaction summary|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * Receive teller cash data in JSON format
     */
    def receiveTellerCashJSON() {
        log.debug("Receiving teller cash JSON data")
        
        try {
            def sql = new Sql(dataSource)
            String query
            
            if (params.txn.toInteger() == 0) {
                query = """
                    SELECT A.*, CONCAT(TRIM(B.name1), ' ', TRIM(B.name2), ' ', TRIM(B.name3)) as fullname 
                    FROM txn_file A 
                    LEFT JOIN user_master B ON A.user_id = B.id 
                    WHERE A.id = ?
                """
            } else {
                query = """
                    SELECT *, txn_amt as total 
                    FROM txn_teller_cash 
                    WHERE txn_file_id = ?
                """
            }
            
            def results = sql.rows(query, [params.recid])
            def jsonResult = new JsonBuilder(results).toPrettyString()
            
            // Audit logging
            auditLogService.insert('110', 'TRP01500', 
                "Teller cash JSON data retrieved - Record ID: ${params.recid}", 
                'TransactionReportController', null, null, 'tellering/receiveTellerCashJSON', session.user_id)
            
            render(text: jsonResult) as JSON
            
        } catch (Exception e) {
            log.error("Error receiving teller cash JSON", e)
            render([error: 'Error retrieving teller cash data'] as JSON)
        }
    }
    
    /**
     * Generate daily transaction report
     */
    def generateDailyTransactionReport() {
        log.info("Generating daily transaction report")
        
        try {
            Date reportDate = params.reportDate ? Date.parse('yyyy-MM-dd', params.reportDate) : new Date()
            UserMaster user = UserMaster.get(session.user_id)
            
            Map reportData = generateDailyReportData(user, reportDate)
            
            Map reportParams = [:]
            reportParams._name = "Daily Transaction Report"
            reportParams._format = "PDF"
            reportParams._file = "DailyTransactionReport.jasper"
            reportParams.reportDate = reportDate
            reportParams.userId = user.id
            
            // Generate report
            def reportDef = jasperService.buildReportDefinition(reportParams, request.getLocale(), [])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            // Set response headers
            response.setContentType('application/pdf')
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', 'inline; filename=daily_transaction_report.pdf')
            
            response.outputStream << bytes
            response.outputStream.flush()
            
            // Audit logging
            auditLogService.insert('110', 'TRP01600', 
                "Daily transaction report generated for date: ${reportDate}", 
                'TransactionReportController', null, null, 'tellering/generateDailyTransactionReport', session.user_id)
            
        } catch (Exception e) {
            log.error("Error generating daily transaction report", e)
            response.sendError(500, "Error generating daily transaction report")
        }
    }
    
    /**
     * Generate teller balance report
     */
    def generateTellerBalanceReport() {
        log.info("Generating teller balance report")
        
        try {
            Date reportDate = params.reportDate ? Date.parse('yyyy-MM-dd', params.reportDate) : new Date()
            UserMaster user = UserMaster.get(session.user_id)
            
            Map reportData = generateTellerBalanceReportData(user, reportDate)
            
            Map reportParams = [:]
            reportParams._name = "Teller Balance Report"
            reportParams._format = "PDF"
            reportParams._file = "TellerBalanceReport.jasper"
            reportParams.reportDate = reportDate
            reportParams.userId = user.id
            
            // Generate report
            def reportDef = jasperService.buildReportDefinition(reportParams, request.getLocale(), [])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            // Set response headers
            response.setContentType('application/pdf')
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', 'inline; filename=teller_balance_report.pdf')
            
            response.outputStream << bytes
            response.outputStream.flush()
            
            // Audit logging
            auditLogService.insert('110', 'TRP01700', 
                "Teller balance report generated for date: ${reportDate}", 
                'TransactionReportController', null, null, 'tellering/generateTellerBalanceReport', session.user_id)
            
        } catch (Exception e) {
            log.error("Error generating teller balance report", e)
            response.sendError(500, "Error generating teller balance report")
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Generate daily report data
     */
    private Map generateDailyReportData(UserMaster user, Date reportDate) {
        Map reportData = [:]
        
        try {
            // Get all transactions for the date
            List<TxnFile> transactions = TxnFile.findAllByUserAndTxnDate(user, reportDate)
            
            reportData.totalTransactions = transactions.size()
            reportData.totalAmount = transactions.sum { it.txnAmt } ?: 0
            
            // Group by transaction type
            reportData.byType = transactions.groupBy { it.txnType?.description }
                .collectEntries { type, txns ->
                    [type, [count: txns.size(), amount: txns.sum { it.txnAmt } ?: 0]]
                }
            
            // Cash and check summary
            List<TxnCashCheckBlotter> blotterEntries = TxnCashCheckBlotter.createCriteria().list {
                eq("user", user)
                txnFile {
                    eq("txnDate", reportDate)
                }
            }
            
            reportData.cashSummary = [
                cashIn: blotterEntries.sum { it.cashInAmt } ?: 0,
                cashOut: blotterEntries.sum { it.cashOutAmt } ?: 0,
                checkIn: blotterEntries.sum { it.checkInAmt } ?: 0,
                checkOut: blotterEntries.sum { it.checkOutAmt } ?: 0
            ]
            
        } catch (Exception e) {
            log.error("Error generating daily report data", e)
            reportData.error = "Error generating report data"
        }
        
        return reportData
    }
    
    /**
     * Generate teller balance report data
     */
    private Map generateTellerBalanceReportData(UserMaster user, Date reportDate) {
        Map reportData = [:]
        
        try {
            // Get teller balance data
            def sql = new Sql(dataSource)
            def query = """
                SELECT currency_id, txn_date, 
                       SUM(cash_in_amt) as cashin, 
                       SUM(cash_out_amt) as cashout,
                       SUM(check_in_amt) as checkin,
                       SUM(check_out_amt) as checkout
                FROM txn_cash_check_blotter 
                WHERE user_id = ? AND txn_date = ?
                GROUP BY currency_id, txn_date
            """
            
            def balanceData = sql.rows(query, [user.id, reportDate])
            
            reportData.balanceData = balanceData
            reportData.totalCashIn = balanceData.sum { it.cashin } ?: 0
            reportData.totalCashOut = balanceData.sum { it.cashout } ?: 0
            reportData.totalCheckIn = balanceData.sum { it.checkin } ?: 0
            reportData.totalCheckOut = balanceData.sum { it.checkout } ?: 0
            reportData.netCash = reportData.totalCashIn - reportData.totalCashOut
            reportData.netCheck = reportData.totalCheckIn - reportData.totalCheckOut
            
        } catch (Exception e) {
            log.error("Error generating teller balance report data", e)
            reportData.error = "Error generating teller balance report data"
        }
        
        return reportData
    }
}
