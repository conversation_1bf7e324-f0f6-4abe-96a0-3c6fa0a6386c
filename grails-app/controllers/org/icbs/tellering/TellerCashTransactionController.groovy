package org.icbs.tellering

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.TxnTemplate
import org.icbs.admin.Currency
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnTellerCash
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.TxnType

/**
 * TellerCashTransactionController - Handles teller cash operations
 * 
 * This controller manages teller cash operations including:
 * - Cash from vault transactions
 * - Cash to vault transactions
 * - Teller cash transfers
 * - Cash breakdown management
 * - Vault operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class TellerCashTransactionController {
    
    // Service Dependencies
    def userMasterService
    def glTransactionService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        saveTellerCashFromVaultTxn: "POST",
        saveTellerCashToVaultTxn: "POST",
        saveTellerCashTransferTxn: "POST",
        receiveTellerCashTransferSave: "POST",
        receiveTellerCashCancel: "POST"
    ]

    /**
     * Create teller cash from vault transaction
     */
    def createTellerCashFromVaultTxn() {
        def user = UserMaster.get(session.user_id)
        if (user.cash) {
            def txnFileInstance = new TxnFile()
            def txnCashFromVaultInstance = new TxnTellerCash()
            render(view:'/tellering/txnCashFromVault/create', model: [
                txnCashFromVaultInstance: txnCashFromVaultInstance
            ])       
        } else {
            flash.message = 'Error! No cash account defined|error|alert'
            redirect(action: "index", controller: "tellerCore")            
        }
    }

    /**
     * Save teller cash from vault transaction
     */
    @Transactional
    def saveTellerCashFromVaultTxn(TxnTellerCash tc, TxnFile tf) {       
        println params // Error check
        
        def user = UserMaster.get(session.user_id)
        def branch = Branch.get(user.branchId)
        
        // Validate transaction
        if (!tc.netAmtReceived || tc.netAmtReceived <= 0) {
            flash.message = 'Invalid amount|error|alert'
            render(view:'/tellering/txnCashFromVault/create', model: [
                txnCashFromVaultInstance: tc
            ])
            return
        }

        // Create transaction file
        tf.txnCode = "CFVT"
        tf.txnDescription = "Cash From Vault"
        tf.txnDate = branch.runDate
        tf.currency = Currency.get(1) // Default currency
        tf.txnAmt = tc.netAmtReceived
        tf.txnRef = "Cash From Vault - " + user.username
        tf.status = ConfigItemStatus.get(2)
        tf.branch = branch
        tf.txnTimestamp = new Date().toTimestamp()
        tf.txnParticulars = "Cash received from vault by teller " + user.username
        tf.txnType = TxnType.get(79) // Cash from vault type
        tf.txnTemplate = TxnTemplate.get(79)
        tf.user = user
        tf.save(flush: true, failOnError: true)

        // Create teller cash record
        tc.txnFile = tf
        tc.user = user
        tc.branch = branch
        tc.txnDate = tf.txnDate
        tc.save(flush: true, failOnError: true)

        // Update teller balance
        def tb = TxnTellerBalance.findByUserAndCurrency(user, tf.currency)
        if (!tb) {
            tb = new TxnTellerBalance(
                user: user,
                currency: tf.currency,
                cashInAmt: 0,
                cashOutAmt: 0,
                checkInAmt: 0,
                checkOutAmt: 0
            )
        }
        
        tb.cashInAmt += tc.netAmtReceived
        tb.save(flush: true)

        // Create transaction breakdown
        def txnBreakdownInstance = new TxnBreakdown(
            debitAmt: tc.netAmtReceived,
            txnDate: tf.txnDate,
            txnFile: tf
        )
        txnBreakdownInstance.save(flush: true)

        // Update teller balance status
        userMasterService.updateTellerBalanceStatus(false)
        
        // Generate GL transactions
        glTransactionService.saveTxnBreakdown(tf.id)

        def description = "Cash from vault transaction processed - Amount: ${tc.netAmtReceived}"
        auditLogService.insert('120', 'TLR00200', description, 'TellerCash', null, null, null, tf.id)
        
        session["transactionFileId"] = tf.id
        redirect(controller: "tellerCore", action: "txnSuccess")
    }

    /**
     * Create teller cash to vault transaction
     */
    def createTellerCashToVaultTxn() {
        def user = UserMaster.get(session.user_id)
        if (user.cash) {
            def txnFileInstance = new TxnFile()
            def txnCashToVaultInstance = new TxnTellerCash()
            render(view:'/tellering/txnCashToVault/create', model: [
                txnCashToVaultInstance: txnCashToVaultInstance
            ])       
        } else {
            flash.message = 'Error! No cash account defined|error|alert'
            redirect(action: "index", controller: "tellerCore")            
        } 
    }

    /**
     * Save teller cash to vault transaction
     */
    @Transactional
    def saveTellerCashToVaultTxn(TxnTellerCash tc, TxnFile tf) {
        println params // Error check
        
        def user = UserMaster.get(session.user_id)
        def branch = Branch.get(user.branchId)
        
        // Validate transaction
        if (!tc.netAmtPaidOut || tc.netAmtPaidOut <= 0) {
            flash.message = 'Invalid amount|error|alert'
            render(view:'/tellering/txnCashToVault/create', model: [
                txnCashToVaultInstance: tc
            ])
            return
        }

        // Check if teller has sufficient cash
        def tb = TxnTellerBalance.findByUserAndCurrency(user, Currency.get(1))
        if (!tb || tb.cashInAmt < tc.netAmtPaidOut) {
            flash.message = 'Insufficient cash balance|error|alert'
            render(view:'/tellering/txnCashToVault/create', model: [
                txnCashToVaultInstance: tc
            ])
            return
        }

        // Create transaction file
        tf.txnCode = "CTVT"
        tf.txnDescription = "Cash To Vault"
        tf.txnDate = branch.runDate
        tf.currency = Currency.get(1)
        tf.txnAmt = tc.netAmtPaidOut
        tf.txnRef = "Cash To Vault - " + user.username
        tf.status = ConfigItemStatus.get(2)
        tf.branch = branch
        tf.txnTimestamp = new Date().toTimestamp()
        tf.txnParticulars = "Cash sent to vault by teller " + user.username
        tf.txnType = TxnType.get(80) // Cash to vault type
        tf.txnTemplate = TxnTemplate.get(80)
        tf.user = user
        tf.save(flush: true, failOnError: true)

        // Create teller cash record
        tc.txnFile = tf
        tc.user = user
        tc.branch = branch
        tc.txnDate = tf.txnDate
        tc.save(flush: true, failOnError: true)

        // Update teller balance
        tb.cashOutAmt += tc.netAmtPaidOut
        tb.save(flush: true)

        // Create transaction breakdown
        def txnBreakdownInstance = new TxnBreakdown(
            creditAmt: tc.netAmtPaidOut,
            txnDate: tf.txnDate,
            txnFile: tf
        )
        txnBreakdownInstance.save(flush: true)

        // Update teller balance status
        userMasterService.updateTellerBalanceStatus(false)
        
        // Generate GL transactions
        glTransactionService.saveTxnBreakdown(tf.id)

        def description = "Cash to vault transaction processed - Amount: ${tc.netAmtPaidOut}"
        auditLogService.insert('120', 'TLR00300', description, 'TellerCash', null, null, null, tf.id)
        
        session["transactionFileId"] = tf.id
        redirect(controller: "tellerCore", action: "txnSuccess")
    }

    /**
     * Create teller cash transfer transaction
     */
    def createTellerCashTransferTxn() {
        def user = UserMaster.get(session.user_id)
        if (user.cash) {
            def txnFileInstance = new TxnFile()
            def txnCashTransferInstance = new TxnTellerCash()
            
            // Get list of other tellers for transfer
            def otherTellers = UserMaster.createCriteria().list {
                and {
                    eq("branch", user.branch)
                    ne("id", user.id)
                    isNotNull("cash")
                    eq("configItemStatus", ConfigItemStatus.get(2))
                }
            }
            
            render(view:'/tellering/txnCashTransfer/create', model: [
                txnCashTransferInstance: txnCashTransferInstance,
                otherTellers: otherTellers
            ])       
        } else {
            flash.message = 'Error! No cash account defined|error|alert'
            redirect(action: "index", controller: "tellerCore")            
        }
    }

    /**
     * Save teller cash transfer transaction
     */
    @Transactional
    def saveTellerCashTransferTxn(TxnTellerCash tc, TxnFile tf) {
        println params // Error check
        
        def user = UserMaster.get(session.user_id)
        def branch = Branch.get(user.branchId)
        def receivingTeller = UserMaster.get(params.receivingTellerId)
        
        // Validate transaction
        if (!tc.netAmtPaidOut || tc.netAmtPaidOut <= 0) {
            flash.message = 'Invalid amount|error|alert'
            redirect(action: "createTellerCashTransferTxn")
            return
        }
        
        if (!receivingTeller) {
            flash.message = 'Invalid receiving teller|error|alert'
            redirect(action: "createTellerCashTransferTxn")
            return
        }

        // Check if sender has sufficient cash
        def senderBalance = TxnTellerBalance.findByUserAndCurrency(user, Currency.get(1))
        if (!senderBalance || senderBalance.cashInAmt < tc.netAmtPaidOut) {
            flash.message = 'Insufficient cash balance|error|alert'
            redirect(action: "createTellerCashTransferTxn")
            return
        }

        // Create transaction file
        tf.txnCode = "CTTR"
        tf.txnDescription = "Cash Transfer"
        tf.txnDate = branch.runDate
        tf.currency = Currency.get(1)
        tf.txnAmt = tc.netAmtPaidOut
        tf.txnRef = "Cash Transfer from ${user.username} to ${receivingTeller.username}"
        tf.status = ConfigItemStatus.get(1) // Pending - requires receiving teller confirmation
        tf.branch = branch
        tf.txnTimestamp = new Date().toTimestamp()
        tf.txnParticulars = "Cash transfer between tellers"
        tf.txnType = TxnType.get(81) // Cash transfer type
        tf.txnTemplate = TxnTemplate.get(81)
        tf.user = user
        tf.save(flush: true, failOnError: true)

        // Create teller cash record
        tc.txnFile = tf
        tc.user = user
        tc.receivingUser = receivingTeller
        tc.branch = branch
        tc.txnDate = tf.txnDate
        tc.save(flush: true, failOnError: true)

        def description = "Cash transfer initiated - From: ${user.username} To: ${receivingTeller.username} Amount: ${tc.netAmtPaidOut}"
        auditLogService.insert('120', 'TLR00400', description, 'TellerCash', null, null, null, tf.id)
        
        flash.message = "Cash transfer initiated. Awaiting confirmation from receiving teller.|success"
        redirect(action: "index", controller: "tellerCore")
    }

    /**
     * Create PHP breakdown template
     */
    def createPHPbreakdown() {
        render(template: '/tellering/txnCashFromVault/php') as JSON
        return
    }

    /**
     * Create USD breakdown template
     */
    def createUSDbreakdown() {
        render(template: '/tellering/txnCashFromVault/usd') as JSON
        return
    }

    /**
     * Get cash on hand for current teller
     */
    def getCashOnHand() {
        def user = UserMaster.get(session.user_id)
        def currency = Currency.get(params.currencyId ?: 1)
        
        def tellerBalance = TxnTellerBalance.findByUserAndCurrency(user, currency)
        def cashOnHand = tellerBalance ? (tellerBalance.cashInAmt - tellerBalance.cashOutAmt) : 0
        
        render([
            cashOnHand: cashOnHand,
            currency: currency.code,
            formatted: String.format("%,.2f", cashOnHand)
        ] as JSON)
        return
    }
}
