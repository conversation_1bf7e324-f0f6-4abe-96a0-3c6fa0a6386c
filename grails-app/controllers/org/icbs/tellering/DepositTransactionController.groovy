package org.icbs.tellering

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnCOCI
import org.icbs.tellering.TxnCashCheckBlotter
import org.icbs.deposit.Deposit
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.Currency
import org.icbs.admin.ProductTxn
import org.icbs.admin.CheckDepositClearingType
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.YesNoNa
import org.icbs.lov.CheckStatus
import org.icbs.lov.TxnCheckStatus
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import grails.converters.JSON
import groovy.sql.Sql
import javax.sql.DataSource

/**
 * REFACTORED: Deposit Transaction Controller
 * Extracted from TelleringController.groovy (7,319 lines)
 * Handles all deposit transaction operations with modern patterns
 *
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class DepositTransactionController {

    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def userMasterService
    def glTransactionService
    def auditLogService
    DataSource dataSource

    // Session variables for deposit operations
    def txnDepAccMinId
    def jrxmlTcId
    def passbookline

    static allowedMethods = [
        saveTellerCashDepositTxn: "POST",
        saveTellerCheckDepositTxn: "POST",
        saveTellerCashWithdrawalTxn: "POST",
        saveTellerFDInterestWithdrawalTxn: "POST",
        saveTellerFDPreTerminationTxn: "POST"
    ]

    // =====================================================
    // CASH DEPOSIT OPERATIONS
    // =====================================================

    /**
     * Create teller cash deposit transaction
     */
    def createTellerCashDepositTxn() {
        log.info("Creating teller cash deposit transaction")

        try {
            session["map"] = 'deposit'
            def user = UserMaster.get(session.user_id)

            if (!user.cash) {
                flash.message = 'Error! No cash account defined'
                redirect(controller: 'tellering', action: 'index')
                return
            }

            def txnFileInstance = new TxnFile()
            def txnCashDepositInstance = new TxnDepositAcctLedger()

            // Audit logging
            auditLogService.insert('110', 'DTX01100',
                "Cash deposit transaction creation initiated",
                'DepositTransactionController', null, null, 'tellering/createTellerCashDepositTxn', session.user_id)

            render(view: '/tellering/txnCashDeposit/create',
                model: [
                    txnCashDepositInstance: txnCashDepositInstance,
                    id: txnDepAccMinId,
                    jrxmlTcId: jrxmlTcId,
                    passbookline: passbookline
                ])

        } catch (Exception e) {
            log.error("Error creating cash deposit transaction", e)
            flash.message = 'Error creating cash deposit transaction|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }

    /**
     * Save teller cash deposit transaction
     */
    def saveTellerCashDepositTxn(TxnDepositAcctLedger tc, TxnFile tf) {
        log.info("Saving teller cash deposit transaction")

        try {
            if (tc.hasErrors() || tf.hasErrors()) {
                flash.message = 'Failed to transfer|error|alert'
                redirect(action: "createTellerCashDepositTxn")
                return
            }

            // Validation
            Map validationResult = unifiedValidationService.validateDepositTransaction(params, tc, tf)
            if (!validationResult.isValid) {
                flash.message = validationResult.errors.join(', ') + '|error|alert'
                render(view: '/tellering/txnCashDeposit/create', model: [txnCashDepositInstance: tc])
                return
            }

            // Process cash deposit
            Map depositResult = processCashDeposit(tc, tf, params)

            if (depositResult.success) {
                // Update teller balance and GL
                userMasterService.updateTellerBalanceStatus(false)
                glTransactionService.saveTxnBreakdown(tf.id)

                flash.message = 'Cash Deposit Transaction (Success)|success|alert'

                // Set session variables for reporting
                session["transactionFileId"] = tf.id.toInteger()
                session["map"] = "deposit"

                // Audit logging
                auditLogService.insert('110', 'DTX01200',
                    "Cash deposit completed - Account: ${tc.acctNo}, Amount: ${tc.creditAmt}",
                    'DepositTransactionController', null, null, 'tellering/saveTellerCashDepositTxn', tf.id)

                redirect(controller: "tellering", action: "txnSuccess")
            } else {
                flash.message = depositResult.message + '|error|alert'
                render(view: '/tellering/txnCashDeposit/create', model: [txnCashDepositInstance: tc])
            }

        } catch (Exception e) {
            log.error("Error saving cash deposit transaction", e)
            flash.message = 'Error processing cash deposit transaction|error|alert'
            render(view: '/tellering/txnCashDeposit/create', model: [txnCashDepositInstance: tc])
        }
    }

    // =====================================================
    // CHECK DEPOSIT OPERATIONS
    // =====================================================

    /**
     * Create teller check deposit transaction
     */
    def createTellerCheckDepositTxn() {
        log.info("Creating teller check deposit transaction")

        try {
            session["map"] = 'deposit'
            def user = UserMaster.get(session.user_id)

            if (!user.coci) {
                flash.message = 'Error! No COCI account defined'
                redirect(controller: 'tellering', action: 'index')
                return
            }

            def txnFileInstance = new TxnFile()
            def txnCOCIInstance = new TxnCOCI()
            def txnCheckDepositInstance = new TxnDepositAcctLedger()
            session["checks"] = []

            // Audit logging
            auditLogService.insert('110', 'DTX01300',
                "Check deposit transaction creation initiated",
                'DepositTransactionController', null, null, 'tellering/createTellerCheckDepositTxn', session.user_id)

            render(view: '/tellering/txnCheckDeposit/create',
                model: [
                    txnCheckDepositInstance: txnCheckDepositInstance,
                    id: txnDepAccMinId,
                    jrxmlTcId: jrxmlTcId,
                    passbookline: passbookline
                ])

        } catch (Exception e) {
            log.error("Error creating check deposit transaction", e)
            flash.message = 'Error creating check deposit transaction|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }

    /**
     * Save teller check deposit transaction
     */
    def saveTellerCheckDepositTxn(TxnDepositAcctLedger tc, TxnFile tf) {
        log.info("Saving teller check deposit transaction")

        try {
            if (tc.hasErrors() || tf.hasErrors() || !params.deposit.id) {
                flash.message = 'Failed to transfer|error'
                redirect(action: "createTellerCheckDepositTxn")
                return
            }

            // Validation
            Map validationResult = unifiedValidationService.validateCheckDepositTransaction(params, tc, tf)
            if (!validationResult.isValid) {
                flash.message = validationResult.errors.join(', ') + '|error|alert'
                render(view: '/tellering/txnCheckDeposit/create', model: [txnCheckDepositInstance: tc])
                return
            }

            // Process check deposit
            Map depositResult = processCheckDeposit(tc, tf, params)

            if (depositResult.success) {
                // Update teller balance and GL
                userMasterService.updateTellerBalanceStatus(false)
                glTransactionService.saveTxnBreakdown(tf.id)

                flash.message = 'Check Deposit Transaction (Success)|success|alert'

                // Set session variables for reporting
                session["transactionFileId"] = tf.id.toInteger()
                session["map"] = "deposit"

                // Audit logging
                auditLogService.insert('110', 'DTX01400',
                    "Check deposit completed - Account: ${tc.acctNo}, Amount: ${tc.creditAmt}",
                    'DepositTransactionController', null, null, 'tellering/saveTellerCheckDepositTxn', tf.id)

                redirect(controller: "tellering", action: "txnSuccess")
            } else {
                flash.message = depositResult.message + '|error|alert'
                render(view: '/tellering/txnCheckDeposit/create', model: [txnCheckDepositInstance: tc])
            }

        } catch (Exception e) {
            log.error("Error saving check deposit transaction", e)
            flash.message = 'Error processing check deposit transaction|error|alert'
            render(view: '/tellering/txnCheckDeposit/create', model: [txnCheckDepositInstance: tc])
        }
    }

    // =====================================================
    // CASH WITHDRAWAL OPERATIONS
    // =====================================================

    /**
     * Create teller cash withdrawal transaction
     */
    def createTellerCashWithdrawalTxn() {
        log.info("Creating teller cash withdrawal transaction")

        try {
            session["map"] = 'deposit'
            def user = UserMaster.get(session.user_id)

            if (!user.cash) {
                flash.message = 'Error! No cash account defined.|error|alert'
                redirect(controller: 'tellering', action: 'index')
                return
            }

            def txnFileInstance = new TxnFile()
            def txnCashWithdrawalInstance = new TxnDepositAcctLedger()

            // Audit logging
            auditLogService.insert('110', 'DTX01500',
                "Cash withdrawal transaction creation initiated",
                'DepositTransactionController', null, null, 'tellering/createTellerCashWithdrawalTxn', session.user_id)

            render(view: '/tellering/txnCashWithdrawal/create',
                model: [
                    txnCashWithdrawalInstance: txnCashWithdrawalInstance,
                    id: txnDepAccMinId,
                    jrxmlTcId: jrxmlTcId,
                    passbookline: passbookline
                ])

        } catch (Exception e) {
            log.error("Error creating cash withdrawal transaction", e)
            flash.message = 'Error creating cash withdrawal transaction|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }

    /**
     * Save teller cash withdrawal transaction
     */
    def saveTellerCashWithdrawalTxn(TxnDepositAcctLedger tc, TxnFile tf) {
        log.info("Saving teller cash withdrawal transaction")

        try {
            if (tc.hasErrors() || tf.hasErrors()) {
                flash.message = 'Failed to transfer.|error|alert'
                render(view: '/tellering/txnCashWithdrawal/create', model: [txnCashWithdrawalInstance: tc])
                return
            }

            // Validation
            Map validationResult = unifiedValidationService.validateWithdrawalTransaction(params, tc, tf)
            if (!validationResult.isValid) {
                flash.message = validationResult.errors.join(', ') + '|error|alert'
                render(view: '/tellering/txnCashWithdrawal/create', model: [txnCashWithdrawalInstance: tc])
                return
            }

            // Process cash withdrawal
            Map withdrawalResult = processCashWithdrawal(tc, tf, params)

            if (withdrawalResult.success) {
                // Update teller balance and GL
                userMasterService.updateTellerBalanceStatus(false)
                glTransactionService.saveTxnBreakdown(tf.id)

                flash.message = 'Transaction complete.|success|alert'

                // Set session variables for reporting
                session["transactionFileId"] = tf.id.toInteger()
                session["map"] = "deposit"

                // Audit logging
                auditLogService.insert('110', 'DTX01600',
                    "Cash withdrawal completed - Account: ${tc.acctNo}, Amount: ${tc.debitAmt}",
                    'DepositTransactionController', null, null, 'tellering/saveTellerCashWithdrawalTxn', tf.id)

                redirect(controller: "tellering", action: "txnSuccess")
            } else {
                flash.message = withdrawalResult.message + '|error|alert'
                render(view: '/tellering/txnCashWithdrawal/create', model: [txnCashWithdrawalInstance: tc])
            }

        } catch (Exception e) {
            log.error("Error saving cash withdrawal transaction", e)
            flash.message = 'Error processing cash withdrawal transaction|error|alert'
            render(view: '/tellering/txnCashWithdrawal/create', model: [txnCashWithdrawalInstance: tc])
        }
    }

    // =====================================================
    // FIXED DEPOSIT OPERATIONS
    // =====================================================

    /**
     * Create teller FD interest withdrawal transaction
     */
    def createTellerFDInterestWithdrawalTxn() {
        log.info("Creating teller FD interest withdrawal transaction")

        try {
            session["map"] = 'deposit'
            def user = UserMaster.get(session.user_id)

            if (!user.cash) {
                flash.message = 'Error! No cash account defined.|error|alert'
                redirect(controller: 'tellering', action: 'index')
                return
            }

            def txnFileInstance = new TxnFile()
            def txnFDInterestWithdrawalInstance = new TxnDepositAcctLedger()

            // Audit logging
            auditLogService.insert('110', 'DTX01700',
                "FD interest withdrawal transaction creation initiated",
                'DepositTransactionController', null, null, 'tellering/createTellerFDInterestWithdrawalTxn', session.user_id)

            render(view: '/tellering/txnFDInterestWithdrawal/create',
                model: [
                    txnFDInterestWithdrawalInstance: txnFDInterestWithdrawalInstance,
                    id: txnDepAccMinId,
                    jrxmlTcId: jrxmlTcId,
                    passbookline: passbookline
                ])

        } catch (Exception e) {
            log.error("Error creating FD interest withdrawal transaction", e)
            flash.message = 'Error creating FD interest withdrawal transaction|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }

    /**
     * Save teller FD interest withdrawal transaction
     */
    def saveTellerFDInterestWithdrawalTxn(TxnDepositAcctLedger tc, TxnFile tf) {
        log.info("Saving teller FD interest withdrawal transaction")

        try {
            if (tc.hasErrors() || tf.hasErrors()) {
                flash.message = 'Failed to transfer.|error|alert'
                render(view: '/tellering/txnFDInterestWithdrawal/create', model: [txnFDInterestWithdrawalInstance: tc])
                return
            }

            // Process FD interest withdrawal
            Map withdrawalResult = processFDInterestWithdrawal(tc, tf, params)

            if (withdrawalResult.success) {
                // Update teller balance and GL
                userMasterService.updateTellerBalanceStatus(false)
                glTransactionService.saveTxnBreakdown(tf.id)

                flash.message = 'Transaction complete.|success|alert'

                // Set session variables for reporting
                session["transactionFileId"] = tf.id.toInteger()
                session["map"] = "deposit"

                // Audit logging
                auditLogService.insert('110', 'DTX01800',
                    "FD interest withdrawal completed - Account: ${tc.acctNo}, Amount: ${tc.debitAmt}",
                    'DepositTransactionController', null, null, 'tellering/saveTellerFDInterestWithdrawalTxn', tf.id)

                redirect(controller: "tellering", action: "txnSuccess")
            } else {
                flash.message = withdrawalResult.message + '|error|alert'
                render(view: '/tellering/txnFDInterestWithdrawal/create', model: [txnFDInterestWithdrawalInstance: tc])
            }

        } catch (Exception e) {
            log.error("Error saving FD interest withdrawal transaction", e)
            flash.message = 'Error processing FD interest withdrawal transaction|error|alert'
            render(view: '/tellering/txnFDInterestWithdrawal/create', model: [txnFDInterestWithdrawalInstance: tc])
        }
    }

    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================

    /**
     * Process cash deposit transaction
     */
    private Map processCashDeposit(TxnDepositAcctLedger tc, TxnFile tf, Map params) {
        Map result = [success: false, message: '']

        try {
            def depositInstance = Deposit.get(params.deposit.id)
            if (!depositInstance) {
                result.message = 'Deposit account not found'
                return result
            }

            def temp = ((params.creditAmt).replace(',', '')).toDouble()
            def txnTemp = tf.txnTemplate
            def branch = Branch.get(depositInstance.branchId)
            def acctProduct = ProductTxn.findAllWhere(product: depositInstance.product, txnTemplate: txnTemp)
            def depLedger = TxnDepositAcctLedger.findAllWhere(acctNo: depositInstance.acctNo)
            def userBranch = UserMaster.get(session.user_id)

            // Account status validation
            if (depositInstance.statusId == 5) {
                result.message = 'Dormant Account'
                return result
            }

            // Product transaction validation
            if (!acctProduct) {
                result.message = 'Transaction not allowed for product'
                return result
            }

            // Minimum opening deposit validation
            if (!(depLedger) && temp < depositInstance.product.minOpen) {
                result.message = 'Product requires minimum opening deposit'
                return result
            }

            // Update deposit balances
            depositInstance.ledgerBalAmt += temp
            depositInstance.availableBalAmt += temp
            depositInstance.save(flush: true, failOnError: true)

            // Create transaction ledger entry
            tc.acct = depositInstance
            tc.acctNo = depositInstance.acctNo
            tc.bal = depositInstance.ledgerBalAmt
            tc.branch = branch
            tc.creditAmt = temp
            tc.currency = Currency.get(depositInstance.product.currencyId)
            tc.txnDate = new Date()
            tc.txnFile = tf
            tc.user = userBranch
            tc.save(flush: true, failOnError: true)

            // Update transaction file
            tf.depAcct = depositInstance
            tf.save(flush: true, failOnError: true)

            // Create teller blotter entry
            createTellerBlotterEntry(tf, temp, 'cash_in')

            result.success = true
            result.message = 'Cash deposit processed successfully'

        } catch (Exception e) {
            log.error("Error processing cash deposit", e)
            result.message = 'Error processing cash deposit'
        }

        return result
    }

    /**
     * Process check deposit transaction
     */
    private Map processCheckDeposit(TxnDepositAcctLedger tc, TxnFile tf, Map params) {
        Map result = [success: false, message: '']

        try {
            def depositInstance = Deposit.get(params.deposit.id)
            if (!depositInstance) {
                result.message = 'Deposit account not found'
                return result
            }

            def temp = (params.creditAmt.replace(',', '')).toDouble()
            def txnTemp = tf.txnTemplate
            def branch = Branch.get(depositInstance.branchId)
            def acctProduct = ProductTxn.findAllWhere(product: depositInstance.product, txnTemplate: txnTemp)
            def depLedger = TxnDepositAcctLedger.findAllWhere(acctNo: depositInstance.acctNo)
            def userBranch = UserMaster.get(session.user_id)

            // Account status validation
            if (depositInstance.statusId == 5) {
                result.message = 'Dormant Account'
                return result
            }

            // Product transaction validation
            if (!acctProduct) {
                result.message = 'Transaction not allowed for product'
                return result
            }

            // Process checks
            if (session["checks"]?.id) {
                session["checks"].id.each { tcoci_id ->
                    def tcoci = TxnCOCI.get(tcoci_id)
                    if (tcoci) {
                        tcoci.status = ConfigItemStatus.get(2)
                        tcoci.txnCheckStatus = TxnCheckStatus.get(2)
                        tcoci.txnFile = tf
                        tcoci.depAcct = depositInstance
                        def checkClearDate = tcoci.checkType.clearingDate
                        tcoci.clearingDate = checkClearDate
                        tcoci.save(flush: true, failOnError: true)
                    }
                }
            }

            // Update deposit balances
            depositInstance.ledgerBalAmt += temp
            depositInstance.unclearedCheckBalAmt += temp
            depositInstance.save(flush: true, failOnError: true)

            // Create transaction ledger entry
            tc.acct = depositInstance
            tc.acctNo = depositInstance.acctNo
            tc.bal = depositInstance.ledgerBalAmt
            tc.branch = branch
            tc.creditAmt = temp
            tc.currency = Currency.get(depositInstance.product.currencyId)
            tc.txnDate = new Date()
            tc.txnFile = tf
            tc.user = userBranch
            tc.save(flush: true, failOnError: true)

            // Update transaction file
            tf.depAcct = depositInstance
            tf.save(flush: true, failOnError: true)

            // Create teller blotter entry
            createTellerBlotterEntry(tf, temp, 'check_in')

            result.success = true
            result.message = 'Check deposit processed successfully'

        } catch (Exception e) {
            log.error("Error processing check deposit", e)
            result.message = 'Error processing check deposit'
        }

        return result
    }

    /**
     * Process cash withdrawal transaction
     */
    private Map processCashWithdrawal(TxnDepositAcctLedger tc, TxnFile tf, Map params) {
        Map result = [success: false, message: '']

        try {
            def depositInstance = Deposit.get(params.deposit.id)
            if (!depositInstance) {
                result.message = 'Deposit account not found'
                return result
            }

            def temp = params.debitAmt.replaceAll(",", "").toDouble()
            def txnTemp = tf.txnTemplate
            def branch = Branch.get(depositInstance.branchId)
            def acctProduct = ProductTxn.findAllWhere(product: depositInstance.product, txnTemplate: txnTemp)
            def depLedger = TxnDepositAcctLedger.findAllWhere(acctNo: depositInstance.acctNo)
            def userBranch = UserMaster.get(session.user_id)

            // Account status validation
            if (depositInstance.statusId == 5) {
                result.message = 'Dormant Account'
                return result
            }

            // Product transaction validation
            if (!acctProduct) {
                result.message = 'Transaction not allowed for product'
                return result
            }

            // Sufficient balance validation
            if (temp > depositInstance.availableBalAmt) {
                result.message = 'Insufficient available balance'
                return result
            }

            // Interbranch transaction validation
            if (depositInstance.branch != userBranch.branch && txnTemp.interbranchTxn != YesNoNa.get(1)) {
                result.message = 'Interbranch transaction for Other branch account only'
                return result
            }

            // Update deposit balances
            depositInstance.ledgerBalAmt -= temp
            depositInstance.availableBalAmt -= temp
            depositInstance.save(flush: true, failOnError: true)

            // Create transaction ledger entry
            tc.acct = depositInstance
            tc.acctNo = depositInstance.acctNo
            tc.bal = depositInstance.ledgerBalAmt
            tc.branch = branch
            tc.debitAmt = temp
            tc.currency = Currency.get(depositInstance.product.currencyId)
            tc.txnDate = new Date()
            tc.txnFile = tf
            tc.user = userBranch
            tc.save(flush: true, failOnError: true)

            // Update transaction file
            tf.depAcct = depositInstance
            tf.save(flush: true, failOnError: true)

            // Create teller blotter entry
            createTellerBlotterEntry(tf, temp, 'cash_out')

            result.success = true
            result.message = 'Cash withdrawal processed successfully'

        } catch (Exception e) {
            log.error("Error processing cash withdrawal", e)
            result.message = 'Error processing cash withdrawal'
        }

        return result
    }

    /**
     * Process FD interest withdrawal transaction
     */
    private Map processFDInterestWithdrawal(TxnDepositAcctLedger tc, TxnFile tf, Map params) {
        Map result = [success: false, message: '']

        try {
            def depositInstance = Deposit.get(params.deposit.id)
            if (!depositInstance) {
                result.message = 'Deposit account not found'
                return result
            }

            def txnTemp = tf.txnTemplate
            def branch = Branch.get(depositInstance.branchId)
            def acctProduct = ProductTxn.findAllWhere(product: depositInstance.product, txnTemplate: txnTemp)
            def userBranch = UserMaster.get(session.user_id)

            // Account status validation
            if (depositInstance.statusId == 5) {
                result.message = 'Dormant Account'
                return result
            }

            // Product transaction validation
            if (!acctProduct) {
                result.message = 'Transaction not allowed for product'
                return result
            }

            // Calculate interest withdrawal amount
            def interestAmount = calculateInterestWithdrawal(depositInstance)

            if (interestAmount <= 0) {
                result.message = 'No interest available for withdrawal'
                return result
            }

            // Update deposit balances
            depositInstance.interestBalAmt -= interestAmount
            depositInstance.save(flush: true, failOnError: true)

            // Create transaction ledger entry
            tc.acct = depositInstance
            tc.acctNo = depositInstance.acctNo
            tc.bal = depositInstance.ledgerBalAmt
            tc.branch = branch
            tc.debitAmt = interestAmount
            tc.currency = Currency.get(depositInstance.product.currencyId)
            tc.txnDate = new Date()
            tc.txnFile = tf
            tc.user = userBranch
            tc.save(flush: true, failOnError: true)

            // Update transaction file
            tf.depAcct = depositInstance
            tf.txnAmt = interestAmount
            tf.save(flush: true, failOnError: true)

            // Create teller blotter entry
            createTellerBlotterEntry(tf, interestAmount, 'cash_out')

            result.success = true
            result.message = 'FD interest withdrawal processed successfully'

        } catch (Exception e) {
            log.error("Error processing FD interest withdrawal", e)
            result.message = 'Error processing FD interest withdrawal'
        }

        return result
    }

    /**
     * Create teller blotter entry
     */
    private void createTellerBlotterEntry(TxnFile tf, Double amount, String transactionType) {
        def tb = new TxnCashCheckBlotter(
            branch: Branch.get(UserMaster.get(session.user_id).branchId),
            currency: tf.currency,
            user: UserMaster.get(session.user_id),
            txnParticulars: tf.txnParticulars,
            txnFile: tf
        )

        switch (transactionType) {
            case 'cash_in':
                tb.cashInAmt = amount
                tb.cashOutAmt = 0
                tb.checkInAmt = 0
                tb.checkOutAmt = 0
                break
            case 'cash_out':
                tb.cashInAmt = 0
                tb.cashOutAmt = amount
                tb.checkInAmt = 0
                tb.checkOutAmt = 0
                break
            case 'check_in':
                tb.cashInAmt = 0
                tb.cashOutAmt = 0
                tb.checkInAmt = amount
                tb.checkOutAmt = 0
                break
            case 'check_out':
                tb.cashInAmt = 0
                tb.cashOutAmt = 0
                tb.checkInAmt = 0
                tb.checkOutAmt = amount
                break
        }

        tb.save(flush: true, failOnError: true)
    }

    /**
     * Calculate interest withdrawal amount for FD
     */
    private Double calculateInterestWithdrawal(Deposit depositInstance) {
        try {
            // This would contain the actual interest calculation logic
            // For now, returning the current interest balance
            return depositInstance.interestBalAmt ?: 0.0
        } catch (Exception e) {
            log.error("Error calculating interest withdrawal", e)
            return 0.0
        }
    }
}