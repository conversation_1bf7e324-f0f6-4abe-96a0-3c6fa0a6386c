package org.icbs.tellering

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.cif.Customer
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TellerBalance
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.TxnTemplate
import org.icbs.admin.Currency
import org.icbs.lov.ConfigItemStatus
import org.icbs.transaction.TransactionProcessingService
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService

/**
 * REFACTORED: Cash Transaction Controller
 * Extracted from TelleringController.groovy (7,319 lines)
 * Handles all cash-related banking transactions with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class CashTransactionController {
    
    TransactionProcessingService transactionProcessingService
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def policyService
    def userMasterService
    def glTransactionService
    
    static allowedMethods = [
        processCashDeposit: "POST",
        processCashWithdrawal: "POST",
        processCashTransfer: "POST",
        validateCashTransaction: "POST"
    ]
    
    // =====================================================
    // CASH DEPOSIT OPERATIONS
    // =====================================================
    
    /**
     * Process cash deposit transaction
     */
    def processCashDeposit() {
        try {
            // 1. Validate input parameters
            Map validation = validateCashDepositParams(params)
            if (!validation.isValid) {
                render(status: 400, contentType: 'application/json') {
                    [success: false, errors: validation.errors]
                }
                return
            }
            
            // 2. Process transaction
            Map result = transactionProcessingService.processCashTransfer(params, session.user_id as Long)
            
            if (result.success) {
                flash.message = "Cash deposit processed successfully|success"
                
                // 3. Audit the transaction
                auditCashTransaction('CASH_DEPOSIT', result.txnFile, 'SUCCESS')
                
                render(contentType: 'application/json') {
                    [
                        success: true,
                        message: result.message,
                        transactionId: result.txnFile.id,
                        transactionRef: result.txnFile.txnRef,
                        balance: result.txnFile.beneficiary?.deposits?.find { it.id == params.depositId }?.availableBalance
                    ]
                }
            } else {
                auditCashTransaction('CASH_DEPOSIT', null, 'FAILURE', result.errors.join(', '))
                
                render(status: 400, contentType: 'application/json') {
                    [success: false, errors: result.errors]
                }
            }
            
        } catch (Exception e) {
            log.error("Error processing cash deposit", e)
            auditCashTransaction('CASH_DEPOSIT', null, 'ERROR', e.message)
            
            render(status: 500, contentType: 'application/json') {
                [success: false, error: "Transaction processing failed: ${e.message}"]
            }
        }
    }
    
    /**
     * Process cash withdrawal transaction
     */
    def processCashWithdrawal() {
        try {
            // 1. Validate withdrawal parameters
            Map validation = validateCashWithdrawalParams(params)
            if (!validation.isValid) {
                render(status: 400, contentType: 'application/json') {
                    [success: false, errors: validation.errors]
                }
                return
            }
            
            // 2. Check account balance
            Map balanceCheck = checkSufficientBalance(params)
            if (!balanceCheck.sufficient) {
                render(status: 400, contentType: 'application/json') {
                    [success: false, error: "Insufficient funds"]
                }
                return
            }
            
            // 3. Process withdrawal
            Map result = transactionProcessingService.processCashTransfer(params, session.user_id as Long)
            
            if (result.success) {
                flash.message = "Cash withdrawal processed successfully|success"
                auditCashTransaction('CASH_WITHDRAWAL', result.txnFile, 'SUCCESS')
                
                render(contentType: 'application/json') {
                    [
                        success: true,
                        message: result.message,
                        transactionId: result.txnFile.id,
                        transactionRef: result.txnFile.txnRef,
                        remainingBalance: balanceCheck.remainingBalance
                    ]
                }
            } else {
                auditCashTransaction('CASH_WITHDRAWAL', null, 'FAILURE', result.errors.join(', '))
                
                render(status: 400, contentType: 'application/json') {
                    [success: false, errors: result.errors]
                }
            }
            
        } catch (Exception e) {
            log.error("Error processing cash withdrawal", e)
            auditCashTransaction('CASH_WITHDRAWAL', null, 'ERROR', e.message)
            
            render(status: 500, contentType: 'application/json') {
                [success: false, error: "Transaction processing failed: ${e.message}"]
            }
        }
    }
    
    /**
     * Process cash transfer between accounts
     */
    def processCashTransfer() {
        try {
            // 1. Validate transfer parameters
            Map validation = validateCashTransferParams(params)
            if (!validation.isValid) {
                render(status: 400, contentType: 'application/json') {
                    [success: false, errors: validation.errors]
                }
                return
            }
            
            // 2. Process transfer
            Map result = transactionProcessingService.processCashTransfer(params, session.user_id as Long)
            
            if (result.success) {
                flash.message = "Cash transfer processed successfully|success"
                auditCashTransaction('CASH_TRANSFER', result.txnFile, 'SUCCESS')
                
                render(contentType: 'application/json') {
                    [
                        success: true,
                        message: result.message,
                        transactionId: result.txnFile.id,
                        transactionRef: result.txnFile.txnRef
                    ]
                }
            } else {
                auditCashTransaction('CASH_TRANSFER', null, 'FAILURE', result.errors.join(', '))
                
                render(status: 400, contentType: 'application/json') {
                    [success: false, errors: result.errors]
                }
            }
            
        } catch (Exception e) {
            log.error("Error processing cash transfer", e)
            auditCashTransaction('CASH_TRANSFER', null, 'ERROR', e.message)
            
            render(status: 500, contentType: 'application/json') {
                [success: false, error: "Transaction processing failed: ${e.message}"]
            }
        }
    }
    
    // =====================================================
    // VALIDATION METHODS
    // =====================================================
    
    /**
     * Validate cash deposit parameters
     */
    private Map validateCashDepositParams(Map params) {
        Map result = [isValid: true, errors: []]
        
        // Amount validation
        if (!params.txnAmt || params.txnAmt <= 0) {
            result.isValid = false
            result.errors << "Deposit amount must be greater than zero"
        }
        
        // Customer validation
        if (!params.customerId) {
            result.isValid = false
            result.errors << "Customer is required"
        }
        
        // Deposit account validation
        if (!params.depositId) {
            result.isValid = false
            result.errors << "Deposit account is required"
        }
        
        return result
    }
    
    /**
     * Validate cash withdrawal parameters
     */
    private Map validateCashWithdrawalParams(Map params) {
        Map result = [isValid: true, errors: []]
        
        // Amount validation
        if (!params.txnAmt || params.txnAmt <= 0) {
            result.isValid = false
            result.errors << "Withdrawal amount must be greater than zero"
        }
        
        // Customer validation
        if (!params.customerId) {
            result.isValid = false
            result.errors << "Customer is required"
        }
        
        // Account validation
        if (!params.depositId) {
            result.isValid = false
            result.errors << "Account is required"
        }
        
        return result
    }
    
    /**
     * Validate cash transfer parameters
     */
    private Map validateCashTransferParams(Map params) {
        Map result = [isValid: true, errors: []]
        
        // Amount validation
        if (!params.txnAmt || params.txnAmt <= 0) {
            result.isValid = false
            result.errors << "Transfer amount must be greater than zero"
        }
        
        // Source account validation
        if (!params.fromAccountId) {
            result.isValid = false
            result.errors << "Source account is required"
        }
        
        // Destination account validation
        if (!params.toAccountId) {
            result.isValid = false
            result.errors << "Destination account is required"
        }
        
        // Same account check
        if (params.fromAccountId == params.toAccountId) {
            result.isValid = false
            result.errors << "Source and destination accounts cannot be the same"
        }
        
        return result
    }
    
    /**
     * Check if account has sufficient balance
     */
    private Map checkSufficientBalance(Map params) {
        try {
            def deposit = org.icbs.deposit.Deposit.get(params.depositId)
            if (!deposit) {
                return [sufficient: false, error: "Account not found"]
            }
            
            BigDecimal withdrawalAmount = new BigDecimal(params.txnAmt.toString())
            BigDecimal availableBalance = deposit.availableBalance ?: 0
            
            if (availableBalance >= withdrawalAmount) {
                return [
                    sufficient: true,
                    availableBalance: availableBalance,
                    remainingBalance: availableBalance - withdrawalAmount
                ]
            } else {
                return [
                    sufficient: false,
                    availableBalance: availableBalance,
                    shortfall: withdrawalAmount - availableBalance
                ]
            }
            
        } catch (Exception e) {
            log.error("Error checking account balance", e)
            return [sufficient: false, error: "Balance check failed"]
        }
    }
    
    /**
     * Audit cash transaction
     */
    private void auditCashTransaction(String transactionType, TxnFile txnFile, String result, String errorMessage = null) {
        try {
            securityAuditService.logSecurityEvent([
                eventType: 'CASH_TRANSACTION',
                eventDescription: "${transactionType} transaction processed",
                transactionType: transactionType,
                transactionId: txnFile?.id,
                transactionRef: txnFile?.txnRef,
                amount: txnFile?.txnAmt,
                customerId: txnFile?.beneficiary?.id,
                userId: session.user_id,
                result: result,
                errorMessage: errorMessage
            ])
        } catch (Exception e) {
            log.error("Error auditing cash transaction", e)
        }
    }
    
    // =====================================================
    // UTILITY METHODS
    // =====================================================
    
    /**
     * Get cash transaction history for customer
     */
    def getCashTransactionHistory() {
        try {
            Long customerId = params.customerId as Long
            Date fromDate = params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 30
            Date toDate = params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date()
            
            def transactions = TxnFile.createCriteria().list {
                eq('beneficiary.id', customerId)
                between('txnDate', fromDate, toDate)
                txnTemplate {
                    'in'('code', ['CASH_DEPOSIT', 'CASH_WITHDRAWAL', 'CASH_TRANSFER'])
                }
                order('txnDate', 'desc')
                maxResults(params.max as Integer ?: 50)
            }
            
            render(contentType: 'application/json') {
                [
                    success: true,
                    transactions: transactions.collect { txn ->
                        [
                            id: txn.id,
                            date: txn.txnDate.format('yyyy-MM-dd HH:mm:ss'),
                            type: txn.txnTemplate.shortDescription,
                            amount: txn.txnAmt,
                            reference: txn.txnRef,
                            status: txn.status.description
                        ]
                    }
                ]
            }
            
        } catch (Exception e) {
            log.error("Error retrieving cash transaction history", e)
            render(status: 500, contentType: 'application/json') {
                [success: false, error: "Failed to retrieve transaction history"]
            }
        }
    }
}
