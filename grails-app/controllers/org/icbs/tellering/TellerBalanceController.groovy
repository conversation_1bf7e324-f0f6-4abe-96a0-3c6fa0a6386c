package org.icbs.tellering

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnCashCheckBlotter
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.Currency
import org.icbs.lov.ConfigItemStatus
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import grails.converters.JSON
import groovy.json.JsonBuilder
import groovy.sql.Sql
import javax.sql.DataSource

/**
 * REFACTORED: Teller Balance Controller
 * Extracted from TelleringController.groovy (7,319 lines)
 * Handles all teller balance operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class TellerBalanceController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def userMasterService
    def glTransactionService
    def auditLogService
    DataSource dataSource
    
    static allowedMethods = [
        confirmTellerBalance: "POST",
        forceBalancePerUser: "POST",
        forceBalanceAllUser: "POST",
        updateTellerBalanceStatus: "POST"
    ]
    
    // =====================================================
    // TELLER BALANCE MANAGEMENT
    // =====================================================
    
    /**
     * View teller balancing interface
     */
    def viewTellerBalancing() {
        log.info("Viewing teller balancing for user: ${session.user_id}")
        
        try {
            // Audit logging
            auditLogService.insert('120', 'TLB01100', 
                "Teller balancing view accessed", 
                'TellerBalanceController', null, null, 'tellering/viewTellerBalancing', session.user_id)
            
            render(view: '/tellering/tellerBalancing/create')
            
        } catch (Exception e) {
            log.error("Error viewing teller balancing", e)
            flash.message = 'Error accessing teller balancing|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * View teller cash transactions
     */
    def viewTellerCashTxn() {
        log.info("Viewing teller cash transactions for user: ${session.user_id}")
        
        try {
            def tbCash = TxnCashCheckBlotter.createCriteria().list() {
                and {
                    eq("user", UserMaster.get(session.user_id))
                }
                order("txnFile", "asc")
            }
            
            // Audit logging
            auditLogService.insert('120', 'TLB01200', 
                "Teller cash transactions viewed", 
                'TellerBalanceController', null, null, 'tellering/viewTellerCashTxn', session.user_id)
            
            render(view: '/tellering/tellerBalancing/viewTellerCashTxn', model: [tbCash: tbCash])
            
        } catch (Exception e) {
            log.error("Error viewing teller cash transactions", e)
            flash.message = 'Error viewing cash transactions|error|alert'
            redirect(action: 'viewTellerBalancing')
        }
    }
    
    /**
     * Confirm teller balance
     */
    def confirmTellerBalance() {
        log.info("Confirming teller balance for user: ${session.user_id}")
        
        try {
            // Validation
            Map validationResult = unifiedValidationService.validateTellerBalance(params)
            if (!validationResult.isValid) {
                flash.message = validationResult.errors.join(', ') + '|error|alert'
                redirect(action: 'viewTellerBalancing')
                return
            }
            
            def branchDate = UserMaster.get(session.user_id).branch.runDate
            def userMaster = UserMaster.get(session.user_id)
            def currency = Currency.get(params.txnTemplate.toInteger())
            def txnBalanceTeller = TxnTellerBalance.findByUserAndTxnDateAndCurrency(userMaster, branchDate, currency)
            
            if (!txnBalanceTeller) {
                flash.message = 'Teller balance record not found|error|alert'
                redirect(action: 'viewTellerBalancing')
                return
            }
            
            def cashOnHand = (params.txnAmt).replace(',', '').toDouble()
            def expectedBalance = txnBalanceTeller.cashIn - txnBalanceTeller.cashOut
            
            // Process balance confirmation
            Map balanceResult = processBalanceConfirmation(txnBalanceTeller, cashOnHand, expectedBalance)
            
            // Update user balance status
            updateUserBalanceStatus(userMaster, branchDate)
            
            // Audit logging
            auditLogService.insert('120', 'TLB01300', 
                "Teller balance confirmed: ${balanceResult.isBalanced ? 'BALANCED' : 'UNBALANCED'}", 
                'TellerBalanceController', null, null, 'tellering/confirmTellerBalance', userMaster.id)
            
            flash.message = balanceResult.message
            
            [
                txnBalanceTeller: txnBalanceTeller,
                isUserBalanced: balanceResult.isUserBalanced,
                msgContent: balanceResult.msgContent,
                user: userMaster
            ]
            
        } catch (Exception e) {
            log.error("Error confirming teller balance", e)
            flash.message = 'Error confirming teller balance|error|alert'
            redirect(action: 'viewTellerBalancing')
        }
    }
    
    /**
     * Force balance for specific user
     */
    def forceBalancePerUser() {
        log.info("Force balancing user")
        
        try {
            def json = request.JSON
            
            if (!json.id) {
                render([error: 'User ID is required'] as JSON)
                return
            }
            
            def userMasterForceBalanceUser = UserMaster.get(json.id.toInteger())
            if (!userMasterForceBalanceUser) {
                render([error: 'User not found'] as JSON)
                return
            }
            
            def username = userMasterForceBalanceUser.username
            
            // Force balance the user
            userMasterForceBalanceUser.isTellerBalanced = true
            userMasterForceBalanceUser.save(flush: true)
            
            // Audit logging
            auditLogService.insert('120', 'TLB01400', 
                "Force balanced user ${username}", 
                'TellerBalanceController', null, null, 'periodicOps/EODCheck', userMasterForceBalanceUser.id)
            
            log.info("Successfully force balanced user: ${username}")
            
            def sql = new Sql(dataSource)
            def queryall = "select * from branch limit 1"
            def resultqueryall = sql.rows(queryall)
            
            render resultqueryall as JSON
            
        } catch (Exception e) {
            log.error("Error force balancing user", e)
            render([error: 'Error force balancing user'] as JSON)
        }
    }
    
    /**
     * Force balance all unbalanced users
     */
    def forceBalanceAllUser() {
        log.info("Force balancing all unbalanced users")
        
        try {
            def userMasterInstanceList = UserMaster.createCriteria().list(params) {
                and {
                    eq("isTellerBalanced", false)
                    eq("configItemStatus", ConfigItemStatus.read(2))
                }
            }
            
            int balancedCount = 0
            
            if (userMasterInstanceList) {
                for (usrLst in userMasterInstanceList) {
                    def updateIsTellerBalance = UserMaster.get(usrLst.id)
                    def username = updateIsTellerBalance.username
                    
                    updateIsTellerBalance.isTellerBalanced = true
                    updateIsTellerBalance.save(flush: true)
                    
                    // Audit logging for each user
                    auditLogService.insert('120', 'TLB01500', 
                        "Force balanced user ${username}", 
                        'TellerBalanceController', null, null, 'periodicOps/EODCheck', updateIsTellerBalance.id)
                    
                    balancedCount++
                    log.info("Force balanced user: ${username}")
                }
            }
            
            flash.message = "Successfully force balanced ${balancedCount} users|success|alert"
            
            // Audit logging for batch operation
            auditLogService.insert('120', 'TLB01600', 
                "Force balanced all users - Total: ${balancedCount}", 
                'TellerBalanceController', null, null, 'periodicOps/EODCheck', session.user_id)
            
            redirect(controller: 'periodicOps', action: 'EODCheck')
            
        } catch (Exception e) {
            log.error("Error force balancing all users", e)
            flash.message = 'Error force balancing all users|error|alert'
            redirect(controller: 'periodicOps', action: 'EODCheck')
        }
    }
    
    /**
     * Update teller balance status via AJAX
     */
    def updateTellerBalanceStatus() {
        log.debug("Updating teller balance status")
        
        try {
            def sql = new Sql(dataSource)
            def query = """
                SELECT currency_id, txn_date, 
                       SUM(cash_in_amt) as cashin, 
                       SUM(cash_out_amt) as cashout 
                FROM txn_cash_check_blotter 
                WHERE user_id = ? 
                  AND txn_date = (SELECT run_date FROM branch WHERE id = ?)
                GROUP BY currency_id, txn_date
            """
            
            def userMaster = UserMaster.get(session.user_id)
            def sqlresult = sql.rows(query, [userMaster.id, userMaster.branchId])
            
            sqlresult.each { values ->
                def txnTellerBalanceInstance = TxnTellerBalance.findByCurrencyAndTxnDateAndUser(
                    Currency.get(values.currency_id), 
                    values.txn_date, 
                    userMaster
                )
                
                if (txnTellerBalanceInstance) {
                    txnTellerBalanceInstance.cashIn = values.cashin
                    txnTellerBalanceInstance.cashOut = values.cashout
                    txnTellerBalanceInstance.isBalance = 
                        (values.cashin - values.cashout) == txnTellerBalanceInstance.lastBalanceAmt
                    txnTellerBalanceInstance.save(flush: true)
                }
            }
            
            def results = new JsonBuilder(sqlresult).toPrettyString()
            render(text: results) as JSON
            
        } catch (Exception e) {
            log.error("Error updating teller balance status", e)
            render([error: 'Error updating teller balance status'] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Process balance confirmation logic
     */
    private Map processBalanceConfirmation(TxnTellerBalance txnBalanceTeller, Double cashOnHand, Double expectedBalance) {
        Map result = [isBalanced: false, isUserBalanced: false, message: '', msgContent: '']
        
        try {
            if (!txnBalanceTeller.isBalance) {
                if (cashOnHand == expectedBalance) {
                    txnBalanceTeller.isBalance = true
                    txnBalanceTeller.lastBalanceAmt = cashOnHand
                    txnBalanceTeller.save(flush: true)
                    
                    result.isBalanced = true
                    result.message = 'Congratulations, you have balanced!|success|alert'
                } else {
                    result.message = 'Teller Balance Error: Cash on Hand does not match with the system cash!|error|alert'
                }
            }
            
            result.msgContent = result.isBalanced ? 
                'Congratulations, you have balanced!' : 
                'Sorry, you still have unbalanced transactions'
            
        } catch (Exception e) {
            log.error("Error processing balance confirmation", e)
            result.message = 'Error processing balance confirmation|error|alert'
        }
        
        return result
    }
    
    /**
     * Update user balance status
     */
    private void updateUserBalanceStatus(UserMaster userMaster, Date branchDate) {
        try {
            // Check for any unbalanced transactions
            def unbalancedTransactions = TxnTellerBalance.findAllByUserAndTxnDateAndIsBalance(
                userMaster, branchDate, false
            )
            
            // Auto-balance zero transactions
            unbalancedTransactions.each { tellerbal ->
                if ((tellerbal.cashIn + tellerbal.cashOut) == 0) {
                    tellerbal.isBalance = true
                    tellerbal.save(flush: true)
                }
            }
            
            // Recheck unbalanced transactions
            unbalancedTransactions = TxnTellerBalance.findAllByUserAndTxnDateAndIsBalance(
                userMaster, branchDate, false
            )
            
            // Update user balance status
            userMaster.isTellerBalanced = (unbalancedTransactions.size() == 0)
            userMaster.save(flush: true)
            
        } catch (Exception e) {
            log.error("Error updating user balance status", e)
        }
    }
}
