package org.icbs.tellering

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.tellering.TxnFile
import org.icbs.admin.UserMaster
import org.icbs.admin.PolicyException
import org.icbs.admin.ApprovalWorkflow
import org.icbs.lov.ConfigItemStatus
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import grails.converters.JSON
import groovy.sql.Sql
import javax.sql.DataSource

/**
 * REFACTORED: Transaction Approval Controller
 * Extracted from TelleringController.groovy (7,319 lines)
 * Handles all transaction approval workflow operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class TransactionApprovalController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def policyService
    def workflowService
    def auditLogService
    DataSource dataSource
    
    static allowedMethods = [
        viewPendingApprovals: "GET",
        approveTransaction: "POST",
        rejectTransaction: "POST",
        bulkApproval: "POST",
        delegateApproval: "POST"
    ]
    
    // =====================================================
    // TRANSACTION APPROVAL OPERATIONS
    // =====================================================
    
    /**
     * View pending transactions requiring approval
     */
    def viewPendingApprovals() {
        log.info("Viewing pending approvals for user: ${session.user_id}")
        
        try {
            UserMaster currentUser = UserMaster.get(session.user_id)
            
            // Get transactions pending approval for current user
            List<Map> pendingTransactions = getPendingApprovalsForUser(currentUser)
            
            // Get approval statistics
            Map approvalStats = getApprovalStatistics(currentUser)
            
            // Audit logging
            auditLogService.insert('110', 'TAP01100', 
                "Pending approvals viewed - Count: ${pendingTransactions.size()}", 
                'TransactionApprovalController', null, null, 'tellering/viewPendingApprovals', session.user_id)
            
            render(view: '/tellering/approval/pendingApprovals', 
                model: [
                    pendingTransactions: pendingTransactions,
                    approvalStats: approvalStats,
                    currentUser: currentUser
                ])
            
        } catch (Exception e) {
            log.error("Error viewing pending approvals", e)
            flash.message = 'Error viewing pending approvals|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * Approve a transaction
     */
    def approveTransaction() {
        log.info("Approving transaction: ${params.txnId}")
        
        try {
            if (!params.txnId) {
                render([success: false, error: 'Transaction ID is required'] as JSON)
                return
            }
            
            def txnFile = TxnFile.get(params.txnId)
            if (!txnFile) {
                render([success: false, error: 'Transaction not found'] as JSON)
                return
            }
            
            // Validate approval authority
            Map authorityResult = validateApprovalAuthority(txnFile, session.user_id)
            if (!authorityResult.isValid) {
                render([success: false, error: authorityResult.message] as JSON)
                return
            }
            
            // Process approval
            Map approvalResult = processTransactionApproval(txnFile, params.comments ?: '')
            
            if (approvalResult.success) {
                // Audit logging
                auditLogService.insert('110', 'TAP01200', 
                    "Transaction approved - ID: ${txnFile.id}, Amount: ${txnFile.txnAmt}", 
                    'TransactionApprovalController', null, null, 'tellering/approveTransaction', txnFile.id)
                
                render([success: true, message: 'Transaction approved successfully'] as JSON)
            } else {
                render([success: false, error: approvalResult.message] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error approving transaction", e)
            render([success: false, error: 'Error approving transaction'] as JSON)
        }
    }
    
    /**
     * Reject a transaction
     */
    def rejectTransaction() {
        log.info("Rejecting transaction: ${params.txnId}")
        
        try {
            if (!params.txnId || !params.reason) {
                render([success: false, error: 'Transaction ID and rejection reason are required'] as JSON)
                return
            }
            
            def txnFile = TxnFile.get(params.txnId)
            if (!txnFile) {
                render([success: false, error: 'Transaction not found'] as JSON)
                return
            }
            
            // Validate approval authority
            Map authorityResult = validateApprovalAuthority(txnFile, session.user_id)
            if (!authorityResult.isValid) {
                render([success: false, error: authorityResult.message] as JSON)
                return
            }
            
            // Process rejection
            Map rejectionResult = processTransactionRejection(txnFile, params.reason, params.comments ?: '')
            
            if (rejectionResult.success) {
                // Audit logging
                auditLogService.insert('110', 'TAP01300', 
                    "Transaction rejected - ID: ${txnFile.id}, Reason: ${params.reason}", 
                    'TransactionApprovalController', null, null, 'tellering/rejectTransaction', txnFile.id)
                
                render([success: true, message: 'Transaction rejected successfully'] as JSON)
            } else {
                render([success: false, error: rejectionResult.message] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error rejecting transaction", e)
            render([success: false, error: 'Error rejecting transaction'] as JSON)
        }
    }
    
    /**
     * Bulk approval of multiple transactions
     */
    def bulkApproval() {
        log.info("Processing bulk approval for transactions: ${params.txnIds}")
        
        try {
            if (!params.txnIds) {
                render([success: false, error: 'Transaction IDs are required'] as JSON)
                return
            }
            
            List<Long> txnIds = params.txnIds.split(',').collect { it.toLong() }
            Map bulkResult = processBulkApproval(txnIds, params.comments ?: '')
            
            // Audit logging
            auditLogService.insert('110', 'TAP01400', 
                "Bulk approval processed - Transactions: ${txnIds.size()}, Approved: ${bulkResult.approvedCount}, Failed: ${bulkResult.failedCount}", 
                'TransactionApprovalController', null, null, 'tellering/bulkApproval', session.user_id)
            
            render(bulkResult as JSON)
            
        } catch (Exception e) {
            log.error("Error processing bulk approval", e)
            render([success: false, error: 'Error processing bulk approval'] as JSON)
        }
    }
    
    /**
     * Delegate approval to another user
     */
    def delegateApproval() {
        log.info("Delegating approval for transaction: ${params.txnId} to user: ${params.delegateToUserId}")
        
        try {
            if (!params.txnId || !params.delegateToUserId) {
                render([success: false, error: 'Transaction ID and delegate user ID are required'] as JSON)
                return
            }
            
            def txnFile = TxnFile.get(params.txnId)
            def delegateUser = UserMaster.get(params.delegateToUserId)
            
            if (!txnFile || !delegateUser) {
                render([success: false, error: 'Transaction or delegate user not found'] as JSON)
                return
            }
            
            // Process delegation
            Map delegationResult = processDelegation(txnFile, delegateUser, params.comments ?: '')
            
            if (delegationResult.success) {
                // Audit logging
                auditLogService.insert('110', 'TAP01500', 
                    "Approval delegated - Transaction: ${txnFile.id}, To: ${delegateUser.username}", 
                    'TransactionApprovalController', null, null, 'tellering/delegateApproval', txnFile.id)
                
                render([success: true, message: 'Approval delegated successfully'] as JSON)
            } else {
                render([success: false, error: delegationResult.message] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error delegating approval", e)
            render([success: false, error: 'Error delegating approval'] as JSON)
        }
    }
    
    /**
     * Get approval history for transaction
     */
    def getApprovalHistory() {
        log.debug("Getting approval history for transaction: ${params.txnId}")
        
        try {
            if (!params.txnId) {
                render([error: 'Transaction ID is required'] as JSON)
                return
            }
            
            def txnFile = TxnFile.get(params.txnId)
            if (!txnFile) {
                render([error: 'Transaction not found'] as JSON)
                return
            }
            
            List<Map> approvalHistory = getTransactionApprovalHistory(txnFile)
            
            render([
                success: true,
                approvalHistory: approvalHistory,
                currentStatus: txnFile.status?.description
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting approval history", e)
            render([error: 'Error getting approval history'] as JSON)
        }
    }
    
    /**
     * Get approval workflow for transaction type
     */
    def getApprovalWorkflow() {
        log.debug("Getting approval workflow for transaction type: ${params.txnTypeId}")
        
        try {
            if (!params.txnTypeId) {
                render([error: 'Transaction type ID is required'] as JSON)
                return
            }
            
            Map workflowInfo = getWorkflowForTransactionType(params.txnTypeId.toLong())
            
            render(workflowInfo as JSON)
            
        } catch (Exception e) {
            log.error("Error getting approval workflow", e)
            render([error: 'Error getting approval workflow'] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Get pending approvals for specific user
     */
    private List<Map> getPendingApprovalsForUser(UserMaster user) {
        try {
            def sql = new Sql(dataSource)
            def query = """
                SELECT t.id, t.txn_date, t.txn_amt, t.txn_ref, t.txn_particulars,
                       tt.description as txn_type, c.name1 as customer_name,
                       u.username as created_by, pe.exception_code, pe.date_created as exception_date
                FROM txn_file t
                JOIN txn_type tt ON t.txn_type_id = tt.id
                JOIN user_master u ON t.user_id = u.id
                LEFT JOIN customer c ON t.beneficiary_id = c.id
                LEFT JOIN policy_exception pe ON pe.record_id = t.id AND pe.entity_type = 'txnFile'
                WHERE t.status_id = 1 
                  AND (pe.approving_user_id = ? OR pe.approving_user_id IS NULL)
                  AND pe.date_of_action IS NULL
                ORDER BY t.txn_date DESC, t.txn_amt DESC
                LIMIT 100
            """
            
            return sql.rows(query, [user.id])
        } catch (Exception e) {
            log.error("Error getting pending approvals for user", e)
            return []
        }
    }
    
    /**
     * Get approval statistics for user
     */
    private Map getApprovalStatistics(UserMaster user) {
        Map stats = [:]
        
        try {
            def sql = new Sql(dataSource)
            
            // Pending approvals count
            def pendingQuery = """
                SELECT COUNT(*) as pending_count
                FROM policy_exception pe
                JOIN txn_file t ON pe.record_id = t.id
                WHERE pe.approving_user_id = ? 
                  AND pe.date_of_action IS NULL
                  AND t.status_id = 1
            """
            def pendingResult = sql.firstRow(pendingQuery, [user.id])
            stats.pendingCount = pendingResult.pending_count
            
            // Today's approvals
            def todayQuery = """
                SELECT COUNT(*) as today_count
                FROM policy_exception pe
                WHERE pe.action_by_id = ? 
                  AND DATE(pe.date_of_action) = CURRENT_DATE
            """
            def todayResult = sql.firstRow(todayQuery, [user.id])
            stats.todayApprovals = todayResult.today_count
            
            // High value pending (> 100,000)
            def highValueQuery = """
                SELECT COUNT(*) as high_value_count
                FROM policy_exception pe
                JOIN txn_file t ON pe.record_id = t.id
                WHERE pe.approving_user_id = ? 
                  AND pe.date_of_action IS NULL
                  AND t.status_id = 1
                  AND t.txn_amt > 100000
            """
            def highValueResult = sql.firstRow(highValueQuery, [user.id])
            stats.highValuePending = highValueResult.high_value_count
            
        } catch (Exception e) {
            log.error("Error getting approval statistics", e)
            stats.error = "Error getting approval statistics"
        }
        
        return stats
    }
    
    /**
     * Validate approval authority
     */
    private Map validateApprovalAuthority(TxnFile txnFile, Long userId) {
        Map result = [isValid: true, message: '']
        
        try {
            UserMaster user = UserMaster.get(userId)
            if (!user) {
                result.isValid = false
                result.message = 'User not found'
                return result
            }
            
            // Check if user has approval authority for this transaction amount
            BigDecimal approvalLimit = getApprovalLimit(user)
            if (txnFile.txnAmt > approvalLimit) {
                result.isValid = false
                result.message = 'Transaction amount exceeds your approval limit'
                return result
            }
            
            // Check if user is not the same as transaction creator
            if (txnFile.user.id == userId) {
                result.isValid = false
                result.message = 'Cannot approve your own transaction'
                return result
            }
            
        } catch (Exception e) {
            log.error("Error validating approval authority", e)
            result.isValid = false
            result.message = 'Error validating approval authority'
        }
        
        return result
    }
    
    /**
     * Process transaction approval
     */
    private Map processTransactionApproval(TxnFile txnFile, String comments) {
        Map result = [success: false, message: '']
        
        try {
            // Update transaction status
            txnFile.status = ConfigItemStatus.get(2) // Approved
            txnFile.save(flush: true, failOnError: true)
            
            // Update policy exception if exists
            def policyException = PolicyException.findByRecordIdAndEntityType(txnFile.id.toString(), 'txnFile')
            if (policyException) {
                policyException.dateOfAction = new Date()
                policyException.isApproved = true
                policyException.actionComments = comments
                policyException.actionBy = UserMaster.get(session.user_id)
                policyException.save(flush: true, failOnError: true)
            }
            
            // Process GL transactions if needed
            if (txnFile.status.id == 2) {
                glTransactionService.saveTxnBreakdown(txnFile.id)
            }
            
            result.success = true
            result.message = 'Transaction approved successfully'
            
        } catch (Exception e) {
            log.error("Error processing transaction approval", e)
            result.message = 'Error processing transaction approval'
        }
        
        return result
    }
    
    /**
     * Process transaction rejection
     */
    private Map processTransactionRejection(TxnFile txnFile, String reason, String comments) {
        Map result = [success: false, message: '']
        
        try {
            // Update transaction status
            txnFile.status = ConfigItemStatus.get(4) // Rejected
            txnFile.save(flush: true, failOnError: true)
            
            // Update policy exception if exists
            def policyException = PolicyException.findByRecordIdAndEntityType(txnFile.id.toString(), 'txnFile')
            if (policyException) {
                policyException.dateOfAction = new Date()
                policyException.isApproved = false
                policyException.rejectionReason = reason
                policyException.actionComments = comments
                policyException.actionBy = UserMaster.get(session.user_id)
                policyException.save(flush: true, failOnError: true)
            }
            
            result.success = true
            result.message = 'Transaction rejected successfully'
            
        } catch (Exception e) {
            log.error("Error processing transaction rejection", e)
            result.message = 'Error processing transaction rejection'
        }
        
        return result
    }
    
    /**
     * Process bulk approval
     */
    private Map processBulkApproval(List<Long> txnIds, String comments) {
        Map result = [success: true, approvedCount: 0, failedCount: 0, errors: []]
        
        try {
            for (Long txnId : txnIds) {
                try {
                    def txnFile = TxnFile.get(txnId)
                    if (txnFile) {
                        Map approvalResult = processTransactionApproval(txnFile, comments)
                        if (approvalResult.success) {
                            result.approvedCount++
                        } else {
                            result.failedCount++
                            result.errors << "Transaction ${txnId}: ${approvalResult.message}"
                        }
                    } else {
                        result.failedCount++
                        result.errors << "Transaction ${txnId}: Not found"
                    }
                } catch (Exception e) {
                    result.failedCount++
                    result.errors << "Transaction ${txnId}: ${e.message}"
                }
            }
            
            result.message = "Bulk approval completed: ${result.approvedCount} approved, ${result.failedCount} failed"
            
        } catch (Exception e) {
            log.error("Error processing bulk approval", e)
            result.success = false
            result.message = 'Error processing bulk approval'
        }
        
        return result
    }
    
    /**
     * Process delegation
     */
    private Map processDelegation(TxnFile txnFile, UserMaster delegateUser, String comments) {
        Map result = [success: false, message: '']
        
        try {
            // Update policy exception with new approving user
            def policyException = PolicyException.findByRecordIdAndEntityType(txnFile.id.toString(), 'txnFile')
            if (policyException) {
                policyException.approvingUser = delegateUser
                policyException.delegationComments = comments
                policyException.delegatedBy = UserMaster.get(session.user_id)
                policyException.delegationDate = new Date()
                policyException.save(flush: true, failOnError: true)
                
                result.success = true
                result.message = 'Approval delegated successfully'
            } else {
                result.message = 'No policy exception found for this transaction'
            }
            
        } catch (Exception e) {
            log.error("Error processing delegation", e)
            result.message = 'Error processing delegation'
        }
        
        return result
    }
    
    /**
     * Get transaction approval history
     */
    private List<Map> getTransactionApprovalHistory(TxnFile txnFile) {
        try {
            def sql = new Sql(dataSource)
            def query = """
                SELECT pe.date_created, pe.date_of_action, pe.is_approved, 
                       pe.action_comments, pe.rejection_reason,
                       u1.username as requesting_user, u2.username as action_by,
                       u3.username as approving_user
                FROM policy_exception pe
                LEFT JOIN user_master u1 ON pe.requesting_user_id = u1.id
                LEFT JOIN user_master u2 ON pe.action_by_id = u2.id
                LEFT JOIN user_master u3 ON pe.approving_user_id = u3.id
                WHERE pe.record_id = ? AND pe.entity_type = 'txnFile'
                ORDER BY pe.date_created DESC
            """
            
            return sql.rows(query, [txnFile.id.toString()])
        } catch (Exception e) {
            log.error("Error getting transaction approval history", e)
            return []
        }
    }
    
    /**
     * Get workflow for transaction type
     */
    private Map getWorkflowForTransactionType(Long txnTypeId) {
        Map workflow = [:]
        
        try {
            // This would be based on actual workflow configuration
            // For now, returning a basic structure
            workflow.txnTypeId = txnTypeId
            workflow.approvalLevels = [
                [level: 1, minAmount: 0, maxAmount: 10000, role: 'TELLER_SUPERVISOR'],
                [level: 2, minAmount: 10001, maxAmount: 100000, role: 'BRANCH_MANAGER'],
                [level: 3, minAmount: 100001, maxAmount: 1000000, role: 'REGIONAL_MANAGER']
            ]
            
        } catch (Exception e) {
            log.error("Error getting workflow for transaction type", e)
            workflow.error = "Error getting workflow information"
        }
        
        return workflow
    }
    
    /**
     * Get approval limit for user
     */
    private BigDecimal getApprovalLimit(UserMaster user) {
        // This would be based on user role/position
        // For now, returning a default limit based on user level
        switch (user.userType?.id) {
            case 1: // Teller
                return new BigDecimal("10000")
            case 2: // Supervisor
                return new BigDecimal("100000")
            case 3: // Manager
                return new BigDecimal("1000000")
            default:
                return new BigDecimal("5000")
        }
    }
}
