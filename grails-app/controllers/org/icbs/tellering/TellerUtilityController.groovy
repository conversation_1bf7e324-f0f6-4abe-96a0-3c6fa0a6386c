package org.icbs.tellering

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.TxnTemplate
import org.icbs.tellering.TxnFile
import org.icbs.deposit.Deposit
import org.icbs.loans.Loan
import org.icbs.cif.Customer
import org.icbs.lov.ConfigItemStatus
import groovy.sql.Sql

/**
 * TellerUtilityController - Handles teller utility operations
 * 
 * This controller manages utility operations including:
 * - Transaction success handling
 * - Account lookup and validation
 * - Customer information retrieval
 * - Session management utilities
 * - Helper functions for teller operations
 * - Common validation routines
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class TellerUtilityController {
    
    // Service Dependencies
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        lookupAccount: "GET",
        validateCustomer: "GET",
        clearSession: "POST"
    ]

    /**
     * Transaction success page
     */
    def txnSuccess() {
        def txnFileId = session["transactionFileId"]
        def txnFile = null
        
        if (txnFileId) {
            txnFile = TxnFile.get(txnFileId)
        }
        
        render(view: '/tellering/txnSuccess', model: [
            txnFile: txnFile
        ])
    }

    /**
     * Lookup account information
     */
    def lookupAccount() {
        def accountNo = params.accountNo
        def accountType = params.accountType // "deposit" or "loan"
        
        if (!accountNo) {
            render([error: "Account number required"] as JSON)
            return
        }
        
        def accountInfo = [:]
        
        try {
            if (accountType == "loan") {
                def loanAccount = Loan.findByAccountNo(accountNo)
                if (loanAccount) {
                    accountInfo = [
                        found: true,
                        accountNo: loanAccount.accountNo,
                        customerName: loanAccount.customer.displayName,
                        customerId: loanAccount.customer.id,
                        accountType: "Loan",
                        productName: loanAccount.product.name,
                        currency: loanAccount.currency.code,
                        balance: loanAccount.balanceAmount,
                        interestBalance: loanAccount.interestBalanceAmount,
                        serviceChargeBalance: loanAccount.serviceChargeBalanceAmount,
                        status: loanAccount.status.description,
                        maturityDate: loanAccount.maturityDate?.format("MM/dd/yyyy"),
                        interestRate: loanAccount.interestRate
                    ]
                } else {
                    accountInfo = [found: false, message: "Loan account not found"]
                }
            } else {
                def depositAccount = Deposit.findByAccountNo(accountNo)
                if (depositAccount) {
                    accountInfo = [
                        found: true,
                        accountNo: depositAccount.accountNo,
                        customerName: depositAccount.customer.displayName,
                        customerId: depositAccount.customer.id,
                        accountType: "Deposit",
                        productName: depositAccount.type.description,
                        currency: depositAccount.currency.code,
                        ledgerBalance: depositAccount.ledgerBalAmt,
                        availableBalance: depositAccount.availableBalAmt,
                        status: depositAccount.status.description,
                        dateOpened: depositAccount.dateOpened.format("MM/dd/yyyy"),
                        maturityDate: depositAccount.maturityDate?.format("MM/dd/yyyy"),
                        interestRate: depositAccount.interestRate
                    ]
                } else {
                    accountInfo = [found: false, message: "Deposit account not found"]
                }
            }
            
        } catch (Exception e) {
            log.error("Error looking up account", e)
            accountInfo = [found: false, message: "Error looking up account: ${e.message}"]
        }
        
        render(accountInfo as JSON)
        return
    }

    /**
     * Validate customer information
     */
    def validateCustomer() {
        def customerId = params.customerId?.toInteger()
        def customerName = params.customerName
        
        def validationResult = [
            valid: false,
            customer: null,
            message: ""
        ]
        
        try {
            def customer = null
            
            if (customerId) {
                customer = Customer.get(customerId)
            } else if (customerName) {
                customer = Customer.findByDisplayNameIlike("%${customerName}%")
            }
            
            if (customer) {
                validationResult.valid = true
                validationResult.customer = [
                    id: customer.id,
                    displayName: customer.displayName,
                    firstName: customer.firstName,
                    lastName: customer.lastName,
                    middleName: customer.middleName,
                    birthDate: customer.birthDate?.format("MM/dd/yyyy"),
                    address: customer.address,
                    contactNo: customer.contactNo,
                    email: customer.email,
                    status: customer.status?.description
                ]
                validationResult.message = "Customer found"
            } else {
                validationResult.message = "Customer not found"
            }
            
        } catch (Exception e) {
            log.error("Error validating customer", e)
            validationResult.message = "Error validating customer: ${e.message}"
        }
        
        render(validationResult as JSON)
        return
    }

    /**
     * Get transaction templates
     */
    def getTransactionTemplates() {
        def txnType = params.txnType
        def templates = []
        
        try {
            if (txnType) {
                templates = TxnTemplate.createCriteria().list {
                    eq("txnType.id", txnType.toInteger())
                    eq("status", ConfigItemStatus.get(2))
                    order("description", "asc")
                }
            } else {
                templates = TxnTemplate.createCriteria().list {
                    eq("status", ConfigItemStatus.get(2))
                    order("txnType", "asc")
                    order("description", "asc")
                }
            }
            
            def templateData = templates.collect { template ->
                [
                    id: template.id,
                    code: template.code,
                    description: template.description,
                    txnType: template.txnType.description,
                    currency: template.currency?.code
                ]
            }
            
            render(templateData as JSON)
            
        } catch (Exception e) {
            log.error("Error getting transaction templates", e)
            render([error: "Error getting templates: ${e.message}"] as JSON)
        }
        return
    }

    /**
     * Clear session data
     */
    @Transactional
    def clearSession() {
        def sessionKeys = params.sessionKeys?.split(",") ?: []
        
        if (sessionKeys.contains("all")) {
            // Clear all teller-related session data
            session["checks"] = null
            session["map"] = null
            session["transactionFileId"] = null
            session["jrxmlTcId"] = null
            session["type"] = null
            session["pbPrintCode"] = null
        } else {
            // Clear specific session keys
            sessionKeys.each { key ->
                session[key.trim()] = null
            }
        }
        
        render([success: true, message: "Session data cleared"] as JSON)
        return
    }

    /**
     * Get user transaction summary
     */
    def getUserTransactionSummary() {
        def user = UserMaster.get(session.user_id)
        def branch = user.branch
        def txnDate = params.txnDate ? Date.parse("MM/dd/yyyy", params.txnDate) : branch.runDate
        
        def sql = new Sql(dataSource)
        def summary = [:]
        
        try {
            // Transaction count and amount summary
            summary.transactionSummary = sql.firstRow("""
                SELECT 
                    COUNT(*) as transaction_count,
                    SUM(txn_amt) as total_amount,
                    MIN(txn_timestamp) as first_txn_time,
                    MAX(txn_timestamp) as last_txn_time
                FROM txn_file 
                WHERE user_id = ? AND txn_date = ? AND status_id = 2
            """, [user.id, txnDate])
            
            // Transaction type breakdown
            summary.typeBreakdown = sql.rows("""
                SELECT 
                    tt.description as txn_type,
                    COUNT(*) as count,
                    SUM(tf.txn_amt) as amount
                FROM txn_file tf
                JOIN txn_type tt ON tf.txn_type_id = tt.id
                WHERE tf.user_id = ? AND tf.txn_date = ? AND tf.status_id = 2
                GROUP BY tt.id, tt.description
                ORDER BY count DESC
            """, [user.id, txnDate])
            
            // Currency breakdown
            summary.currencyBreakdown = sql.rows("""
                SELECT 
                    c.code as currency,
                    COUNT(*) as count,
                    SUM(tf.txn_amt) as amount
                FROM txn_file tf
                JOIN currency c ON tf.currency_id = c.id
                WHERE tf.user_id = ? AND tf.txn_date = ? AND tf.status_id = 2
                GROUP BY c.id, c.code
                ORDER BY amount DESC
            """, [user.id, txnDate])
            
        } catch (Exception e) {
            log.error("Error getting user transaction summary", e)
            summary.error = "Error getting summary: ${e.message}"
        }
        
        render(summary as JSON)
        return
    }

    /**
     * Validate transaction limits
     */
    def validateTransactionLimits() {
        def user = UserMaster.get(session.user_id)
        def amount = params.amount?.toDouble() ?: 0
        def txnType = params.txnType
        
        def validation = [
            valid: true,
            warnings: [],
            errors: []
        ]
        
        try {
            // Check single transaction limit
            if (user.singleTransactionLimit && amount > user.singleTransactionLimit) {
                validation.errors.add("Amount exceeds single transaction limit of ${user.singleTransactionLimit}")
                validation.valid = false
            }
            
            // Check daily transaction limit
            if (user.dailyTransactionLimit) {
                def dailyTotal = getCurrentDailyTotal(user)
                if ((dailyTotal + amount) > user.dailyTransactionLimit) {
                    validation.errors.add("Amount exceeds daily transaction limit of ${user.dailyTransactionLimit}")
                    validation.valid = false
                }
            }
            
            // Check for large amounts
            if (amount > 100000) {
                validation.warnings.add("Large amount transaction - requires supervisor approval")
            }
            
            // Check transaction type specific limits
            if (txnType == "FOREX" && user.dailyForexLimit) {
                def dailyForexTotal = getCurrentDailyForexTotal(user)
                if ((dailyForexTotal + amount) > user.dailyForexLimit) {
                    validation.errors.add("Amount exceeds daily forex limit of ${user.dailyForexLimit}")
                    validation.valid = false
                }
            }
            
        } catch (Exception e) {
            log.error("Error validating transaction limits", e)
            validation.errors.add("Error validating limits: ${e.message}")
            validation.valid = false
        }
        
        render(validation as JSON)
        return
    }

    /**
     * Get system status information
     */
    def getSystemStatus() {
        def user = UserMaster.get(session.user_id)
        def branch = user.branch
        
        def status = [
            branchName: branch.name,
            branchCode: branch.code,
            runDate: branch.runDate.format("MM/dd/yyyy"),
            isEOD: branch.isEOD,
            userName: user.username,
            userRole: user.role?.description,
            isTellerBalanced: user.isTellerBalanced,
            serverTime: new Date().format("MM/dd/yyyy HH:mm:ss"),
            systemStatus: "Online"
        ]
        
        render(status as JSON)
        return
    }

    /**
     * Log user activity
     */
    @Transactional
    def logUserActivity() {
        def activity = params.activity
        def details = params.details
        
        if (activity) {
            def user = UserMaster.get(session.user_id)
            def description = "User activity: ${activity}"
            if (details) {
                description += " - ${details}"
            }
            
            auditLogService.insert('120', 'TLR02600', description, 'TellerUtility', null, null, null, user.id)
            
            render([success: true, message: "Activity logged"] as JSON)
        } else {
            render([success: false, message: "Activity description required"] as JSON)
        }
        return
    }

    // Helper methods
    private def getCurrentDailyTotal(UserMaster user) {
        def branch = user.branch
        def today = branch.runDate
        
        def total = TxnFile.createCriteria().get {
            projections {
                sum("txnAmt")
            }
            and {
                eq("user", user)
                eq("txnDate", today)
                eq("status", ConfigItemStatus.get(2)) // Posted
            }
        }
        
        return total ?: 0
    }

    private def getCurrentDailyForexTotal(UserMaster user) {
        def branch = user.branch
        def today = branch.runDate
        
        def sql = new Sql(dataSource)
        def total = sql.firstRow("""
            SELECT COALESCE(SUM(tf.txn_amt), 0) as total
            FROM txn_file tf
            JOIN txn_type tt ON tf.txn_type_id = tt.id
            WHERE tf.user_id = ? AND tf.txn_date = ? AND tf.status_id = 2
            AND tt.description LIKE '%Forex%'
        """, [user.id, today])
        
        return total?.total ?: 0
    }
}
