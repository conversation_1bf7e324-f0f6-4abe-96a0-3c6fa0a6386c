package org.icbs.tellering

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnCashCheckBlotter
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnLoanPaymentDetails
import org.icbs.deposit.Deposit
import org.icbs.loans.Loan
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.lov.ConfigItemStatus
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import grails.converters.JSON

/**
 * REFACTORED: Transaction Inquiry Controller
 * Extracted from TelleringController.groovy (7,319 lines)
 * Handles all transaction inquiry and search operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class TransactionInquiryController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def auditLogService
    
    static allowedMethods = [
        search: "GET",
        viewTellerTxnInquiry: "GET",
        viewTellerTxnInquiry2: "GET",
        viewTellerOtherTxn: "GET",
        showGlEntries: "GET"
    ]
    
    // =====================================================
    // TRANSACTION INQUIRY OPERATIONS
    // =====================================================
    
    /**
     * Main transaction inquiry view
     */
    def viewTellerTxnInquiry() {
        log.info("Viewing teller transaction inquiry")
        
        try {
            // Audit logging
            auditLogService.insert('110', 'TIQ01100', 
                "Transaction inquiry interface accessed", 
                'TransactionInquiryController', null, null, 'tellering/viewTellerTxnInquiry', session.user_id)
            
            render(view: '/tellering/txnInquiry/view')
            
        } catch (Exception e) {
            log.error("Error viewing transaction inquiry", e)
            flash.message = 'Error accessing transaction inquiry|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * View specific transaction details
     */
    def viewTellerTxnInquiry2(TxnFile tf) {
        log.info("Viewing transaction details for transaction: ${tf?.id}")
        
        try {
            if (!tf) {
                flash.message = 'Transaction not found|error|alert'
                redirect(action: 'viewTellerTxnInquiry')
                return
            }
            
            // Get related transaction data
            Map transactionData = getTransactionDetails(tf)
            
            // Audit logging
            auditLogService.insert('110', 'TIQ01200', 
                "Transaction details viewed - ID: ${tf.id}", 
                'TransactionInquiryController', null, null, 'tellering/viewTellerTxnInquiry2', tf.id)
            
            render(view: '/tellering/txnInquiry/view2', 
                model: [
                    txnFileInstance: tf,
                    transactionData: transactionData
                ])
            
        } catch (Exception e) {
            log.error("Error viewing transaction details", e)
            flash.message = 'Error viewing transaction details|error|alert'
            redirect(action: 'viewTellerTxnInquiry')
        }
    }
    
    /**
     * Search transactions
     */
    def search(Integer max) {
        log.debug("Searching transactions with query: ${params.query}")
        
        try {
            params.max = Math.min(max ?: 10, 100)
            def thisUser = UserMaster.get(session.user_id)
            
            if (params.sort == null) {
                params.sort = "id"
            }
            
            List<TxnFile> result
            
            if (params.query == null || params.query.trim() == "") {
                // Show all transactions for current user and date
                result = TxnFile.createCriteria().list(params) {
                    and {
                        eq("user", thisUser)
                        eq("txnDate", Branch.get(1).runDate)
                    }
                    order(params.sort, params.order ?: "desc")
                }
            } else {
                // Show query results
                result = TxnFile.createCriteria().list(params) {
                    and {
                        eq("user", thisUser)
                        eq("txnDate", Branch.get(1).runDate)
                    }
                    or {
                        if (params.query.isNumber()) {
                            eq("id", params.query.toLong())
                        } else {
                            ilike("acctNo", "%${params.query}%")
                            ilike("txnDescription", "%${params.query}%")
                            ilike("txnParticulars", "%${params.query}%")
                            ilike("txnRef", "%${params.query}%")
                            ilike("txnCode", "%${params.query}%")
                        }
                    }
                    order(params.sort, params.order ?: "desc")
                }
            }
            
            // Audit logging for search
            auditLogService.insert('110', 'TIQ01300', 
                "Transaction search performed - Query: ${params.query ?: 'ALL'}, Results: ${result.size()}", 
                'TransactionInquiryController', null, null, 'tellering/search', session.user_id)
            
            render(template: "details/txnDetails", 
                model: [
                    params: params, 
                    domainInstanceList: result, 
                    domainInstanceCount: result.totalCount
                ]) as JSON
            
        } catch (Exception e) {
            log.error("Error searching transactions", e)
            render([error: 'Error searching transactions'] as JSON)
        }
    }
    
    /**
     * View teller other transactions
     */
    def viewTellerOtherTxn(Integer max) {
        log.info("Viewing teller other transactions")
        
        try {
            params.max = Math.min(max ?: 10, 100)
            
            List<TxnFile> otherTxn
            
            if (params.query == null || params.query == "") {
                // Show all transactions
                otherTxn = TxnFile.createCriteria().list(params) {
                    and {
                        eq("user", UserMaster.get(session.user_id))
                        eq("txnDate", Branch.get(1).runDate)
                    }
                    order("id", "asc")
                }
            } else {
                // Show query results
                otherTxn = TxnFile.createCriteria().list(params) {
                    and {
                        eq("user", UserMaster.get(session.user_id))
                        eq("txnDate", Branch.get(1).runDate)
                        eq("id", params.query.toLong())
                    }
                }
            }
            
            // Audit logging
            auditLogService.insert('110', 'TIQ01400', 
                "Other transactions viewed - Count: ${otherTxn.size()}", 
                'TransactionInquiryController', null, null, 'tellering/viewTellerOtherTxn', session.user_id)
            
            render(view: '/tellering/tellerBalancing/viewTellerOtherTxn.gsp', 
                model: [
                    params: params,
                    otherTxn: otherTxn,
                    otherTxnInstanceCount: otherTxn.totalCount
                ])
            
        } catch (Exception e) {
            log.error("Error viewing other transactions", e)
            flash.message = 'Error viewing other transactions|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * Show GL entries for a transaction
     */
    def showGlEntries(TxnFile txnFileInstance) {
        log.info("Showing GL entries for transaction: ${txnFileInstance?.id}")
        
        try {
            if (txnFileInstance) {
                def glEntries = TxnBreakdown.findAllByTxnFile(txnFileInstance)
                
                // Audit logging
                auditLogService.insert('110', 'TIQ01500', 
                    "GL entries viewed for transaction: ${txnFileInstance.id}", 
                    'TransactionInquiryController', null, null, 'tellering/showGlEntries', txnFileInstance.id)
                
                render(view: '/tellering/tellerBalancing/showGlEntries.gsp', 
                    model: [
                        glEntries: glEntries,
                        txnFileInstance: txnFileInstance
                    ])
            } else {
                flash.message = 'Transaction not found|error|alert'
                render(view: '/tellering/index')
            }
            
        } catch (Exception e) {
            log.error("Error showing GL entries", e)
            flash.message = 'Error showing GL entries|error|alert'
            render(view: '/tellering/index')
        }
    }
    
    /**
     * Get account transaction history
     */
    def getAccountTransactionHistory() {
        log.debug("Getting account transaction history for account: ${params.accountNo}")
        
        try {
            if (!params.accountNo) {
                render([error: 'Account number is required'] as JSON)
                return
            }
            
            Map historyData = getAccountHistory(params.accountNo, params)
            
            // Audit logging
            auditLogService.insert('110', 'TIQ01600', 
                "Account transaction history retrieved - Account: ${params.accountNo}", 
                'TransactionInquiryController', null, null, 'tellering/getAccountTransactionHistory', session.user_id)
            
            render(historyData as JSON)
            
        } catch (Exception e) {
            log.error("Error getting account transaction history", e)
            render([error: 'Error getting account transaction history'] as JSON)
        }
    }
    
    /**
     * Get transaction summary
     */
    def getTransactionSummary() {
        log.debug("Getting transaction summary for date: ${params.txnDate}")
        
        try {
            Date txnDate = params.txnDate ? Date.parse('yyyy-MM-dd', params.txnDate) : new Date()
            UserMaster user = UserMaster.get(session.user_id)
            
            Map summary = calculateTransactionSummary(user, txnDate)
            
            // Audit logging
            auditLogService.insert('110', 'TIQ01700', 
                "Transaction summary retrieved for date: ${txnDate}", 
                'TransactionInquiryController', null, null, 'tellering/getTransactionSummary', session.user_id)
            
            render(summary as JSON)
            
        } catch (Exception e) {
            log.error("Error getting transaction summary", e)
            render([error: 'Error getting transaction summary'] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Get detailed transaction information
     */
    private Map getTransactionDetails(TxnFile tf) {
        Map details = [:]
        
        try {
            details.basicInfo = [
                id: tf.id,
                txnDate: tf.txnDate,
                txnType: tf.txnType?.description,
                txnTemplate: tf.txnTemplate?.description,
                amount: tf.txnAmt,
                status: tf.status?.description,
                reference: tf.txnRef,
                particulars: tf.txnParticulars
            ]
            
            // Get related records based on transaction type
            if (tf.txnType?.id in [1, 2, 3, 4, 5]) { // Deposit transactions
                details.depositLedger = TxnDepositAcctLedger.findAllByTxnFile(tf)
                details.account = tf.depAcct
            }
            
            if (tf.txnType?.id in [12, 13, 14, 15]) { // Loan transactions
                details.loanPayment = TxnLoanPaymentDetails.findAllByTxnFile(tf)
                details.account = tf.loanAcct
            }
            
            // Get teller blotter entry
            details.tellerBlotter = TxnCashCheckBlotter.findByTxnFile(tf)
            
            // Get GL breakdown
            details.glBreakdown = TxnBreakdown.findAllByTxnFile(tf)
            
        } catch (Exception e) {
            log.error("Error getting transaction details", e)
            details.error = "Error retrieving transaction details"
        }
        
        return details
    }
    
    /**
     * Get account transaction history
     */
    private Map getAccountHistory(String accountNo, Map params) {
        Map history = [transactions: [], summary: [:]]
        
        try {
            // Determine account type and get transactions
            Deposit depositAccount = Deposit.findByAcctNo(accountNo)
            Loan loanAccount = Loan.findByAccountNo(accountNo)
            
            if (depositAccount) {
                history.accountType = 'DEPOSIT'
                history.account = depositAccount
                history.transactions = TxnDepositAcctLedger.createCriteria().list {
                    eq("acctNo", accountNo)
                    order("txnDate", "desc")
                    maxResults(params.max?.toInteger() ?: 50)
                }
            } else if (loanAccount) {
                history.accountType = 'LOAN'
                history.account = loanAccount
                history.transactions = TxnLoanPaymentDetails.createCriteria().list {
                    eq("acctNo", accountNo)
                    order("txnDate", "desc")
                    maxResults(params.max?.toInteger() ?: 50)
                }
            } else {
                history.error = "Account not found"
            }
            
            // Calculate summary
            if (history.transactions) {
                history.summary = calculateAccountSummary(history.transactions, history.accountType)
            }
            
        } catch (Exception e) {
            log.error("Error getting account history", e)
            history.error = "Error retrieving account history"
        }
        
        return history
    }
    
    /**
     * Calculate transaction summary for user and date
     */
    private Map calculateTransactionSummary(UserMaster user, Date txnDate) {
        Map summary = [:]
        
        try {
            // Get all transactions for user and date
            List<TxnFile> transactions = TxnFile.findAllByUserAndTxnDate(user, txnDate)
            
            summary.totalTransactions = transactions.size()
            summary.totalAmount = transactions.sum { it.txnAmt } ?: 0
            
            // Group by transaction type
            summary.byType = transactions.groupBy { it.txnType?.description }
                .collectEntries { type, txns ->
                    [type, [count: txns.size(), amount: txns.sum { it.txnAmt } ?: 0]]
                }
            
            // Group by status
            summary.byStatus = transactions.groupBy { it.status?.description }
                .collectEntries { status, txns ->
                    [status, [count: txns.size(), amount: txns.sum { it.txnAmt } ?: 0]]
                }
            
            // Cash and check summary
            List<TxnCashCheckBlotter> blotterEntries = TxnCashCheckBlotter.createCriteria().list {
                eq("user", user)
                txnFile {
                    eq("txnDate", txnDate)
                }
            }
            
            summary.cashSummary = [
                cashIn: blotterEntries.sum { it.cashInAmt } ?: 0,
                cashOut: blotterEntries.sum { it.cashOutAmt } ?: 0,
                checkIn: blotterEntries.sum { it.checkInAmt } ?: 0,
                checkOut: blotterEntries.sum { it.checkOutAmt } ?: 0
            ]
            
        } catch (Exception e) {
            log.error("Error calculating transaction summary", e)
            summary.error = "Error calculating summary"
        }
        
        return summary
    }
    
    /**
     * Calculate account summary
     */
    private Map calculateAccountSummary(List transactions, String accountType) {
        Map summary = [:]
        
        try {
            summary.totalTransactions = transactions.size()
            
            if (accountType == 'DEPOSIT') {
                summary.totalDebits = transactions.sum { it.debitAmt } ?: 0
                summary.totalCredits = transactions.sum { it.creditAmt } ?: 0
                summary.netAmount = summary.totalCredits - summary.totalDebits
            } else if (accountType == 'LOAN') {
                summary.totalPayments = transactions.sum { it.paymentAmt } ?: 0
                summary.totalPrincipal = transactions.sum { it.principalAmt } ?: 0
                summary.totalInterest = transactions.sum { it.interestAmt } ?: 0
                summary.totalPenalty = transactions.sum { it.penaltyAmt } ?: 0
            }
            
            // Date range
            if (transactions) {
                summary.dateRange = [
                    from: transactions.min { it.txnDate }?.txnDate,
                    to: transactions.max { it.txnDate }?.txnDate
                ]
            }
            
        } catch (Exception e) {
            log.error("Error calculating account summary", e)
            summary.error = "Error calculating account summary"
        }
        
        return summary
    }
}
