package org.icbs.tellering

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.tellering.TxnPassbookLine
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.deposit.Deposit
import org.icbs.lov.ConfigItemStatus
import groovy.sql.Sql

/**
 * TellerPassbookController - Handles passbook operations
 * 
 * This controller manages passbook operations including:
 * - Passbook printing and updating
 * - Passbook line management
 * - Transaction posting to passbook
 * - Passbook balance validation
 * - Passbook inquiry operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class TellerPassbookController {
    
    // Service Dependencies
    def jasperService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        savePassbookPrint: "POST",
        updatePassbookBalance: "POST",
        validatePassbookEntry: "POST"
    ]

    /**
     * Create passbook print transaction
     */
    def createPassbookPrintTxn() {
        def user = UserMaster.get(session.user_id)
        def txnPassbookLineInstance = new TxnPassbookLine()
        
        render(view:'/tellering/txnPassbookPrint/create', model: [
            txnPassbookLineInstance: txnPassbookLineInstance
        ])
    }

    /**
     * Save passbook print transaction
     */
    @Transactional
    def savePassbookPrint(TxnPassbookLine tpl) {
        println params // Error check
        
        def user = UserMaster.get(session.user_id)
        def branch = Branch.get(user.branchId)
        def depositAccount = Deposit.get(params.acct)
        
        // Validate transaction
        if (!depositAccount) {
            flash.message = 'Invalid deposit account|error|alert'
            render(view:'/tellering/txnPassbookPrint/create', model: [
                txnPassbookLineInstance: tpl
            ])
            return
        }

        // Get unprinted transactions for this account
        def unprintedTransactions = TxnDepositAcctLedger.createCriteria().list {
            and {
                eq("acct", depositAccount)
                or {
                    isNull("passbookBal")
                    eq("passbookBal", 0)
                }
                eq("txnFile.status", ConfigItemStatus.get(2)) // Posted transactions only
            }
            order("txnDate", "asc")
            order("id", "asc")
        }

        if (!unprintedTransactions || unprintedTransactions.size() == 0) {
            flash.message = 'No transactions to print|info'
            render(view:'/tellering/txnPassbookPrint/create', model: [
                txnPassbookLineInstance: tpl
            ])
            return
        }

        // Generate passbook print code
        def printCode = generatePassbookPrintCode()
        def runningBalance = params.startingBalance?.toDouble() ?: depositAccount.ledgerBalAmt
        
        // Create passbook lines for each transaction
        unprintedTransactions.each { txnLedger ->
            def passbookLine = new TxnPassbookLine(
                acct: depositAccount,
                txnDate: txnLedger.txnDate,
                txnRef: txnLedger.txnRef,
                debitAmt: txnLedger.debitAmt,
                creditAmt: txnLedger.creditAmt,
                bal: runningBalance,
                pbPrintCode: printCode,
                user: user,
                branch: branch,
                dateCreated: new Date(),
                txnDepositAcctLedger: txnLedger
            )
            
            // Update running balance
            runningBalance = runningBalance - txnLedger.debitAmt + txnLedger.creditAmt
            passbookLine.bal = runningBalance
            
            passbookLine.save(flush: true, failOnError: true)
            
            // Update the ledger entry with passbook balance
            txnLedger.passbookBal = runningBalance
            txnLedger.save(flush: true)
        }

        def description = "Passbook printed - Account: ${depositAccount.accountNo} Transactions: ${unprintedTransactions.size()} Print Code: ${printCode}"
        auditLogService.insert('120', 'TLR02300', description, 'TellerPassbook', null, null, null, depositAccount.id)
        
        session["pbPrintCode"] = printCode
        session["jrxmlTcId"] = depositAccount.id
        
        redirect(controller: "tellerReport", action: "printToPassbookTransactions", params: [pbPrintCode: printCode])
    }

    /**
     * View passbook inquiry
     */
    def viewPassbookInquiry() {
        def user = UserMaster.get(session.user_id)
        
        render(view:'/tellering/passbookInquiry/view')
    }

    /**
     * Get passbook transactions via AJAX
     */
    def getPassbookTransactionsAjax() {
        def accountNo = params.accountNo
        def fromDate = params.fromDate ? Date.parse("MM/dd/yyyy", params.fromDate) : null
        def toDate = params.toDate ? Date.parse("MM/dd/yyyy", params.toDate) : null
        
        if (!accountNo) {
            render([error: "Account number required"] as JSON)
            return
        }
        
        def depositAccount = Deposit.findByAccountNo(accountNo)
        if (!depositAccount) {
            render([error: "Account not found"] as JSON)
            return
        }
        
        def criteria = TxnPassbookLine.createCriteria()
        def transactions = criteria.list {
            eq("acct", depositAccount)
            if (fromDate) {
                ge("txnDate", fromDate)
            }
            if (toDate) {
                le("txnDate", toDate)
            }
            order("txnDate", "desc")
            order("id", "desc")
            maxResults(50) // Limit to last 50 transactions
        }
        
        def transactionData = transactions.collect { txn ->
            [
                txnDate: txn.txnDate.format("MM/dd/yyyy"),
                txnRef: txn.txnRef,
                debitAmt: txn.debitAmt,
                creditAmt: txn.creditAmt,
                balance: txn.bal,
                printCode: txn.pbPrintCode
            ]
        }
        
        render([
            accountNo: depositAccount.accountNo,
            customerName: depositAccount.customer.displayName,
            currentBalance: depositAccount.ledgerBalAmt,
            transactions: transactionData
        ] as JSON)
        return
    }

    /**
     * Update passbook balance
     */
    @Transactional
    def updatePassbookBalance() {
        def accountNo = params.accountNo
        def newBalance = params.newBalance?.toDouble()
        
        if (!accountNo || newBalance == null) {
            render([success: false, message: "Account number and balance required"] as JSON)
            return
        }
        
        def depositAccount = Deposit.findByAccountNo(accountNo)
        if (!depositAccount) {
            render([success: false, message: "Account not found"] as JSON)
            return
        }
        
        try {
            // Create adjustment entry if there's a difference
            def currentBalance = depositAccount.ledgerBalAmt
            def difference = newBalance - currentBalance
            
            if (Math.abs(difference) > 0.01) { // Only if difference is significant
                // Create passbook adjustment entry
                def adjustmentLine = new TxnPassbookLine(
                    acct: depositAccount,
                    txnDate: new Date(),
                    txnRef: "Passbook Balance Adjustment",
                    debitAmt: difference < 0 ? Math.abs(difference) : 0,
                    creditAmt: difference > 0 ? difference : 0,
                    bal: newBalance,
                    pbPrintCode: generatePassbookPrintCode(),
                    user: UserMaster.get(session.user_id),
                    branch: UserMaster.get(session.user_id).branch,
                    dateCreated: new Date()
                )
                adjustmentLine.save(flush: true, failOnError: true)
                
                // Update account balance
                depositAccount.ledgerBalAmt = newBalance
                depositAccount.availableBalAmt = newBalance
                depositAccount.save(flush: true)
                
                def description = "Passbook balance adjusted - Account: ${accountNo} From: ${currentBalance} To: ${newBalance}"
                auditLogService.insert('120', 'TLR02400', description, 'TellerPassbook', null, null, null, depositAccount.id)
            }
            
            render([
                success: true,
                message: "Passbook balance updated successfully",
                oldBalance: currentBalance,
                newBalance: newBalance,
                adjustment: difference
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error updating passbook balance", e)
            render([
                success: false,
                message: "Error updating balance: ${e.message}"
            ] as JSON)
        }
        return
    }

    /**
     * Validate passbook entry
     */
    def validatePassbookEntry() {
        def accountNo = params.accountNo
        def entryDate = params.entryDate ? Date.parse("MM/dd/yyyy", params.entryDate) : null
        def entryAmount = params.entryAmount?.toDouble()
        def entryType = params.entryType // "debit" or "credit"
        
        if (!accountNo || !entryDate || !entryAmount || !entryType) {
            render([valid: false, message: "All fields required"] as JSON)
            return
        }
        
        def depositAccount = Deposit.findByAccountNo(accountNo)
        if (!depositAccount) {
            render([valid: false, message: "Account not found"] as JSON)
            return
        }
        
        def validationResult = [
            valid: true,
            warnings: [],
            errors: []
        ]
        
        // Check if entry date is not in the future
        if (entryDate > new Date()) {
            validationResult.errors.add("Entry date cannot be in the future")
            validationResult.valid = false
        }
        
        // Check if debit amount doesn't exceed balance
        if (entryType == "debit" && entryAmount > depositAccount.availableBalAmt) {
            validationResult.errors.add("Insufficient account balance")
            validationResult.valid = false
        }
        
        // Check for large amounts
        if (entryAmount > 50000) {
            validationResult.warnings.add("Large amount - verify transaction details")
        }
        
        render(validationResult as JSON)
        return
    }

    /**
     * Get account passbook status
     */
    def getAccountPassbookStatus() {
        def accountNo = params.accountNo
        
        if (!accountNo) {
            render([error: "Account number required"] as JSON)
            return
        }
        
        def depositAccount = Deposit.findByAccountNo(accountNo)
        if (!depositAccount) {
            render([error: "Account not found"] as JSON)
            return
        }
        
        // Count unprinted transactions
        def unprintedCount = TxnDepositAcctLedger.createCriteria().count {
            and {
                eq("acct", depositAccount)
                or {
                    isNull("passbookBal")
                    eq("passbookBal", 0)
                }
                eq("txnFile.status", ConfigItemStatus.get(2))
            }
        }
        
        // Get last printed transaction
        def lastPrintedTxn = TxnPassbookLine.createCriteria().get {
            eq("acct", depositAccount)
            order("dateCreated", "desc")
            maxResults(1)
        }
        
        render([
            accountNo: depositAccount.accountNo,
            customerName: depositAccount.customer.displayName,
            currentBalance: depositAccount.ledgerBalAmt,
            unprintedTransactions: unprintedCount,
            lastPrintedDate: lastPrintedTxn?.dateCreated?.format("MM/dd/yyyy HH:mm:ss"),
            lastPrintCode: lastPrintedTxn?.pbPrintCode,
            needsPrinting: unprintedCount > 0
        ] as JSON)
        return
    }

    /**
     * Reprint passbook transactions
     */
    def reprintPassbookTransactions() {
        def printCode = params.printCode
        
        if (!printCode) {
            flash.message = "Print code required|error|alert"
            redirect(action: "viewPassbookInquiry")
            return
        }
        
        def passbookLines = TxnPassbookLine.findAllByPbPrintCode(printCode)
        if (!passbookLines || passbookLines.size() == 0) {
            flash.message = "No transactions found for print code|error|alert"
            redirect(action: "viewPassbookInquiry")
            return
        }
        
        def description = "Passbook transactions reprinted - Print Code: ${printCode} Lines: ${passbookLines.size()}"
        auditLogService.insert('120', 'TLR02500', description, 'TellerPassbook', null, null, null, null)
        
        redirect(controller: "tellerReport", action: "printToPassbookTransactions", params: [pbPrintCode: printCode])
    }

    // Helper methods
    private def generatePassbookPrintCode() {
        def user = UserMaster.get(session.user_id)
        def branch = user.branch
        def timestamp = new Date().format("yyyyMMddHHmmss")
        
        return "PB${branch.code}${user.id}${timestamp}"
    }
}
