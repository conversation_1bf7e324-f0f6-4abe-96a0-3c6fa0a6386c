package org.icbs.tellering

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnCOCI
import org.icbs.tellering.StopPaymentOrder
import org.icbs.deposit.Deposit
import org.icbs.loans.Loan
import org.icbs.admin.UserMaster
import org.icbs.admin.ProductTxn
import org.icbs.admin.CheckDepositClearingType
import org.icbs.lov.ConfigItemStatus
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import grails.converters.JSON

/**
 * REFACTORED: Transaction Validation Controller
 * Extracted from TelleringController.groovy (7,319 lines)
 * Handles all transaction validation operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class TransactionValidationController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def policyService
    def auditLogService
    
    static allowedMethods = [
        validateTransaction: "POST",
        validatePassbookBal: "POST",
        validateCheckDetails: "POST",
        validateBusinessRules: "POST",
        validatePolicyCompliance: "POST"
    ]
    
    // =====================================================
    // TRANSACTION VALIDATION OPERATIONS
    // =====================================================
    
    /**
     * Validate transaction parameters and business rules
     */
    def validateTransaction() {
        log.debug("Validating transaction with parameters: ${params}")
        
        try {
            Map validationResult = [isValid: true, errors: [], warnings: []]
            
            // 1. Basic parameter validation
            validationResult = validateBasicParameters(params, validationResult)
            
            // 2. Business rule validation
            if (validationResult.isValid) {
                validationResult = validateBusinessRules(params, validationResult)
            }
            
            // 3. Policy compliance validation
            if (validationResult.isValid) {
                validationResult = validatePolicyCompliance(params, validationResult)
            }
            
            // 4. Account-specific validation
            if (validationResult.isValid && params.accountId) {
                validationResult = validateAccountSpecificRules(params, validationResult)
            }
            
            // Audit logging
            auditLogService.insert('110', 'TVL01100', 
                "Transaction validation performed - Result: ${validationResult.isValid ? 'VALID' : 'INVALID'}", 
                'TransactionValidationController', null, null, 'tellering/validateTransaction', session.user_id)
            
            render(validationResult as JSON)
            
        } catch (Exception e) {
            log.error("Error validating transaction", e)
            render([isValid: false, errors: ['Error during transaction validation']] as JSON)
        }
    }
    
    /**
     * Validate passbook balance
     */
    def validatePassbookBal() {
        log.debug("Validating passbook balance for account: ${params.acctNo}")
        
        try {
            if (!params.acctNo || !params.passbookBal) {
                render(text: false) as JSON
                return
            }
            
            def depositInstance = Deposit.findByAcctNo(params.acctNo)
            if (!depositInstance) {
                render(text: false) as JSON
                return
            }
            
            def passbookBalance = Float.parseFloat(params.passbookBal)
            def accountBalance = depositInstance.passbookBalAmt
            
            boolean isValid = (passbookBalance == accountBalance)
            
            // Audit logging
            auditLogService.insert('110', 'TVL01200', 
                "Passbook balance validation - Account: ${params.acctNo}, Valid: ${isValid}", 
                'TransactionValidationController', null, null, 'tellering/validatePassbookBal', session.user_id)
            
            render(text: isValid) as JSON
            
        } catch (Exception e) {
            log.error("Error validating passbook balance", e)
            render(text: false) as JSON
        }
    }
    
    /**
     * Validate check details
     */
    def validateCheckDetails() {
        log.debug("Validating check details: ${params}")
        
        try {
            Map validationResult = [isValid: true, errors: [], warnings: []]
            
            // Basic check validations
            if (!params.checkType) {
                validationResult.isValid = false
                validationResult.errors << 'Check type is required!'
            }
            
            if (!params.bank && params.checkType != '3') {
                validationResult.isValid = false
                validationResult.errors << 'Bank is required!'
            }
            
            if (!params.acctNo) {
                validationResult.isValid = false
                validationResult.errors << 'Account number is required!'
            }
            
            if (!params.checkDate) {
                validationResult.isValid = false
                validationResult.errors << 'Check date is required!'
            }
            
            if (!params.checkNo) {
                validationResult.isValid = false
                validationResult.errors << 'Check number is required!'
            }
            
            if (!params.checkAmt) {
                validationResult.isValid = false
                validationResult.errors << 'Check amount is required!'
            }
            
            // Advanced check validations
            if (validationResult.isValid) {
                validationResult = validateCheckBusinessRules(params, validationResult)
            }
            
            // Audit logging
            auditLogService.insert('110', 'TVL01300', 
                "Check details validation - Check: ${params.checkNo}, Valid: ${validationResult.isValid}", 
                'TransactionValidationController', null, null, 'tellering/validateCheckDetails', session.user_id)
            
            render(validationResult as JSON)
            
        } catch (Exception e) {
            log.error("Error validating check details", e)
            render([isValid: false, errors: ['Error validating check details']] as JSON)
        }
    }
    
    /**
     * Validate business rules for transaction
     */
    def validateBusinessRules() {
        log.debug("Validating business rules for transaction")
        
        try {
            Map validationResult = [isValid: true, errors: [], warnings: []]
            
            // Transaction amount limits
            if (params.txnAmt) {
                def amount = Double.parseDouble(params.txnAmt.toString().replace(',', ''))
                
                // Daily transaction limits
                validationResult = validateDailyLimits(amount, validationResult)
                
                // Single transaction limits
                validationResult = validateSingleTransactionLimits(amount, validationResult)
                
                // Account-specific limits
                if (params.accountId) {
                    validationResult = validateAccountLimits(params.accountId, amount, validationResult)
                }
            }
            
            // Time-based validations
            validationResult = validateTransactionTiming(validationResult)
            
            // User-specific validations
            validationResult = validateUserPermissions(validationResult)
            
            render(validationResult as JSON)
            
        } catch (Exception e) {
            log.error("Error validating business rules", e)
            render([isValid: false, errors: ['Error validating business rules']] as JSON)
        }
    }
    
    /**
     * Validate policy compliance
     */
    def validatePolicyCompliance() {
        log.debug("Validating policy compliance for transaction")
        
        try {
            Map validationResult = [isValid: true, errors: [], warnings: []]
            
            if (params.txnTemplate && params.txnAmt) {
                def amount = Long.parseLong(params.txnAmt.toString().replace(',', ''))
                def isTxnAllowed = policyService.isTxnAllowed(params.txnTemplate, amount)
                
                if (!isTxnAllowed) {
                    validationResult.isValid = false
                    validationResult.errors << 'Transaction not allowed by policy'
                    validationResult.requiresException = true
                }
            }
            
            // Additional policy validations
            validationResult = validateAdditionalPolicies(params, validationResult)
            
            // Audit logging
            auditLogService.insert('110', 'TVL01400', 
                "Policy compliance validation - Template: ${params.txnTemplate}, Valid: ${validationResult.isValid}", 
                'TransactionValidationController', null, null, 'tellering/validatePolicyCompliance', session.user_id)
            
            render(validationResult as JSON)
            
        } catch (Exception e) {
            log.error("Error validating policy compliance", e)
            render([isValid: false, errors: ['Error validating policy compliance']] as JSON)
        }
    }
    
    /**
     * Validate account status and eligibility
     */
    def validateAccountEligibility() {
        log.debug("Validating account eligibility: ${params.accountId}")
        
        try {
            Map validationResult = [isValid: true, errors: [], warnings: []]
            
            if (!params.accountId) {
                validationResult.isValid = false
                validationResult.errors << 'Account ID is required'
                render(validationResult as JSON)
                return
            }
            
            // Check deposit account
            def depositAccount = Deposit.get(params.accountId)
            if (depositAccount) {
                validationResult = validateDepositAccountEligibility(depositAccount, validationResult)
            }
            
            // Check loan account
            def loanAccount = Loan.get(params.accountId)
            if (loanAccount) {
                validationResult = validateLoanAccountEligibility(loanAccount, validationResult)
            }
            
            if (!depositAccount && !loanAccount) {
                validationResult.isValid = false
                validationResult.errors << 'Account not found'
            }
            
            render(validationResult as JSON)
            
        } catch (Exception e) {
            log.error("Error validating account eligibility", e)
            render([isValid: false, errors: ['Error validating account eligibility']] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Validate basic transaction parameters
     */
    private Map validateBasicParameters(Map params, Map validationResult) {
        // Required fields validation
        if (!params.txnType) {
            validationResult.isValid = false
            validationResult.errors << 'Transaction type is required'
        }
        
        if (!params.txnAmt || Double.parseDouble(params.txnAmt.toString().replace(',', '')) <= 0) {
            validationResult.isValid = false
            validationResult.errors << 'Valid transaction amount is required'
        }
        
        if (!params.txnDate) {
            validationResult.isValid = false
            validationResult.errors << 'Transaction date is required'
        }
        
        return validationResult
    }
    
    /**
     * Validate check business rules
     */
    private Map validateCheckBusinessRules(Map params, Map validationResult) {
        try {
            // Check clearing type validation
            def chkClearing = CheckDepositClearingType.get(params.checkType.toInteger())
            
            if (chkClearing?.isOnUs) {
                // On-us check validations
                validationResult = validateOnUsCheck(params, validationResult)
            } else {
                // Off-us check validations
                validationResult = validateOffUsCheck(params, validationResult)
            }
            
            // Check date validations
            if (params.checkDate) {
                Date checkDate = Date.parse('yyyy-MM-dd', params.checkDate)
                Date today = new Date()
                
                // Post-dated check validation (6 months limit)
                if (checkDate.after(today + 180)) {
                    validationResult.warnings << 'Check is post-dated beyond 6 months'
                }
                
                // Stale-dated check validation (6 months limit)
                if (checkDate.before(today - 180)) {
                    validationResult.isValid = false
                    validationResult.errors << 'Check is stale-dated (older than 6 months)'
                }
            }
            
        } catch (Exception e) {
            log.error("Error validating check business rules", e)
            validationResult.warnings << 'Error validating check business rules'
        }
        
        return validationResult
    }
    
    /**
     * Validate on-us check
     */
    private Map validateOnUsCheck(Map params, Map validationResult) {
        try {
            // Check for stop payment orders
            def spo = StopPaymentOrder.createCriteria().get {
                cheque {
                    eq('checkNo', params.checkNo)
                }
                eq('status', ConfigItemStatus.read(2))
            }
            
            if (spo) {
                validationResult.isValid = false
                validationResult.errors << "Check stopped [${params.checkNo}]"
            }
            
            // Validate account balance for on-us checks
            def sourceAccount = Deposit.findByAcctNo(params.acctNo)
            if (sourceAccount) {
                def checkAmount = Double.parseDouble(params.checkAmt.toString().replace(',', ''))
                if (sourceAccount.availableBalAmt < checkAmount) {
                    validationResult.isValid = false
                    validationResult.errors << "Insufficient funds in source account [${params.acctNo}]"
                }
            }
            
        } catch (Exception e) {
            log.error("Error validating on-us check", e)
            validationResult.warnings << 'Error validating on-us check'
        }
        
        return validationResult
    }
    
    /**
     * Validate off-us check
     */
    private Map validateOffUsCheck(Map params, Map validationResult) {
        try {
            // Off-us checks require additional verification
            if (!params.bank) {
                validationResult.isValid = false
                validationResult.errors << 'Bank information is required for off-us checks'
            }
            
            // Check amount limits for off-us checks
            def checkAmount = Double.parseDouble(params.checkAmt.toString().replace(',', ''))
            if (checkAmount > 100000) {
                validationResult.warnings << 'Large amount off-us check requires additional verification'
            }
            
        } catch (Exception e) {
            log.error("Error validating off-us check", e)
            validationResult.warnings << 'Error validating off-us check'
        }
        
        return validationResult
    }
    
    /**
     * Validate daily transaction limits
     */
    private Map validateDailyLimits(Double amount, Map validationResult) {
        try {
            // Get user's daily transaction total
            def user = UserMaster.get(session.user_id)
            def today = new Date()
            
            // This would query actual daily totals from database
            def dailyTotal = 0.0 // Placeholder
            def dailyLimit = 1000000.0 // Placeholder
            
            if ((dailyTotal + amount) > dailyLimit) {
                validationResult.warnings << 'Transaction exceeds daily limit'
            }
            
        } catch (Exception e) {
            log.error("Error validating daily limits", e)
        }
        
        return validationResult
    }
    
    /**
     * Validate single transaction limits
     */
    private Map validateSingleTransactionLimits(Double amount, Map validationResult) {
        try {
            def singleTxnLimit = 500000.0 // Placeholder
            
            if (amount > singleTxnLimit) {
                validationResult.warnings << 'Transaction exceeds single transaction limit'
            }
            
        } catch (Exception e) {
            log.error("Error validating single transaction limits", e)
        }
        
        return validationResult
    }
    
    /**
     * Validate account-specific limits
     */
    private Map validateAccountLimits(def accountId, Double amount, Map validationResult) {
        try {
            // Account-specific validation logic would go here
            // This is a placeholder implementation
            
        } catch (Exception e) {
            log.error("Error validating account limits", e)
        }
        
        return validationResult
    }
    
    /**
     * Validate transaction timing
     */
    private Map validateTransactionTiming(Map validationResult) {
        try {
            def currentTime = new Date()
            def hour = currentTime.hours
            
            // Business hours validation (8 AM to 6 PM)
            if (hour < 8 || hour > 18) {
                validationResult.warnings << 'Transaction outside normal business hours'
            }
            
        } catch (Exception e) {
            log.error("Error validating transaction timing", e)
        }
        
        return validationResult
    }
    
    /**
     * Validate user permissions
     */
    private Map validateUserPermissions(Map validationResult) {
        try {
            def user = UserMaster.get(session.user_id)
            
            if (!user || user.configItemStatus?.id != 2) {
                validationResult.isValid = false
                validationResult.errors << 'User not authorized for transactions'
            }
            
        } catch (Exception e) {
            log.error("Error validating user permissions", e)
        }
        
        return validationResult
    }
    
    /**
     * Validate additional policies
     */
    private Map validateAdditionalPolicies(Map params, Map validationResult) {
        try {
            // Additional policy validation logic would go here
            // This is a placeholder for extensibility
            
        } catch (Exception e) {
            log.error("Error validating additional policies", e)
        }
        
        return validationResult
    }
    
    /**
     * Validate account-specific rules
     */
    private Map validateAccountSpecificRules(Map params, Map validationResult) {
        try {
            // Account-specific business rule validation
            // This is a placeholder for extensibility
            
        } catch (Exception e) {
            log.error("Error validating account-specific rules", e)
        }
        
        return validationResult
    }
    
    /**
     * Validate deposit account eligibility
     */
    private Map validateDepositAccountEligibility(Deposit account, Map validationResult) {
        if (account.statusId == 5) { // Dormant
            validationResult.warnings << 'Account is dormant'
        }
        
        if (account.statusId == 4) { // Closed
            validationResult.isValid = false
            validationResult.errors << 'Account is closed'
        }
        
        return validationResult
    }
    
    /**
     * Validate loan account eligibility
     */
    private Map validateLoanAccountEligibility(Loan account, Map validationResult) {
        if (account.status?.id == 6) { // Closed
            validationResult.isValid = false
            validationResult.errors << 'Loan account is closed'
        }
        
        if (account.status?.id == 8) { // Written-off
            validationResult.isValid = false
            validationResult.errors << 'Loan account is written-off'
        }
        
        return validationResult
    }
}
