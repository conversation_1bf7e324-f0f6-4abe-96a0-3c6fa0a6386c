package org.icbs.tellering

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.cif.Customer
import org.icbs.tellering.TxnFile
import org.icbs.deposit.Cheque
import org.icbs.deposit.Chequebook
import org.icbs.admin.CheckDepositClearingType
import org.icbs.lov.CheckStatus
import org.icbs.lov.TxnCheckStatus
import org.icbs.lov.ConfigItemStatus
import org.icbs.transaction.TransactionProcessingService
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService

/**
 * REFACTORED: Check Transaction Controller
 * Extracted from TelleringController.groovy (7,319 lines)
 * Handles all check-related banking transactions with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class CheckTransactionController {
    
    TransactionProcessingService transactionProcessingService
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def policyService
    def userMasterService
    def glTransactionService
    
    static allowedMethods = [
        processCheckDeposit: "POST",
        processCheckClearance: "POST",
        issueChequebook: "POST",
        stopPaymentOrder: "POST"
    ]
    
    // =====================================================
    // CHECK DEPOSIT OPERATIONS
    // =====================================================
    
    /**
     * Process check deposit transaction
     */
    def processCheckDeposit() {
        try {
            // 1. Validate check deposit parameters
            Map validation = validateCheckDepositParams(params)
            if (!validation.isValid) {
                render(status: 400, contentType: 'application/json') {
                    [success: false, errors: validation.errors]
                }
                return
            }
            
            // 2. Validate check details
            Map checkValidation = validateCheckDetails(params)
            if (!checkValidation.isValid) {
                render(status: 400, contentType: 'application/json') {
                    [success: false, errors: checkValidation.errors]
                }
                return
            }
            
            // 3. Process check deposit
            Map result = transactionProcessingService.processCheckDeposit(params, session.user_id as Long)
            
            if (result.success) {
                // 4. Update check status
                updateCheckStatus(params.checkNumber, CheckStatus.get(2)) // Deposited
                
                flash.message = "Check deposit processed successfully|success"
                auditCheckTransaction('CHECK_DEPOSIT', result.txnFile, 'SUCCESS')
                
                render(contentType: 'application/json') {
                    [
                        success: true,
                        message: result.message,
                        transactionId: result.txnFile.id,
                        transactionRef: result.txnFile.txnRef,
                        holdInfo: result.holdInfo
                    ]
                }
            } else {
                auditCheckTransaction('CHECK_DEPOSIT', null, 'FAILURE', result.errors.join(', '))
                
                render(status: 400, contentType: 'application/json') {
                    [success: false, errors: result.errors]
                }
            }
            
        } catch (Exception e) {
            log.error("Error processing check deposit", e)
            auditCheckTransaction('CHECK_DEPOSIT', null, 'ERROR', e.message)
            
            render(status: 500, contentType: 'application/json') {
                [success: false, error: "Check deposit processing failed: ${e.message}"]
            }
        }
    }
    
    /**
     * Process check clearance
     */
    def processCheckClearance() {
        try {
            // 1. Validate clearance parameters
            Map validation = validateCheckClearanceParams(params)
            if (!validation.isValid) {
                render(status: 400, contentType: 'application/json') {
                    [success: false, errors: validation.errors]
                }
                return
            }
            
            // 2. Process clearance
            Map result = processCheckClearanceLogic(params)
            
            if (result.success) {
                flash.message = "Check clearance processed successfully|success"
                auditCheckTransaction('CHECK_CLEARANCE', null, 'SUCCESS')
                
                render(contentType: 'application/json') {
                    [
                        success: true,
                        message: result.message,
                        clearedChecks: result.clearedChecks,
                        returnedChecks: result.returnedChecks
                    ]
                }
            } else {
                auditCheckTransaction('CHECK_CLEARANCE', null, 'FAILURE', result.errors.join(', '))
                
                render(status: 400, contentType: 'application/json') {
                    [success: false, errors: result.errors]
                }
            }
            
        } catch (Exception e) {
            log.error("Error processing check clearance", e)
            auditCheckTransaction('CHECK_CLEARANCE', null, 'ERROR', e.message)
            
            render(status: 500, contentType: 'application/json') {
                [success: false, error: "Check clearance processing failed: ${e.message}"]
            }
        }
    }
    
    /**
     * Issue new chequebook
     */
    def issueChequebook() {
        try {
            // 1. Validate chequebook request
            Map validation = validateChequebookRequest(params)
            if (!validation.isValid) {
                render(status: 400, contentType: 'application/json') {
                    [success: false, errors: validation.errors]
                }
                return
            }
            
            // 2. Create chequebook
            Map result = createChequebook(params)
            
            if (result.success) {
                flash.message = "Chequebook issued successfully|success"
                auditCheckTransaction('CHEQUEBOOK_ISSUED', null, 'SUCCESS')
                
                render(contentType: 'application/json') {
                    [
                        success: true,
                        message: result.message,
                        chequebookId: result.chequebook.id,
                        startingNumber: result.chequebook.startingNumber,
                        endingNumber: result.chequebook.endingNumber
                    ]
                }
            } else {
                auditCheckTransaction('CHEQUEBOOK_ISSUED', null, 'FAILURE', result.errors.join(', '))
                
                render(status: 400, contentType: 'application/json') {
                    [success: false, errors: result.errors]
                }
            }
            
        } catch (Exception e) {
            log.error("Error issuing chequebook", e)
            auditCheckTransaction('CHEQUEBOOK_ISSUED', null, 'ERROR', e.message)
            
            render(status: 500, contentType: 'application/json') {
                [success: false, error: "Chequebook issuance failed: ${e.message}"]
            }
        }
    }
    
    /**
     * Process stop payment order
     */
    def stopPaymentOrder() {
        try {
            // 1. Validate stop payment request
            Map validation = validateStopPaymentRequest(params)
            if (!validation.isValid) {
                render(status: 400, contentType: 'application/json') {
                    [success: false, errors: validation.errors]
                }
                return
            }
            
            // 2. Process stop payment
            Map result = processStopPayment(params)
            
            if (result.success) {
                flash.message = "Stop payment order processed successfully|success"
                auditCheckTransaction('STOP_PAYMENT', null, 'SUCCESS')
                
                render(contentType: 'application/json') {
                    [
                        success: true,
                        message: result.message,
                        stopPaymentOrderId: result.stopPaymentOrder.id
                    ]
                }
            } else {
                auditCheckTransaction('STOP_PAYMENT', null, 'FAILURE', result.errors.join(', '))
                
                render(status: 400, contentType: 'application/json') {
                    [success: false, errors: result.errors]
                }
            }
            
        } catch (Exception e) {
            log.error("Error processing stop payment order", e)
            auditCheckTransaction('STOP_PAYMENT', null, 'ERROR', e.message)
            
            render(status: 500, contentType: 'application/json') {
                [success: false, error: "Stop payment processing failed: ${e.message}"]
            }
        }
    }
    
    // =====================================================
    // VALIDATION METHODS
    // =====================================================
    
    /**
     * Validate check deposit parameters
     */
    private Map validateCheckDepositParams(Map params) {
        Map result = [isValid: true, errors: []]
        
        // Amount validation
        if (!params.checkAmount || params.checkAmount <= 0) {
            result.isValid = false
            result.errors << "Check amount must be greater than zero"
        }
        
        // Check number validation
        if (!params.checkNumber) {
            result.isValid = false
            result.errors << "Check number is required"
        }
        
        // Drawer bank validation
        if (!params.drawerBank) {
            result.isValid = false
            result.errors << "Drawer bank is required"
        }
        
        // Account validation
        if (!params.depositAccountId) {
            result.isValid = false
            result.errors << "Deposit account is required"
        }
        
        return result
    }
    
    /**
     * Validate check details
     */
    private Map validateCheckDetails(Map params) {
        Map result = [isValid: true, errors: []]
        
        try {
            // Check if check number already exists
            def existingCheck = Cheque.findByCheckNo(params.checkNumber)
            if (existingCheck && existingCheck.status?.id != 1) { // Not unused
                result.isValid = false
                result.errors << "Check number already processed"
            }
            
            // Validate check date
            if (params.checkDate) {
                Date checkDate = Date.parse('yyyy-MM-dd', params.checkDate)
                Date today = new Date()
                
                // Check if post-dated (more than 6 months in future)
                if (checkDate.after(today + 180)) {
                    result.isValid = false
                    result.errors << "Check is post-dated beyond acceptable limit"
                }
                
                // Check if stale-dated (more than 6 months old)
                if (checkDate.before(today - 180)) {
                    result.isValid = false
                    result.errors << "Check is stale-dated"
                }
            }
            
        } catch (Exception e) {
            log.error("Error validating check details", e)
            result.isValid = false
            result.errors << "Check validation failed"
        }
        
        return result
    }
    
    /**
     * Validate check clearance parameters
     */
    private Map validateCheckClearanceParams(Map params) {
        Map result = [isValid: true, errors: []]
        
        if (!params.clearingDate) {
            result.isValid = false
            result.errors << "Clearing date is required"
        }
        
        if (!params.clearingType) {
            result.isValid = false
            result.errors << "Clearing type is required"
        }
        
        return result
    }
    
    /**
     * Validate chequebook request
     */
    private Map validateChequebookRequest(Map params) {
        Map result = [isValid: true, errors: []]
        
        if (!params.accountId) {
            result.isValid = false
            result.errors << "Account is required"
        }
        
        if (!params.numberOfLeaves || params.numberOfLeaves <= 0) {
            result.isValid = false
            result.errors << "Number of leaves must be greater than zero"
        }
        
        // Check if account has pending chequebook request
        def pendingChequebook = Chequebook.findByDepositAndStatus(
            org.icbs.deposit.Deposit.get(params.accountId),
            ConfigItemStatus.get(1) // Pending
        )
        
        if (pendingChequebook) {
            result.isValid = false
            result.errors << "Account has pending chequebook request"
        }
        
        return result
    }
    
    /**
     * Validate stop payment request
     */
    private Map validateStopPaymentRequest(Map params) {
        Map result = [isValid: true, errors: []]
        
        if (!params.checkNumber) {
            result.isValid = false
            result.errors << "Check number is required"
        }
        
        if (!params.accountId) {
            result.isValid = false
            result.errors << "Account is required"
        }
        
        if (!params.reason) {
            result.isValid = false
            result.errors << "Reason for stop payment is required"
        }
        
        return result
    }
    
    // =====================================================
    // BUSINESS LOGIC METHODS
    // =====================================================
    
    /**
     * Process check clearance logic
     */
    private Map processCheckClearanceLogic(Map params) {
        Map result = [success: true, errors: [], clearedChecks: [], returnedChecks: []]
        
        try {
            Date clearingDate = Date.parse('yyyy-MM-dd', params.clearingDate)
            CheckDepositClearingType clearingType = CheckDepositClearingType.get(params.clearingType)
            
            // Get checks pending clearance
            def pendingChecks = Cheque.createCriteria().list {
                eq('status', CheckStatus.get(2)) // Deposited
                le('depositDate', clearingDate - clearingType.clearingDays)
            }
            
            pendingChecks.each { check ->
                if (shouldClearCheck(check)) {
                    clearCheck(check)
                    result.clearedChecks << check.id
                } else {
                    returnCheck(check, "Insufficient funds")
                    result.returnedChecks << check.id
                }
            }
            
        } catch (Exception e) {
            log.error("Error in check clearance logic", e)
            result.success = false
            result.errors << "Check clearance processing failed"
        }
        
        return result
    }
    
    /**
     * Create new chequebook
     */
    private Map createChequebook(Map params) {
        Map result = [success: true, errors: []]
        
        try {
            def deposit = org.icbs.deposit.Deposit.get(params.accountId)
            int numberOfLeaves = params.numberOfLeaves as Integer
            
            // Generate starting number
            long startingNumber = generateNextCheckNumber()
            long endingNumber = startingNumber + numberOfLeaves - 1
            
            // Create chequebook
            Chequebook chequebook = new Chequebook(
                deposit: deposit,
                startingNumber: startingNumber,
                endingNumber: endingNumber,
                numberOfLeaves: numberOfLeaves,
                issuedDate: new Date(),
                status: ConfigItemStatus.get(2), // Active
                issuedBy: session.user_id
            )
            
            if (chequebook.save(flush: true)) {
                // Create individual cheques
                createIndividualCheques(chequebook)
                result.chequebook = chequebook
            } else {
                result.success = false
                result.errors = extractValidationErrors(chequebook)
            }
            
        } catch (Exception e) {
            log.error("Error creating chequebook", e)
            result.success = false
            result.errors << "Chequebook creation failed"
        }
        
        return result
    }
    
    /**
     * Process stop payment
     */
    private Map processStopPayment(Map params) {
        Map result = [success: true, errors: []]
        
        try {
            def check = Cheque.findByCheckNo(params.checkNumber)
            if (!check) {
                result.success = false
                result.errors << "Check not found"
                return result
            }
            
            if (check.status?.id != 1) { // Not unused
                result.success = false
                result.errors << "Check cannot be stopped - already processed"
                return result
            }
            
            // Create stop payment order
            def stopPaymentOrder = new org.icbs.deposit.StopPaymentOrder(
                deposit: org.icbs.deposit.Deposit.get(params.accountId),
                chequeNo: params.checkNumber,
                reason: params.reason,
                requestDate: new Date(),
                status: ConfigItemStatus.get(2) // Active
            )
            
            if (stopPaymentOrder.save(flush: true)) {
                // Update check status
                check.status = CheckStatus.get(4) // Stopped
                check.save(flush: true)
                
                result.stopPaymentOrder = stopPaymentOrder
            } else {
                result.success = false
                result.errors = extractValidationErrors(stopPaymentOrder)
            }
            
        } catch (Exception e) {
            log.error("Error processing stop payment", e)
            result.success = false
            result.errors << "Stop payment processing failed"
        }
        
        return result
    }
    
    // =====================================================
    // UTILITY METHODS
    // =====================================================
    
    /**
     * Update check status
     */
    private void updateCheckStatus(String checkNumber, CheckStatus status) {
        try {
            def check = Cheque.findByCheckNo(checkNumber)
            if (check) {
                check.status = status
                check.save(flush: true)
            }
        } catch (Exception e) {
            log.error("Error updating check status", e)
        }
    }
    
    /**
     * Determine if check should be cleared
     */
    private boolean shouldClearCheck(Cheque check) {
        // Implement business logic for check clearance
        // This could include balance checks, fraud detection, etc.
        return true // Simplified for now
    }
    
    /**
     * Clear check
     */
    private void clearCheck(Cheque check) {
        check.status = CheckStatus.get(3) // Cleared
        check.clearanceDate = new Date()
        check.save(flush: true)
    }
    
    /**
     * Return check
     */
    private void returnCheck(Cheque check, String reason) {
        check.status = CheckStatus.get(5) // Returned
        check.returnReason = reason
        check.returnDate = new Date()
        check.save(flush: true)
    }
    
    /**
     * Generate next check number
     */
    private long generateNextCheckNumber() {
        def lastChequebook = Chequebook.createCriteria().get {
            projections {
                max('endingNumber')
            }
        }
        
        return (lastChequebook ?: 1000000) + 1
    }
    
    /**
     * Create individual cheques for chequebook
     */
    private void createIndividualCheques(Chequebook chequebook) {
        for (long i = chequebook.startingNumber; i <= chequebook.endingNumber; i++) {
            Cheque cheque = new Cheque(
                chequebook: chequebook,
                checkNo: i.toString(),
                status: CheckStatus.get(1), // Unused
                deposit: chequebook.deposit
            )
            cheque.save(flush: true)
        }
    }
    
    /**
     * Audit check transaction
     */
    private void auditCheckTransaction(String transactionType, TxnFile txnFile, String result, String errorMessage = null) {
        try {
            securityAuditService.logSecurityEvent([
                eventType: 'CHECK_TRANSACTION',
                eventDescription: "${transactionType} transaction processed",
                transactionType: transactionType,
                transactionId: txnFile?.id,
                transactionRef: txnFile?.txnRef,
                amount: txnFile?.txnAmt,
                customerId: txnFile?.beneficiary?.id,
                userId: session.user_id,
                result: result,
                errorMessage: errorMessage
            ])
        } catch (Exception e) {
            log.error("Error auditing check transaction", e)
        }
    }
    
    /**
     * Extract validation errors
     */
    private List<String> extractValidationErrors(def domainInstance) {
        List<String> errors = []
        domainInstance.errors.allErrors.each { error ->
            errors << error.defaultMessage
        }
        return errors
    }
}
