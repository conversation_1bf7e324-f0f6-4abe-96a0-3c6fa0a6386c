package org.icbs.tellering

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.TxnTemplate
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnCheck
import org.icbs.tellering.TxnTellerBalance
import org.icbs.deposit.Deposit
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.CheckStatus
import org.icbs.lov.TxnType

/**
 * TellerCheckTransactionController - <PERSON>les check processing operations
 * 
 * This controller manages check processing operations including:
 * - Check deposit transactions
 * - Check encashment transactions
 * - COCI (Checks on Collection Items) operations
 * - Check clearing operations
 * - Check status management
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class TellerCheckTransactionController {
    
    // Service Dependencies
    def userMasterService
    def glTransactionService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        saveTellerCheckDepositTxn: "POST",
        saveTellerCheckEncashmentTxn: "POST",
        addCheckAjax: "POST",
        removeCheckAjax: "DELETE",
        validateCheckAjax: "POST"
    ]

    /**
     * Create teller check deposit transaction
     */
    def createTellerCheckDepositTxn() {
        session["map"] = 'deposit'
        session["checks"] = []
        
        def user = UserMaster.get(session.user_id)
        def txnFileInstance = new TxnFile()
        def txnCheckDepositInstance = new TxnDepositAcctLedger()
        
        render(view:'/tellering/txnCheckDeposit/create', model: [
            txnCheckDepositInstance: txnCheckDepositInstance
        ])
    }

    /**
     * Save teller check deposit transaction
     */
    @Transactional
    def saveTellerCheckDepositTxn(TxnDepositAcctLedger tc, TxnFile tf) {
        println params // Error check
        
        def user = UserMaster.get(session.user_id)
        def branch = Branch.get(user.branchId)
        def depositAccount = Deposit.get(params.acct)
        
        // Validate transaction
        if (!depositAccount) {
            flash.message = 'Invalid deposit account|error|alert'
            render(view:'/tellering/txnCheckDeposit/create', model: [
                txnCheckDepositInstance: tc
            ])
            return
        }
        
        if (!session["checks"] || session["checks"].size() == 0) {
            flash.message = 'No checks added for deposit|error|alert'
            render(view:'/tellering/txnCheckDeposit/create', model: [
                txnCheckDepositInstance: tc
            ])
            return
        }

        // Calculate total check amount
        def totalCheckAmount = 0
        session["checks"].each { check ->
            totalCheckAmount += check.amount
        }
        
        tc.creditAmt = totalCheckAmount

        // Check account status
        if (depositAccount.status.id != 4) { // Not active
            flash.message = 'Account is not active|error|alert'
            render(view:'/tellering/txnCheckDeposit/create', model: [
                txnCheckDepositInstance: tc
            ])
            return
        }

        // Create transaction file
        tf.txnCode = "CHDP"
        tf.txnDescription = "Check Deposit"
        tf.txnDate = branch.runDate
        tf.currency = depositAccount.currency
        tf.txnAmt = tc.creditAmt
        tf.txnRef = params.txnRef ?: "Check Deposit"
        tf.status = ConfigItemStatus.get(2)
        tf.branch = branch
        tf.acctNo = depositAccount.accountNo
        tf.txnTimestamp = new Date().toTimestamp()
        tf.txnParticulars = params.txnParticulars ?: "Check deposit to account ${depositAccount.accountNo}"
        tf.txnType = TxnType.get(3) // Check deposit type
        tf.txnTemplate = TxnTemplate.get(3)
        tf.user = user
        tf.beneficiary = depositAccount.customer
        tf.save(flush: true, failOnError: true)

        // Create deposit ledger entry
        tc.txnFile = tf
        tc.acct = depositAccount
        tc.txnDate = tf.txnDate
        tc.creditAmt = tc.creditAmt
        tc.debitAmt = 0
        tc.bal = depositAccount.ledgerBalAmt + tc.creditAmt
        tc.passbookBal = 0 // Will be updated when passbook is printed
        tc.txnRef = tf.txnRef
        tc.save(flush: true, failOnError: true)

        // Save checks
        session["checks"].each { checkData ->
            def txnCheck = new TxnCheck(
                txnFile: tf,
                checkNo: checkData.checkNo,
                checkDate: checkData.checkDate,
                amount: checkData.amount,
                draweeBank: checkData.draweeBank,
                drawer: checkData.drawer,
                endorser: checkData.endorser,
                status: CheckStatus.get(1), // Pending clearing
                dateReceived: tf.txnDate
            )
            txnCheck.save(flush: true, failOnError: true)
        }

        // Update deposit account balance (subject to clearing)
        depositAccount.ledgerBalAmt += tc.creditAmt
        depositAccount.save(flush: true)

        // Update teller check balance
        def tellerBalance = TxnTellerBalance.findByUserAndCurrency(user, tf.currency)
        if (tellerBalance) {
            tellerBalance.checkInAmt += tc.creditAmt
            tellerBalance.save(flush: true)
        }

        // Update teller balance status
        userMasterService.updateTellerBalanceStatus(false)
        
        // Generate GL transactions
        glTransactionService.saveTxnBreakdown(tf.id)

        def description = "Check deposit processed - Account: ${depositAccount.accountNo} Amount: ${tc.creditAmt} Checks: ${session['checks'].size()}"
        auditLogService.insert('120', 'TLR02100', description, 'TellerCheck', null, null, null, tf.id)
        
        session["transactionFileId"] = tf.id
        session["jrxmlTcId"] = tc.id
        session["type"] = depositAccount.type.id
        
        // Clear checks from session
        session["checks"] = []
        
        redirect(controller: "tellerCore", action: "txnSuccess")
    }

    /**
     * Create teller check encashment transaction
     */
    def createTellerCheckEncashmentTxn() {
        session["map"] = 'check'
        session["checks"] = []
        
        def user = UserMaster.get(session.user_id)
        
        if (user.cash) {
            def txnFileInstance = new TxnFile()
            def txnCheckEncashmentInstance = new TxnDepositAcctLedger()
            
            render(view:'/tellering/txnCheckEncashment/create', model: [
                txnCheckEncashmentInstance: txnCheckEncashmentInstance
            ])       
        } else {
            flash.message = 'Error! No cash account defined|error|alert'
            redirect(action: "index", controller: "tellerCore")            
        }
    }

    /**
     * Save teller check encashment transaction
     */
    @Transactional
    def saveTellerCheckEncashmentTxn(TxnDepositAcctLedger tc, TxnFile tf) {
        println params // Error check
        
        def user = UserMaster.get(session.user_id)
        def branch = Branch.get(user.branchId)
        
        // Validate transaction
        if (!session["checks"] || session["checks"].size() == 0) {
            flash.message = 'No checks added for encashment|error|alert'
            render(view:'/tellering/txnCheckEncashment/create', model: [
                txnCheckEncashmentInstance: tc
            ])
            return
        }

        // Calculate total check amount
        def totalCheckAmount = 0
        session["checks"].each { check ->
            totalCheckAmount += check.amount
        }
        
        tc.debitAmt = totalCheckAmount

        // Create transaction file
        tf.txnCode = "CHEN"
        tf.txnDescription = "Check Encashment"
        tf.txnDate = branch.runDate
        tf.currency = user.cash.currency
        tf.txnAmt = tc.debitAmt
        tf.txnRef = params.txnRef ?: "Check Encashment"
        tf.status = ConfigItemStatus.get(2)
        tf.branch = branch
        tf.txnTimestamp = new Date().toTimestamp()
        tf.txnParticulars = params.txnParticulars ?: "Check encashment transaction"
        tf.txnType = TxnType.get(4) // Check encashment type
        tf.txnTemplate = TxnTemplate.get(4)
        tf.user = user
        tf.save(flush: true, failOnError: true)

        // Create ledger entry
        tc.txnFile = tf
        tc.txnDate = tf.txnDate
        tc.debitAmt = tc.debitAmt
        tc.creditAmt = 0
        tc.txnRef = tf.txnRef
        tc.save(flush: true, failOnError: true)

        // Save checks
        session["checks"].each { checkData ->
            def txnCheck = new TxnCheck(
                txnFile: tf,
                checkNo: checkData.checkNo,
                checkDate: checkData.checkDate,
                amount: checkData.amount,
                draweeBank: checkData.draweeBank,
                drawer: checkData.drawer,
                endorser: checkData.endorser,
                status: CheckStatus.get(1), // Pending clearing
                dateReceived: tf.txnDate
            )
            txnCheck.save(flush: true, failOnError: true)
        }

        // Update teller cash balance
        def tellerBalance = TxnTellerBalance.findByUserAndCurrency(user, tf.currency)
        if (tellerBalance) {
            tellerBalance.cashOutAmt += tc.debitAmt
            tellerBalance.save(flush: true)
        }

        // Update teller balance status
        userMasterService.updateTellerBalanceStatus(false)
        
        // Generate GL transactions
        glTransactionService.saveTxnBreakdown(tf.id)

        def description = "Check encashment processed - Amount: ${tc.debitAmt} Checks: ${session['checks'].size()}"
        auditLogService.insert('120', 'TLR02200', description, 'TellerCheck', null, null, null, tf.id)
        
        session["transactionFileId"] = tf.id
        
        // Clear checks from session
        session["checks"] = []
        
        redirect(controller: "tellerCore", action: "txnSuccess")
    }

    /**
     * Add check via AJAX
     */
    @Transactional
    def addCheckAjax() {
        def json = request.JSON
        
        if (!session["checks"]) {
            session["checks"] = []
        }
        
        def check = [
            checkNo: json.checkNo,
            checkDate: json.checkDate ? Date.parse("MM/dd/yyyy", json.checkDate) : null,
            amount: json.amount?.toDouble() ?: 0,
            draweeBank: json.draweeBank,
            drawer: json.drawer,
            endorser: json.endorser,
            remarks: json.remarks
        ]
        
        // Validate check data
        if (!check.checkNo || check.amount <= 0) {
            render([success: false, message: "Invalid check data"] as JSON)
            return
        }
        
        // Check for duplicate check number
        def isDuplicate = session["checks"].any { it.checkNo == check.checkNo }
        if (isDuplicate) {
            render([success: false, message: "Check number already exists"] as JSON)
            return
        }
        
        session["checks"].add(check)
        
        render([
            success: true, 
            message: "Check added successfully",
            checkCount: session["checks"].size(),
            totalAmount: session["checks"].sum { it.amount }
        ] as JSON)
        return
    }

    /**
     * Remove check via AJAX
     */
    @Transactional
    def removeCheckAjax() {
        def json = request.JSON
        def checkIndex = json.checkIndex?.toInteger()
        
        if (checkIndex != null && session["checks"] && checkIndex < session["checks"].size()) {
            session["checks"].remove(checkIndex)
            
            render([
                success: true,
                message: "Check removed successfully",
                checkCount: session["checks"].size(),
                totalAmount: session["checks"].sum { it.amount ?: 0 }
            ] as JSON)
        } else {
            render([success: false, message: "Invalid check index"] as JSON)
        }
        return
    }

    /**
     * Validate check via AJAX
     */
    def validateCheckAjax() {
        def json = request.JSON
        def checkNo = json.checkNo
        def draweeBank = json.draweeBank
        def amount = json.amount?.toDouble() ?: 0
        
        // Perform check validation logic here
        def validationResult = [
            valid: true,
            warnings: [],
            errors: []
        ]
        
        // Check if check number format is valid
        if (!checkNo || checkNo.length() < 6) {
            validationResult.errors.add("Invalid check number format")
            validationResult.valid = false
        }
        
        // Check amount limits
        if (amount > 100000) { // Example limit
            validationResult.warnings.add("Large amount - requires supervisor approval")
        }
        
        // Check if drawee bank is in our clearing network
        if (draweeBank && !isInClearingNetwork(draweeBank)) {
            validationResult.warnings.add("Drawee bank not in clearing network - extended clearing time")
        }
        
        render(validationResult as JSON)
        return
    }

    /**
     * Get check details template
     */
    def getCheckDetailsTemplate() {
        render(template: '/tellering/checks/checkForm') as JSON
        return
    }

    /**
     * Get checks list template
     */
    def getChecksListTemplate() {
        def checks = session["checks"] ?: []
        def totalAmount = checks.sum { it.amount ?: 0 }
        
        render(template: '/tellering/checks/checksList', model: [
            checks: checks,
            totalAmount: totalAmount
        ]) as JSON
        return
    }

    // Helper methods
    private def isInClearingNetwork(String bankName) {
        // Check if bank is in our clearing network
        // This would typically check against a database of clearing banks
        def clearingBanks = [
            "BPI", "BDO", "METROBANK", "PNB", "UNIONBANK", 
            "SECURITY BANK", "RCBC", "CHINABANK", "PSB"
        ]
        
        return clearingBanks.any { bankName.toUpperCase().contains(it) }
    }
}
