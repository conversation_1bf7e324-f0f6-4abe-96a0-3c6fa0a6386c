package org.icbs.tellering

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.TxnTemplate
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnLoanPaymentDetails
import org.icbs.tellering.TxnTellerBalance
import org.icbs.loans.Loan
import org.icbs.loans.LoanInstallment
import org.icbs.loans.LoanLedger
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanInstallmentStatus
import org.icbs.lov.TxnType

/**
 * TellerLoanTransactionController - Handles teller loan operations
 * 
 * This controller manages teller loan operations including:
 * - Loan cash repayment transactions
 * - Loan check repayment transactions
 * - Loan specified repayment transactions
 * - Loan proceeds disbursement
 * - Loan payment processing
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class TellerLoanTransactionController {
    
    // Service Dependencies
    def loanService
    def userMasterService
    def glTransactionService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        saveTellerLoanCashRepaymentTxn: "POST",
        saveTellerLoanCheckRepaymentTxn: "POST",
        saveTellerLoanCashSpecifiedRepaymentTxn: "POST",
        saveTellerLoanCheckSpecifiedRepaymentTxn: "POST",
        saveTellerLoanProceedsDisbursementTxn: "POST"
    ]

    /**
     * Create teller loan cash repayment transaction
     */
    def createTellerLoanCashRepaymentTxn() {
        def user = UserMaster.get(session.user_id)
        
        if (user.cash) {
            def txnFileInstance = new TxnFile()        
            def txnLoanCashRepaymentInstance = new TxnLoanPaymentDetails()                
            
            render(view:'/tellering/txnLoanCashRepayment/create', model: [
                txnLoanCashRepaymentInstance: txnLoanCashRepaymentInstance
            ])       
        } else {
            flash.message = 'Error! No cash account defined|error|alert'
            redirect(action: "index", controller: "tellerCore")            
        }        
    }

    /**
     * Save teller loan cash repayment transaction
     */
    @Transactional
    def saveTellerLoanCashRepaymentTxn(TxnLoanPaymentDetails tc, TxnFile tf) {
        println "TEST PARAMS : " + params // Error check
        
        def user = UserMaster.get(session.user_id)
        def branch = Branch.get(user.branchId)
        def loanAccount = Loan.get(params.acct)
        
        // Validate transaction
        if (!loanAccount) {
            flash.message = 'Invalid loan account|error|alert'
            render(view:'/tellering/txnLoanCashRepayment/create', model: [
                txnLoanCashRepaymentInstance: tc
            ])
            return
        }
        
        if (!tc.totalAmtPaid || tc.totalAmtPaid <= 0) {
            flash.message = 'Invalid payment amount|error|alert'
            render(view:'/tellering/txnLoanCashRepayment/create', model: [
                txnLoanCashRepaymentInstance: tc
            ])
            return
        }

        // Check loan status
        if (loanAccount.status.id != 4) { // Not active
            flash.message = 'Loan account is not active|error|alert'
            render(view:'/tellering/txnLoanCashRepayment/create', model: [
                txnLoanCashRepaymentInstance: tc
            ])
            return
        }

        // Create transaction file
        tf.txnCode = "LCRP"
        tf.txnDescription = "Loan Cash Repayment"
        tf.txnDate = branch.runDate
        tf.currency = loanAccount.currency
        tf.txnAmt = tc.totalAmtPaid
        tf.txnRef = params.txnRef ?: "Loan Cash Repayment"
        tf.status = ConfigItemStatus.get(2)
        tf.branch = branch
        tf.acctNo = loanAccount.accountNo
        tf.txnTimestamp = new Date().toTimestamp()
        tf.txnParticulars = params.txnParticulars ?: "Cash repayment for loan ${loanAccount.accountNo}"
        tf.txnType = TxnType.get(58) // Loan cash repayment type
        tf.txnTemplate = TxnTemplate.get(58)
        tf.user = user
        tf.beneficiary = loanAccount.customer
        tf.save(flush: true, failOnError: true)

        // Create loan payment details
        tc.txnFile = tf
        tc.acctId = loanAccount.id
        tc.txnDate = tf.txnDate
        tc.principalAmtPaid = params.principalAmtPaid?.toDouble() ?: 0
        tc.interestAmtPaid = params.interestAmtPaid?.toDouble() ?: 0
        tc.serviceChargeAmtPaid = params.serviceChargeAmtPaid?.toDouble() ?: 0
        tc.penaltyAmtPaid = params.penaltyAmtPaid?.toDouble() ?: 0
        tc.totalAmtPaid = tc.principalAmtPaid + tc.interestAmtPaid + tc.serviceChargeAmtPaid + tc.penaltyAmtPaid
        tc.save(flush: true, failOnError: true)

        // Create loan ledger entry
        def loanLedger = new LoanLedger(
            loan: loanAccount,
            txnFile: tf,
            txnDate: tf.txnDate,
            txnTemplate: tf.txnTemplate,
            principalDebit: tc.principalAmtPaid,
            interestDebit: tc.interestAmtPaid,
            serviceChargeDebit: tc.serviceChargeAmtPaid,
            penaltyDebit: tc.penaltyAmtPaid,
            txnRef: tf.txnRef,
            principalBalance: loanAccount.balanceAmount - tc.principalAmtPaid
        )
        loanLedger.save(flush: true, failOnError: true)

        // Update loan account balances
        loanAccount.balanceAmount -= tc.principalAmtPaid
        loanAccount.interestBalanceAmount -= tc.interestAmtPaid
        loanAccount.serviceChargeBalanceAmount -= tc.serviceChargeAmtPaid
        loanAccount.penaltyBalanceAmount -= tc.penaltyAmtPaid
        
        // Check if loan is fully paid
        if (loanAccount.balanceAmount <= 0) {
            loanAccount.status = LoanAcctStatus.get(5) // Closed
            loanAccount.dateClosed = tf.txnDate
        }
        
        loanAccount.save(flush: true)

        // Update installments if applicable
        updateLoanInstallments(loanAccount, tc)

        // Update teller cash balance
        def tellerBalance = TxnTellerBalance.findByUserAndCurrency(user, tf.currency)
        if (tellerBalance) {
            tellerBalance.cashInAmt += tc.totalAmtPaid
            tellerBalance.save(flush: true)
        }

        // Update teller balance status
        userMasterService.updateTellerBalanceStatus(false)
        
        // Generate GL transactions
        glTransactionService.saveTxnBreakdown(tf.id)

        def description = "Loan cash repayment processed - Account: ${loanAccount.accountNo} Amount: ${tc.totalAmtPaid}"
        auditLogService.insert('120', 'TLR00700', description, 'TellerLoan', null, null, null, tf.id)
        
        session["transactionFileId"] = tf.id
        redirect(controller: "tellerCore", action: "txnSuccess")
    }

    /**
     * Create teller loan proceeds disbursement transaction
     */
    def createTellerLoanProceedsDisbursementTxn() {
        def user = UserMaster.get(session.user_id)
        
        if (user.cash) {
            def txnFileInstance = new TxnFile()        
            def txnLoanDisbursementInstance = new TxnLoanPaymentDetails()                
            
            render(view:'/tellering/txnLoanDisbursement/create', model: [
                txnLoanDisbursementInstance: txnLoanDisbursementInstance
            ])       
        } else {
            flash.message = 'Error! No cash account defined|error|alert'
            redirect(action: "index", controller: "tellerCore")            
        }        
    }

    /**
     * Save teller loan proceeds disbursement transaction
     */
    @Transactional
    def saveTellerLoanProceedsDisbursementTxn(TxnLoanPaymentDetails tc, TxnFile tf) {
        println "PARAMS TEST: " + params // Error check
        
        def user = UserMaster.get(session.user_id)
        def branch = Branch.get(user.branchId)
        def loanAccount = Loan.get(params.acct)
        
        // Validate transaction
        if (!loanAccount) {
            flash.message = 'Invalid loan account|error|alert'
            render(view:'/tellering/txnLoanDisbursement/create', model: [
                txnLoanDisbursementInstance: tc
            ])
            return
        }
        
        if (!tc.totalAmtPaid || tc.totalAmtPaid <= 0) {
            flash.message = 'Invalid disbursement amount|error|alert'
            render(view:'/tellering/txnLoanDisbursement/create', model: [
                txnLoanDisbursementInstance: tc
            ])
            return
        }

        // Check loan status - should be approved but not yet disbursed
        if (loanAccount.status.id != 6) { // Not approved
            flash.message = 'Loan is not approved for disbursement|error|alert'
            render(view:'/tellering/txnLoanDisbursement/create', model: [
                txnLoanDisbursementInstance: tc
            ])
            return
        }

        // Create transaction file
        tf.txnCode = "LDIS"
        tf.txnDescription = "Loan Proceeds Disbursement"
        tf.txnDate = branch.runDate
        tf.currency = loanAccount.currency
        tf.txnAmt = tc.totalAmtPaid
        tf.txnRef = params.txnRef ?: "Loan Disbursement"
        tf.status = ConfigItemStatus.get(2)
        tf.branch = branch
        tf.acctNo = loanAccount.accountNo
        tf.txnTimestamp = new Date().toTimestamp()
        tf.txnParticulars = params.txnParticulars ?: "Loan proceeds disbursement for ${loanAccount.accountNo}"
        tf.txnType = TxnType.get(56) // Loan disbursement type
        tf.txnTemplate = TxnTemplate.get(56)
        tf.user = user
        tf.beneficiary = loanAccount.customer
        tf.save(flush: true, failOnError: true)

        // Create loan payment details (for disbursement tracking)
        tc.txnFile = tf
        tc.acctId = loanAccount.id
        tc.txnDate = tf.txnDate
        tc.totalAmtPaid = tc.totalAmtPaid // Amount disbursed
        tc.save(flush: true, failOnError: true)

        // Create loan ledger entry
        def loanLedger = new LoanLedger(
            loan: loanAccount,
            txnFile: tf,
            txnDate: tf.txnDate,
            txnTemplate: tf.txnTemplate,
            principalCredit: tc.totalAmtPaid,
            txnRef: tf.txnRef,
            principalBalance: loanAccount.balanceAmount + tc.totalAmtPaid
        )
        loanLedger.save(flush: true, failOnError: true)

        // Update loan account
        loanAccount.balanceAmount = loanAccount.grantedAmount // Set to full loan amount
        loanAccount.status = LoanAcctStatus.get(4) // Active
        loanAccount.dateReleased = tf.txnDate
        loanAccount.save(flush: true)

        // Update teller cash balance
        def tellerBalance = TxnTellerBalance.findByUserAndCurrency(user, tf.currency)
        if (tellerBalance) {
            tellerBalance.cashOutAmt += tc.totalAmtPaid
            tellerBalance.save(flush: true)
        }

        // Update teller balance status
        userMasterService.updateTellerBalanceStatus(false)
        
        // Generate GL transactions
        glTransactionService.saveTxnBreakdown(tf.id)

        def description = "Loan disbursement processed - Account: ${loanAccount.accountNo} Amount: ${tc.totalAmtPaid}"
        auditLogService.insert('120', 'TLR00800', description, 'TellerLoan', null, null, null, tf.id)
        
        session["transactionFileId"] = tf.id
        redirect(controller: "tellerCore", action: "txnSuccess")
    }

    /**
     * Get loan account details
     */
    def getLoanAccountDetails() {
        def accountNo = params.accountNo
        if (!accountNo) {
            render([error: "Account number required"] as JSON)
            return
        }

        def loanAccount = Loan.findByAccountNo(accountNo)
        if (!loanAccount) {
            render([error: "Loan account not found"] as JSON)
            return
        }

        render([
            accountNo: loanAccount.accountNo,
            customerName: loanAccount.customer.displayName,
            productName: loanAccount.product.name,
            currency: loanAccount.currency.code,
            grantedAmount: loanAccount.grantedAmount,
            balanceAmount: loanAccount.balanceAmount,
            interestBalance: loanAccount.interestBalanceAmount,
            serviceChargeBalance: loanAccount.serviceChargeBalanceAmount,
            penaltyBalance: loanAccount.penaltyBalanceAmount,
            status: loanAccount.status.description,
            interestRate: loanAccount.interestRate,
            maturityDate: loanAccount.maturityDate?.format("MM/dd/yyyy"),
            nextDueDate: getNextDueDate(loanAccount)
        ] as JSON)
        return
    }

    // Helper methods
    private def updateLoanInstallments(Loan loanAccount, TxnLoanPaymentDetails payment) {
        // Update installments based on payment
        def unpaidInstallments = LoanInstallment.findAllByLoanAndStatus(
            loanAccount, 
            LoanInstallmentStatus.get(1), // Unpaid
            [sort: "installmentDate", order: "asc"]
        )
        
        def remainingPayment = payment.totalAmtPaid
        
        for (installment in unpaidInstallments) {
            if (remainingPayment <= 0) break
            
            def installmentTotal = installment.principalAmount + installment.interestAmount + installment.serviceChargeAmount
            
            if (remainingPayment >= installmentTotal) {
                // Fully pay this installment
                installment.status = LoanInstallmentStatus.get(2) // Paid
                installment.datePaid = payment.txnDate
                installment.save(flush: true)
                remainingPayment -= installmentTotal
            } else {
                // Partial payment - create partial payment record
                break
            }
        }
    }

    private def getNextDueDate(Loan loanAccount) {
        def nextInstallment = LoanInstallment.findByLoanAndStatus(
            loanAccount,
            LoanInstallmentStatus.get(1), // Unpaid
            [sort: "installmentDate", order: "asc"]
        )
        
        return nextInstallment?.installmentDate?.format("MM/dd/yyyy")
    }
}
