package org.icbs.tellering

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.TxnTemplate
import org.icbs.admin.Currency
import org.icbs.admin.CurrencyDetail
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnForex
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.TxnType

/**
 * TellerForexController - Handles foreign exchange operations
 * 
 * This controller manages foreign exchange operations including:
 * - Currency exchange transactions
 * - Foreign currency buying and selling
 * - Exchange rate management
 * - Multi-currency operations
 * - Forex transaction processing
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class TellerForexController {
    
    // Service Dependencies
    def userMasterService
    def glTransactionService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        saveTellerForexTxn: "POST",
        getCurrencyOnTemplate: "GET"
    ]

    /**
     * Create teller forex transaction
     */
    def createTellerForexTxn() {
        def user = UserMaster.get(session.user_id)
        
        if (user.cash) {
            def txnFileInstance = new TxnFile()
            def txnForexInstance = new TxnForex()
            
            // Get available currencies
            def currencies = Currency.findAllByStatus(ConfigItemStatus.get(2))
            
            render(view:'/tellering/txnForex/create', model: [
                txnForexInstance: txnForexInstance,
                currencies: currencies
            ])       
        } else {
            flash.message = 'Error! No cash account defined|error|alert'
            redirect(action: "index", controller: "tellerCore")            
        }
    }

    /**
     * Save teller forex transaction
     */
    @Transactional
    def saveTellerForexTxn(TxnForex tc, TxnFile tf) {
        println params // Error check
        
        def user = UserMaster.get(session.user_id)
        def branch = Branch.get(user.branchId)
        
        // Validate transaction
        if (!tc.fromCurrency || !tc.toCurrency) {
            flash.message = 'Please select both currencies|error|alert'
            render(view:'/tellering/txnForex/create', model: [
                txnForexInstance: tc
            ])
            return
        }
        
        if (!tc.fromAmount || tc.fromAmount <= 0) {
            flash.message = 'Invalid exchange amount|error|alert'
            render(view:'/tellering/txnForex/create', model: [
                txnForexInstance: tc
            ])
            return
        }
        
        if (!tc.exchangeRate || tc.exchangeRate <= 0) {
            flash.message = 'Invalid exchange rate|error|alert'
            render(view:'/tellering/txnForex/create', model: [
                txnForexInstance: tc
            ])
            return
        }

        // Calculate exchange amounts
        tc.toAmount = tc.fromAmount * tc.exchangeRate
        tc.netAmtPaidOut = tc.toAmount

        // Check if teller has sufficient balance in source currency
        def tellerBalance = TxnTellerBalance.findByUserAndCurrency(user, tc.fromCurrency)
        if (!tellerBalance || (tellerBalance.cashInAmt - tellerBalance.cashOutAmt) < tc.fromAmount) {
            flash.message = "Insufficient ${tc.fromCurrency.code} balance|error|alert"
            render(view:'/tellering/txnForex/create', model: [
                txnForexInstance: tc
            ])
            return
        }

        // Create transaction file
        tf.txnCode = "FOREX"
        tf.txnDescription = "Foreign Exchange"
        tf.txnDate = branch.runDate
        tf.currency = tc.fromCurrency // Base currency
        tf.txnAmt = tc.fromAmount
        tf.txnRef = params.txnRef ?: "Currency Exchange ${tc.fromCurrency.code} to ${tc.toCurrency.code}"
        tf.status = ConfigItemStatus.get(2)
        tf.branch = branch
        tf.txnTimestamp = new Date().toTimestamp()
        tf.txnParticulars = params.txnParticulars ?: "Foreign exchange transaction"
        tf.txnType = TxnType.get(82) // Forex type
        tf.txnTemplate = TxnTemplate.get(82)
        tf.user = user
        tf.save(flush: true, failOnError: true)

        // Create forex record
        tc.txnFile = tf
        tc.user = user
        tc.branch = branch
        tc.txnDate = tf.txnDate
        tc.save(flush: true, failOnError: true)

        // Update teller balances
        // Deduct from source currency
        def fromBalance = TxnTellerBalance.findByUserAndCurrency(user, tc.fromCurrency)
        if (fromBalance) {
            fromBalance.cashOutAmt += tc.fromAmount
            fromBalance.save(flush: true)
        }

        // Add to target currency
        def toBalance = TxnTellerBalance.findByUserAndCurrency(user, tc.toCurrency)
        if (!toBalance) {
            toBalance = new TxnTellerBalance(
                user: user,
                currency: tc.toCurrency,
                cashInAmt: 0,
                cashOutAmt: 0,
                checkInAmt: 0,
                checkOutAmt: 0
            )
        }
        toBalance.cashInAmt += tc.toAmount
        toBalance.save(flush: true)

        // Create transaction breakdown
        def txnBreakdownInstance = new TxnBreakdown(
            debitAmt: tc.fromAmount,
            creditAmt: tc.toAmount,
            txnDate: tf.txnDate,
            txnFile: tf
        )
        txnBreakdownInstance.save(flush: true)

        // Update teller balance status
        userMasterService.updateTellerBalanceStatus(false)
        
        // Generate GL transactions
        glTransactionService.saveTxnBreakdown(tf.id)

        def description = "Forex transaction processed - ${tc.fromAmount} ${tc.fromCurrency.code} to ${tc.toAmount} ${tc.toCurrency.code} at rate ${tc.exchangeRate}"
        auditLogService.insert('120', 'TLR02000', description, 'TellerForex', null, null, null, tf.id)
        
        session["transactionFileId"] = tf.id
        redirect(controller: "tellerCore", action: "txnSuccess")
    }

    /**
     * Get currency details for template
     */
    def getCurrencyOnTemplate() {
        def templateId = params.templateId?.toInteger()
        def template = TxnTemplate.get(templateId)
        
        if (template && template.currency) {
            render([
                currencyId: template.currency.id,
                currencyCode: template.currency.code,
                currencyName: template.currency.description
            ] as JSON)
        } else {
            render([error: "Currency not found for template"] as JSON)
        }
        return
    }

    /**
     * Get current exchange rates
     */
    def getCurrentExchangeRates() {
        def fromCurrencyId = params.fromCurrencyId?.toInteger()
        def toCurrencyId = params.toCurrencyId?.toInteger()
        
        if (!fromCurrencyId || !toCurrencyId) {
            render([error: "Both currencies required"] as JSON)
            return
        }
        
        def fromCurrency = Currency.get(fromCurrencyId)
        def toCurrency = Currency.get(toCurrencyId)
        
        if (!fromCurrency || !toCurrency) {
            render([error: "Invalid currencies"] as JSON)
            return
        }
        
        // Get current exchange rate from currency details
        def exchangeRate = getCurrentRate(fromCurrency, toCurrency)
        
        render([
            fromCurrency: fromCurrency.code,
            toCurrency: toCurrency.code,
            buyRate: exchangeRate.buyRate,
            sellRate: exchangeRate.sellRate,
            midRate: exchangeRate.midRate,
            lastUpdated: exchangeRate.lastUpdated?.format("MM/dd/yyyy HH:mm:ss")
        ] as JSON)
        return
    }

    /**
     * Calculate exchange amount
     */
    def calculateExchangeAmount() {
        def fromAmount = params.fromAmount?.toDouble() ?: 0
        def exchangeRate = params.exchangeRate?.toDouble() ?: 0
        def transactionType = params.transactionType // "buy" or "sell"
        
        if (fromAmount <= 0 || exchangeRate <= 0) {
            render([error: "Invalid amount or rate"] as JSON)
            return
        }
        
        def toAmount = fromAmount * exchangeRate
        def commission = calculateCommission(fromAmount, transactionType)
        def netAmount = toAmount - commission
        
        render([
            fromAmount: fromAmount,
            toAmount: toAmount,
            commission: commission,
            netAmount: netAmount,
            exchangeRate: exchangeRate
        ] as JSON)
        return
    }

    /**
     * Get available currency balances for teller
     */
    def getTellerCurrencyBalances() {
        def user = UserMaster.get(session.user_id)
        def balances = []
        
        def tellerBalances = TxnTellerBalance.findAllByUser(user)
        
        tellerBalances.each { balance ->
            def availableAmount = balance.cashInAmt - balance.cashOutAmt
            if (availableAmount != 0) { // Only show currencies with balance
                balances.add([
                    currencyId: balance.currency.id,
                    currencyCode: balance.currency.code,
                    availableAmount: availableAmount,
                    formatted: String.format("%,.2f", availableAmount)
                ])
            }
        }
        
        render(balances as JSON)
        return
    }

    /**
     * Validate forex transaction limits
     */
    def validateForexLimits() {
        def user = UserMaster.get(session.user_id)
        def amount = params.amount?.toDouble() ?: 0
        def currencyId = params.currencyId?.toInteger()
        
        if (!currencyId || amount <= 0) {
            render([valid: false, message: "Invalid parameters"] as JSON)
            return
        }
        
        def currency = Currency.get(currencyId)
        if (!currency) {
            render([valid: false, message: "Invalid currency"] as JSON)
            return
        }
        
        // Check daily forex limit
        def dailyLimit = user.dailyForexLimit ?: 0
        def currentDailyTotal = getCurrentDailyForexTotal(user, currency)
        
        def withinLimits = (amount <= user.singleForexLimit) && 
                          ((currentDailyTotal + amount) <= dailyLimit)
        
        render([
            valid: withinLimits,
            dailyLimit: dailyLimit,
            singleLimit: user.singleForexLimit ?: 0,
            currentDailyTotal: currentDailyTotal,
            amount: amount,
            message: withinLimits ? "Within limits" : "Exceeds transaction limits"
        ] as JSON)
        return
    }

    // Helper methods
    private def getCurrentRate(Currency fromCurrency, Currency toCurrency) {
        // Get the most recent exchange rate
        def currencyDetail = CurrencyDetail.createCriteria().get {
            and {
                eq("fromCurrency", fromCurrency)
                eq("toCurrency", toCurrency)
                eq("status", ConfigItemStatus.get(2))
            }
            order("dateCreated", "desc")
            maxResults(1)
        }
        
        if (currencyDetail) {
            return [
                buyRate: currencyDetail.buyRate,
                sellRate: currencyDetail.sellRate,
                midRate: (currencyDetail.buyRate + currencyDetail.sellRate) / 2,
                lastUpdated: currencyDetail.dateCreated
            ]
        } else {
            // Default rates if no specific rate found
            return [
                buyRate: 1.0,
                sellRate: 1.0,
                midRate: 1.0,
                lastUpdated: new Date()
            ]
        }
    }

    private def calculateCommission(Double amount, String transactionType) {
        // Calculate commission based on amount and transaction type
        def commissionRate = 0.001 // 0.1% default commission
        
        if (transactionType == "buy") {
            commissionRate = 0.0015 // 0.15% for buying
        } else if (transactionType == "sell") {
            commissionRate = 0.0010 // 0.10% for selling
        }
        
        return amount * commissionRate
    }

    private def getCurrentDailyForexTotal(UserMaster user, Currency currency) {
        def branch = user.branch
        def today = branch.runDate
        
        def total = TxnForex.createCriteria().get {
            projections {
                sum("fromAmount")
            }
            and {
                eq("user", user)
                eq("txnDate", today)
                eq("fromCurrency", currency)
                eq("txnFile.status", ConfigItemStatus.get(2)) // Posted
            }
        }
        
        return total ?: 0
    }
}
