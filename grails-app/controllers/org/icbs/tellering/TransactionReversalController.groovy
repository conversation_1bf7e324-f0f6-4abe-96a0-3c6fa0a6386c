package org.icbs.tellering

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnReversal
import org.icbs.tellering.TxnCashCheckBlotter
import org.icbs.tellering.TxnCOCI
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.admin.UserMaster
import org.icbs.admin.PolicyException
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.CheckStatus
import org.icbs.lov.TxnCheckStatus
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import grails.converters.JSON

/**
 * REFACTORED: Transaction Reversal Controller
 * Extracted from TelleringController.groovy (7,319 lines)
 * Handles all transaction reversal and cancellation operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class TransactionReversalController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def userMasterService
    def glTransactionService
    def auditLogService
    def policyService
    
    // Session variables for reversal operations
    def txnDepAccMinId
    def jrxmlTcId
    def passbookline
    
    static allowedMethods = [
        reverseTxn: "POST",
        viewTellerReverseCancelTxn: "GET",
        validateReversal: "POST"
    ]
    
    // =====================================================
    // TRANSACTION REVERSAL OPERATIONS
    // =====================================================
    
    /**
     * View teller reverse/cancel transaction interface
     */
    def viewTellerReverseCancelTxn() {
        log.info("Viewing teller reverse/cancel transaction interface")
        
        try {
            def txnReversalInstance = new TxnDepositAcctLedger()
            
            // Audit logging
            auditLogService.insert('120', 'TRV01100', 
                "Transaction reversal interface accessed", 
                'TransactionReversalController', null, null, 'tellering/viewTellerReverseCancelTxn', session.user_id)
            
            render(view: '/tellering/txnReversal/view', 
                model: [
                    txnReversalInstance: txnReversalInstance, 
                    id: txnDepAccMinId, 
                    jrxmlTcId: jrxmlTcId, 
                    passbookline: passbookline
                ])
            
        } catch (Exception e) {
            log.error("Error viewing transaction reversal interface", e)
            flash.message = 'Error accessing transaction reversal interface|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * Reverse a transaction
     */
    def reverseTxn() {
        log.info("Processing transaction reversal for transaction: ${params.txnID}")
        
        try {
            if (!params.txnID) {
                flash.message = 'Transaction ID is required|error|alert'
                redirect(action: 'viewTellerReverseCancelTxn')
                return
            }
            
            def tf = TxnFile.get(params.txnID)
            if (!tf) {
                flash.message = 'Transaction not found!|error|alert'
                redirect(action: 'viewTellerReverseCancelTxn')
                return
            }
            
            // Validate reversal eligibility
            Map validationResult = validateReversalEligibility(tf)
            if (!validationResult.isValid) {
                flash.message = validationResult.message + '|error|alert'
                redirect(action: 'viewTellerReverseCancelTxn')
                return
            }
            
            // Process the reversal
            Map reversalResult = processTransactionReversal(tf, params)
            
            if (reversalResult.success) {
                flash.message = "Reversal Complete..|success|alert"
                
                // Audit logging
                auditLogService.insert('120', 'TRV01200', 
                    "Transaction ${tf.id} reversed successfully", 
                    'TransactionReversalController', null, null, 'tellering/reverseTxn', tf.id)
                
                // Get reversal details for display
                def reverseTransactionFileInstance = TxnFile.get(params.txnID)
                def getRefereceAndParticularsReverseInstance = TxnReversal.createCriteria().list {
                    and {
                        eq("txnFile", TxnFile.get(params.txnID))
                    }
                }
                
                render(view: '/tellering/reverseTxn', 
                    model: [
                        reverseTransactionFileInstance: reverseTransactionFileInstance,
                        getRefereceAndParticularsReverseInstance: getRefereceAndParticularsReverseInstance
                    ])
            } else {
                flash.message = reversalResult.message + '|error|alert'
                redirect(action: 'viewTellerReverseCancelTxn')
            }
            
        } catch (Exception e) {
            log.error("Error processing transaction reversal", e)
            flash.message = 'Error processing transaction reversal|error|alert'
            redirect(action: 'viewTellerReverseCancelTxn')
        }
    }
    
    /**
     * Validate reversal request via AJAX
     */
    def validateReversal() {
        log.debug("Validating reversal for transaction: ${params.txnID}")
        
        try {
            if (!params.txnID) {
                render([isValid: false, message: 'Transaction ID is required'] as JSON)
                return
            }
            
            def tf = TxnFile.get(params.txnID)
            if (!tf) {
                render([isValid: false, message: 'Transaction not found'] as JSON)
                return
            }
            
            Map validationResult = validateReversalEligibility(tf)
            render(validationResult as JSON)
            
        } catch (Exception e) {
            log.error("Error validating reversal", e)
            render([isValid: false, message: 'Error validating reversal'] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Validate if transaction is eligible for reversal
     */
    private Map validateReversalEligibility(TxnFile tf) {
        Map result = [isValid: true, message: '']
        
        try {
            // Check if already cancelled
            if (tf.status == ConfigItemStatus.read(4)) {
                result.isValid = false
                result.message = 'Transaction already cancelled'
                return result
            }
            
            // Only allow reversal for same user
            if (tf.user != UserMaster.get(session.user_id)) {
                result.isValid = false
                result.message = 'Transaction to reverse has different user'
                return result
            }
            
            // Check transaction date (same day reversal only)
            def currentUser = UserMaster.get(session.user_id)
            if (tf.txnDate != currentUser.branch.runDate) {
                result.isValid = false
                result.message = 'Only same day transaction reversal allowed'
                return result
            }
            
            // System transaction reversal not allowed
            if (tf.txnType.id >= 24) {
                result.isValid = false
                result.message = 'System transaction reversal not allowed'
                return result
            }
            
            // Check specific transaction type validations
            result = validateSpecificTransactionType(tf, result)
            
        } catch (Exception e) {
            log.error("Error validating reversal eligibility", e)
            result.isValid = false
            result.message = 'Error validating reversal eligibility'
        }
        
        return result
    }
    
    /**
     * Validate specific transaction type rules
     */
    private Map validateSpecificTransactionType(TxnFile tf, Map result) {
        try {
            // Check transaction validations
            if (tf.txnType.id == 4) { // Check transactions
                def rtcoci = TxnCOCI.findAllByTxnFile(tf)
                boolean isCleared = false
                for (tc in rtcoci) {
                    if (tc.checkStatus.id == 5) {
                        isCleared = true
                        break
                    }
                }
                if (isCleared) {
                    result.isValid = false
                    result.message = 'Cannot reverse check already cleared'
                    return result
                }
            }
            
            // Cash withdrawal validations
            if (tf.txnType.id == 3) {
                if (tf.txnAmt > tf.depAcct.availableBalAmt) {
                    result.isValid = false
                    result.message = 'Cannot reverse - negative available balance result'
                    return result
                }
            }
            
        } catch (Exception e) {
            log.error("Error validating specific transaction type", e)
            result.isValid = false
            result.message = 'Error validating transaction type'
        }
        
        return result
    }
    
    /**
     * Process the actual transaction reversal
     */
    private Map processTransactionReversal(TxnFile tf, Map params) {
        Map result = [success: false, message: '']
        
        try {
            // Handle check transactions
            if ((tf.txnType.id == 4) || (tf.txnType.id == 13) || (tf.txnType.id == 15) || (tf.txnType.id == 20)) {
                def tcoci = TxnCOCI.findAllByTxnFile(tf)
                for (tc in tcoci) {
                    tc.checkStatus = CheckStatus.get(4)
                    tc.status = ConfigItemStatus.get(4)
                    tc.txnCheckStatus = TxnCheckStatus.get(4)
                    tc.cleared = 'TRUE'
                    tc.save(flush: true)
                }
            }
            
            // Create reversal record
            def insrtToTxnReversal = new TxnReversal(
                txnFile: params.txnID,
                txnParticulars: params.txnParticulars,
                txnReference: params.txnReference,
                txnTimestamp: new Date().toTimestamp(),
                reversedBy: UserMaster.get(session.user_id)
            )
            insrtToTxnReversal.save(flush: true)
            
            // Handle teller blotter reversal
            def tlrBlotter = TxnCashCheckBlotter.findByTxnFile(tf)
            if (tlrBlotter) {
                def cashIn = tlrBlotter.cashInAmt
                def cashOut = tlrBlotter.cashOutAmt
                def checkIn = tlrBlotter.checkInAmt
                def checkOut = tlrBlotter.checkOutAmt
                
                def rvsBlotter = new TxnCashCheckBlotter(
                    branch: tlrBlotter.branch,
                    cashInAmt: cashOut,
                    cashOutAmt: cashIn,
                    checkInAmt: checkOut,
                    checkOutAmt: checkIn,
                    currency: tlrBlotter.currency,
                    txnFile: tlrBlotter.txnFile,
                    user: tlrBlotter.user,
                    txnParticulars: 'Reversal of ' + tlrBlotter.txnParticulars
                )
                rvsBlotter.save(flush: true)
            }
            
            // Handle cash transfer policy exceptions
            if (tf.txnType.id == 2) {
                def policyExceptionInstance = PolicyException.findByRecordId(tf.id)
                if (policyExceptionInstance) {
                    policyExceptionInstance.dateOfAction = insrtToTxnReversal.txnTimestamp
                    policyExceptionInstance.isApproved = false
                    policyExceptionInstance.save(flush: true)
                }
            }
            
            // Update transaction status
            tf.status = ConfigItemStatus.get(4)
            tf.save(flush: true)
            
            // Process GL reversal
            glTransactionService.reverseTxn(tf)
            
            // Update teller balance
            userMasterService.updateTellerBalanceStatus(false)
            
            // Handle loan transactions
            if ((tf.txnType.id == 12) || (tf.txnType.id == 13) || (tf.txnType.id == 14) || (tf.txnType.id == 15)) {
                // loanService.updateInstallment(tf) - would be handled by LoanPaymentService
            }
            
            result.success = true
            result.message = 'Transaction reversed successfully'
            
        } catch (Exception e) {
            log.error("Error processing transaction reversal", e)
            result.message = 'Error processing transaction reversal'
        }
        
        return result
    }
}
