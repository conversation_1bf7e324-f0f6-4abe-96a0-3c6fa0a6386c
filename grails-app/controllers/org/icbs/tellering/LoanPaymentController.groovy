package org.icbs.tellering

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.tellering.TxnLoanPaymentDetails
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnCOCI
import org.icbs.tellering.TxnCashCheckBlotter
import org.icbs.loans.Loan
import org.icbs.loans.LoanInstallment
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.Currency
import org.icbs.lov.ConfigItemStatus
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import grails.converters.JSON
import groovy.sql.Sql
import javax.sql.DataSource

/**
 * REFACTORED: Loan Payment Controller
 * Extracted from TelleringController.groovy (7,319 lines)
 * Handles all loan payment operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanPaymentController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def userMasterService
    def glTransactionService
    def auditLogService
    def loanService
    DataSource dataSource
    
    // Session variables for loan operations
    def userMasterId
    def pbvalidate
    def prevTxnDepAcctLedgerId
    
    static allowedMethods = [
        saveTellerLoanCashRepaymentTxn: "POST",
        saveTellerLoanCheckRepaymentTxn: "POST",
        saveTellerLoanCashSpecifiedRepaymentTxn: "POST",
        saveTellerLoanCheckSpecifiedRepaymentTxn: "POST",
        changeLoanDetails: "POST"
    ]
    
    // =====================================================
    // LOAN CASH REPAYMENT OPERATIONS
    // =====================================================
    
    /**
     * Create teller loan cash repayment transaction
     */
    def createTellerLoanCashRepaymentTxn() {
        log.info("Creating teller loan cash repayment transaction")
        
        try {
            def user = UserMaster.get(session.user_id)
            if (!user.cash) {
                flash.message = 'Error! No cash account defined.|error|alert'
                redirect(controller: 'tellering', action: 'index')
                return
            }
            
            def txnFileInstance = new TxnFile()
            def txnLoanCashRepaymentInstance = new TxnLoanPaymentDetails()
            
            // Audit logging
            auditLogService.insert('110', 'LPY01100', 
                "Loan cash repayment transaction creation initiated", 
                'LoanPaymentController', null, null, 'tellering/createTellerLoanCashRepaymentTxn', session.user_id)
            
            render(view: '/tellering/txnLoanCashRepayment/create', 
                model: [txnLoanCashRepaymentInstance: txnLoanCashRepaymentInstance])
            
        } catch (Exception e) {
            log.error("Error creating loan cash repayment transaction", e)
            flash.message = 'Error creating loan cash repayment transaction|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * Save teller loan cash repayment transaction
     */
    def saveTellerLoanCashRepaymentTxn(TxnLoanPaymentDetails tc, TxnFile tf) {
        log.info("Saving teller loan cash repayment transaction")
        
        try {
            if (tc.hasErrors() || tf.hasErrors()) {
                flash.message = 'Failed to transfer.|error|alert'
                render(view: '/tellering/txnLoanCashRepayment/create', model: [txnLoanCashRepaymentInstance: tc])
                return
            }
            
            // Validation
            Map validationResult = unifiedValidationService.validateLoanPayment(params, tc, tf)
            if (!validationResult.isValid) {
                flash.message = validationResult.errors.join(', ') + '|error|alert'
                render(view: '/tellering/txnLoanCashRepayment/create', model: [txnLoanCashRepaymentInstance: tc])
                return
            }
            
            // Process loan cash repayment
            Map paymentResult = processLoanCashRepayment(tc, tf, params)
            
            if (paymentResult.success) {
                // Update teller balance and GL
                userMasterService.updateTellerBalanceStatus(false)
                glTransactionService.saveTxnBreakdown(tf.id)
                
                flash.message = 'Transaction complete.|success|alert'
                
                // Audit logging
                auditLogService.insert('110', 'LPY01200', 
                    "Loan cash repayment completed - Loan: ${tc.acctNo}, Amount: ${tc.paymentAmt}", 
                    'LoanPaymentController', null, null, 'tellering/saveTellerLoanCashRepaymentTxn', tf.id)
                
                redirect(controller: "tellering", action: "txnSuccess")
            } else {
                flash.message = paymentResult.message + '|error|alert'
                render(view: '/tellering/txnLoanCashRepayment/create', model: [txnLoanCashRepaymentInstance: tc])
            }
            
        } catch (Exception e) {
            log.error("Error saving loan cash repayment transaction", e)
            flash.message = 'Error processing loan cash repayment|error|alert'
            render(view: '/tellering/txnLoanCashRepayment/create', model: [txnLoanCashRepaymentInstance: tc])
        }
    }
    
    // =====================================================
    // LOAN CHECK REPAYMENT OPERATIONS
    // =====================================================
    
    /**
     * Create teller loan check repayment transaction
     */
    def createTellerLoanCheckRepaymentTxn() {
        log.info("Creating teller loan check repayment transaction")
        
        try {
            def user = UserMaster.get(session.user_id)
            if (!user.coci) {
                flash.message = 'Error! No COCI account defined.|error|alert'
                redirect(controller: 'tellering', action: 'index')
                return
            }
            
            def txnFileInstance = new TxnFile()
            def txnLoanCheckRepaymentInstance = new TxnLoanPaymentDetails()
            session["checks"] = []
            
            // Audit logging
            auditLogService.insert('110', 'LPY01300', 
                "Loan check repayment transaction creation initiated", 
                'LoanPaymentController', null, null, 'tellering/createTellerLoanCheckRepaymentTxn', session.user_id)
            
            render(view: '/tellering/txnLoanCheckRepayment/create', 
                model: [txnLoanCheckRepaymentInstance: txnLoanCheckRepaymentInstance])
            
        } catch (Exception e) {
            log.error("Error creating loan check repayment transaction", e)
            flash.message = 'Error creating loan check repayment transaction|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * Save teller loan check repayment transaction
     */
    def saveTellerLoanCheckRepaymentTxn(TxnLoanPaymentDetails tc, TxnFile tf) {
        log.info("Saving teller loan check repayment transaction")
        
        try {
            if (tc.hasErrors() || tf.hasErrors()) {
                flash.message = 'Failed to transfer.|error|alert'
                render(view: '/tellering/txnLoanCheckRepayment/create', model: [txnLoanCheckRepaymentInstance: tc])
                return
            }
            
            // Validation
            Map validationResult = unifiedValidationService.validateLoanCheckPayment(params, tc, tf)
            if (!validationResult.isValid) {
                flash.message = validationResult.errors.join(', ') + '|error|alert'
                render(view: '/tellering/txnLoanCheckRepayment/create', model: [txnLoanCheckRepaymentInstance: tc])
                return
            }
            
            // Process loan check repayment
            Map paymentResult = processLoanCheckRepayment(tc, tf, params)
            
            if (paymentResult.success) {
                // Update teller balance and GL
                userMasterService.updateTellerBalanceStatus(false)
                glTransactionService.saveTxnBreakdown(tf.id)
                
                flash.message = 'Transaction complete.|success|alert'
                
                // Audit logging
                auditLogService.insert('110', 'LPY01400', 
                    "Loan check repayment completed - Loan: ${tc.acctNo}, Amount: ${tc.paymentAmt}", 
                    'LoanPaymentController', null, null, 'tellering/saveTellerLoanCheckRepaymentTxn', tf.id)
                
                redirect(controller: "tellering", action: "txnSuccess")
            } else {
                flash.message = paymentResult.message + '|error|alert'
                render(view: '/tellering/txnLoanCheckRepayment/create', model: [txnLoanCheckRepaymentInstance: tc])
            }
            
        } catch (Exception e) {
            log.error("Error saving loan check repayment transaction", e)
            flash.message = 'Error processing loan check repayment|error|alert'
            render(view: '/tellering/txnLoanCheckRepayment/create', model: [txnLoanCheckRepaymentInstance: tc])
        }
    }
    
    // =====================================================
    // LOAN SPECIFIED REPAYMENT OPERATIONS
    // =====================================================
    
    /**
     * Create teller loan cash specified repayment transaction
     */
    def createTellerLoanCashSpecifiedRepaymentTxn() {
        log.info("Creating teller loan cash specified repayment transaction")
        
        try {
            def user = UserMaster.get(session.user_id)
            if (!user.cash) {
                flash.message = 'Error! No cash account defined.|error|alert'
                redirect(controller: 'tellering', action: 'index')
                return
            }
            
            def loanInstallmentInstance = new LoanInstallment()
            def txnFileInstance = new TxnFile()
            def txnLoanCashSpecifiedRepaymentInstance = new TxnLoanPaymentDetails()
            
            // Audit logging
            auditLogService.insert('110', 'LPY01500', 
                "Loan cash specified repayment transaction creation initiated", 
                'LoanPaymentController', null, null, 'tellering/createTellerLoanCashSpecifiedRepaymentTxn', session.user_id)
            
            render(view: '/tellering/txnLoanCashSpecifiedRepayment/create', 
                model: [txnLoanCashSpecifiedRepaymentInstance: txnLoanCashSpecifiedRepaymentInstance])
            
        } catch (Exception e) {
            log.error("Error creating loan cash specified repayment transaction", e)
            flash.message = 'Error creating loan cash specified repayment transaction|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * Save teller loan cash specified repayment transaction
     */
    def saveTellerLoanCashSpecifiedRepaymentTxn(TxnLoanPaymentDetails tc, TxnFile tf) {
        log.info("Saving teller loan cash specified repayment transaction")
        
        try {
            if (tc.hasErrors() || tf.hasErrors()) {
                flash.message = 'Failed to transfer.|error|alert'
                render(view: '/tellering/txnLoanCashSpecifiedRepayment/create', model: [txnLoanCashSpecifiedRepaymentInstance: tc])
                return
            }
            
            // Process specified repayment
            Map paymentResult = processLoanSpecifiedRepayment(tc, tf, params, 'cash')
            
            if (paymentResult.success) {
                // Update teller balance and GL
                userMasterService.updateTellerBalanceStatus(false)
                glTransactionService.saveTxnBreakdown(tf.id)
                
                flash.message = 'Transaction complete.|success|alert'
                
                // Audit logging
                auditLogService.insert('110', 'LPY01600', 
                    "Loan cash specified repayment completed - Loan: ${tc.acctNo}, Amount: ${tc.paymentAmt}", 
                    'LoanPaymentController', null, null, 'tellering/saveTellerLoanCashSpecifiedRepaymentTxn', tf.id)
                
                redirect(controller: "tellering", action: "txnSuccess")
            } else {
                flash.message = paymentResult.message + '|error|alert'
                render(view: '/tellering/txnLoanCashSpecifiedRepayment/create', model: [txnLoanCashSpecifiedRepaymentInstance: tc])
            }
            
        } catch (Exception e) {
            log.error("Error saving loan cash specified repayment transaction", e)
            flash.message = 'Error processing loan cash specified repayment|error|alert'
            render(view: '/tellering/txnLoanCashSpecifiedRepayment/create', model: [txnLoanCashSpecifiedRepaymentInstance: tc])
        }
    }
    
    /**
     * Create teller loan check specified repayment transaction
     */
    def createTellerLoanCheckSpecifiedRepaymentTxn() {
        log.info("Creating teller loan check specified repayment transaction")
        
        try {
            def user = UserMaster.get(session.user_id)
            if (!user.coci) {
                flash.message = 'Error! No cash account defined.|error|alert'
                redirect(controller: 'tellering', action: 'index')
                return
            }
            
            def loanInstallmentInstance = new LoanInstallment()
            def txnFileInstance = new TxnFile()
            def txnCOCIInstance = new TxnCOCI()
            def txnLoanCheckSpecifiedRepaymentInstance = new TxnLoanPaymentDetails()
            session["checks"] = []
            
            // Audit logging
            auditLogService.insert('110', 'LPY01700', 
                "Loan check specified repayment transaction creation initiated", 
                'LoanPaymentController', null, null, 'tellering/createTellerLoanCheckSpecifiedRepaymentTxn', session.user_id)
            
            render(view: '/tellering/txnLoanCheckSpecifiedRepayment/create', 
                model: [txnLoanCheckSpecifiedRepaymentInstance: txnLoanCheckSpecifiedRepaymentInstance])
            
        } catch (Exception e) {
            log.error("Error creating loan check specified repayment transaction", e)
            flash.message = 'Error creating loan check specified repayment transaction|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * Save teller loan check specified repayment transaction
     */
    def saveTellerLoanCheckSpecifiedRepaymentTxn(TxnLoanPaymentDetails tc, TxnFile tf) {
        log.info("Saving teller loan check specified repayment transaction")
        
        try {
            if (tc.hasErrors() || tf.hasErrors()) {
                flash.message = 'Failed to transfer.|error|alert'
                render(view: '/tellering/txnLoanCheckSpecifiedRepayment/create', model: [txnLoanCheckSpecifiedRepaymentInstance: tc])
                return
            }
            
            // Process specified repayment
            Map paymentResult = processLoanSpecifiedRepayment(tc, tf, params, 'check')
            
            if (paymentResult.success) {
                // Update teller balance and GL
                userMasterService.updateTellerBalanceStatus(false)
                glTransactionService.saveTxnBreakdown(tf.id)
                
                flash.message = 'Transaction complete.|success|alert'
                
                // Audit logging
                auditLogService.insert('110', 'LPY01800', 
                    "Loan check specified repayment completed - Loan: ${tc.acctNo}, Amount: ${tc.paymentAmt}", 
                    'LoanPaymentController', null, null, 'tellering/saveTellerLoanCheckSpecifiedRepaymentTxn', tf.id)
                
                redirect(controller: "tellering", action: "txnSuccess")
            } else {
                flash.message = paymentResult.message + '|error|alert'
                render(view: '/tellering/txnLoanCheckSpecifiedRepayment/create', model: [txnLoanCheckSpecifiedRepaymentInstance: tc])
            }
            
        } catch (Exception e) {
            log.error("Error saving loan check specified repayment transaction", e)
            flash.message = 'Error processing loan check specified repayment|error|alert'
            render(view: '/tellering/txnLoanCheckSpecifiedRepayment/create', model: [txnLoanCheckSpecifiedRepaymentInstance: tc])
        }
    }
    
    // =====================================================
    // LOAN DETAILS MANAGEMENT
    // =====================================================
    
    /**
     * Change loan details for payment processing
     */
    def changeLoanDetails() {
        log.debug("Changing loan details for loan: ${params.loan}")
        
        try {
            def loanInstance = Loan.get(params.loan)
            if (!loanInstance) {
                notFound()
                return
            }
            
            def id_loan = params.loan
            userMasterId = UserMaster.get(session.user_id).id
            pbvalidate = id_loan
            
            def sql = new Sql(dataSource)
            def row = sql.firstRow("select max(txn_file_id) from txn_loan_payment_details where id = (select max(id) FROM txn_loan_payment_details where acct_id = " + id_loan + ")")
            prevTxnDepAcctLedgerId = row.max
            
            // Audit logging
            auditLogService.insert('110', 'LPY01900', 
                "Loan details changed for payment processing - Loan: ${loanInstance.accountNo}", 
                'LoanPaymentController', null, null, 'tellering/changeLoanDetails', loanInstance.id)
            
            render(loanInstance as JSON)
            
        } catch (Exception e) {
            log.error("Error changing loan details", e)
            render([error: 'Error changing loan details'] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Process loan cash repayment
     */
    private Map processLoanCashRepayment(TxnLoanPaymentDetails tc, TxnFile tf, Map params) {
        Map result = [success: false, message: '']
        
        try {
            def loanInstance = Loan.get(params.loan?.toInteger())
            if (!loanInstance) {
                result.message = 'Loan account not found'
                return result
            }
            
            def paymentAmt = Double.parseDouble(params.paymentAmt.replaceAll(",", ""))
            def totalAmountDue = loanInstance.balanceAmount + loanInstance.interestBalanceAmount + 
                                loanInstance.penaltyBalanceAmount + loanInstance.serviceChargeBalanceAmount
            
            if (paymentAmt > totalAmountDue.round(2)) {
                result.message = 'Account Overpayment'
                return result
            }
            
            // Calculate payment allocation
            Map paymentAllocation = calculatePaymentAllocation(loanInstance, paymentAmt)
            
            // Update loan balances
            updateLoanBalances(loanInstance, paymentAllocation)
            
            // Create payment details record
            createPaymentDetailsRecord(tc, tf, loanInstance, paymentAllocation, params)
            
            // Create teller blotter entry
            createTellerBlotterEntry(tf, paymentAmt, 'cash')
            
            result.success = true
            result.message = 'Loan cash repayment processed successfully'
            
        } catch (Exception e) {
            log.error("Error processing loan cash repayment", e)
            result.message = 'Error processing loan cash repayment'
        }
        
        return result
    }
    
    /**
     * Process loan check repayment
     */
    private Map processLoanCheckRepayment(TxnLoanPaymentDetails tc, TxnFile tf, Map params) {
        Map result = [success: false, message: '']
        
        try {
            def loanInstance = Loan.get(params.loan?.toInteger())
            if (!loanInstance) {
                result.message = 'Loan account not found'
                return result
            }
            
            def paymentAmt = Double.parseDouble(params.paymentAmt.replaceAll(",", ""))
            def totalAmountDue = loanInstance.balanceAmount + loanInstance.interestBalanceAmount + 
                                loanInstance.penaltyBalanceAmount + loanInstance.serviceChargeBalanceAmount
            
            if (paymentAmt > totalAmountDue.round(2)) {
                result.message = 'Account Overpayment'
                return result
            }
            
            // Calculate payment allocation
            Map paymentAllocation = calculatePaymentAllocation(loanInstance, paymentAmt)
            
            // Update loan balances
            updateLoanBalances(loanInstance, paymentAllocation)
            
            // Create payment details record
            createPaymentDetailsRecord(tc, tf, loanInstance, paymentAllocation, params)
            
            // Create teller blotter entry
            createTellerBlotterEntry(tf, paymentAmt, 'check')
            
            result.success = true
            result.message = 'Loan check repayment processed successfully'
            
        } catch (Exception e) {
            log.error("Error processing loan check repayment", e)
            result.message = 'Error processing loan check repayment'
        }
        
        return result
    }
    
    /**
     * Process loan specified repayment
     */
    private Map processLoanSpecifiedRepayment(TxnLoanPaymentDetails tc, TxnFile tf, Map params, String paymentType) {
        Map result = [success: false, message: '']
        
        try {
            def loanInstance = Loan.get(params.loan?.toInteger())
            if (!loanInstance) {
                result.message = 'Loan account not found'
                return result
            }
            
            // Get specified amounts
            def totalPrincipalPaid = Double.parseDouble(params.principalInstallmentAmount?.replaceAll(",", "") ?: "0")
            def totalInterestPaid = Double.parseDouble(params.interestInstallmentAmount?.replaceAll(",", "") ?: "0")
            def totalPenaltyPaid = Double.parseDouble(params.penaltyInstallmentAmount?.replaceAll(",", "") ?: "0")
            def totalServiceChargePaid = Double.parseDouble(params.serviceChargeInstallmentAmount?.replaceAll(",", "") ?: "0")
            
            // Validate amounts
            if (totalPrincipalPaid > loanInstance.balanceAmount) {
                result.message = 'ERROR: Principal Payment Greater than Account balance'
                return result
            }
            
            // Create payment allocation map
            Map paymentAllocation = [
                principalPaid: totalPrincipalPaid,
                interestPaid: totalInterestPaid,
                penaltyPaid: totalPenaltyPaid,
                serviceChargePaid: totalServiceChargePaid
            ]
            
            // Update loan balances
            updateLoanBalances(loanInstance, paymentAllocation)
            
            // Create payment details record
            createPaymentDetailsRecord(tc, tf, loanInstance, paymentAllocation, params)
            
            // Create teller blotter entry
            def totalPayment = totalPrincipalPaid + totalInterestPaid + totalPenaltyPaid + totalServiceChargePaid
            createTellerBlotterEntry(tf, totalPayment, paymentType)
            
            result.success = true
            result.message = 'Loan specified repayment processed successfully'
            
        } catch (Exception e) {
            log.error("Error processing loan specified repayment", e)
            result.message = 'Error processing loan specified repayment'
        }
        
        return result
    }
    
    /**
     * Calculate payment allocation based on loan payment hierarchy
     */
    private Map calculatePaymentAllocation(Loan loanInstance, Double paymentAmount) {
        Map allocation = [
            principalPaid: 0.0,
            interestPaid: 0.0,
            penaltyPaid: 0.0,
            serviceChargePaid: 0.0
        ]
        
        Double remainingPayment = paymentAmount
        
        // Payment hierarchy: Service Charges -> Penalties -> Interest -> Principal
        
        // 1. Pay service charges first
        if (remainingPayment > 0 && loanInstance.serviceChargeBalanceAmount > 0) {
            Double serviceChargePayment = Math.min(remainingPayment, loanInstance.serviceChargeBalanceAmount)
            allocation.serviceChargePaid = serviceChargePayment
            remainingPayment -= serviceChargePayment
        }
        
        // 2. Pay penalties
        if (remainingPayment > 0 && loanInstance.penaltyBalanceAmount > 0) {
            Double penaltyPayment = Math.min(remainingPayment, loanInstance.penaltyBalanceAmount)
            allocation.penaltyPaid = penaltyPayment
            remainingPayment -= penaltyPayment
        }
        
        // 3. Pay interest
        if (remainingPayment > 0 && loanInstance.interestBalanceAmount > 0) {
            Double interestPayment = Math.min(remainingPayment, loanInstance.interestBalanceAmount)
            allocation.interestPaid = interestPayment
            remainingPayment -= interestPayment
        }
        
        // 4. Pay principal
        if (remainingPayment > 0 && loanInstance.balanceAmount > 0) {
            Double principalPayment = Math.min(remainingPayment, loanInstance.balanceAmount)
            allocation.principalPaid = principalPayment
        }
        
        return allocation
    }
    
    /**
     * Update loan balances after payment
     */
    private void updateLoanBalances(Loan loanInstance, Map paymentAllocation) {
        loanInstance.balanceAmount -= paymentAllocation.principalPaid
        loanInstance.interestBalanceAmount -= paymentAllocation.interestPaid
        loanInstance.penaltyBalanceAmount -= paymentAllocation.penaltyPaid
        loanInstance.serviceChargeBalanceAmount -= paymentAllocation.serviceChargePaid
        
        // Update transaction sequence and dates
        loanInstance.transactionSequenceNo = (loanInstance.transactionSequenceNo ?: 0) + 1
        loanInstance.lastTransactionDate = new Date()
        loanInstance.lastCustormerTransactionDate = new Date()
        
        loanInstance.save(flush: true, failOnError: true)
    }
    
    /**
     * Create payment details record
     */
    private void createPaymentDetailsRecord(TxnLoanPaymentDetails tc, TxnFile tf, Loan loanInstance, Map paymentAllocation, Map params) {
        tc.acct = loanInstance
        tc.acctNo = loanInstance.accountNo
        tc.balForwarded = loanInstance.balanceAmount + paymentAllocation.principalPaid
        tc.branch = Branch.get(UserMaster.get(session.user_id).branchId)
        tc.currency = Currency.get(loanInstance.product.currencyId)
        tc.interestAmt = paymentAllocation.interestPaid
        tc.interestBalAfterPayment = loanInstance.interestBalanceAmount
        tc.otherAmt = 0
        tc.paymentAmt = Double.parseDouble(params.paymentAmt.replaceAll(",", ""))
        tc.penaltyAmt = paymentAllocation.penaltyPaid
        tc.penaltyBalAfterPayment = loanInstance.penaltyBalanceAmount
        tc.principalAmt = paymentAllocation.principalPaid
        tc.principalBalAfterPayment = loanInstance.balanceAmount
        tc.serviceChargeAmt = paymentAllocation.serviceChargePaid
        tc.totalNetProceeds = loanInstance.totalNetProceeds
        tc.txnDate = new Date()
        tc.txnFile = tf
        tc.txnRef = tf.txnRef
        tc.user = UserMaster.get(session.user_id)
        
        tc.save(flush: true, failOnError: true)
    }
    
    /**
     * Create teller blotter entry
     */
    private void createTellerBlotterEntry(TxnFile tf, Double amount, String paymentType) {
        def tb = new TxnCashCheckBlotter(
            branch: Branch.get(UserMaster.get(session.user_id).branchId),
            currency: tf.currency,
            user: UserMaster.get(session.user_id),
            txnParticulars: tf.txnParticulars,
            txnFile: tf
        )
        
        if (paymentType == 'cash') {
            tb.cashInAmt = amount
            tb.cashOutAmt = 0
            tb.checkInAmt = 0
            tb.checkOutAmt = 0
        } else {
            tb.cashInAmt = 0
            tb.cashOutAmt = 0
            tb.checkInAmt = amount
            tb.checkOutAmt = 0
        }
        
        tb.save(flush: true, failOnError: true)
    }
}
