package org.icbs.tellering

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnReversal
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnCashCheckBlotter
import org.icbs.admin.UserMaster
import org.icbs.admin.AuditLog
import org.icbs.lov.ConfigItemStatus
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import grails.converters.JSON
import groovy.sql.Sql
import javax.sql.DataSource

/**
 * REFACTORED: Transaction Audit Controller
 * Extracted from TelleringController.groovy (7,319 lines)
 * Handles all transaction audit and monitoring operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class TransactionAuditController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def auditLogService
    DataSource dataSource
    
    static allowedMethods = [
        viewAuditTrail: "GET",
        viewTransactionAudit: "GET",
        viewUserActivity: "GET",
        generateAuditReport: "GET",
        searchAuditLogs: "POST"
    ]
    
    // =====================================================
    // TRANSACTION AUDIT OPERATIONS
    // =====================================================
    
    /**
     * View audit trail for specific transaction
     */
    def viewAuditTrail() {
        log.info("Viewing audit trail for transaction: ${params.txnId}")
        
        try {
            if (!params.txnId) {
                flash.message = 'Transaction ID is required|error|alert'
                redirect(controller: 'tellering', action: 'index')
                return
            }
            
            def txnFile = TxnFile.get(params.txnId)
            if (!txnFile) {
                flash.message = 'Transaction not found|error|alert'
                redirect(controller: 'tellering', action: 'index')
                return
            }
            
            // Get audit logs for this transaction
            List<AuditLog> auditLogs = getTransactionAuditLogs(txnFile.id)
            
            // Get transaction reversals if any
            List<TxnReversal> reversals = TxnReversal.findAllByTxnFile(txnFile)
            
            // Get GL breakdown
            List<TxnBreakdown> glBreakdown = TxnBreakdown.findAllByTxnFile(txnFile)
            
            // Audit logging
            auditLogService.insert('110', 'TAU01100', 
                "Transaction audit trail viewed - Transaction: ${txnFile.id}", 
                'TransactionAuditController', null, null, 'tellering/viewAuditTrail', txnFile.id)
            
            render(view: '/tellering/audit/auditTrail', 
                model: [
                    txnFile: txnFile,
                    auditLogs: auditLogs,
                    reversals: reversals,
                    glBreakdown: glBreakdown
                ])
            
        } catch (Exception e) {
            log.error("Error viewing audit trail", e)
            flash.message = 'Error viewing audit trail|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * View transaction audit summary
     */
    def viewTransactionAudit() {
        log.info("Viewing transaction audit summary")
        
        try {
            Date fromDate = params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 7
            Date toDate = params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date()
            
            Map auditSummary = generateTransactionAuditSummary(fromDate, toDate)
            
            // Audit logging
            auditLogService.insert('110', 'TAU01200', 
                "Transaction audit summary viewed - Period: ${fromDate} to ${toDate}", 
                'TransactionAuditController', null, null, 'tellering/viewTransactionAudit', session.user_id)
            
            render(view: '/tellering/audit/transactionAudit', 
                model: [
                    auditSummary: auditSummary,
                    fromDate: fromDate,
                    toDate: toDate
                ])
            
        } catch (Exception e) {
            log.error("Error viewing transaction audit", e)
            flash.message = 'Error viewing transaction audit|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * View user activity audit
     */
    def viewUserActivity() {
        log.info("Viewing user activity audit")
        
        try {
            Long userId = params.userId?.toLong() ?: session.user_id
            Date fromDate = params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 1
            Date toDate = params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date()
            
            UserMaster user = UserMaster.get(userId)
            if (!user) {
                flash.message = 'User not found|error|alert'
                redirect(controller: 'tellering', action: 'index')
                return
            }
            
            Map userActivity = generateUserActivitySummary(user, fromDate, toDate)
            
            // Audit logging
            auditLogService.insert('110', 'TAU01300', 
                "User activity audit viewed - User: ${user.username}, Period: ${fromDate} to ${toDate}", 
                'TransactionAuditController', null, null, 'tellering/viewUserActivity', userId)
            
            render(view: '/tellering/audit/userActivity', 
                model: [
                    user: user,
                    userActivity: userActivity,
                    fromDate: fromDate,
                    toDate: toDate
                ])
            
        } catch (Exception e) {
            log.error("Error viewing user activity", e)
            flash.message = 'Error viewing user activity|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * Search audit logs
     */
    def searchAuditLogs() {
        log.debug("Searching audit logs with criteria: ${params}")
        
        try {
            Map searchCriteria = [
                fromDate: params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 7,
                toDate: params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date(),
                userId: params.userId?.toLong(),
                actionCode: params.actionCode,
                entityType: params.entityType,
                searchText: params.searchText
            ]
            
            List<AuditLog> auditLogs = searchAuditLogsWithCriteria(searchCriteria)
            
            // Audit logging
            auditLogService.insert('110', 'TAU01400', 
                "Audit logs searched - Criteria: ${searchCriteria}", 
                'TransactionAuditController', null, null, 'tellering/searchAuditLogs', session.user_id)
            
            render([
                success: true,
                auditLogs: auditLogs,
                totalCount: auditLogs.size()
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error searching audit logs", e)
            render([success: false, error: 'Error searching audit logs'] as JSON)
        }
    }
    
    /**
     * Generate audit report
     */
    def generateAuditReport() {
        log.info("Generating audit report")
        
        try {
            Date fromDate = params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 30
            Date toDate = params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date()
            String reportType = params.reportType ?: 'TRANSACTION_AUDIT'
            
            Map reportData = generateAuditReportData(fromDate, toDate, reportType)
            
            // Audit logging
            auditLogService.insert('110', 'TAU01500', 
                "Audit report generated - Type: ${reportType}, Period: ${fromDate} to ${toDate}", 
                'TransactionAuditController', null, null, 'tellering/generateAuditReport', session.user_id)
            
            render(reportData as JSON)
            
        } catch (Exception e) {
            log.error("Error generating audit report", e)
            render([success: false, error: 'Error generating audit report'] as JSON)
        }
    }
    
    /**
     * View transaction exceptions
     */
    def viewTransactionExceptions() {
        log.info("Viewing transaction exceptions")
        
        try {
            Date fromDate = params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 7
            Date toDate = params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date()
            
            List<Map> exceptions = getTransactionExceptions(fromDate, toDate)
            
            // Audit logging
            auditLogService.insert('110', 'TAU01600', 
                "Transaction exceptions viewed - Period: ${fromDate} to ${toDate}", 
                'TransactionAuditController', null, null, 'tellering/viewTransactionExceptions', session.user_id)
            
            render(view: '/tellering/audit/transactionExceptions', 
                model: [
                    exceptions: exceptions,
                    fromDate: fromDate,
                    toDate: toDate
                ])
            
        } catch (Exception e) {
            log.error("Error viewing transaction exceptions", e)
            flash.message = 'Error viewing transaction exceptions|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    /**
     * View failed transactions
     */
    def viewFailedTransactions() {
        log.info("Viewing failed transactions")
        
        try {
            Date fromDate = params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 7
            Date toDate = params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date()
            
            List<TxnFile> failedTransactions = getFailedTransactions(fromDate, toDate)
            
            // Audit logging
            auditLogService.insert('110', 'TAU01700', 
                "Failed transactions viewed - Period: ${fromDate} to ${toDate}", 
                'TransactionAuditController', null, null, 'tellering/viewFailedTransactions', session.user_id)
            
            render(view: '/tellering/audit/failedTransactions', 
                model: [
                    failedTransactions: failedTransactions,
                    fromDate: fromDate,
                    toDate: toDate
                ])
            
        } catch (Exception e) {
            log.error("Error viewing failed transactions", e)
            flash.message = 'Error viewing failed transactions|error|alert'
            redirect(controller: 'tellering', action: 'index')
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Get audit logs for specific transaction
     */
    private List<AuditLog> getTransactionAuditLogs(Long txnId) {
        try {
            return AuditLog.createCriteria().list {
                or {
                    eq("entityId", txnId.toString())
                    like("description", "%${txnId}%")
                }
                order("dateCreated", "desc")
            }
        } catch (Exception e) {
            log.error("Error getting transaction audit logs", e)
            return []
        }
    }
    
    /**
     * Generate transaction audit summary
     */
    private Map generateTransactionAuditSummary(Date fromDate, Date toDate) {
        Map summary = [:]
        
        try {
            def sql = new Sql(dataSource)
            
            // Total transactions
            def totalTxnQuery = """
                SELECT COUNT(*) as total_count, SUM(txn_amt) as total_amount
                FROM txn_file 
                WHERE txn_date BETWEEN ? AND ?
            """
            def totalResult = sql.firstRow(totalTxnQuery, [fromDate, toDate])
            summary.totalTransactions = totalResult.total_count
            summary.totalAmount = totalResult.total_amount ?: 0
            
            // Transactions by status
            def statusQuery = """
                SELECT s.description, COUNT(*) as count, SUM(t.txn_amt) as amount
                FROM txn_file t
                JOIN config_item_status s ON t.status_id = s.id
                WHERE t.txn_date BETWEEN ? AND ?
                GROUP BY s.description
            """
            summary.byStatus = sql.rows(statusQuery, [fromDate, toDate])
            
            // Transactions by type
            def typeQuery = """
                SELECT tt.description, COUNT(*) as count, SUM(t.txn_amt) as amount
                FROM txn_file t
                JOIN txn_type tt ON t.txn_type_id = tt.id
                WHERE t.txn_date BETWEEN ? AND ?
                GROUP BY tt.description
            """
            summary.byType = sql.rows(typeQuery, [fromDate, toDate])
            
            // Failed transactions
            def failedQuery = """
                SELECT COUNT(*) as failed_count
                FROM txn_file 
                WHERE txn_date BETWEEN ? AND ? AND status_id = 4
            """
            def failedResult = sql.firstRow(failedQuery, [fromDate, toDate])
            summary.failedTransactions = failedResult.failed_count
            
            // Reversed transactions
            def reversedQuery = """
                SELECT COUNT(*) as reversed_count
                FROM txn_reversal r
                JOIN txn_file t ON r.txn_file_id = t.id
                WHERE t.txn_date BETWEEN ? AND ?
            """
            def reversedResult = sql.firstRow(reversedQuery, [fromDate, toDate])
            summary.reversedTransactions = reversedResult.reversed_count
            
        } catch (Exception e) {
            log.error("Error generating transaction audit summary", e)
            summary.error = "Error generating audit summary"
        }
        
        return summary
    }
    
    /**
     * Generate user activity summary
     */
    private Map generateUserActivitySummary(UserMaster user, Date fromDate, Date toDate) {
        Map activity = [:]
        
        try {
            // User transactions
            List<TxnFile> userTransactions = TxnFile.createCriteria().list {
                eq("user", user)
                between("txnDate", fromDate, toDate)
                order("txnDate", "desc")
            }
            
            activity.totalTransactions = userTransactions.size()
            activity.totalAmount = userTransactions.sum { it.txnAmt } ?: 0
            
            // Transactions by type
            activity.byType = userTransactions.groupBy { it.txnType?.description }
                .collectEntries { type, txns ->
                    [type, [count: txns.size(), amount: txns.sum { it.txnAmt } ?: 0]]
                }
            
            // Daily activity
            activity.dailyActivity = userTransactions.groupBy { 
                it.txnDate.format('yyyy-MM-dd') 
            }.collectEntries { date, txns ->
                [date, [count: txns.size(), amount: txns.sum { it.txnAmt } ?: 0]]
            }
            
            // Audit logs for user
            List<AuditLog> userAuditLogs = AuditLog.createCriteria().list {
                eq("userId", user.id.toString())
                between("dateCreated", fromDate, toDate)
                order("dateCreated", "desc")
                maxResults(100)
            }
            
            activity.auditLogs = userAuditLogs
            activity.loginCount = userAuditLogs.count { it.actionCode == 'LOGIN' }
            activity.logoutCount = userAuditLogs.count { it.actionCode == 'LOGOUT' }
            
        } catch (Exception e) {
            log.error("Error generating user activity summary", e)
            activity.error = "Error generating user activity summary"
        }
        
        return activity
    }
    
    /**
     * Search audit logs with criteria
     */
    private List<AuditLog> searchAuditLogsWithCriteria(Map criteria) {
        try {
            return AuditLog.createCriteria().list {
                between("dateCreated", criteria.fromDate, criteria.toDate)
                
                if (criteria.userId) {
                    eq("userId", criteria.userId.toString())
                }
                
                if (criteria.actionCode) {
                    eq("actionCode", criteria.actionCode)
                }
                
                if (criteria.entityType) {
                    eq("entityType", criteria.entityType)
                }
                
                if (criteria.searchText) {
                    or {
                        ilike("description", "%${criteria.searchText}%")
                        ilike("entityId", "%${criteria.searchText}%")
                    }
                }
                
                order("dateCreated", "desc")
                maxResults(1000)
            }
        } catch (Exception e) {
            log.error("Error searching audit logs", e)
            return []
        }
    }
    
    /**
     * Generate audit report data
     */
    private Map generateAuditReportData(Date fromDate, Date toDate, String reportType) {
        Map reportData = [:]
        
        try {
            switch (reportType) {
                case 'TRANSACTION_AUDIT':
                    reportData = generateTransactionAuditSummary(fromDate, toDate)
                    break
                case 'USER_ACTIVITY':
                    reportData = generateAllUsersActivitySummary(fromDate, toDate)
                    break
                case 'SYSTEM_AUDIT':
                    reportData = generateSystemAuditSummary(fromDate, toDate)
                    break
                default:
                    reportData = generateTransactionAuditSummary(fromDate, toDate)
            }
            
            reportData.reportType = reportType
            reportData.fromDate = fromDate
            reportData.toDate = toDate
            reportData.generatedBy = UserMaster.get(session.user_id).username
            reportData.generatedDate = new Date()
            
        } catch (Exception e) {
            log.error("Error generating audit report data", e)
            reportData.error = "Error generating audit report data"
        }
        
        return reportData
    }
    
    /**
     * Get transaction exceptions
     */
    private List<Map> getTransactionExceptions(Date fromDate, Date toDate) {
        try {
            def sql = new Sql(dataSource)
            def query = """
                SELECT t.id, t.txn_date, t.txn_amt, t.txn_ref, t.txn_particulars,
                       tt.description as txn_type, s.description as status,
                       u.username, pe.exception_code, pe.exception_description
                FROM txn_file t
                JOIN txn_type tt ON t.txn_type_id = tt.id
                JOIN config_item_status s ON t.status_id = s.id
                JOIN user_master u ON t.user_id = u.id
                LEFT JOIN policy_exception pe ON pe.record_id = t.id AND pe.entity_type = 'txnFile'
                WHERE t.txn_date BETWEEN ? AND ? 
                  AND (t.status_id = 1 OR pe.id IS NOT NULL)
                ORDER BY t.txn_date DESC
            """
            
            return sql.rows(query, [fromDate, toDate])
        } catch (Exception e) {
            log.error("Error getting transaction exceptions", e)
            return []
        }
    }
    
    /**
     * Get failed transactions
     */
    private List<TxnFile> getFailedTransactions(Date fromDate, Date toDate) {
        try {
            return TxnFile.createCriteria().list {
                between("txnDate", fromDate, toDate)
                eq("status", ConfigItemStatus.get(4)) // Failed status
                order("txnDate", "desc")
                maxResults(500)
            }
        } catch (Exception e) {
            log.error("Error getting failed transactions", e)
            return []
        }
    }
    
    /**
     * Generate all users activity summary
     */
    private Map generateAllUsersActivitySummary(Date fromDate, Date toDate) {
        Map summary = [:]
        
        try {
            def sql = new Sql(dataSource)
            def query = """
                SELECT u.username, COUNT(t.id) as txn_count, SUM(t.txn_amt) as total_amount
                FROM user_master u
                LEFT JOIN txn_file t ON u.id = t.user_id AND t.txn_date BETWEEN ? AND ?
                WHERE u.config_item_status_id = 2
                GROUP BY u.username
                ORDER BY txn_count DESC
            """
            
            summary.userActivity = sql.rows(query, [fromDate, toDate])
            
        } catch (Exception e) {
            log.error("Error generating all users activity summary", e)
            summary.error = "Error generating users activity summary"
        }
        
        return summary
    }
    
    /**
     * Generate system audit summary
     */
    private Map generateSystemAuditSummary(Date fromDate, Date toDate) {
        Map summary = [:]
        
        try {
            // System-level audit information
            summary.systemEvents = AuditLog.createCriteria().list {
                between("dateCreated", fromDate, toDate)
                in("actionCode", ["SYSTEM_START", "SYSTEM_STOP", "EOD_START", "EOD_END"])
                order("dateCreated", "desc")
            }
            
            summary.errorCount = AuditLog.createCriteria().count {
                between("dateCreated", fromDate, toDate)
                like("actionCode", "ERROR%")
            }
            
        } catch (Exception e) {
            log.error("Error generating system audit summary", e)
            summary.error = "Error generating system audit summary"
        }
        
        return summary
    }
}
