package org.icbs.home

import org.icbs.admin.UserMessage
import org.icbs.admin.UserMaster

import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnCashCheckBlotter
import org.icbs.tellering.TxnBillsPayment
import org.icbs.admin.Branch
								 


class HomeController {

	def landing() {
            //println "landing always"
            //println "Timeout: ${session.getMaxInactiveInterval()} seconds"
            
        def tbCash = TxnCashCheckBlotter.createCriteria().list() {
            and{
                eq("user",UserMaster.get(session.user_id))   
                //eq("txnFile.txnDate",Branch.get(1).runDate)
            }
            order("txnFile", "asc")
        }  
        def userInstance = UserMaster.get(session.user_id)
            respond UserMessage.findAllByRecipientAndIsRead(UserMaster.get(session.user_id), false, [max:10]), model:[params:params,
                    UserMessageInstanceCount:  UserMessage.count(),userInstance:userInstance,tbCash:tbCash]   
            return
            
	}
        
}
