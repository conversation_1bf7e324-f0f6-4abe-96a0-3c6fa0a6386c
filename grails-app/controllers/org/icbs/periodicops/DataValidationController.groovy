package org.icbs.periodicops

import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.grails.web.json.JSONObject
import groovy.time.TimeCategory
import org.icbs.admin.UserMaster
import java.nio.file.*
import org.icbs.admin.Branch
import org.icbs.admin.Holiday
import org.icbs.admin.UserMaster
import org.icbs.admin.CheckDepositClearingType
import org.icbs.admin.Institution
import org.icbs.admin.BranchHoliday
import org.icbs.security.UserSession
import org.icbs.lov.BranchRunStatus
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.gl.GlBatchHdr
import org.icbs.gl.GlBatch
import org.icbs.gl.GlDailyFile
import org.icbs.gl.GlMonthlyBalance
import org.icbs.gl.GlAccount
import org.icbs.deposit.Deposit
import org.icbs.periodicops.PeriodicOpsLog
import org.icbs.periodicops.DailyLoanRecoveries
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositStatus
import org.icbs.lov.DepositType
import org.icbs.loans.Loan
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanInstallmentStatus
import org.icbs.lov.GlBatchHdrStatus
import org.icbs.loans.Loan
import org.icbs.loans.LoanLedger
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnPassbookLine
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.cif.Customer
import grails.gorm.transactions.Transactional
import org.apache.commons.io.FileUtils
import org.apache.tools.zip.ZipEntry
import java.text.SimpleDateFormat
import java.util.zip.ZipOutputStream
import java.util.Calendar
import org.icbs.admin.UserMessage
import static grails.async.Promises.*
import groovy.sql.Sql
import org.icbs.tellering.TxnCOCI
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.lov.CheckStatus
import org.icbs.lov.TxnCheckStatus
import org.icbs.periodicops.DailyBalance
import org.icbs.periodicops.MonthlyBalance
import org.icbs.periodicops.MonthlyPointerLoan
import org.icbs.periodicops.MonthlyCustomer
import org.icbs.periodicops.DailyCheckClearing

/**
 * DataValidationController - Handles data validation and EOD checks
 * 
 * This controller manages the validation processes including:
 * - End of Day validation checks
 * - Teller balance validation
 * - User session validation
 * - GL batch validation
 * - Loan disbursement validation
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class DataValidationController {
    
    // Service Dependencies
    def periodicOpsService
    def loanPeriodicOpsService
    def IsTelleringActiveService
    def depositPeriodicOpsService
    def jasperService
    def AuditLogService
    def GlTransactionService
    def UserMasterService
    def sessionFactory
    def dataSource
    def glPeriodicOpsService

    /**
     * Display the data validation dashboard
     * @return rendered view with validation results
     */
    def index() {
        try {
            def runDate = Branch.read(1).runDate
            def validationResults = performAllValidations()
            
            render(view: "index", model: [
                currentDate: runDate,
                validationResults: validationResults
            ])
        } catch (Exception e) {
            log.error("Error loading data validation index: ${e.message}", e)
            flash.message = "Error loading data validation page |error|alert"
            redirect(controller: "periodicOps", action: "index")
        }
    }

    /**
     * Perform End of Day validation checks
     * @return rendered view with EOD check results
     */
    def EODCheck() {
        try {
            log.info("Performing EOD validation checks")
            
            // Get unbalanced tellers
            def userMasterInstanceList = UserMaster.createCriteria().list(params) {
                and {
                    eq("isTellerBalanced", false)
                    eq("configItemStatus", ConfigItemStatus.read(2))
                }
            }
            
            // Get unbalanced cash transactions
            def txnCashList = TxnTellerBalance.createCriteria().list() {
                and {
                    neProperty("cashIn", "cashOut")
                    eq("txnDate", Branch.get(1).runDate)
                }
            }
            
            // Get loans with disbursement issues
            def loanInstanceList = Loan.createCriteria().list() {
                and {
                    neProperty("totalDisbursementAmount", "totalNetProceeds")
                    eq("status", LoanAcctStatus.get(4))
                }
            }
            
            // Get logged users (excluding current user)
            def loggedUserList = UserSession.createCriteria().list() {
                and {
                    ne("userMaster", UserMaster.get(session.user_id))
                    isNull("logout")
                }
            }
            
            // Get unposted GL batches
            def unpostedGlList = GlBatchHdr.createCriteria().list() {
                and {
                    eq("txnDate", Branch.get(1).runDate)
                    lt("status", GlBatchHdrStatus.get(3))
                }
            }
            
            render(view: '/periodicOps/EODCheck', model: [
                userMasterInstanceList: userMasterInstanceList,
                UserMasterInstanceCount: userMasterInstanceList.totalCount,
                loanInstanceList: loanInstanceList,
                txnCashList: txnCashList,
                loggedUserList: loggedUserList,
                unpostedGlList: unpostedGlList
            ])
            
        } catch (Exception e) {
            log.error("Error performing EOD check: ${e.message}", e)
            flash.message = "Error performing EOD validation: ${e.message} |error|alert"
            redirect(action: "index")
        }
    }

    /**
     * Validate teller balances
     * @return JSON response with teller balance validation results
     */
    def validateTellerBalances() {
        try {
            def unbalancedTellers = UserMaster.createCriteria().list {
                and {
                    eq("isTellerBalanced", false)
                    eq("configItemStatus", ConfigItemStatus.read(2))
                }
            }
            
            def tellerValidation = [
                isValid: unbalancedTellers.isEmpty(),
                unbalancedCount: unbalancedTellers.size(),
                unbalancedTellers: unbalancedTellers.collect { teller ->
                    [
                        id: teller.id,
                        username: teller.username,
                        name: teller.firstName + " " + teller.lastName,
                        branch: teller.branch?.name
                    ]
                },
                message: unbalancedTellers.isEmpty() ? 
                    "All tellers are balanced" : 
                    "${unbalancedTellers.size()} teller(s) not balanced"
            ]
            
            render tellerValidation as JSON
            
        } catch (Exception e) {
            log.error("Error validating teller balances: ${e.message}", e)
            render([
                isValid: false,
                error: "Unable to validate teller balances: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate user sessions
     * @return JSON response with user session validation results
     */
    def validateUserSessions() {
        try {
            def activeUsers = UserSession.createCriteria().list {
                and {
                    ne("userMaster", UserMaster.get(session.user_id))
                    isNull("logout")
                }
            }
            
            def sessionValidation = [
                isValid: activeUsers.isEmpty(),
                activeUserCount: activeUsers.size(),
                activeUsers: activeUsers.collect { userSession ->
                    [
                        id: userSession.id,
                        username: userSession.userMaster?.username,
                        loginTime: userSession.login,
                        ipAddress: userSession.ipAddress
                    ]
                },
                message: activeUsers.isEmpty() ? 
                    "No other users logged in" : 
                    "${activeUsers.size()} user(s) still logged in"
            ]
            
            render sessionValidation as JSON
            
        } catch (Exception e) {
            log.error("Error validating user sessions: ${e.message}", e)
            render([
                isValid: false,
                error: "Unable to validate user sessions: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate GL batches
     * @return JSON response with GL batch validation results
     */
    def validateGLBatches() {
        try {
            def unpostedBatches = GlBatchHdr.createCriteria().list {
                and {
                    eq("txnDate", Branch.get(1).runDate)
                    lt("status", GlBatchHdrStatus.get(3))
                }
            }
            
            def glValidation = [
                isValid: unpostedBatches.isEmpty(),
                unpostedCount: unpostedBatches.size(),
                unpostedBatches: unpostedBatches.collect { batch ->
                    [
                        id: batch.id,
                        batchNo: batch.batchNo,
                        description: batch.description,
                        status: batch.status?.name,
                        amount: batch.totalAmount
                    ]
                },
                message: unpostedBatches.isEmpty() ? 
                    "All GL batches posted" : 
                    "${unpostedBatches.size()} GL batch(es) not posted"
            ]
            
            render glValidation as JSON
            
        } catch (Exception e) {
            log.error("Error validating GL batches: ${e.message}", e)
            render([
                isValid: false,
                error: "Unable to validate GL batches: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate loan disbursements
     * @return JSON response with loan disbursement validation results
     */
    def validateLoanDisbursements() {
        try {
            def problematicLoans = Loan.createCriteria().list {
                and {
                    neProperty("totalDisbursementAmount", "totalNetProceeds")
                    eq("status", LoanAcctStatus.get(4))
                }
            }
            
            def loanValidation = [
                isValid: problematicLoans.isEmpty(),
                problematicCount: problematicLoans.size(),
                problematicLoans: problematicLoans.collect { loan ->
                    [
                        id: loan.id,
                        accountNo: loan.accountNo,
                        customer: loan.customer?.displayName,
                        disbursementAmount: loan.totalDisbursementAmount,
                        netProceeds: loan.totalNetProceeds,
                        difference: loan.totalDisbursementAmount - loan.totalNetProceeds
                    ]
                },
                message: problematicLoans.isEmpty() ? 
                    "All loan disbursements balanced" : 
                    "${problematicLoans.size()} loan(s) with disbursement issues"
            ]
            
            render loanValidation as JSON
            
        } catch (Exception e) {
            log.error("Error validating loan disbursements: ${e.message}", e)
            render([
                isValid: false,
                error: "Unable to validate loan disbursements: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate cash balances
     * @return JSON response with cash balance validation results
     */
    def validateCashBalances() {
        try {
            def unbalancedCash = TxnTellerBalance.createCriteria().list {
                and {
                    neProperty("cashIn", "cashOut")
                    eq("txnDate", Branch.get(1).runDate)
                }
            }
            
            def cashValidation = [
                isValid: unbalancedCash.isEmpty(),
                unbalancedCount: unbalancedCash.size(),
                unbalancedCash: unbalancedCash.collect { cash ->
                    [
                        id: cash.id,
                        user: cash.user?.username,
                        currency: cash.currency?.code,
                        cashIn: cash.cashIn,
                        cashOut: cash.cashOut,
                        difference: cash.cashIn - cash.cashOut
                    ]
                },
                message: unbalancedCash.isEmpty() ? 
                    "All cash balances are balanced" : 
                    "${unbalancedCash.size()} cash balance(s) not balanced"
            ]
            
            render cashValidation as JSON
            
        } catch (Exception e) {
            log.error("Error validating cash balances: ${e.message}", e)
            render([
                isValid: false,
                error: "Unable to validate cash balances: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Perform comprehensive validation check
     * @return JSON response with comprehensive validation results
     */
    def performComprehensiveValidation() {
        try {
            def validationResults = performAllValidations()
            
            def overallValid = validationResults.every { key, result ->
                result.isValid
            }
            
            def response = [
                overallValid: overallValid,
                validationResults: validationResults,
                summary: generateValidationSummary(validationResults),
                timestamp: new Date()
            ]
            
            render response as JSON
            
        } catch (Exception e) {
            log.error("Error performing comprehensive validation: ${e.message}", e)
            render([
                overallValid: false,
                error: "Unable to perform comprehensive validation: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Perform all validation checks
     * @return Map containing all validation results
     */
    private Map performAllValidations() {
        try {
            def validations = [:]
            
            // Teller balance validation
            validations.tellerBalances = validateTellerBalancesInternal()
            
            // User session validation
            validations.userSessions = validateUserSessionsInternal()
            
            // GL batch validation
            validations.glBatches = validateGLBatchesInternal()
            
            // Loan disbursement validation
            validations.loanDisbursements = validateLoanDisbursementsInternal()
            
            // Cash balance validation
            validations.cashBalances = validateCashBalancesInternal()
            
            return validations
            
        } catch (Exception e) {
            log.error("Error performing all validations: ${e.message}", e)
            return [error: "Unable to perform validations: ${e.message}"]
        }
    }

    /**
     * Internal method to validate teller balances
     * @return Map containing teller balance validation results
     */
    private Map validateTellerBalancesInternal() {
        try {
            def unbalancedTellers = UserMaster.createCriteria().list {
                and {
                    eq("isTellerBalanced", false)
                    eq("configItemStatus", ConfigItemStatus.read(2))
                }
            }
            
            return [
                isValid: unbalancedTellers.isEmpty(),
                count: unbalancedTellers.size(),
                message: unbalancedTellers.isEmpty() ? 
                    "All tellers balanced" : 
                    "${unbalancedTellers.size()} teller(s) not balanced"
            ]
        } catch (Exception e) {
            return [isValid: false, error: e.message]
        }
    }

    /**
     * Internal method to validate user sessions
     * @return Map containing user session validation results
     */
    private Map validateUserSessionsInternal() {
        try {
            def activeUsers = UserSession.createCriteria().list {
                and {
                    ne("userMaster", UserMaster.get(session.user_id))
                    isNull("logout")
                }
            }
            
            return [
                isValid: activeUsers.isEmpty(),
                count: activeUsers.size(),
                message: activeUsers.isEmpty() ? 
                    "No other users logged in" : 
                    "${activeUsers.size()} user(s) still logged in"
            ]
        } catch (Exception e) {
            return [isValid: false, error: e.message]
        }
    }

    /**
     * Internal method to validate GL batches
     * @return Map containing GL batch validation results
     */
    private Map validateGLBatchesInternal() {
        try {
            def unpostedBatches = GlBatchHdr.createCriteria().list {
                and {
                    eq("txnDate", Branch.get(1).runDate)
                    lt("status", GlBatchHdrStatus.get(3))
                }
            }
            
            return [
                isValid: unpostedBatches.isEmpty(),
                count: unpostedBatches.size(),
                message: unpostedBatches.isEmpty() ? 
                    "All GL batches posted" : 
                    "${unpostedBatches.size()} GL batch(es) not posted"
            ]
        } catch (Exception e) {
            return [isValid: false, error: e.message]
        }
    }

    /**
     * Internal method to validate loan disbursements
     * @return Map containing loan disbursement validation results
     */
    private Map validateLoanDisbursementsInternal() {
        try {
            def problematicLoans = Loan.createCriteria().list {
                and {
                    neProperty("totalDisbursementAmount", "totalNetProceeds")
                    eq("status", LoanAcctStatus.get(4))
                }
            }
            
            return [
                isValid: problematicLoans.isEmpty(),
                count: problematicLoans.size(),
                message: problematicLoans.isEmpty() ? 
                    "All loan disbursements balanced" : 
                    "${problematicLoans.size()} loan(s) with disbursement issues"
            ]
        } catch (Exception e) {
            return [isValid: false, error: e.message]
        }
    }

    /**
     * Internal method to validate cash balances
     * @return Map containing cash balance validation results
     */
    private Map validateCashBalancesInternal() {
        try {
            def unbalancedCash = TxnTellerBalance.createCriteria().list {
                and {
                    neProperty("cashIn", "cashOut")
                    eq("txnDate", Branch.get(1).runDate)
                }
            }
            
            return [
                isValid: unbalancedCash.isEmpty(),
                count: unbalancedCash.size(),
                message: unbalancedCash.isEmpty() ? 
                    "All cash balances balanced" : 
                    "${unbalancedCash.size()} cash balance(s) not balanced"
            ]
        } catch (Exception e) {
            return [isValid: false, error: e.message]
        }
    }

    /**
     * Generate validation summary
     * @param validationResults The validation results
     * @return String containing validation summary
     */
    private String generateValidationSummary(Map validationResults) {
        try {
            def validCount = validationResults.count { key, result -> result.isValid }
            def totalCount = validationResults.size()
            
            if (validCount == totalCount) {
                return "All validations passed successfully"
            } else {
                def failedCount = totalCount - validCount
                return "${failedCount} validation(s) failed out of ${totalCount}"
            }
        } catch (Exception e) {
            return "Unable to generate summary"
        }
    }
}
