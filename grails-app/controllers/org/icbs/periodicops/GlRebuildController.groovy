package org.icbs.periodicops

import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.grails.web.json.JSONObject
import groovy.time.TimeCategory
import org.icbs.admin.UserMaster
import java.nio.file.*
import org.icbs.admin.Branch
import org.icbs.admin.Holiday
import org.icbs.admin.UserMaster
import org.icbs.admin.CheckDepositClearingType
import org.icbs.admin.Institution
import org.icbs.admin.BranchHoliday
import org.icbs.security.UserSession
import org.icbs.lov.BranchRunStatus
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.gl.GlBatchHdr
import org.icbs.gl.GlBatch
import org.icbs.gl.GlDailyFile
import org.icbs.gl.GlMonthlyBalance
import org.icbs.gl.GlAccount
import org.icbs.deposit.Deposit
import org.icbs.periodicops.PeriodicOpsLog
import org.icbs.periodicops.DailyLoanRecoveries
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositStatus
import org.icbs.lov.DepositType
import org.icbs.loans.Loan
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanInstallmentStatus
import org.icbs.lov.GlBatchHdrStatus
import org.icbs.loans.Loan
import org.icbs.loans.LoanLedger
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnPassbookLine
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.cif.Customer
import grails.gorm.transactions.Transactional
import org.apache.commons.io.FileUtils
import org.apache.tools.zip.ZipEntry
import java.text.SimpleDateFormat
import java.util.zip.ZipOutputStream
import java.util.Calendar
import org.icbs.admin.UserMessage
import static grails.async.Promises.*
import groovy.sql.Sql
import org.icbs.tellering.TxnCOCI
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.lov.CheckStatus
import org.icbs.lov.TxnCheckStatus
import org.icbs.periodicops.DailyBalance
import org.icbs.periodicops.MonthlyBalance
import org.icbs.periodicops.MonthlyPointerLoan
import org.icbs.periodicops.MonthlyCustomer
import org.icbs.periodicops.DailyCheckClearing

/**
 * GlRebuildController - Handles GL account rebuilding operations
 * 
 * This controller manages GL rebuilding processes including:
 * - GL account balance rebuilding
 * - GL transaction reconciliation
 * - GL account validation
 * - GL balance recalculation
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class GlRebuildController {
    
    // Service Dependencies
    def periodicOpsService
    def loanPeriodicOpsService
    def IsTelleringActiveService
    def depositPeriodicOpsService
    def jasperService
    def AuditLogService
    def GlTransactionService
    def UserMasterService
    def sessionFactory
    def dataSource
    def glPeriodicOpsService

    /**
     * Display the GL rebuild index page
     * @return rendered view with GL rebuild options
     */
    def index() {
        try {
            def runDate = Branch.read(1).runDate
            render(view: "index", model: [currentDate: runDate])
        } catch (Exception e) {
            log.error("Error loading GL rebuild index: ${e.message}", e)
            flash.message = "Error loading GL rebuild page |error|alert"
            redirect(controller: "periodicOps", action: "index")
        }
    }

    /**
     * Display the GL rebuild form
     */
    def rebuildGl() {
        try {
            def runDate = Branch.read(1).runDate
            render(view: "rebuildGl", model: [currentDate: runDate])
        } catch (Exception e) {
            log.error("Error loading GL rebuild form: ${e.message}", e)
            flash.message = "Error loading GL rebuild form |error|alert"
            redirect(action: "index")
        }
    }

    /**
     * Save and execute GL rebuild operation
     */
    @Transactional
    def saveNewGl() {
        try {
            log.info("Starting GL rebuild process with params: ${params}")
            
            // Parse and validate cut-off date
            SimpleDateFormat format = new SimpleDateFormat("MM/dd/yyyy")
            Date cutDate = format.parse(params.cutOffDate)
            Date endDate = Branch.get(1).runDate
            
            // Validate cut-off date
            if (!validateCutOffDate(cutDate, endDate)) {
                return
            }
            
            // Perform GL rebuild
            def rebuildResult = performGlRebuild(cutDate)
            
            if (rebuildResult.success) {
                // Log the operation
                AuditLogService.insert('850', 'GL00850', 
                    "GL rebuild completed for cut-off date: ${cutDate} - ${rebuildResult.accountsProcessed} accounts processed", 
                    'GL', null, null, null, null)
                
                flash.message = "GL rebuild completed successfully - ${rebuildResult.accountsProcessed} accounts processed |success|alert"
            } else {
                flash.message = "GL rebuild failed: ${rebuildResult.message} |error|alert"
            }
            
        } catch (Exception e) {
            log.error("Error during GL rebuild: ${e.message}", e)
            flash.message = "GL rebuild failed due to error: ${e.message} |error|alert"
        }
        
        redirect(action: "index")
    }

    /**
     * Get GL rebuild progress
     * @return JSON response with rebuild progress
     */
    def getGlRebuildProgress() {
        try {
            def progress = [
                progress: session.glRebuildProgress ?: "0",
                message: session.glRebuildMessage ?: "Ready",
                status: session.glRebuildStatus ?: "ready",
                accountsProcessed: session.glAccountsProcessed ?: 0,
                totalAccounts: session.glTotalAccounts ?: 0,
                timestamp: new Date()
            ]
            
            render progress as JSON
            
        } catch (Exception e) {
            log.error("Error getting GL rebuild progress: ${e.message}", e)
            render([error: "Unable to get rebuild progress"] as JSON)
        }
    }

    /**
     * Validate GL accounts
     * @return JSON response with validation results
     */
    def validateGlAccounts() {
        try {
            def currentDate = Branch.get(1).runDate
            def validationResults = performGlValidation(currentDate)
            
            render validationResults as JSON
            
        } catch (Exception e) {
            log.error("Error validating GL accounts: ${e.message}", e)
            render([
                isValid: false,
                error: "GL validation failed: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Recalculate GL balances for specific date range
     */
    @Transactional
    def recalculateGlBalances() {
        try {
            def startDate = params.startDate ? Date.parse("MM/dd/yyyy", params.startDate) : null
            def endDate = params.endDate ? Date.parse("MM/dd/yyyy", params.endDate) : null
            
            if (!startDate || !endDate) {
                render([
                    success: false,
                    error: "Start date and end date are required"
                ] as JSON)
                return
            }
            
            def recalcResult = recalculateBalances(startDate, endDate)
            
            if (recalcResult.success) {
                AuditLogService.insert('851', 'GL00851', 
                    "GL balance recalculation completed for period ${startDate} to ${endDate} - ${recalcResult.accountsProcessed} accounts", 
                    'GL', null, null, null, null)
            }
            
            render recalcResult as JSON
            
        } catch (Exception e) {
            log.error("Error recalculating GL balances: ${e.message}", e)
            render([
                success: false,
                error: "Balance recalculation failed: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Validate cut-off date
     * @param cutDate The cut-off date to validate
     * @param endDate The current system date
     * @return true if valid
     */
    private boolean validateCutOffDate(Date cutDate, Date endDate) {
        try {
            if (cutDate >= endDate) {
                flash.message = "Invalid system date - cut-off date cannot be greater than or equal to current date |error|alert"
                render(view: "rebuildGl")
                return false
            }
            
            if (cutDate.format('yyyy') != endDate.format('yyyy')) {
                flash.message = "Rebuild only allowed for current financial year |error|alert"
                render(view: "rebuildGl")
                return false
            }
            
            return true
            
        } catch (Exception e) {
            log.error("Error validating cut-off date: ${e.message}", e)
            flash.message = "Date validation failed: ${e.message} |error|alert"
            render(view: "rebuildGl")
            return false
        }
    }

    /**
     * Perform GL rebuild operation
     * @param cutDate The cut-off date for rebuild
     * @return Map containing rebuild results
     */
    private Map performGlRebuild(Date cutDate) {
        try {
            def db = new Sql(dataSource)
            
            // Set up date range
            def firstDay = new Date(cutDate.year, cutDate.month, 1).format('yyyy-MM-dd')
            def lastDay = cutDate.format('yyyy-MM-dd')
            
            // Build SQL query for GL accounts with transactions
            def sqlstmt = "select X.id, " +
                "A.debit_balance as debitBal, A.credit_balance as creditBal, " +
                "sum(B.debit_amount) as debits, sum(B.credit_amount) as credits " +
                "from gl_account X " +
                "left outer join gl_daily_file A on X.id = A.gl_acct_id and A.ref_date = '" + firstDay + "' " +
                "left outer join gl_txn_file B on X.code = B.gl_account_code and " +
                "B.txn_date > '" + firstDay + "' and B.txn_date <= '" + lastDay + "' " +
                "where X.financial_year = " + cutDate.format('yyyy').toInteger() + " " +
                "group by X.id, A.debit_balance, A.credit_balance " +
                "order by X.id"
            
            def glAccounts = db.rows(sqlstmt)
            def totalAccounts = glAccounts.size()
            def processedAccounts = 0
            
            // Initialize progress tracking
            session.glTotalAccounts = totalAccounts
            session.glAccountsProcessed = 0
            session.glRebuildStatus = "processing"
            
            for (glRow in glAccounts) {
                try {
                    processGlAccount(glRow)
                    processedAccounts++
                    
                    // Update progress
                    session.glAccountsProcessed = processedAccounts
                    def progressPercent = (processedAccounts * 100 / totalAccounts).intValue()
                    session.glRebuildProgress = progressPercent.toString()
                    session.glRebuildMessage = "Processing account ${processedAccounts} of ${totalAccounts}"
                    
                    if (processedAccounts % 100 == 0) {
                        cleanUpGorm()
                    }
                    
                } catch (Exception e) {
                    log.error("Error processing GL account ${glRow.id}: ${e.message}", e)
                }
            }
            
            // Complete progress tracking
            session.glRebuildStatus = "completed"
            session.glRebuildProgress = "100"
            session.glRebuildMessage = "GL rebuild completed"
            
            return [
                success: true,
                accountsProcessed: processedAccounts,
                totalAccounts: totalAccounts,
                message: "GL rebuild completed successfully"
            ]
            
        } catch (Exception e) {
            log.error("Error performing GL rebuild: ${e.message}", e)
            session.glRebuildStatus = "error"
            session.glRebuildMessage = "GL rebuild failed: ${e.message}"
            
            return [
                success: false,
                message: "GL rebuild failed: ${e.message}"
            ]
        }
    }

    /**
     * Process individual GL account
     * @param glRow The GL account row data
     */
    private void processGlAccount(def glRow) {
        try {
            def gla = GlAccount.get(glRow.id)
            
            // Handle null values
            def debitBal = glRow.debitBal ?: 0.00D
            def debits = glRow.debits ?: 0.00D
            def creditBal = glRow.creditBal ?: 0.00D
            def credits = glRow.credits ?: 0.00D
            
            // Calculate new balances
            def totalDebits = debitBal + debits
            def totalCredits = creditBal + credits
            
            if (totalDebits >= totalCredits) {
                // Debit balance
                gla.debitBalance = totalDebits - totalCredits
                gla.creditBalance = 0.00D
            } else {
                // Credit balance
                gla.debitBalance = 0.00D
                gla.creditBalance = totalCredits - totalDebits
            }
            
            gla.balance = totalDebits - totalCredits
            gla.save(flush: true)
            
        } catch (Exception e) {
            log.error("Error processing GL account ${glRow.id}: ${e.message}", e)
            throw e
        }
    }

    /**
     * Perform GL validation
     * @param currentDate The validation date
     * @return Map containing validation results
     */
    private Map performGlValidation(Date currentDate) {
        try {
            def db = new Sql(dataSource)
            def validationResults = [:]
            
            // Check for unbalanced GL accounts
            def unbalancedQuery = "select count(*) as count from gl_account where abs(debit_balance - credit_balance) > 0.01"
            def unbalancedCount = db.firstRow(unbalancedQuery).count
            
            // Check for GL accounts with negative balances where they shouldn't be
            def negativeQuery = "select count(*) as count from gl_account where balance < 0 and account_type in ('ASSET', 'EXPENSE')"
            def negativeCount = db.firstRow(negativeQuery).count
            
            // Check for missing GL daily files
            def missingDailyQuery = "select count(*) as count from gl_account ga where not exists (select 1 from gl_daily_file gdf where gdf.gl_acct_id = ga.id and gdf.ref_date = ?)"
            def missingDailyCount = db.firstRow(missingDailyQuery, [currentDate]).count
            
            validationResults = [
                isValid: (unbalancedCount == 0 && negativeCount == 0 && missingDailyCount == 0),
                unbalancedAccounts: unbalancedCount,
                negativeBalanceAccounts: negativeCount,
                missingDailyFiles: missingDailyCount,
                validationDate: currentDate,
                message: generateValidationMessage(unbalancedCount, negativeCount, missingDailyCount)
            ]
            
            return validationResults
            
        } catch (Exception e) {
            log.error("Error performing GL validation: ${e.message}", e)
            return [
                isValid: false,
                error: "GL validation failed: ${e.message}"
            ]
        }
    }

    /**
     * Recalculate balances for date range
     * @param startDate Start date for recalculation
     * @param endDate End date for recalculation
     * @return Map containing recalculation results
     */
    private Map recalculateBalances(Date startDate, Date endDate) {
        try {
            def db = new Sql(dataSource)
            def processedAccounts = 0
            
            // Get all GL accounts for the financial year
            def accountsQuery = "select id from gl_account where financial_year = ?"
            def accounts = db.rows(accountsQuery, [endDate.format('yyyy').toInteger()])
            
            for (accountRow in accounts) {
                try {
                    recalculateAccountBalance(accountRow.id, startDate, endDate)
                    processedAccounts++
                    
                    if (processedAccounts % 50 == 0) {
                        cleanUpGorm()
                    }
                    
                } catch (Exception e) {
                    log.error("Error recalculating account ${accountRow.id}: ${e.message}", e)
                }
            }
            
            return [
                success: true,
                accountsProcessed: processedAccounts,
                totalAccounts: accounts.size(),
                message: "Balance recalculation completed"
            ]
            
        } catch (Exception e) {
            log.error("Error recalculating balances: ${e.message}", e)
            return [
                success: false,
                message: "Balance recalculation failed: ${e.message}"
            ]
        }
    }

    /**
     * Recalculate balance for specific account
     * @param accountId The GL account ID
     * @param startDate Start date for recalculation
     * @param endDate End date for recalculation
     */
    private void recalculateAccountBalance(Long accountId, Date startDate, Date endDate) {
        try {
            def account = GlAccount.get(accountId)
            def db = new Sql(dataSource)
            
            // Get transaction totals for the period
            def txnQuery = "select sum(debit_amount) as debits, sum(credit_amount) as credits " +
                          "from gl_txn_file where gl_account_code = ? and txn_date between ? and ?"
            
            def txnResult = db.firstRow(txnQuery, [account.code, startDate, endDate])
            
            def debits = txnResult.debits ?: 0.00D
            def credits = txnResult.credits ?: 0.00D
            
            // Update account balance
            account.debitToday = debits
            account.creditToday = credits
            account.balance = account.balance + debits - credits
            
            if (account.balance > 0) {
                account.debitBalance = account.balance
                account.creditBalance = 0.00D
            } else {
                account.debitBalance = 0.00D
                account.creditBalance = account.balance.abs()
            }
            
            account.save(flush: true)
            
        } catch (Exception e) {
            log.error("Error recalculating account balance for ${accountId}: ${e.message}", e)
            throw e
        }
    }

    /**
     * Generate validation message
     * @param unbalanced Number of unbalanced accounts
     * @param negative Number of accounts with negative balances
     * @param missing Number of missing daily files
     * @return Validation message string
     */
    private String generateValidationMessage(int unbalanced, int negative, int missing) {
        if (unbalanced == 0 && negative == 0 && missing == 0) {
            return "All GL accounts are valid"
        }
        
        def messages = []
        if (unbalanced > 0) messages.add("${unbalanced} unbalanced account(s)")
        if (negative > 0) messages.add("${negative} account(s) with invalid negative balances")
        if (missing > 0) messages.add("${missing} account(s) missing daily files")
        
        return "Issues found: " + messages.join(", ")
    }

    /**
     * Clean up GORM session to prevent memory issues
     */
    private void cleanUpGorm() {
        try {
            def session = sessionFactory.currentSession
            session.flush()
            session.clear()
            def propertyInstanceMap = org.codehaus.groovy.grails.plugins.DomainClassGrailsPlugin.PROPERTY_INSTANCE_MAP
            propertyInstanceMap.get().clear()
        } catch (Exception e) {
            log.error("Error cleaning up GORM: ${e.message}", e)
        }
    }
}
