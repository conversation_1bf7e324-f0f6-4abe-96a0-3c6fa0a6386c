package org.icbs.periodicops

import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.grails.web.json.JSONObject
import groovy.time.TimeCategory
import org.icbs.admin.UserMaster
import java.nio.file.*
import org.icbs.admin.Branch
import org.icbs.admin.Holiday
import org.icbs.admin.UserMaster
import org.icbs.admin.CheckDepositClearingType
import org.icbs.admin.Institution
import org.icbs.admin.BranchHoliday
import org.icbs.security.UserSession
import org.icbs.lov.BranchRunStatus
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.gl.GlBatchHdr
import org.icbs.gl.GlBatch
import org.icbs.gl.GlDailyFile
import org.icbs.gl.GlMonthlyBalance
import org.icbs.gl.GlAccount
import org.icbs.deposit.Deposit
import org.icbs.periodicops.PeriodicOpsLog
import org.icbs.periodicops.DailyLoanRecoveries
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositStatus
import org.icbs.lov.DepositType
import org.icbs.loans.Loan
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanInstallmentStatus
import org.icbs.lov.GlBatchHdrStatus
import org.icbs.loans.Loan
import org.icbs.loans.LoanLedger
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnPassbookLine
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.cif.Customer
import grails.gorm.transactions.Transactional
import org.apache.commons.io.FileUtils
import org.apache.tools.zip.ZipEntry
import java.text.SimpleDateFormat
import java.util.zip.ZipOutputStream
import java.util.Calendar
import org.icbs.admin.UserMessage
import static grails.async.Promises.*
import groovy.sql.Sql
import org.icbs.tellering.TxnCOCI
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.lov.CheckStatus
import org.icbs.lov.TxnCheckStatus
import org.icbs.periodicops.DailyBalance
import org.icbs.periodicops.MonthlyBalance
import org.icbs.periodicops.MonthlyPointerLoan
import org.icbs.periodicops.MonthlyCustomer
import org.icbs.periodicops.DailyCheckClearing

/**
 * EndOfDayController - Handles all End of Day banking operations
 * 
 * This controller manages the critical end-of-day processes including:
 * - User session validation and teller balance checks
 * - Branch-specific end of day operations
 * - GL daily file updates and balance calculations
 * - System status updates and date advancement
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class EndOfDayController {
    
    // Service Dependencies
    def periodicOpsService
    def loanPeriodicOpsService
    def IsTelleringActiveService
    def depositPeriodicOpsService
    def jasperService
    def AuditLogService
    def GlTransactionService
    def UserMasterService
    def sessionFactory
    def dataSource
    def glPeriodicOpsService

    /**
     * Display the end of day index page
     * @return rendered view with current run date
     */
    def index() {
        try {
            def runDate = Branch.read(1).runDate
            render(view: "index", model: [endOfDayDate: runDate])
        } catch (Exception e) {
            log.error("Error loading end of day index: ${e.message}", e)
            flash.message = "Error loading end of day page |error|alert"
            redirect(controller: "periodicOps", action: "index")
        }
    }

    /**
     * Get end of day progress via AJAX
     * @return JSON response with progress information
     */
    def endOfDayProgressAjax() {
        try {
            def jsonObject = new JSONObject()
            jsonObject = jsonObject.put('progress', session.progress)
            jsonObject = jsonObject.put('message', session.message)
            jsonObject = jsonObject.put('flag', session.flag)
            render jsonObject as JSON
        } catch (Exception e) {
            log.error("Error getting end of day progress: ${e.message}", e)
            render([error: "Unable to get progress information"] as JSON)
        }
    }

    /**
     * Main end of day processing method
     * Handles validation, branch operations, GL updates, and system advancement
     */
    @Transactional
    def endOfDay() {
        try {
            log.info("Starting End of Day process")
            
            // Initialize progress tracking
            setProgressSession("0", "Starting End of Day Process", "start", "0")
            
            def currentDate = Branch.get(1).runDate
            
            // Validate system state before proceeding
            if (!validateSystemForEndOfDay()) {
                return
            }
            
            def pLog = new PeriodicOpsLog(runDate: currentDate, startTime: new Date().toString(), 
                                        processType: 'EOD', status: 0)
            pLog.save(flush: true)
            
            // Process end of day operations
            processEndOfDayOperations(currentDate)
            
            // Update GL and system
            updateGLAndSystem(currentDate)
            
            // Advance system date
            advanceSystemDate(currentDate)
            
            // Complete the process
            pLog.endTime = new Date().toString()
            pLog.status = 1
            pLog.save(flush: true)
            
            setProgressSession("100", "End of Day Complete", "end@@#${pLog.id}", "${pLog.id}")
            log.info("End of Day process completed successfully")
            
        } catch (Exception e) {
            log.error("Error during end of day process: ${e.message}", e)
            setProgressSession("0", "ERROR: ${e.message}", "error", "0")
            flash.message = "End of Day process failed: ${e.message} |error|alert"
            redirect(controller: "periodicOps", action: "index")
        }
    }

    /**
     * Validate system state before end of day processing
     * @return true if system is ready for end of day
     */
    private boolean validateSystemForEndOfDay() {
        try {
            // Check for active users
            def activeUsers = UserSession.findAllByLogout(null)
            def numLogin = activeUsers.size()
            
            if (numLogin > 1) {
                setProgressSession("0", "ERROR: Users still logged in, cannot proceed", "error", "0")
                flash.message = "Cannot proceed with End of Day - users still logged in |error|alert"
                return false
            }
            
            // Check teller balances
            def unbalancedTellers = UserMaster.findAllByIsTellerBalancedAndConfigItemStatus(
                false, ConfigItemStatus.read(2))
            
            if (unbalancedTellers) {
                setProgressSession("0", "ERROR: Tellers not balanced", "error", "0")
                flash.message = "Cannot proceed - tellers not balanced |error|alert"
                return false
            }
            
            // Check for unposted GL batches
            def unpostedBatches = GlBatchHdr.findAllByTxnDateAndStatusLessThan(
                Branch.get(1).runDate, GlBatchHdrStatus.get(3))
            
            if (unpostedBatches) {
                setProgressSession("0", "ERROR: Unposted GL batches exist", "error", "0")
                flash.message = "Cannot proceed - unposted GL batches exist |error|alert"
                return false
            }
            
            return true
            
        } catch (Exception e) {
            log.error("Error validating system for end of day: ${e.message}", e)
            setProgressSession("0", "ERROR: Validation failed", "error", "0")
            return false
        }
    }

    /**
     * Process end of day operations for all branches
     * @param currentDate The processing date
     */
    private void processEndOfDayOperations(Date currentDate) {
        try {
            setProgressSession("25", "Processing End of Day Operations", "processing", "0")
            
            def branchList = Branch.list(sort: "id", order: "asc")
            def totalBranches = branchList.size()
            def currentBranch = 0
            
            for (branch in branchList) {
                currentBranch++
                def progressPercent = (currentBranch * 50 / totalBranches) + 25
                
                setProgressSession("${progressPercent}", "Processing Branch: ${branch.name}", "processing", "0")
                
                // Disable tellering
                IsTelleringActiveService.disableTellering()
                
                // Update daily balances
                updateDailyBalances(currentDate, branch)
                
                // Process deposits end of day
                depositPeriodicOpsService.endOfDay(currentDate, branch, UserMaster.get(session.user_id))
                
                // Process loans end of day
                loanPeriodicOpsService.endOfDay(currentDate, branch, UserMaster.get(session.user_id))
                
                // Transfer to dormant
                depositPeriodicOpsService.TransferToDormant(currentDate, branch, 
                    UserMaster.get(session.user_id), 'daily')
            }
            
        } catch (Exception e) {
            log.error("Error processing end of day operations: ${e.message}", e)
            throw new RuntimeException("End of day operations failed: ${e.message}")
        }
    }

    /**
     * Update daily balances for a branch
     * @param currentDate The processing date
     * @param branch The branch to process
     */
    private void updateDailyBalances(Date currentDate, Branch branch) {
        try {
            def db = new Sql(dataSource)
            def sqlstmt = "select id from deposit where branch_id = ${branch.id}"
            def depositList = db.rows(sqlstmt)
            
            Integer i = 1
            for (d in depositList) {
                def deposit = Deposit.read(d.id)
                
                def dailyBalanceInstance = new DailyBalance(
                    accountNo: deposit.acctNo,
                    refDate: currentDate,
                    branch: branch,
                    accountStatus: deposit.status.id,
                    currency: org.icbs.admin.Currency.get(1),
                    availableBal: deposit.availableBalAmt,
                    closingBal: deposit.ledgerBalAmt,
                    holdBal: deposit.holdBalAmt,
                    refYear: currentDate.format('yyyy'),
                    refMonth: new SimpleDateFormat("MM").format(currentDate)
                )
                dailyBalanceInstance.save(validate: false)
                
                i++
                if (i == 1000) {
                    cleanUpGorm()
                    i = 0
                }
            }
            
        } catch (Exception e) {
            log.error("Error updating daily balances for ${branch.name}: ${e.message}", e)
            throw new RuntimeException("Daily balance update failed for ${branch.name}: ${e.message}")
        }
    }

    /**
     * Update GL and system components
     * @param currentDate The processing date
     */
    private void updateGLAndSystem(Date currentDate) {
        try {
            setProgressSession("75", "Updating GL and System", "processing", "0")
            
            // Update GL entries
            GlTransactionService.PeriodicGlEntries(currentDate, UserMaster.get(session.user_id))
            
            // Forex revaluation
            GlTransactionService.forexRevaluation()
            
            // Update GL daily balances
            updateGLDailyBalances(currentDate)
            
            // Clean up passbook lines
            TxnPassbookLine.executeUpdate("delete TxnPassbookLine")
            
            // Process zero balance account closures
            processZeroBalanceClosures(currentDate)
            
        } catch (Exception e) {
            log.error("Error updating GL and system: ${e.message}", e)
            throw new RuntimeException("GL and system update failed: ${e.message}")
        }
    }

    /**
     * Update GL daily balances
     * @param currentDate The processing date
     */
    private void updateGLDailyBalances(Date currentDate) {
        try {
            def db = new Sql(dataSource)
            def sqlstmt = "select id from gl_account where financial_year = ${currentDate.format('yyyy').toInteger()}"
            def glDly = db.rows(sqlstmt)
            
            Integer i = 0
            for (g in glDly) {
                def gl = GlAccount.read(g.id)
                Double drBal = 0.00D
                Double crBal = 0.00D
                
                if (gl.balance > 0) {
                    drBal = gl.balance
                } else {
                    crBal = gl.balance.abs()
                }
                
                def gld = new GlDailyFile(
                    glAcct: gl, branch: gl.branch, currency: gl.currency, name: gl.name,
                    code: gl.code, refDate: currentDate,
                    debitToday: gl.debitToday, creditToday: gl.creditToday,
                    debitBalance: drBal, creditBalance: crBal,
                    financialYear: currentDate.format('yyyy').toInteger()
                )
                gld.save(validate: false)
                
                i++
                if (i == 1000) {
                    cleanUpGorm()
                    i = 0
                }
            }
            
            // Clean up old GL daily files
            GlDailyFile.executeUpdate("delete from GlDailyFile where financial_year<>? and ref_date=?", 
                [currentDate.format('yyyy').toInteger(), currentDate])
                
        } catch (Exception e) {
            log.error("Error updating GL daily balances: ${e.message}", e)
            throw new RuntimeException("GL daily balance update failed: ${e.message}")
        }
    }

    /**
     * Process zero balance account closures
     * @param currentDate The processing date
     */
    private void processZeroBalanceClosures(Date currentDate) {
        try {
            if (Institution.findByParamCode('DEP.40124').paramValue == 'TRUE') {
                def clsDep = Deposit.createCriteria().list {
                    and {
                        eq("ledgerBalAmt", 0.00D)
                        ne("status", DepositStatus.get(7))
                        eq("type", DepositType.get(1))
                    }
                }
                
                for (deposit in clsDep) {
                    def txnDate = deposit.lastTxnDate ?: deposit.dateOpened
                    def durationDays
                    
                    use(TimeCategory) {
                        durationDays = (currentDate - txnDate).days
                    }
                    
                    if (durationDays > 30 && deposit.status.id != 8 && deposit.status.id != 6) {
                        deposit.status = DepositStatus.get(7)
                        AuditLogService.insert('080', 'DEP00501', 
                            "Automatic Closing of SA ${deposit.acctNo}", 'Deposit', null, null, null, deposit.id)
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("Error processing zero balance closures: ${e.message}", e)
            // Non-critical error, continue processing
        }
    }

    /**
     * Advance system date and update branch status
     * @param currentDate The current processing date
     */
    private void advanceSystemDate(Date currentDate) {
        try {
            setProgressSession("90", "Advancing System Date", "processing", "0")
            
            def calendar = Calendar.getInstance()
            calendar.setTime(currentDate)
            
            def branchList = Branch.list(sort: "id", order: "asc")
            
            for (branch in branchList) {
                // Determine next run date
                if (calendar.get(Calendar.DATE) == calendar.getActualMaximum(Calendar.DATE) && 
                    currentDate.format('MM') == '12') {
                    branch.runDate = currentDate
                } else {
                    branch.runDate = currentDate.plus(1)
                }
                
                branch.branchRunStatus = BranchRunStatus.get(2)
                branch.isTelleringActive = false
                branch.save(flush: true, failOnError: true)
            }
            
            // Create cash balance forward
            createCashBalanceForward(currentDate)
            
            // Update rollover status
            depositPeriodicOpsService.rolloverStatusUpdate()
            
            // Initialize teller balances
            UserMasterService.initTellerBalance()
            
        } catch (Exception e) {
            log.error("Error advancing system date: ${e.message}", e)
            throw new RuntimeException("System date advancement failed: ${e.message}")
        }
    }

    /**
     * Create cash balance forward for next day
     * @param currentDate The current processing date
     */
    private void createCashBalanceForward(Date currentDate) {
        try {
            def txnCash = TxnTellerBalance.createCriteria().list {
                and {
                    eq("txnDate", currentDate)
                }
            }
            
            for (tc in txnCash) {
                if (tc.cashIn != tc.cashOut) {
                    def tn = new TxnTellerBalance(
                        txnDate: currentDate.plus(1),
                        user: tc.user,
                        currency: tc.currency,
                        cashIn: tc.cashIn,
                        cashOut: tc.cashOut,
                        lastBalanceAmt: 0d,
                        isBalance: false,
                        isCashier: tc.isCashier
                    )
                    tn.save(flush: true)
                }
            }
            
        } catch (Exception e) {
            log.error("Error creating cash balance forward: ${e.message}", e)
            // Non-critical error, continue processing
        }
    }

    /**
     * Clean up GORM session to prevent memory issues
     */
    private void cleanUpGorm() {
        try {
            def session = sessionFactory.currentSession
            session.flush()
            session.clear()
            def propertyInstanceMap = org.codehaus.groovy.grails.plugins.DomainClassGrailsPlugin.PROPERTY_INSTANCE_MAP
            propertyInstanceMap.get().clear()
        } catch (Exception e) {
            log.error("Error cleaning up GORM: ${e.message}", e)
        }
    }

    /**
     * Set progress session for tracking end of day progress
     * @param progress Progress percentage
     * @param message Progress message
     * @param flag Progress flag
     * @param logId Log ID
     */
    private void setProgressSession(String progress, String message, String flag, String logId) {
        try {
            session.progress = progress
            session.message = message
            session.flag = flag
            session.logId = logId
        } catch (Exception e) {
            log.error("Error setting progress session: ${e.message}", e)
        }
    }
}
