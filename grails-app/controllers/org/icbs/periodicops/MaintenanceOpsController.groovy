package org.icbs.periodicops

import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.grails.web.json.JSONObject
import groovy.time.TimeCategory
import org.icbs.admin.UserMaster
import java.nio.file.*
import org.icbs.admin.Branch
import org.icbs.admin.Holiday
import org.icbs.admin.UserMaster
import org.icbs.admin.CheckDepositClearingType
import org.icbs.admin.Institution
import org.icbs.admin.BranchHoliday
import org.icbs.security.UserSession
import org.icbs.lov.BranchRunStatus
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.gl.GlBatchHdr
import org.icbs.gl.GlBatch
import org.icbs.gl.GlDailyFile
import org.icbs.gl.GlMonthlyBalance
import org.icbs.gl.GlAccount
import org.icbs.deposit.Deposit
import org.icbs.periodicops.PeriodicOpsLog
import org.icbs.periodicops.DailyLoanRecoveries
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositStatus
import org.icbs.lov.DepositType
import org.icbs.loans.Loan
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanInstallmentStatus
import org.icbs.lov.GlBatchHdrStatus
import org.icbs.loans.Loan
import org.icbs.loans.LoanLedger
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnPassbookLine
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.cif.Customer
import grails.gorm.transactions.Transactional
import org.apache.commons.io.FileUtils
import org.apache.tools.zip.ZipEntry
import java.text.SimpleDateFormat
import java.util.zip.ZipOutputStream
import java.util.Calendar
import org.icbs.admin.UserMessage
import static grails.async.Promises.*
import groovy.sql.Sql
import org.icbs.tellering.TxnCOCI
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.lov.CheckStatus
import org.icbs.lov.TxnCheckStatus
import org.icbs.periodicops.DailyBalance
import org.icbs.periodicops.MonthlyBalance
import org.icbs.periodicops.MonthlyPointerLoan
import org.icbs.periodicops.MonthlyCustomer
import org.icbs.periodicops.DailyCheckClearing

/**
 * MaintenanceOpsController - Handles maintenance and utility operations
 * 
 * This controller manages maintenance operations including:
 * - Loan recovery rebuilding
 * - Loan installment rebuilding
 * - Periodic operations utilities
 * - System maintenance tasks
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class MaintenanceOpsController {
    
    // Service Dependencies
    def periodicOpsService
    def loanPeriodicOpsService
    def IsTelleringActiveService
    def depositPeriodicOpsService
    def jasperService
    def AuditLogService
    def GlTransactionService
    def UserMasterService
    def sessionFactory
    def dataSource
    def glPeriodicOpsService

    /**
     * Display the maintenance operations index page
     * @return rendered view with maintenance options
     */
    def index() {
        try {
            def runDate = Branch.read(1).runDate
            render(view: "index", model: [currentDate: runDate])
        } catch (Exception e) {
            log.error("Error loading maintenance operations index: ${e.message}", e)
            flash.message = "Error loading maintenance operations page |error|alert"
            redirect(controller: "periodicOps", action: "index")
        }
    }

    /**
     * Display periodic operations utilities page
     */
    def periodicOpsUtil() {
        try {
            def runDate = Branch.read(1).runDate
            render(view: "periodicOpsUtil", model: [currentDate: runDate])
        } catch (Exception e) {
            log.error("Error loading periodic ops utilities: ${e.message}", e)
            flash.message = "Error loading utilities page |error|alert"
            redirect(action: "index")
        }
    }

    /**
     * Rebuild loan recovery transactions
     * Rebuilds transaction breakdowns for loan recovery entries
     */
    @Transactional
    def rebuildLoanRecovery() {
        try {
            log.info("Starting loan recovery rebuild process")
            
            def runDate = Branch.get(1).runDate
            def recoveries = DailyLoanRecoveries.findAllByProcessDate(runDate)
            
            if (!recoveries) {
                flash.message = "No loan recoveries found for current date |warning|alert"
                redirect(action: "index")
                return
            }
            
            def processedCount = 0
            def errorCount = 0
            
            for (recovery in recoveries) {
                try {
                    processLoanRecovery(recovery)
                    processedCount++
                } catch (Exception e) {
                    log.error("Error processing loan recovery ${recovery.id}: ${e.message}", e)
                    errorCount++
                }
            }
            
            // Log the operation
            AuditLogService.insert('800', 'MNT00800', 
                "Loan recovery rebuild completed - Processed: ${processedCount}, Errors: ${errorCount}", 
                'Maintenance', null, null, null, null)
            
            if (errorCount == 0) {
                flash.message = "Loan recovery rebuild completed successfully - ${processedCount} records processed |success|alert"
            } else {
                flash.message = "Loan recovery rebuild completed with ${errorCount} errors - ${processedCount} records processed |warning|alert"
            }
            
        } catch (Exception e) {
            log.error("Error during loan recovery rebuild: ${e.message}", e)
            flash.message = "Loan recovery rebuild failed: ${e.message} |error|alert"
        }
        
        redirect(action: "index")
    }

    /**
     * Rebuild loan installments
     * Updates loan installment statuses based on current date
     */
    @Transactional
    def rebuildLoanInst() {
        try {
            log.info("Starting loan installment rebuild process")
            
            // Validate system state
            if (Branch.get(1).branchRunStatus.id != 2) {
                flash.message = "Error! Must run rebuild after End-of-Day! |error|alert"
                redirect(action: "index")
                return
            }
            
            def currentDate = Branch.get(1).runDate
            def db = new Sql(dataSource)
            def sqlstmt = "select id from loan where status_id in (2,3,4,5)"
            def loans = db.rows(sqlstmt)
            
            def processedLoans = 0
            def processedInstallments = 0
            def errorCount = 0
            
            for (loanRow in loans) {
                try {
                    def loan = Loan.get(loanRow.id)
                    if (!loan.isAttached()) {
                        loan.attach()
                    }
                    
                    def installments = loan?.loanInstallments
                    
                    for (installment in installments) {
                        try {
                            // Update installment status based on date
                            if (installment.installmentDate <= loan?.branch.runDate) {
                                installment.status = LoanInstallmentStatus.get(3) // Due
                            } else {
                                installment.status = LoanInstallmentStatus.get(2) // Approaching
                            }
                            
                            installment.save(flush: true, failOnError: true)
                            processedInstallments++
                            
                            if (processedInstallments % 50 == 0) {
                                cleanUpGorm()
                            }
                            
                        } catch (Exception e) {
                            log.error("Error processing installment ${installment.id}: ${e.message}", e)
                            errorCount++
                        }
                    }
                    
                    processedLoans++
                    
                } catch (Exception e) {
                    log.error("Error processing loan ${loanRow.id}: ${e.message}", e)
                    errorCount++
                }
            }
            
            // Log the operation
            AuditLogService.insert('801', 'MNT00801', 
                "Loan installment rebuild completed - Loans: ${processedLoans}, Installments: ${processedInstallments}, Errors: ${errorCount}", 
                'Maintenance', null, null, null, null)
            
            if (errorCount == 0) {
                flash.message = "Loan installment rebuild completed successfully - ${processedLoans} loans, ${processedInstallments} installments processed |success|alert"
            } else {
                flash.message = "Loan installment rebuild completed with ${errorCount} errors - ${processedLoans} loans, ${processedInstallments} installments processed |warning|alert"
            }
            
        } catch (Exception e) {
            log.error("Error during loan installment rebuild: ${e.message}", e)
            flash.message = "Loan installment rebuild failed: ${e.message} |error|alert"
        }
        
        redirect(action: "index")
    }

    /**
     * Clean up system data
     * Performs various cleanup operations
     */
    @Transactional
    def cleanupSystemData() {
        try {
            log.info("Starting system data cleanup")
            
            def cleanupResults = [:]
            
            // Clean up old passbook lines
            def passbookCount = TxnPassbookLine.executeUpdate("delete TxnPassbookLine")
            cleanupResults.passbookLines = passbookCount
            
            // Clean up old temporary files
            def tempFileCount = cleanupTempFiles()
            cleanupResults.tempFiles = tempFileCount
            
            // Clean up old session data
            def sessionCount = cleanupOldSessions()
            cleanupResults.sessions = sessionCount
            
            // Log the operation
            AuditLogService.insert('802', 'MNT00802', 
                "System cleanup completed - Passbook: ${passbookCount}, Temp files: ${tempFileCount}, Sessions: ${sessionCount}", 
                'Maintenance', null, null, null, null)
            
            flash.message = "System cleanup completed successfully |success|alert"
            
            render cleanupResults as JSON
            
        } catch (Exception e) {
            log.error("Error during system cleanup: ${e.message}", e)
            render([error: "System cleanup failed: ${e.message}"] as JSON)
        }
    }

    /**
     * Optimize database performance
     * Performs database optimization tasks
     */
    @Transactional
    def optimizeDatabase() {
        try {
            log.info("Starting database optimization")
            
            def db = new Sql(dataSource)
            def optimizationResults = [:]
            
            // Update table statistics
            def tableCount = updateTableStatistics(db)
            optimizationResults.tablesOptimized = tableCount
            
            // Rebuild indexes
            def indexCount = rebuildIndexes(db)
            optimizationResults.indexesRebuilt = indexCount
            
            // Clean up GORM session
            cleanUpGorm()
            
            // Log the operation
            AuditLogService.insert('803', 'MNT00803', 
                "Database optimization completed - Tables: ${tableCount}, Indexes: ${indexCount}", 
                'Maintenance', null, null, null, null)
            
            flash.message = "Database optimization completed successfully |success|alert"
            
            render optimizationResults as JSON
            
        } catch (Exception e) {
            log.error("Error during database optimization: ${e.message}", e)
            render([error: "Database optimization failed: ${e.message}"] as JSON)
        }
    }

    /**
     * Get maintenance operation status
     * @return JSON response with maintenance status
     */
    def getMaintenanceStatus() {
        try {
            def status = [
                systemDate: Branch.get(1).runDate,
                branchStatus: Branch.get(1).branchRunStatus?.name,
                telleringActive: Branch.get(1).isTelleringActive,
                lastMaintenanceLog: getLastMaintenanceLog(),
                systemHealth: getSystemHealth(),
                timestamp: new Date()
            ]
            
            render status as JSON
            
        } catch (Exception e) {
            log.error("Error getting maintenance status: ${e.message}", e)
            render([error: "Unable to get maintenance status"] as JSON)
        }
    }

    /**
     * Process individual loan recovery
     * @param recovery The loan recovery to process
     */
    private void processLoanRecovery(DailyLoanRecoveries recovery) {
        try {
            def drTxn = recovery.txnFileDrDeposit
            def crTxn = recovery.txnFileCrLoan
            
            // Check and create debit transaction breakdown
            def tDr = TxnBreakdown.findAllByTxnFile(drTxn)
            if (!tDr) {
                GlTransactionService.saveTxnBreakdown(drTxn.id)
                log.debug("Added debit breakdown for recovery ${recovery.id}")
            }
            
            // Check and create credit transaction breakdown
            def tCr = TxnBreakdown.findAllByTxnFile(crTxn)
            if (!tCr) {
                GlTransactionService.saveTxnBreakdown(crTxn.id)
                log.debug("Added credit breakdown for recovery ${recovery.id}")
            }
            
        } catch (Exception e) {
            log.error("Error processing loan recovery ${recovery.id}: ${e.message}", e)
            throw e
        }
    }

    /**
     * Clean up temporary files
     * @return Number of files cleaned up
     */
    private Integer cleanupTempFiles() {
        try {
            def tempDir = System.getProperty("java.io.tmpdir")
            def tempPath = Paths.get(tempDir)
            def cleanedCount = 0
            
            // Implementation for cleaning up temporary files
            // This would depend on your specific temporary file patterns
            
            return cleanedCount
        } catch (Exception e) {
            log.error("Error cleaning up temp files: ${e.message}", e)
            return 0
        }
    }

    /**
     * Clean up old user sessions
     * @return Number of sessions cleaned up
     */
    private Integer cleanupOldSessions() {
        try {
            // Clean up sessions older than 24 hours
            def cutoffDate = new Date() - 1
            def oldSessions = UserSession.findAllByLoginLessThanAndLogoutIsNotNull(cutoffDate)
            
            def cleanedCount = 0
            for (session in oldSessions) {
                try {
                    session.delete(flush: true)
                    cleanedCount++
                } catch (Exception e) {
                    log.error("Error deleting session ${session.id}: ${e.message}", e)
                }
            }
            
            return cleanedCount
        } catch (Exception e) {
            log.error("Error cleaning up old sessions: ${e.message}", e)
            return 0
        }
    }

    /**
     * Update table statistics
     * @param db SQL connection
     * @return Number of tables updated
     */
    private Integer updateTableStatistics(Sql db) {
        try {
            // Implementation would depend on your database type
            // This is a placeholder for database-specific optimization
            return 0
        } catch (Exception e) {
            log.error("Error updating table statistics: ${e.message}", e)
            return 0
        }
    }

    /**
     * Rebuild database indexes
     * @param db SQL connection
     * @return Number of indexes rebuilt
     */
    private Integer rebuildIndexes(Sql db) {
        try {
            // Implementation would depend on your database type
            // This is a placeholder for database-specific index rebuilding
            return 0
        } catch (Exception e) {
            log.error("Error rebuilding indexes: ${e.message}", e)
            return 0
        }
    }

    /**
     * Get last maintenance log entry
     * @return Map containing last maintenance log information
     */
    private Map getLastMaintenanceLog() {
        try {
            // Implementation to get last maintenance operation from audit log
            return [message: "No recent maintenance operations"]
        } catch (Exception e) {
            log.error("Error getting last maintenance log: ${e.message}", e)
            return [error: "Unable to get maintenance log"]
        }
    }

    /**
     * Get system health information
     * @return Map containing system health metrics
     */
    private Map getSystemHealth() {
        try {
            return [
                memoryUsage: getMemoryUsage(),
                diskSpace: getDiskSpace(),
                databaseConnections: getDatabaseConnections(),
                status: "Healthy"
            ]
        } catch (Exception e) {
            log.error("Error getting system health: ${e.message}", e)
            return [status: "Unknown", error: e.message]
        }
    }

    /**
     * Get memory usage information
     * @return Map containing memory usage data
     */
    private Map getMemoryUsage() {
        try {
            def runtime = Runtime.getRuntime()
            def totalMemory = runtime.totalMemory()
            def freeMemory = runtime.freeMemory()
            def usedMemory = totalMemory - freeMemory
            
            return [
                total: totalMemory,
                used: usedMemory,
                free: freeMemory,
                percentage: (usedMemory * 100 / totalMemory).intValue()
            ]
        } catch (Exception e) {
            return [error: "Unable to get memory usage"]
        }
    }

    /**
     * Get disk space information
     * @return Map containing disk space data
     */
    private Map getDiskSpace() {
        try {
            def file = new File(".")
            def totalSpace = file.getTotalSpace()
            def freeSpace = file.getFreeSpace()
            def usedSpace = totalSpace - freeSpace
            
            return [
                total: totalSpace,
                used: usedSpace,
                free: freeSpace,
                percentage: (usedSpace * 100 / totalSpace).intValue()
            ]
        } catch (Exception e) {
            return [error: "Unable to get disk space"]
        }
    }

    /**
     * Get database connection information
     * @return Map containing database connection data
     */
    private Map getDatabaseConnections() {
        try {
            // Implementation would depend on your connection pool
            return [active: 0, idle: 0, max: 0]
        } catch (Exception e) {
            return [error: "Unable to get database connections"]
        }
    }

    /**
     * Clean up GORM session to prevent memory issues
     */
    private void cleanUpGorm() {
        try {
            def session = sessionFactory.currentSession
            session.flush()
            session.clear()
            def propertyInstanceMap = org.codehaus.groovy.grails.plugins.DomainClassGrailsPlugin.PROPERTY_INSTANCE_MAP
            propertyInstanceMap.get().clear()
        } catch (Exception e) {
            log.error("Error cleaning up GORM: ${e.message}", e)
        }
    }
}
