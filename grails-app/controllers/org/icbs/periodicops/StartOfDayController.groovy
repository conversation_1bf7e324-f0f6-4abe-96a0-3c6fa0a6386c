package org.icbs.periodicops

import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.grails.web.json.JSONObject
import groovy.time.TimeCategory
import org.icbs.admin.UserMaster
import java.nio.file.*
import org.icbs.admin.Branch
import org.icbs.admin.Holiday
import org.icbs.admin.UserMaster
import org.icbs.admin.CheckDepositClearingType
import org.icbs.admin.Institution
import org.icbs.admin.BranchHoliday
import org.icbs.security.UserSession
import org.icbs.lov.BranchRunStatus
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.gl.GlBatchHdr
import org.icbs.gl.GlBatch
import org.icbs.gl.GlDailyFile
import org.icbs.gl.GlMonthlyBalance
import org.icbs.gl.GlAccount
import org.icbs.deposit.Deposit
import org.icbs.periodicops.PeriodicOpsLog
import org.icbs.periodicops.DailyLoanRecoveries
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositStatus
import org.icbs.lov.DepositType
import org.icbs.loans.Loan
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanInstallmentStatus
import org.icbs.lov.GlBatchHdrStatus
import org.icbs.loans.Loan
import org.icbs.loans.LoanLedger
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnPassbookLine
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.cif.Customer
import grails.gorm.transactions.Transactional
import org.apache.commons.io.FileUtils
import org.apache.tools.zip.ZipEntry
import java.text.SimpleDateFormat
import java.util.zip.ZipOutputStream
import java.util.Calendar
import org.icbs.admin.UserMessage
import static grails.async.Promises.*
import groovy.sql.Sql
import org.icbs.tellering.TxnCOCI
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.lov.CheckStatus
import org.icbs.lov.TxnCheckStatus
import org.icbs.periodicops.DailyBalance
import org.icbs.periodicops.MonthlyBalance
import org.icbs.periodicops.MonthlyPointerLoan
import org.icbs.periodicops.MonthlyCustomer
import org.icbs.periodicops.DailyCheckClearing

/**
 * StartOfDayController - Handles all Start of Day banking operations
 * 
 * This controller manages the critical start-of-day processes including:
 * - Holiday processing and interest calculations
 * - Branch-specific start of day operations
 * - Deposit and loan start of day processing
 * - System status updates and validations
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class StartOfDayController {
    
    // Service Dependencies
    def periodicOpsService
    def loanPeriodicOpsService
    def IsTelleringActiveService
    def depositPeriodicOpsService
    def jasperService
    def AuditLogService
    def GlTransactionService
    def UserMasterService
    def sessionFactory
    def dataSource
    def glPeriodicOpsService

    /**
     * Display the start of day index page
     * @return rendered view with current run date
     */
    def index() {
        try {
            def runDate = Branch.read(1).runDate
            render(view: "index", model: [startOfDayDate: runDate])
        } catch (Exception e) {
            log.error("Error loading start of day index: ${e.message}", e)
            flash.message = "Error loading start of day page |error|alert"
            redirect(controller: "periodicOps", action: "index")
        }
    }

    /**
     * Get start of day progress via AJAX
     * @return JSON response with progress information
     */
    def startOfDayProgressAjax() {
        try {
            def jsonObject = new JSONObject()
            jsonObject = jsonObject.put('progress', session.progress)
            jsonObject = jsonObject.put('message', session.message)
            jsonObject = jsonObject.put('flag', session.flag)
            render jsonObject as JSON
        } catch (Exception e) {
            log.error("Error getting start of day progress: ${e.message}", e)
            render([error: "Unable to get progress information"] as JSON)
        }
    }

    /**
     * Main start of day processing method
     * Handles holiday processing, branch operations, and system updates
     */
    @Transactional
    def startOfDay() {
        try {
            log.info("Starting Start of Day process")
            
            // Initialize progress tracking
            setProgressSession("0", "Starting Start of Day Process", "start", "0")
            
            def startOfDayDate = Branch.get(1).runDate
            def pLog = new PeriodicOpsLog(runDate: startOfDayDate, startTime: new Date().toString(), 
                                        processType: 'SOD', status: 0)
            pLog.save(flush: true)
            
            // Check for bank-wide holiday
            def bankwideHoliday = checkBankwideHoliday(startOfDayDate)
            
            if (bankwideHoliday) {
                processHolidayOperations(startOfDayDate)
            } else {
                processRegularStartOfDay(startOfDayDate)
            }
            
            // Complete the process
            pLog.endTime = new Date().toString()
            pLog.status = 1
            pLog.save(flush: true)
            
            setProgressSession("100", "Start of Day Complete", "end@@#${pLog.id}", "${pLog.id}")
            log.info("Start of Day process completed successfully")
            
        } catch (Exception e) {
            log.error("Error during start of day process: ${e.message}", e)
            setProgressSession("0", "ERROR: ${e.message}", "error", "0")
            flash.message = "Start of Day process failed: ${e.message} |error|alert"
            redirect(controller: "periodicOps", action: "index")
        }
    }

    /**
     * Check if current date is a bank-wide holiday
     * @param date The date to check
     * @return true if it's a bank-wide holiday
     */
    private boolean checkBankwideHoliday(Date date) {
        try {
            def holiday = Holiday.findByHolidayDate(date)
            return holiday != null
        } catch (Exception e) {
            log.error("Error checking bank-wide holiday: ${e.message}", e)
            return false
        }
    }

    /**
     * Process holiday operations - update interest and penalties
     * @param startOfDayDate The processing date
     */
    private void processHolidayOperations(Date startOfDayDate) {
        try {
            log.info("Processing holiday operations for date: ${startOfDayDate}")
            setProgressSession("25", "Processing Holiday Operations", "processing", "0")
            
            def branchList = Branch.list()
            
            // Update penalties and interest for all branches
            for (branch in branchList) {
                loanPeriodicOpsService.updatePenalties(startOfDayDate, branch)
                loanPeriodicOpsService.updateInterest(startOfDayDate, branch)
            }
            
            // Update branch status
            for (branch in branchList) {
                def brUpdate = Branch.get(branch.id)
                brUpdate.branchRunStatus = BranchRunStatus.get(1)
                brUpdate.save(flush: true, failOnError: true)
            }
            
            setProgressSession("100", "Holiday Operations Complete", "processing", "0")
            
        } catch (Exception e) {
            log.error("Error processing holiday operations: ${e.message}", e)
            throw new RuntimeException("Holiday operations failed: ${e.message}")
        }
    }

    /**
     * Process regular start of day operations for non-holiday dates
     * @param startOfDayDate The processing date
     */
    private void processRegularStartOfDay(Date startOfDayDate) {
        try {
            log.info("Processing regular start of day operations")
            
            // First do clearing of checks
            setProgressSession("10", "Processing Check Clearing", "processing", "0")
            depositPeriodicOpsService.clearingChecks(startOfDayDate)
            
            // Process each branch
            def branchList = Branch.list(sort: "id", order: "asc")
            def totalBranches = branchList.size()
            def currentBranch = 0
            
            for (branch in branchList) {
                currentBranch++
                def progressPercent = (currentBranch * 80 / totalBranches) + 10
                
                setProgressSession("${progressPercent}", "Processing Branch: ${branch.name}", "processing", "0")
                
                // Check if branch has holiday
                def branchHoliday = checkBranchHoliday(startOfDayDate, branch)
                
                if (!branchHoliday) {
                    processRegularBranchOperations(startOfDayDate, branch)
                } else {
                    processHolidayBranchOperations(startOfDayDate, branch)
                }
            }
            
            setProgressSession("95", "Finalizing Start of Day", "processing", "0")
            
        } catch (Exception e) {
            log.error("Error processing regular start of day: ${e.message}", e)
            throw new RuntimeException("Regular start of day failed: ${e.message}")
        }
    }

    /**
     * Check if specific branch has holiday on given date
     * @param date The date to check
     * @param branch The branch to check
     * @return true if branch has holiday
     */
    private boolean checkBranchHoliday(Date date, Branch branch) {
        try {
            def branchHoliday = BranchHoliday.findByHolidayDateAndBranch(date, branch)
            return branchHoliday != null
        } catch (Exception e) {
            log.error("Error checking branch holiday: ${e.message}", e)
            return false
        }
    }

    /**
     * Process regular branch operations for non-holiday branches
     * @param startOfDayDate The processing date
     * @param branch The branch to process
     */
    private void processRegularBranchOperations(Date startOfDayDate, Branch branch) {
        try {
            // Release holds
            depositPeriodicOpsService.holdsReleaseProcessing(startOfDayDate, branch, UserMaster.get(session.user_id))
            
            // Process deposits start of day
            depositPeriodicOpsService.startOfDay(startOfDayDate, branch, UserMaster.get(session.user_id))
            
            // Process loans start of day
            loanPeriodicOpsService.startOfDay(startOfDayDate, branch, UserMaster.get(session.user_id))
            
        } catch (Exception e) {
            log.error("Error processing regular branch operations for ${branch.name}: ${e.message}", e)
            throw new RuntimeException("Branch operations failed for ${branch.name}: ${e.message}")
        }
    }

    /**
     * Process holiday branch operations
     * @param startOfDayDate The processing date
     * @param branch The branch to process
     */
    private void processHolidayBranchOperations(Date startOfDayDate, Branch branch) {
        try {
            // Compute penalties and interest for loans only
            loanPeriodicOpsService.updatePenalties(startOfDayDate, branch)
            loanPeriodicOpsService.updateInterest(startOfDayDate, branch)
            
        } catch (Exception e) {
            log.error("Error processing holiday branch operations for ${branch.name}: ${e.message}", e)
            throw new RuntimeException("Holiday branch operations failed for ${branch.name}: ${e.message}")
        }
    }

    /**
     * Set progress session for tracking start of day progress
     * @param progress Progress percentage
     * @param message Progress message
     * @param flag Progress flag
     * @param logId Log ID
     */
    private void setProgressSession(String progress, String message, String flag, String logId) {
        try {
            session.progress = progress
            session.message = message
            session.flag = flag
            session.logId = logId
        } catch (Exception e) {
            log.error("Error setting progress session: ${e.message}", e)
        }
    }
}
