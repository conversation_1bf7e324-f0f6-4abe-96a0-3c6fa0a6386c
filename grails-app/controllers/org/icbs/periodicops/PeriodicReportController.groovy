package org.icbs.periodicops

import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.grails.web.json.JSONObject
import groovy.time.TimeCategory
import org.icbs.admin.UserMaster
import java.nio.file.*
import org.icbs.admin.Branch
import org.icbs.admin.Holiday
import org.icbs.admin.UserMaster
import org.icbs.admin.CheckDepositClearingType
import org.icbs.admin.Institution
import org.icbs.admin.BranchHoliday
import org.icbs.security.UserSession
import org.icbs.lov.BranchRunStatus
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.gl.GlBatchHdr
import org.icbs.gl.GlBatch
import org.icbs.gl.GlDailyFile
import org.icbs.gl.GlMonthlyBalance
import org.icbs.gl.GlAccount
import org.icbs.deposit.Deposit
import org.icbs.periodicops.PeriodicOpsLog
import org.icbs.periodicops.DailyLoanRecoveries
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositStatus
import org.icbs.lov.DepositType
import org.icbs.loans.Loan
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanInstallmentStatus
import org.icbs.lov.GlBatchHdrStatus
import org.icbs.loans.Loan
import org.icbs.loans.LoanLedger
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnPassbookLine
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.cif.Customer
import grails.gorm.transactions.Transactional
import org.apache.commons.io.FileUtils
import org.apache.tools.zip.ZipEntry
import java.text.SimpleDateFormat
import java.util.zip.ZipOutputStream
import java.util.Calendar
import org.icbs.admin.UserMessage
import static grails.async.Promises.*
import groovy.sql.Sql
import org.icbs.tellering.TxnCOCI
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.lov.CheckStatus
import org.icbs.lov.TxnCheckStatus
import org.icbs.periodicops.DailyBalance
import org.icbs.periodicops.MonthlyBalance
import org.icbs.periodicops.MonthlyPointerLoan
import org.icbs.periodicops.MonthlyCustomer
import org.icbs.periodicops.DailyCheckClearing

/**
 * PeriodicReportController - Handles all periodic report generation
 * 
 * This controller manages the generation of periodic reports including:
 * - End of Day reports
 * - End of Month reports
 * - End of Year reports
 * - Custom periodic reports
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class PeriodicReportController {
    
    // Service Dependencies
    def periodicOpsService
    def loanPeriodicOpsService
    def IsTelleringActiveService
    def depositPeriodicOpsService
    def jasperService
    def AuditLogService
    def GlTransactionService
    def UserMasterService
    def sessionFactory
    def dataSource
    def glPeriodicOpsService

    /**
     * Display the periodic reports index page
     * @return rendered view with available reports
     */
    def index() {
        try {
            def runDate = Branch.read(1).runDate
            render(view: "index", model: [currentDate: runDate])
        } catch (Exception e) {
            log.error("Error loading periodic reports index: ${e.message}", e)
            flash.message = "Error loading periodic reports page |error|alert"
            redirect(controller: "periodicOps", action: "index")
        }
    }

    /**
     * Generate and download End of Day reports
     */
    def eodReport() {
        try {
            log.info("Generating EOD reports")
            
            def currentDate = Branch.get(1).runDate.minus(1)
            
            // Generate the reports
            createEndOfDayReport(currentDate, 1)
            
            // Download the generated reports
            downloadEodReport()
            
            flash.message = "EOD reports generated successfully |success|alert"
            render(view: "index", model: [startOfDayDate: currentDate])
            
        } catch (Exception e) {
            log.error("Error generating EOD reports: ${e.message}", e)
            flash.message = "Failed to generate EOD reports: ${e.message} |error|alert"
            redirect(action: "index")
        }
    }

    /**
     * Generate and download End of Month reports
     */
    def eomReport() {
        try {
            log.info("Generating EOM reports")
            
            def currentDate = Branch.get(1).runDate.minus(1)
            
            // Generate the reports
            createEndOfDayReport(currentDate, 2)
            
            // Download the generated reports
            downloadEodReport()
            
            flash.message = "EOM reports generated successfully |success|alert"
            render(view: "index", model: [startOfDayDate: currentDate])
            
        } catch (Exception e) {
            log.error("Error generating EOM reports: ${e.message}", e)
            flash.message = "Failed to generate EOM reports: ${e.message} |error|alert"
            redirect(action: "index")
        }
    }

    /**
     * Get End of Day report download
     */
    def getEndOfDayReport() {
        try {
            Date endOfDayDate = Branch.read(1).runDate.minus(1)
            
            def rootDir = request.getSession().getServletContext().getRealPath("/") + 
                         "reports_repository/endOfDay/" + new SimpleDateFormat("yyyy-MM-dd").format(endOfDayDate)
            
            Path path = Paths.get(rootDir + "/reports.zip")
            
            if (Files.exists(path)) {
                byte[] data = Files.readAllBytes(path)
                response.setHeader("Content-disposition", "filename=\"eod_reports_${endOfDayDate.format('yyyy-MM-dd')}.zip\"")
                response.contentType = "application/zip"
                response.outputStream << data
                response.outputStream.flush()
            } else {
                flash.message = "Report file not found |error|alert"
                render(view: "index", model: [startOfDayDate: endOfDayDate])
            }
            
        } catch (Exception e) {
            log.error("Error downloading EOD report: ${e.message}", e)
            flash.message = "Failed to download EOD report: ${e.message} |error|alert"
            redirect(action: "index")
        }
    }

    /**
     * Create End of Day report
     * @param currentDate The report date
     * @param repMode Report mode (1=EOD, 2=EOM)
     */
    private void createEndOfDayReport(Date currentDate, Integer repMode) {
        try {
            log.info("Creating End of Day report for date: ${currentDate}, mode: ${repMode}")
            
            def rootDir = request.getSession().getServletContext().getRealPath("/") + 
                         "reports_repository/endOfDay/" + new SimpleDateFormat("yyyy-MM-dd").format(currentDate)
            
            // Create directory if it doesn't exist
            def dir = new File(rootDir)
            if (!dir.exists()) {
                dir.mkdirs()
            }
            
            // Generate various reports based on mode
            if (repMode == 1) {
                generateEODReports(currentDate, rootDir)
            } else if (repMode == 2) {
                generateEOMReports(currentDate, rootDir)
            }
            
            // Zip all generated reports
            zipFiles("reports", rootDir)
            
        } catch (Exception e) {
            log.error("Error creating End of Day report: ${e.message}", e)
            throw new RuntimeException("Report creation failed: ${e.message}")
        }
    }

    /**
     * Generate End of Day specific reports
     * @param currentDate The report date
     * @param rootDir The output directory
     */
    private void generateEODReports(Date currentDate, String rootDir) {
        try {
            // Trial Balance Report
            generateTrialBalanceReport(currentDate, rootDir)
            
            // Daily Transaction Summary
            generateDailyTransactionSummary(currentDate, rootDir)
            
            // Cash Position Report
            generateCashPositionReport(currentDate, rootDir)
            
            // Loan Portfolio Summary
            generateLoanPortfolioSummary(currentDate, rootDir)
            
            // Deposit Summary Report
            generateDepositSummaryReport(currentDate, rootDir)
            
        } catch (Exception e) {
            log.error("Error generating EOD reports: ${e.message}", e)
            throw new RuntimeException("EOD report generation failed: ${e.message}")
        }
    }

    /**
     * Generate End of Month specific reports
     * @param currentDate The report date
     * @param rootDir The output directory
     */
    private void generateEOMReports(Date currentDate, String rootDir) {
        try {
            // Monthly Trial Balance
            generateMonthlyTrialBalance(currentDate, rootDir)
            
            // Monthly Transaction Summary
            generateMonthlyTransactionSummary(currentDate, rootDir)
            
            // Monthly Interest Report
            generateMonthlyInterestReport(currentDate, rootDir)
            
            // Monthly Loan Performance
            generateMonthlyLoanPerformance(currentDate, rootDir)
            
            // Monthly Deposit Analysis
            generateMonthlyDepositAnalysis(currentDate, rootDir)
            
        } catch (Exception e) {
            log.error("Error generating EOM reports: ${e.message}", e)
            throw new RuntimeException("EOM report generation failed: ${e.message}")
        }
    }

    /**
     * Generate Trial Balance Report
     * @param currentDate The report date
     * @param rootDir The output directory
     */
    private void generateTrialBalanceReport(Date currentDate, String rootDir) {
        try {
            def params = [
                _name: "trial_balance",
                _format: "PDF",
                _file: "/reports/gl/trial_balance",
                reportDate: currentDate
            ]
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [:])
            def file = jasperService.generateReport(reportDef).toByteArray()
            FileUtils.writeByteArrayToFile(new File(rootDir + "/trial_balance.pdf"), file)
            
        } catch (Exception e) {
            log.error("Error generating trial balance report: ${e.message}", e)
        }
    }

    /**
     * Generate Daily Transaction Summary
     * @param currentDate The report date
     * @param rootDir The output directory
     */
    private void generateDailyTransactionSummary(Date currentDate, String rootDir) {
        try {
            def params = [
                _name: "daily_transaction_summary",
                _format: "PDF",
                _file: "/reports/transactions/daily_summary",
                reportDate: currentDate
            ]
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [:])
            def file = jasperService.generateReport(reportDef).toByteArray()
            FileUtils.writeByteArrayToFile(new File(rootDir + "/daily_transaction_summary.pdf"), file)
            
        } catch (Exception e) {
            log.error("Error generating daily transaction summary: ${e.message}", e)
        }
    }

    /**
     * Generate Cash Position Report
     * @param currentDate The report date
     * @param rootDir The output directory
     */
    private void generateCashPositionReport(Date currentDate, String rootDir) {
        try {
            def params = [
                _name: "cash_position",
                _format: "PDF",
                _file: "/reports/cash/position",
                reportDate: currentDate
            ]
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [:])
            def file = jasperService.generateReport(reportDef).toByteArray()
            FileUtils.writeByteArrayToFile(new File(rootDir + "/cash_position.pdf"), file)
            
        } catch (Exception e) {
            log.error("Error generating cash position report: ${e.message}", e)
        }
    }

    /**
     * Generate Loan Portfolio Summary
     * @param currentDate The report date
     * @param rootDir The output directory
     */
    private void generateLoanPortfolioSummary(Date currentDate, String rootDir) {
        try {
            def params = [
                _name: "loan_portfolio_summary",
                _format: "PDF",
                _file: "/reports/loans/portfolio_summary",
                reportDate: currentDate
            ]
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [:])
            def file = jasperService.generateReport(reportDef).toByteArray()
            FileUtils.writeByteArrayToFile(new File(rootDir + "/loan_portfolio_summary.pdf"), file)
            
        } catch (Exception e) {
            log.error("Error generating loan portfolio summary: ${e.message}", e)
        }
    }

    /**
     * Generate Deposit Summary Report
     * @param currentDate The report date
     * @param rootDir The output directory
     */
    private void generateDepositSummaryReport(Date currentDate, String rootDir) {
        try {
            def params = [
                _name: "deposit_summary",
                _format: "PDF",
                _file: "/reports/deposits/summary",
                reportDate: currentDate
            ]
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [:])
            def file = jasperService.generateReport(reportDef).toByteArray()
            FileUtils.writeByteArrayToFile(new File(rootDir + "/deposit_summary.pdf"), file)
            
        } catch (Exception e) {
            log.error("Error generating deposit summary report: ${e.message}", e)
        }
    }

    /**
     * Generate Monthly Trial Balance
     * @param currentDate The report date
     * @param rootDir The output directory
     */
    private void generateMonthlyTrialBalance(Date currentDate, String rootDir) {
        try {
            def params = [
                _name: "monthly_trial_balance",
                _format: "PDF",
                _file: "/reports/gl/monthly_trial_balance",
                reportDate: currentDate
            ]
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [:])
            def file = jasperService.generateReport(reportDef).toByteArray()
            FileUtils.writeByteArrayToFile(new File(rootDir + "/monthly_trial_balance.pdf"), file)
            
        } catch (Exception e) {
            log.error("Error generating monthly trial balance: ${e.message}", e)
        }
    }

    /**
     * Generate Monthly Transaction Summary
     * @param currentDate The report date
     * @param rootDir The output directory
     */
    private void generateMonthlyTransactionSummary(Date currentDate, String rootDir) {
        try {
            def params = [
                _name: "monthly_transaction_summary",
                _format: "PDF",
                _file: "/reports/transactions/monthly_summary",
                reportDate: currentDate
            ]
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [:])
            def file = jasperService.generateReport(reportDef).toByteArray()
            FileUtils.writeByteArrayToFile(new File(rootDir + "/monthly_transaction_summary.pdf"), file)
            
        } catch (Exception e) {
            log.error("Error generating monthly transaction summary: ${e.message}", e)
        }
    }

    /**
     * Generate Monthly Interest Report
     * @param currentDate The report date
     * @param rootDir The output directory
     */
    private void generateMonthlyInterestReport(Date currentDate, String rootDir) {
        try {
            def params = [
                _name: "monthly_interest_report",
                _format: "PDF",
                _file: "/reports/interest/monthly_report",
                reportDate: currentDate
            ]
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [:])
            def file = jasperService.generateReport(reportDef).toByteArray()
            FileUtils.writeByteArrayToFile(new File(rootDir + "/monthly_interest_report.pdf"), file)
            
        } catch (Exception e) {
            log.error("Error generating monthly interest report: ${e.message}", e)
        }
    }

    /**
     * Generate Monthly Loan Performance
     * @param currentDate The report date
     * @param rootDir The output directory
     */
    private void generateMonthlyLoanPerformance(Date currentDate, String rootDir) {
        try {
            def params = [
                _name: "monthly_loan_performance",
                _format: "PDF",
                _file: "/reports/loans/monthly_performance",
                reportDate: currentDate
            ]
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [:])
            def file = jasperService.generateReport(reportDef).toByteArray()
            FileUtils.writeByteArrayToFile(new File(rootDir + "/monthly_loan_performance.pdf"), file)
            
        } catch (Exception e) {
            log.error("Error generating monthly loan performance: ${e.message}", e)
        }
    }

    /**
     * Generate Monthly Deposit Analysis
     * @param currentDate The report date
     * @param rootDir The output directory
     */
    private void generateMonthlyDepositAnalysis(Date currentDate, String rootDir) {
        try {
            def params = [
                _name: "monthly_deposit_analysis",
                _format: "PDF",
                _file: "/reports/deposits/monthly_analysis",
                reportDate: currentDate
            ]
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [:])
            def file = jasperService.generateReport(reportDef).toByteArray()
            FileUtils.writeByteArrayToFile(new File(rootDir + "/monthly_deposit_analysis.pdf"), file)
            
        } catch (Exception e) {
            log.error("Error generating monthly deposit analysis: ${e.message}", e)
        }
    }

    /**
     * Zip all files in the specified directory
     * @param zipFileName The name of the zip file
     * @param sourceDir The source directory to zip
     */
    private void zipFiles(String zipFileName, String sourceDir) {
        try {
            def zipFile = new File(sourceDir + "/${zipFileName}.zip")
            def zos = new ZipOutputStream(new FileOutputStream(zipFile))
            
            def sourceDirectory = new File(sourceDir)
            sourceDirectory.eachFile { file ->
                if (file.isFile() && !file.name.endsWith('.zip')) {
                    def entry = new ZipEntry(file.name)
                    zos.putNextEntry(entry)
                    zos << file.bytes
                    zos.closeEntry()
                }
            }
            
            zos.close()
            
        } catch (Exception e) {
            log.error("Error zipping files: ${e.message}", e)
            throw new RuntimeException("File zipping failed: ${e.message}")
        }
    }

    /**
     * Download EOD report
     */
    private void downloadEodReport() {
        try {
            // Implementation for downloading the generated report
            log.info("EOD report download initiated")
        } catch (Exception e) {
            log.error("Error downloading EOD report: ${e.message}", e)
        }
    }
}
