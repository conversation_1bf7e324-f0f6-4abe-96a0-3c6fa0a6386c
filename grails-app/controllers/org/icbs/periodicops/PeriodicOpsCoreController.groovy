package org.icbs.periodicops

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.admin.Institution
import org.icbs.periodicops.PeriodicOpsLog
import org.icbs.lov.ConfigItemStatus

/**
 * PeriodicOpsCoreController - Main coordination controller for periodic operations
 * 
 * This controller manages core periodic operations coordination including:
 * - Main periodic operations index and navigation
 * - System status monitoring and coordination
 * - Periodic operations logging and tracking
 * - Progress monitoring and session management
 * - Core utility functions for periodic operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class PeriodicOpsCoreController {
    
    // Service Dependencies
    def periodicOpsService
    def auditLogService
    def sessionFactory
    
    static allowedMethods = [
        index: "GET",
        periodicOpsSuccess: "POST",
        cleanUpGorm: "POST"
    ]

    /**
     * Main periodic operations index
     */
    def index() {
        def runDate = Branch.read(1).runDate
        render(view: "index", model: [startOfDayDate: runDate])
    }

    /**
     * Periodic operations success handler
     */
    def periodicOpsSuccess(PeriodicOpsLog pLog) {
        println("=========== periodicSuccess =============")
        pLog.save(flush: true, failOnError: true)
        
        def description = "Periodic operation completed: ${pLog.operation} - ${pLog.status}"
        auditLogService.insert('130', 'PER00400', description, 'PeriodicOpsCore', null, null, null, pLog.id)
        
        render(view: "success", model: [periodicOpsLog: pLog])
    }

    /**
     * Clean up GORM session
     */
    def cleanUpGorm() {
        try {
            def session = sessionFactory.currentSession
            session.flush()
            session.clear()
            
            render([success: true, message: "GORM session cleaned"] as JSON)
            
        } catch (Exception e) {
            log.error("Error cleaning GORM session", e)
            render([success: false, message: "Error cleaning session: ${e.message}"] as JSON)
        }
        return
    }

    /**
     * Get system status for periodic operations
     */
    def getSystemStatus() {
        def branch = Branch.read(1)
        def systemLock = Institution.findByParamCode('GEN.10250')
        
        def status = [
            runDate: branch.runDate.format("MM/dd/yyyy"),
            isSystemLocked: systemLock?.paramValue == 'TRUE',
            branchStatus: branch.branchRunStatus?.description,
            isTelleringActive: branch.isTelleringActive,
            isEOD: branch.isEOD,
            lastSOD: getLastSODDate(),
            lastEOD: getLastEODDate()
        ]
        
        render(status as JSON)
        return
    }

    /**
     * Set progress session for monitoring
     */
    def setProgressSession(String progress, String message, String flag, String step) {
        session.progress = progress
        session.message = message
        session.flag = flag
        session.step = step
        
        render([
            progress: progress,
            message: message,
            flag: flag,
            step: step
        ] as JSON)
        return
    }

    /**
     * Get current progress status
     */
    def getProgressStatus() {
        render([
            progress: session.progress ?: "0",
            message: session.message ?: "Ready",
            flag: session.flag ?: "ready",
            step: session.step ?: "0"
        ] as JSON)
        return
    }

    /**
     * Clear progress session
     */
    def clearProgressSession() {
        session.progress = null
        session.message = null
        session.flag = null
        session.step = null
        
        render([success: true, message: "Progress session cleared"] as JSON)
        return
    }

    /**
     * Get periodic operations log
     */
    def periodicOpsLog(Integer max) {
        println("params: " + params)
        params.max = Math.min(max ?: 25, 100)
        
        if (params.sort == null) {
            params.sort = "id"
            params.order = "desc"
        }

        def periodicOpsLogList = PeriodicOpsLog.list(params)
        def periodicOpsLogCount = PeriodicOpsLog.count()

        render(view: "log", model: [
            periodicOpsLogList: periodicOpsLogList,
            periodicOpsLogCount: periodicOpsLogCount,
            params: params
        ])
    }

    /**
     * Show periodic operations log details
     */
    def showPeriodicOpsLog() {
        def periodicOpsLogInstance = PeriodicOpsLog.get(params.id)
        if (!periodicOpsLogInstance) {
            notFound()
            return
        }
        
        render(view: "showLog", model: [periodicOpsLogInstance: periodicOpsLogInstance])
    }

    /**
     * Validate system readiness for periodic operations
     */
    def validateSystemReadiness() {
        def validationResult = [
            ready: true,
            warnings: [],
            errors: []
        ]
        
        try {
            def branch = Branch.read(1)
            def systemLock = Institution.findByParamCode('GEN.10250')
            
            // Check if system is locked
            if (systemLock?.paramValue == 'TRUE') {
                validationResult.errors.add("System is currently locked")
                validationResult.ready = false
            }
            
            // Check if tellers are balanced
            def unbalancedTellers = UserMaster.countByIsTellerBalancedAndConfigItemStatus(false, ConfigItemStatus.get(2))
            if (unbalancedTellers > 0) {
                validationResult.warnings.add("${unbalancedTellers} tellers are not balanced")
            }
            
            // Check if EOD is already processed
            if (branch.isEOD) {
                validationResult.warnings.add("End of day already processed for current date")
            }
            
            // Check active user sessions
            def activeSessions = getActiveUserSessions()
            if (activeSessions > 1) {
                validationResult.warnings.add("${activeSessions} users are still logged in")
            }
            
        } catch (Exception e) {
            log.error("Error validating system readiness", e)
            validationResult.errors.add("Error validating system: ${e.message}")
            validationResult.ready = false
        }
        
        render(validationResult as JSON)
        return
    }

    /**
     * Get periodic operations statistics
     */
    def getPeriodicOpsStatistics() {
        def stats = [:]
        
        try {
            def today = new Date().clearTime()
            def thisMonth = new Date().clearTime()
            thisMonth.set(Calendar.DAY_OF_MONTH, 1)
            
            stats.todayOperations = PeriodicOpsLog.countByDateCreatedGreaterThanEquals(today)
            stats.monthlyOperations = PeriodicOpsLog.countByDateCreatedGreaterThanEquals(thisMonth)
            stats.successfulOperations = PeriodicOpsLog.countByStatus("SUCCESS")
            stats.failedOperations = PeriodicOpsLog.countByStatus("FAILED")
            
            // Recent operations
            stats.recentOperations = PeriodicOpsLog.createCriteria().list {
                order("dateCreated", "desc")
                maxResults(5)
            }.collect { log ->
                [
                    operation: log.operation,
                    status: log.status,
                    dateCreated: log.dateCreated.format("MM/dd/yyyy HH:mm:ss"),
                    duration: log.duration
                ]
            }
            
        } catch (Exception e) {
            log.error("Error getting periodic ops statistics", e)
            stats.error = "Error getting statistics: ${e.message}"
        }
        
        render(stats as JSON)
        return
    }

    /**
     * Emergency system unlock
     */
    @Transactional
    def emergencyUnlock() {
        try {
            def currentUser = UserMaster.get(session.user_id)
            
            // Check if user has emergency unlock privileges
            if (!currentUser.hasRole('SYSTEM_ADMIN')) {
                render([success: false, message: "Insufficient privileges for emergency unlock"] as JSON)
                return
            }
            
            def lockMe = Institution.findByParamCode('GEN.10250')
            if (lockMe) {
                lockMe.paramValue = 'FALSE'
                lockMe.save(flush: true)
                
                def description = "Emergency system unlock performed by ${currentUser.username}"
                auditLogService.insert('130', 'PER00500', description, 'PeriodicOpsCore', null, null, null, currentUser.id)
                
                render([success: true, message: "Emergency unlock completed"] as JSON)
            } else {
                render([success: false, message: "System lock configuration not found"] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error performing emergency unlock", e)
            render([success: false, message: "Error performing unlock: ${e.message}"] as JSON)
        }
        return
    }

    // Helper methods
    private def getLastSODDate() {
        def lastSOD = PeriodicOpsLog.createCriteria().get {
            eq("operation", "START_OF_DAY")
            eq("status", "SUCCESS")
            order("dateCreated", "desc")
            maxResults(1)
        }
        return lastSOD?.dateCreated?.format("MM/dd/yyyy HH:mm:ss")
    }

    private def getLastEODDate() {
        def lastEOD = PeriodicOpsLog.createCriteria().get {
            eq("operation", "END_OF_DAY")
            eq("status", "SUCCESS")
            order("dateCreated", "desc")
            maxResults(1)
        }
        return lastEOD?.dateCreated?.format("MM/dd/yyyy HH:mm:ss")
    }

    private def getActiveUserSessions() {
        // This would typically check UserSession or similar
        return UserMaster.countByConfigItemStatus(ConfigItemStatus.get(2))
    }

    protected def notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = message(code: 'default.not.found.message', args: ['PeriodicOpsLog', params.id])
                redirect action: "index", method: "GET"
            }
            '*'{ render status: NOT_FOUND }
        }
    }
}
