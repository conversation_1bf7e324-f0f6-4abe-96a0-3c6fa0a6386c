package org.icbs.periodicops

import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.grails.web.json.JSONObject
import groovy.time.TimeCategory
import org.icbs.admin.UserMaster
import java.nio.file.*
import org.icbs.admin.Branch
import org.icbs.admin.Holiday
import org.icbs.admin.UserMaster
import org.icbs.admin.CheckDepositClearingType
import org.icbs.admin.Institution
import org.icbs.admin.BranchHoliday
import org.icbs.security.UserSession
import org.icbs.lov.BranchRunStatus
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.gl.GlBatchHdr
import org.icbs.gl.GlBatch
import org.icbs.gl.GlDailyFile
import org.icbs.gl.GlMonthlyBalance
import org.icbs.gl.GlAccount
import org.icbs.deposit.Deposit
import org.icbs.periodicops.PeriodicOpsLog
import org.icbs.periodicops.DailyLoanRecoveries
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositStatus
import org.icbs.lov.DepositType
import org.icbs.loans.Loan
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanInstallmentStatus
import org.icbs.lov.GlBatchHdrStatus
import org.icbs.loans.Loan
import org.icbs.loans.LoanLedger
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnPassbookLine
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.cif.Customer
import grails.gorm.transactions.Transactional
import org.apache.commons.io.FileUtils
import org.apache.tools.zip.ZipEntry
import java.text.SimpleDateFormat
import java.util.zip.ZipOutputStream
import java.util.Calendar
import org.icbs.admin.UserMessage
import static grails.async.Promises.*
import groovy.sql.Sql
import org.icbs.tellering.TxnCOCI
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.lov.CheckStatus
import org.icbs.lov.TxnCheckStatus
import org.icbs.periodicops.DailyBalance
import org.icbs.periodicops.MonthlyBalance
import org.icbs.periodicops.MonthlyPointerLoan
import org.icbs.periodicops.MonthlyCustomer
import org.icbs.periodicops.DailyCheckClearing

/**
 * EndOfMonthController - Handles all End of Month banking operations
 * 
 * This controller manages the critical end-of-month processes including:
 * - Monthly balance calculations and archiving
 * - GL monthly balance updates
 * - Deposit and loan monthly processing
 * - Customer monthly record creation
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class EndOfMonthController {
    
    // Service Dependencies
    def periodicOpsService
    def loanPeriodicOpsService
    def IsTelleringActiveService
    def depositPeriodicOpsService
    def jasperService
    def AuditLogService
    def GlTransactionService
    def UserMasterService
    def sessionFactory
    def dataSource
    def glPeriodicOpsService

    /**
     * Display the end of month index page
     * @return rendered view with current run date
     */
    def index() {
        try {
            def runDate = Branch.read(1).runDate
            render(view: "index", model: [endOfMonthDate: runDate])
        } catch (Exception e) {
            log.error("Error loading end of month index: ${e.message}", e)
            flash.message = "Error loading end of month page |error|alert"
            redirect(controller: "periodicOps", action: "index")
        }
    }

    /**
     * Get end of month progress via AJAX
     * @return JSON response with progress information
     */
    def endOfMonthProgressAjax() {
        try {
            def jsonObject = new JSONObject()
            jsonObject = jsonObject.put('progress', session.progress)
            jsonObject = jsonObject.put('message', session.message)
            jsonObject = jsonObject.put('flag', session.flag)
            render jsonObject as JSON
        } catch (Exception e) {
            log.error("Error getting end of month progress: ${e.message}", e)
            render([error: "Unable to get progress information"] as JSON)
        }
    }

    /**
     * Main end of month processing method
     * @param currentDate The processing date (optional, defaults to current run date)
     */
    @Transactional
    def endOfMonth(Date currentDate = null) {
        try {
            log.info("Starting End of Month process")
            
            if (!currentDate) {
                currentDate = Branch.get(1).runDate
            }
            
            // Initialize progress tracking
            setProgressSession("0", "Starting End of Month Process", "start", "0")
            
            def pLog = new PeriodicOpsLog(runDate: currentDate, startTime: new Date().toString(), 
                                        processType: 'EOM', status: 0)
            pLog.save(flush: true)
            
            // Process branch operations
            processBranchOperations(currentDate)
            
            // Update GL monthly balances
            updateGLMonthlyBalances(currentDate)
            
            // Create monthly deposit balances
            createMonthlyDepositBalances(currentDate)
            
            // Create monthly loan balances
            createMonthlyLoanBalances(currentDate)
            
            // Create monthly customer records
            createMonthlyCustomerRecords(currentDate)
            
            // Complete the process
            pLog.endTime = new Date().toString()
            pLog.status = 1
            pLog.save(flush: true)
            
            setProgressSession("100", "End of Month Complete", "end@@#${pLog.id}", "${pLog.id}")
            log.info("End of Month process completed successfully")
            
        } catch (Exception e) {
            log.error("Error during end of month process: ${e.message}", e)
            setProgressSession("0", "ERROR: ${e.message}", "error", "0")
            flash.message = "End of Month process failed: ${e.message} |error|alert"
            redirect(controller: "periodicOps", action: "index")
        }
    }

    /**
     * Process branch operations for end of month
     * @param currentDate The processing date
     */
    private void processBranchOperations(Date currentDate) {
        try {
            setProgressSession("10", "Processing Branch Operations", "processing", "0")
            
            def branchList = Branch.list(sort: "id", order: "asc")
            def totalBranches = branchList.size()
            def currentBranch = 0
            
            for (branch in branchList) {
                currentBranch++
                def progressPercent = (currentBranch * 20 / totalBranches) + 10
                
                setProgressSession("${progressPercent}", "Processing Branch: ${branch.name}", "processing", "0")
                
                // Transfer to dormant
                depositPeriodicOpsService.TransferToDormant(currentDate, branch, 
                    UserMaster.get(session.user_id), 'monthly')
                
                // Process deposits end of month
                depositPeriodicOpsService.endOfMonth(currentDate, branch, UserMaster.get(session.user_id))
                
                // Process loans end of month
                loanPeriodicOpsService.endOfMonth(currentDate, branch, UserMaster.get(session.user_id))
            }
            
        } catch (Exception e) {
            log.error("Error processing branch operations: ${e.message}", e)
            throw new RuntimeException("Branch operations failed: ${e.message}")
        }
    }

    /**
     * Update GL monthly balances
     * @param currentDate The processing date
     */
    private void updateGLMonthlyBalances(Date currentDate) {
        try {
            setProgressSession("30", "Creating GL Monthly Balances", "processing", "0")
            
            def db = new Sql(dataSource)
            def sqlstmt = "select id from gl_account where financial_year = ${currentDate.format('yyyy').toInteger()}"
            def glList = db.rows(sqlstmt)
            
            Integer i = 0
            for (g in glList) {
                def gl = GlAccount.read(g.id)
                
                if (!gl.isAttached()) {
                    gl.attach()
                }
                
                def glm = new GlMonthlyBalance(
                    glAcct: gl,
                    branch: gl.branch,
                    currency: gl.currency,
                    code: gl.code,
                    name: gl.name,
                    refDate: currentDate,
                    refYear: currentDate.format('yyyy'),
                    refMonth: new SimpleDateFormat("MM").format(currentDate),
                    debitBalance: gl.debitBalance,
                    creditBalance: gl.creditBalance
                )
                glm.save(validate: false)
                
                i++
                if (i == 50) {
                    cleanUpGorm()
                    i = 0
                }
            }
            
        } catch (Exception e) {
            log.error("Error updating GL monthly balances: ${e.message}", e)
            throw new RuntimeException("GL monthly balance update failed: ${e.message}")
        }
    }

    /**
     * Create monthly deposit balances
     * @param currentDate The processing date
     */
    private void createMonthlyDepositBalances(Date currentDate) {
        try {
            setProgressSession("50", "Creating Deposit Monthly Balances", "processing", "0")
            
            def db = new Sql(dataSource)
            def sqlstmt = "select id from deposit"
            def depList = db.rows(sqlstmt)
            
            Integer i = 0
            for (d in depList) {
                def dl = Deposit.read(d.id)
                
                if (!dl.isAttached()) {
                    dl.attach()
                }
                
                Double intCap = 0.00D
                Double taxWheld = 0.00D
                
                if (dl.type.id != 3 && dl.status.id > 1 && dl.status.id < 7) {
                    intCap = dl.lastInterestPosted
                    taxWheld = intCap * dl.depositTaxChargeScheme.taxRate.div(100)
                }
                
                def dm = new MonthlyBalance(
                    refMonth: new SimpleDateFormat("MM").format(currentDate),
                    refYear: currentDate.format('yyyy'),
                    appType: dl.type.id.toString(),
                    branch: dl.branch,
                    currency: dl.product.currency,
                    refDate: currentDate,
                    accountNo: dl.acctNo,
                    accountStatus: dl.status.id,
                    availableBal: dl.availableBalAmt,
                    averageBal: dl.lmAveBalAmt,
                    closingBal: dl.ledgerBalAmt,
                    accruedInterestThisMonth: dl.accruedIntForTheMonth,
                    accruedInterestCumulative: dl.accruedIntPayable,
                    grossInterestCapital: intCap,
                    taxWithheld: taxWheld,
                    lastTxnDate: dl.lastTxnDate
                )
                dm.save(validate: true)
                
                i++
                if (i == 50) {
                    cleanUpGorm()
                    i = 0
                }
            }
            
        } catch (Exception e) {
            log.error("Error creating monthly deposit balances: ${e.message}", e)
            throw new RuntimeException("Monthly deposit balance creation failed: ${e.message}")
        }
    }

    /**
     * Create monthly loan balances and pointer records
     * @param currentDate The processing date
     */
    private void createMonthlyLoanBalances(Date currentDate) {
        try {
            setProgressSession("70", "Creating Loan Monthly Balances", "processing", "0")
            
            def db = new Sql(dataSource)
            def sqlstmt = "select id from loan"
            def loanList = db.rows(sqlstmt)
            
            Integer i = 0
            for (ln in loanList) {
                def l = Loan.read(ln.id)
                
                if (!l.isAttached()) {
                    l.attach()
                }
                
                // Create monthly balance record
                def lm = new MonthlyBalance(
                    refMonth: new SimpleDateFormat("MM").format(currentDate),
                    refYear: currentDate.format('yyyy'),
                    appType: '4',
                    branch: l.branch,
                    currency: l.product.currency,
                    refDate: currentDate,
                    accountNo: l.accountNo,
                    accountStatus: l.loanPerformanceId.id,
                    loanPastDueStatus: l.loanPerformanceId.id.toString(),
                    closingBal: l.balanceAmount,
                    loanInterestBal: l.interestBalanceAmount,
                    penaltyBal: l.penaltyBalanceAmount,
                    uidBal: l.advInterest
                )
                lm.save(validate: true)
                
                // Create monthly pointer loan record
                createMonthlyPointerLoan(currentDate, l)
                
                i++
                if (i == 50) {
                    cleanUpGorm()
                    i = 0
                }
            }
            
        } catch (Exception e) {
            log.error("Error creating monthly loan balances: ${e.message}", e)
            throw new RuntimeException("Monthly loan balance creation failed: ${e.message}")
        }
    }

    /**
     * Create monthly pointer loan record
     * @param currentDate The processing date
     * @param loan The loan to create pointer for
     */
    private void createMonthlyPointerLoan(Date currentDate, Loan loan) {
        try {
            def lp = new MonthlyPointerLoan(
                refDate: currentDate,
                loanApplication: loan.loanApplication,
                accountNo: loan.accountNo,
                pnNo: loan.pnNo,
                ownershipType: loan.ownershipType,
                customer: loan.customer,
                product: loan.product,
                branch: loan.branch,
                currency: loan.currency,
                security: loan.security,
                interestIncomeScheme: loan.interestIncomeScheme,
                currentPenaltyScheme: loan.currentPenaltyScheme,
                pastDuePenaltyScheme: loan.pastDuePenaltyScheme,
                interestRate: loan.interestRate,
                penaltyRate: loan.penaltyRate,
                penaltyAmount: loan.penaltyAmount,
                serviceCharge: loan.serviceCharge,
                grantedAmount: loan.grantedAmount,
                term: loan.term,
                frequency: loan.frequency,
                numInstallments: loan.numInstallments,
                balloonInstallments: loan.balloonInstallments,
                applicationDate: loan.applicationDate,
                openingDate: loan.openingDate,
                interestStartDate: loan.interestStartDate,
                firstInstallmentDate: loan.firstInstallmentDate,
                maturityDate: loan.maturityDate,
                effectiveInterestRate: loan.effectiveInterestRate,
                monthlyInterestRate: loan.monthlyInterestRate,
                totalNetProceeds: loan.totalNetProceeds,
                balanceAmount: loan.balanceAmount,
                totalDisbursementAmount: loan.totalDisbursementAmount,
                lastTransactionNo: loan.lastTransactionNo,
                transactionSequenceNo: loan.transactionSequenceNo,
                lastTransactionDate: loan.lastTransactionDate,
                lastCustormerTransactionDate: loan.lastCustormerTransactionDate,
                overduePrincipalBalance: loan.overduePrincipalBalance,
                normalInterestAmount: loan.normalInterestAmount,
                interestBalanceAmount: loan.interestBalanceAmount,
                penaltyBalanceAmount: loan.penaltyBalanceAmount,
                serviceChargeBalanceAmount: loan.serviceChargeBalanceAmount,
                taxBalanceAmount: loan.taxBalanceAmount,
                accruedInterestAmount: loan.accruedInterestAmount,
                advInterest: loan.advInterest,
                advInterestDays: loan.advInterestDays,
                advInterestPeriods: loan.advInterestPeriods,
                hasInterestAccrual: loan.hasInterestAccrual,
                accruedInterestDate: loan.accruedInterestDate,
                special: loan.special,
                performanceClassification: loan.performanceClassification,
                status: loan.status,
                statusChangedDate: loan.statusChangedDate,
                approvedBy: loan.approvedBy,
                dateApproved: loan.dateApproved,
                glLink: loan.glLink,
                prevGLLink: loan.prevGLLink,
                loanType: loan.loanType,
                loanProject: loan.loanProject,
                loanKindOfLoan: loan.loanKindOfLoan,
                loanProvision: loan.loanProvision,
                loanPerformanceId: loan.loanPerformanceId,
                loanSecurity: loan.loanSecurity,
                ageInArrears: loan.ageInArrears,
                loanProvisionBsp: loan.loanProvisionBsp,
                hash: 'X'
            )
            lp.save(validate: true, failOnError: true)
            
        } catch (Exception e) {
            log.error("Error creating monthly pointer loan for ${loan.accountNo}: ${e.message}", e)
            throw new RuntimeException("Monthly pointer loan creation failed for ${loan.accountNo}: ${e.message}")
        }
    }

    /**
     * Create monthly customer records
     * @param currentDate The processing date
     */
    private void createMonthlyCustomerRecords(Date currentDate) {
        try {
            setProgressSession("90", "Creating Monthly Customer Records", "processing", "0")
            
            def db = new Sql(dataSource)
            def sqlstmt = "select id from customer"
            def cl = db.rows(sqlstmt)
            
            Integer i = 0
            for (cust in cl) {
                def c = Customer.read(cust.id)
                
                if (!c.isAttached()) {
                    c.attach()
                }
                
                def cm = new MonthlyCustomer(
                    refDate: currentDate,
                    customer: c,
                    type: c.type,
                    branch: c.branch,
                    cid: c.customerId,
                    name1: c.name1,
                    name2: c.name2,
                    name3: c.name3,
                    name4: c.name4,
                    displayName: c.displayName,
                    shortAddress: c.shortAddress,
                    pepDescription: c.pepDescription,
                    amla: c.amla,
                    birthDate: c.birthDate,
                    title: c.title,
                    gender: c.gender,
                    civilStatus: c.civilStatus,
                    birthPlace: c.birthPlace,
                    isTaxable: c.isTaxable,
                    creditLimit: c.creditLimit,
                    customerCode1: c.customerCode1,
                    customerCode2: c.customerCode2,
                    customerCode3: c.customerCode3,
                    nationality: c.nationality,
                    sourceOfIncome: c.sourceOfIncome,
                    dosriCode: c.dosriCode,
                    sssNo: c.sssNo,
                    gisNo: c.gisNo,
                    tinNo: c.tinNo,
                    passportNo: c.passportNo,
                    remarks: c.remarks,
                    group: c.group,
                    status: c.status
                )
                cm.save(validate: true, failOnError: true)
                
                i++
                if (i == 50) {
                    cleanUpGorm()
                    i = 0
                }
            }
            
        } catch (Exception e) {
            log.error("Error creating monthly customer records: ${e.message}", e)
            throw new RuntimeException("Monthly customer record creation failed: ${e.message}")
        }
    }

    /**
     * Clean up GORM session to prevent memory issues
     */
    private void cleanUpGorm() {
        try {
            def session = sessionFactory.currentSession
            session.flush()
            session.clear()
            def propertyInstanceMap = org.codehaus.groovy.grails.plugins.DomainClassGrailsPlugin.PROPERTY_INSTANCE_MAP
            propertyInstanceMap.get().clear()
        } catch (Exception e) {
            log.error("Error cleaning up GORM: ${e.message}", e)
        }
    }

    /**
     * Set progress session for tracking end of month progress
     * @param progress Progress percentage
     * @param message Progress message
     * @param flag Progress flag
     * @param logId Log ID
     */
    private void setProgressSession(String progress, String message, String flag, String logId) {
        try {
            session.progress = progress
            session.message = message
            session.flag = flag
            session.logId = logId
        } catch (Exception e) {
            log.error("Error setting progress session: ${e.message}", e)
        }
    }
}
