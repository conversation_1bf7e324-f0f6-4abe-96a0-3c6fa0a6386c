package org.icbs.periodicops

import grails.gorm.transactions.Transactional
import org.icbs.admin.Branch
import org.apache.commons.io.FileUtils
import java.text.SimpleDateFormat
import java.nio.file.*
import java.util.zip.ZipOutputStream
import java.util.zip.ZipEntry

/**
 * PERFORMANCE REFACTOR: Extracted report generation functionality from PeriodicOpsController
 * This focused controller handles only report generation operations
 */
class ReportGenerationController {

    def jasperService

    /**
     * PERFORMANCE OPTIMIZED: Generate start of day reports asynchronously
     */
    def generateStartOfDayReports() {
        try {
            Date startOfDayDate = Branch.read(1).runDate
            
            // Generate reports asynchronously to improve performance
            Thread.start {
                createStartOfDayReport(startOfDayDate)
            }
            
            flash.message = 'Start of day reports generation initiated |success|alert'
            
        } catch (Exception e) {
            log.error("Error initiating start of day reports: ${e.message}", e)
            flash.error = 'Failed to initiate report generation |error|alert'
        }
        
        redirect(controller: 'periodicOps', action: 'index')
    }

    /**
     * PERFORMANCE OPTIMIZED: Download start of day reports
     */
    def downloadStartOfDayReports() {
        try {
            Date startOfDayDate = Branch.read(1).runDate
            def rootDir = request.getSession().getServletContext().getRealPath("/") + 
                         "reports_repository/startOfDay/" + 
                         new SimpleDateFormat("yyyy-MM-dd").format(startOfDayDate)
            
            Path path = Paths.get(rootDir + "/reports.zip")
            
            if (!Files.exists(path)) {
                flash.error = 'Reports not found. Please generate reports first |error|alert'
                redirect(controller: 'periodicOps', action: 'index')
                return
            }
            
            byte[] data = Files.readAllBytes(path)
            response.setHeader("Content-disposition", "filename=\"start_of_day_reports.zip\"")
            response.contentType = "application/zip"
            response.outputStream << data
            response.outputStream.flush()
            
        } catch (Exception e) {
            log.error("Error downloading start of day reports: ${e.message}", e)
            flash.error = 'Failed to download reports |error|alert'
            redirect(controller: 'periodicOps', action: 'index')
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Generate end of day reports asynchronously
     */
    def generateEndOfDayReports() {
        try {
            Date endOfDayDate = Branch.read(1).runDate.minus(1)
            
            // Generate reports asynchronously to improve performance
            Thread.start {
                createEndOfDayReport(endOfDayDate, 1)
            }
            
            flash.message = 'End of day reports generation initiated |success|alert'
            
        } catch (Exception e) {
            log.error("Error initiating end of day reports: ${e.message}", e)
            flash.error = 'Failed to initiate report generation |error|alert'
        }
        
        redirect(controller: 'periodicOps', action: 'index')
    }

    /**
     * PERFORMANCE OPTIMIZED: Download end of day reports
     */
    def downloadEndOfDayReports() {
        try {
            Date endOfDayDate = Branch.read(1).runDate.minus(1)
            def rootDir = request.getSession().getServletContext().getRealPath("/") + 
                         "reports_repository/endOfDay/" + 
                         new SimpleDateFormat("yyyy-MM-dd").format(endOfDayDate)
            
            Path path = Paths.get(rootDir + "/reports.zip")
            
            if (!Files.exists(path)) {
                flash.error = 'Reports not found. Please generate reports first |error|alert'
                redirect(controller: 'periodicOps', action: 'index')
                return
            }
            
            byte[] data = Files.readAllBytes(path)
            response.setHeader("Content-disposition", "filename=\"end_of_day_reports.zip\"")
            response.contentType = "application/zip"
            response.outputStream << data
            response.outputStream.flush()
            
        } catch (Exception e) {
            log.error("Error downloading end of day reports: ${e.message}", e)
            flash.error = 'Failed to download reports |error|alert'
            redirect(controller: 'periodicOps', action: 'index')
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Generate end of month reports
     */
    def generateEndOfMonthReports() {
        try {
            Date endOfMonthDate = Branch.read(1).runDate.minus(1)
            
            // Generate reports asynchronously to improve performance
            Thread.start {
                createEndOfDayReport(endOfMonthDate, 2)
            }
            
            flash.message = 'End of month reports generation initiated |success|alert'
            
        } catch (Exception e) {
            log.error("Error initiating end of month reports: ${e.message}", e)
            flash.error = 'Failed to initiate report generation |error|alert'
        }
        
        redirect(controller: 'periodicOps', action: 'index')
    }

    /**
     * PERFORMANCE OPTIMIZED: Get report generation status
     */
    def getReportStatus() {
        try {
            def reportTypes = ['startOfDay', 'endOfDay', 'endOfMonth']
            def status = [:]
            
            reportTypes.each { reportType ->
                def date = reportType == 'startOfDay' ? 
                          Branch.read(1).runDate : 
                          Branch.read(1).runDate.minus(1)
                
                def rootDir = request.getSession().getServletContext().getRealPath("/") + 
                             "reports_repository/${reportType}/" + 
                             new SimpleDateFormat("yyyy-MM-dd").format(date)
                
                Path path = Paths.get(rootDir + "/reports.zip")
                status[reportType] = [
                    available: Files.exists(path),
                    date: date,
                    size: Files.exists(path) ? Files.size(path) : 0
                ]
            }
            
            render(contentType: 'application/json') {
                [
                    status: status,
                    timestamp: new Date()
                ]
            }
            
        } catch (Exception e) {
            log.error("Error checking report status: ${e.message}", e)
            render(status: 500, contentType: 'application/json') {
                [error: 'Failed to check report status']
            }
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Create start of day report with error handling
     */
    private void createStartOfDayReport(Date date) {
        try {
            def rootDir = request.getSession().getServletContext().getRealPath("/") + 
                         "reports_repository/startOfDay/" + 
                         new SimpleDateFormat("yyyy-MM-dd").format(date)
            
            // Ensure directory exists
            new File(rootDir).mkdirs()
            
            // Generate individual reports
            generateReport(rootDir, "FD_TD_Rollover_SOD_NO_LOGO", "FD_TD_Rollover_SOD_NO_LOGO.jrxml")
            generateReport(rootDir, "Dormant_30_Days_before_SOD_NOLOGO", "Dormant_30_Days_before_SOD_NOLOGO.jrxml")
            generateReport(rootDir, "FD_TD_7Days_before_Maturity_Date_SOD_NOLOGO", "FD_TD_7Days_before_Maturity_Date_SOD_NOLOGO.jrxml")
            generateReport(rootDir, "Loans_Due_Report_SOD_NOLOGO", "Loans_Due_Report_SOD_NOLOGO.jrxml")
            generateReport(rootDir, "Deposit_Listing_SOD_NOLOGO", "Deposit_Listing_SOD_NOLOGO.jrxml")
            generateReport(rootDir, "Loan_Listing_SOD_NOLOGO", "Loan_Listing_SOD_NOLOGO.jrxml")
            generateReport(rootDir, "CASA_Listing_SOD_NOLOGO", "CASA_Listing_SOD_NOLOGO.jrxml")
            generateReport(rootDir, "FD_TD_Listing_SOD_NOLOGO", "FD_TD_Listing_SOD_NOLOGO.jrxml")
            
            // Zip all files
            zipFiles("reports", rootDir)
            
            log.info("Start of day reports generated successfully for date: ${date}")
            
        } catch (Exception e) {
            log.error("Error creating start of day report: ${e.message}", e)
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Create end of day report with error handling
     */
    private void createEndOfDayReport(Date date, Integer mode) {
        try {
            def rootDir = request.getSession().getServletContext().getRealPath("/") + 
                         "reports_repository/endOfDay/" + 
                         new SimpleDateFormat("yyyy-MM-dd").format(date)
            
            // Ensure directory exists
            new File(rootDir).mkdirs()
            
            if (mode >= 1) {
                // Daily reports
                generateReport(rootDir, "Cash_to_Vault_EOD_NOLOGO", "Cash_to_Vault_EOD_NOLOGO.jasper")
                generateReport(rootDir, "Transaction_cash_from_Vault_EOD_NOLOGO", "Transaction_cash_from_Vault_EOD_NOLOGO.jrxml")
                generateReport(rootDir, "Full_Trial_Balance_EOD_NOLOGO", "Full_Trial_Balance_EOD_NOLOGO.jrxml")
                generateReport(rootDir, "GL_Batch_Report_EOD_NOLOGO", "GL_Batch_Report_EOD_NOLOGO.jrxml")
                generateReport(rootDir, "DAILY_TRANSACTION_LISTING_EOD", "DAILY_TRANSACTION_LISTING_EOD.jrxml")
            }
            
            if (mode >= 2) {
                // Monthly reports (add monthly specific reports here)
                log.info("Generating monthly reports for date: ${date}")
            }
            
            // Zip all files
            zipFiles("reports", rootDir)
            
            log.info("End of day reports generated successfully for date: ${date}, mode: ${mode}")
            
        } catch (Exception e) {
            log.error("Error creating end of day report: ${e.message}", e)
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Generate individual report with error handling
     */
    private void generateReport(String rootDir, String reportName, String reportFile) {
        try {
            params._name = reportName
            params._format = "PDF"
            params._file = reportFile
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [])
            def file = jasperService.generateReport(reportDef).toByteArray()
            FileUtils.writeByteArrayToFile(new File(rootDir + "/" + reportName + ".pdf"), file)
            
        } catch (Exception e) {
            log.error("Error generating report ${reportName}: ${e.message}", e)
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Zip files with error handling
     */
    private void zipFiles(String fileName, String inputDir) {
        try {
            fileName = fileName + ".zip"
            ByteArrayOutputStream baos = new ByteArrayOutputStream()
            ZipOutputStream zipFile = new ZipOutputStream(baos)
            
            new File(inputDir).eachFile() { file ->
                if (file.name.endsWith('.pdf')) {
                    zipFile.putNextEntry(new ZipEntry(file.name))
                    file.withInputStream { i ->
                        zipFile << i
                    }
                    zipFile.closeEntry()
                }
            }
            zipFile.finish()
            
            // Clean up individual PDF files
            new File(inputDir).eachFile() { file ->
                if (file.name.endsWith('.pdf')) {
                    file.delete()
                }
            }
            
            OutputStream outputStream = new FileOutputStream(inputDir + "/" + fileName)
            baos.writeTo(outputStream)
            outputStream.close()
            
        } catch (Exception e) {
            log.error("Error zipping files: ${e.message}", e)
        }
    }
}
