package org.icbs.periodicops

import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.grails.web.json.JSONObject
import groovy.time.TimeCategory
import org.icbs.admin.UserMaster
import java.nio.file.*
import org.icbs.admin.Branch
import org.icbs.admin.Holiday
import org.icbs.admin.UserMaster
import org.icbs.admin.CheckDepositClearingType
import org.icbs.admin.Institution
import org.icbs.admin.BranchHoliday
import org.icbs.security.UserSession
import org.icbs.lov.BranchRunStatus
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.gl.GlBatchHdr
import org.icbs.gl.GlBatch
import org.icbs.gl.GlDailyFile
import org.icbs.gl.GlMonthlyBalance
import org.icbs.gl.GlAccount
import org.icbs.deposit.Deposit
import org.icbs.periodicops.PeriodicOpsLog
import org.icbs.periodicops.DailyLoanRecoveries
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositStatus
import org.icbs.lov.DepositType
import org.icbs.loans.Loan
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanInstallmentStatus
import org.icbs.lov.GlBatchHdrStatus
import org.icbs.loans.Loan
import org.icbs.loans.LoanLedger
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnPassbookLine
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.cif.Customer
import grails.gorm.transactions.Transactional
import org.apache.commons.io.FileUtils
import org.apache.tools.zip.ZipEntry
import java.text.SimpleDateFormat
import java.util.zip.ZipOutputStream
import java.util.Calendar
import org.icbs.admin.UserMessage
import static grails.async.Promises.*
import groovy.sql.Sql
import org.icbs.tellering.TxnCOCI
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.lov.CheckStatus
import org.icbs.lov.TxnCheckStatus
import org.icbs.periodicops.DailyBalance
import org.icbs.periodicops.MonthlyBalance
import org.icbs.periodicops.MonthlyPointerLoan
import org.icbs.periodicops.MonthlyCustomer
import org.icbs.periodicops.DailyCheckClearing

/**
 * ProgressMonitorController - Handles progress monitoring for periodic operations
 * 
 * This controller manages the progress tracking and monitoring including:
 * - Start of Day progress monitoring
 * - End of Day progress monitoring
 * - End of Month progress monitoring
 * - Real-time progress updates via AJAX
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class ProgressMonitorController {
    
    // Service Dependencies
    def periodicOpsService
    def loanPeriodicOpsService
    def IsTelleringActiveService
    def depositPeriodicOpsService
    def jasperService
    def AuditLogService
    def GlTransactionService
    def UserMasterService
    def sessionFactory
    def dataSource
    def glPeriodicOpsService

    /**
     * Display the progress monitor dashboard
     * @return rendered view with current progress information
     */
    def index() {
        try {
            def runDate = Branch.read(1).runDate
            def currentProgress = getCurrentProgress()
            
            render(view: "index", model: [
                currentDate: runDate,
                progress: currentProgress
            ])
        } catch (Exception e) {
            log.error("Error loading progress monitor index: ${e.message}", e)
            flash.message = "Error loading progress monitor page |error|alert"
            redirect(controller: "periodicOps", action: "index")
        }
    }

    /**
     * Get start of day progress via AJAX
     * @return JSON response with start of day progress information
     */
    def startOfDayProgressAjax() {
        try {
            def jsonObject = new JSONObject()
            jsonObject = jsonObject.put('progress', session.progress ?: "0")
            jsonObject = jsonObject.put('message', session.message ?: "Ready")
            jsonObject = jsonObject.put('flag', session.flag ?: "ready")
            jsonObject = jsonObject.put('logId', session.logId ?: "0")
            jsonObject = jsonObject.put('timestamp', new Date().toString())
            
            render jsonObject as JSON
        } catch (Exception e) {
            log.error("Error getting start of day progress: ${e.message}", e)
            render([
                error: "Unable to get progress information",
                progress: "0",
                message: "Error",
                flag: "error"
            ] as JSON)
        }
    }

    /**
     * Get end of day progress via AJAX
     * @return JSON response with end of day progress information
     */
    def endOfDayProgressAjax() {
        try {
            def jsonObject = new JSONObject()
            jsonObject = jsonObject.put('progress', session.progress ?: "0")
            jsonObject = jsonObject.put('message', session.message ?: "Ready")
            jsonObject = jsonObject.put('flag', session.flag ?: "ready")
            jsonObject = jsonObject.put('logId', session.logId ?: "0")
            jsonObject = jsonObject.put('timestamp', new Date().toString())
            
            render jsonObject as JSON
        } catch (Exception e) {
            log.error("Error getting end of day progress: ${e.message}", e)
            render([
                error: "Unable to get progress information",
                progress: "0",
                message: "Error",
                flag: "error"
            ] as JSON)
        }
    }

    /**
     * Get end of month progress via AJAX
     * @return JSON response with end of month progress information
     */
    def endOfMonthProgressAjax() {
        try {
            def jsonObject = new JSONObject()
            jsonObject = jsonObject.put('progress', session.progress ?: "0")
            jsonObject = jsonObject.put('message', session.message ?: "Ready")
            jsonObject = jsonObject.put('flag', session.flag ?: "ready")
            jsonObject = jsonObject.put('logId', session.logId ?: "0")
            jsonObject = jsonObject.put('timestamp', new Date().toString())
            
            render jsonObject as JSON
        } catch (Exception e) {
            log.error("Error getting end of month progress: ${e.message}", e)
            render([
                error: "Unable to get progress information",
                progress: "0",
                message: "Error",
                flag: "error"
            ] as JSON)
        }
    }

    /**
     * Get end of year progress via AJAX
     * @return JSON response with end of year progress information
     */
    def endOfYearProgressAjax() {
        try {
            def jsonObject = new JSONObject()
            jsonObject = jsonObject.put('progress', session.progress ?: "0")
            jsonObject = jsonObject.put('message', session.message ?: "Ready")
            jsonObject = jsonObject.put('flag', session.flag ?: "ready")
            jsonObject = jsonObject.put('logId', session.logId ?: "0")
            jsonObject = jsonObject.put('timestamp', new Date().toString())
            
            render jsonObject as JSON
        } catch (Exception e) {
            log.error("Error getting end of year progress: ${e.message}", e)
            render([
                error: "Unable to get progress information",
                progress: "0",
                message: "Error",
                flag: "error"
            ] as JSON)
        }
    }

    /**
     * Get comprehensive progress status for all operations
     * @return JSON response with all progress information
     */
    def getAllProgressStatus() {
        try {
            def progressData = [
                startOfDay: getOperationProgress('SOD'),
                endOfDay: getOperationProgress('EOD'),
                endOfMonth: getOperationProgress('EOM'),
                endOfYear: getOperationProgress('EOY'),
                currentSession: [
                    progress: session.progress ?: "0",
                    message: session.message ?: "Ready",
                    flag: session.flag ?: "ready",
                    logId: session.logId ?: "0"
                ],
                timestamp: new Date()
            ]
            
            render progressData as JSON
        } catch (Exception e) {
            log.error("Error getting all progress status: ${e.message}", e)
            render([error: "Unable to get progress status"] as JSON)
        }
    }

    /**
     * Set progress for current session
     * @param progress Progress percentage
     * @param message Progress message
     * @param flag Progress flag
     * @param logId Log ID
     */
    def setProgress() {
        try {
            def progress = params.progress ?: "0"
            def message = params.message ?: "Ready"
            def flag = params.flag ?: "ready"
            def logId = params.logId ?: "0"
            
            setProgressSession(progress, message, flag, logId)
            
            render([
                success: true,
                progress: progress,
                message: message,
                flag: flag,
                logId: logId
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error setting progress: ${e.message}", e)
            render([
                success: false,
                error: "Unable to set progress"
            ] as JSON)
        }
    }

    /**
     * Clear progress for current session
     */
    def clearProgress() {
        try {
            session.progress = "0"
            session.message = "Ready"
            session.flag = "ready"
            session.logId = "0"
            
            render([
                success: true,
                message: "Progress cleared"
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error clearing progress: ${e.message}", e)
            render([
                success: false,
                error: "Unable to clear progress"
            ] as JSON)
        }
    }

    /**
     * Get operation logs for monitoring
     * @return JSON response with operation logs
     */
    def getOperationLogs() {
        try {
            def maxResults = params.max ? params.max.toInteger() : 10
            def offset = params.offset ? params.offset.toInteger() : 0
            
            def logs = PeriodicOpsLog.createCriteria().list(max: maxResults, offset: offset) {
                order("runDate", "desc")
                order("startTime", "desc")
            }
            
            def logData = logs.collect { log ->
                [
                    id: log.id,
                    runDate: log.runDate,
                    processType: log.processType,
                    startTime: log.startTime,
                    endTime: log.endTime,
                    status: log.status,
                    duration: calculateDuration(log.startTime, log.endTime)
                ]
            }
            
            render([
                logs: logData,
                totalCount: PeriodicOpsLog.count()
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting operation logs: ${e.message}", e)
            render([error: "Unable to get operation logs"] as JSON)
        }
    }

    /**
     * Get real-time system status
     * @return JSON response with system status
     */
    def getSystemStatus() {
        try {
            def systemStatus = [
                currentDate: Branch.get(1).runDate,
                branchStatus: getBranchStatus(),
                activeUsers: getActiveUserCount(),
                systemLocked: isSystemLocked(),
                telleringActive: isTelleringActive(),
                lastOperation: getLastOperation(),
                timestamp: new Date()
            ]
            
            render systemStatus as JSON
        } catch (Exception e) {
            log.error("Error getting system status: ${e.message}", e)
            render([error: "Unable to get system status"] as JSON)
        }
    }

    /**
     * Get current progress information
     * @return Map containing current progress
     */
    private Map getCurrentProgress() {
        try {
            return [
                progress: session.progress ?: "0",
                message: session.message ?: "Ready",
                flag: session.flag ?: "ready",
                logId: session.logId ?: "0",
                timestamp: new Date()
            ]
        } catch (Exception e) {
            log.error("Error getting current progress: ${e.message}", e)
            return [
                progress: "0",
                message: "Error",
                flag: "error",
                logId: "0",
                timestamp: new Date()
            ]
        }
    }

    /**
     * Get operation progress for specific operation type
     * @param operationType The operation type (SOD, EOD, EOM, EOY)
     * @return Map containing operation progress
     */
    private Map getOperationProgress(String operationType) {
        try {
            def latestLog = PeriodicOpsLog.createCriteria().get {
                eq("processType", operationType)
                order("runDate", "desc")
                order("startTime", "desc")
                maxResults(1)
            }
            
            if (latestLog) {
                return [
                    id: latestLog.id,
                    runDate: latestLog.runDate,
                    startTime: latestLog.startTime,
                    endTime: latestLog.endTime,
                    status: latestLog.status,
                    duration: calculateDuration(latestLog.startTime, latestLog.endTime)
                ]
            } else {
                return [
                    status: "No records found",
                    message: "No ${operationType} operations found"
                ]
            }
        } catch (Exception e) {
            log.error("Error getting operation progress for ${operationType}: ${e.message}", e)
            return [
                status: "Error",
                message: "Unable to get ${operationType} progress"
            ]
        }
    }

    /**
     * Calculate duration between start and end times
     * @param startTime Start time string
     * @param endTime End time string
     * @return Duration string
     */
    private String calculateDuration(String startTime, String endTime) {
        try {
            if (!startTime || !endTime) {
                return "N/A"
            }
            
            def start = Date.parse("EEE MMM dd HH:mm:ss zzz yyyy", startTime)
            def end = Date.parse("EEE MMM dd HH:mm:ss zzz yyyy", endTime)
            
            def duration = end.time - start.time
            def minutes = duration / (1000 * 60)
            
            return "${minutes.intValue()} minutes"
        } catch (Exception e) {
            return "N/A"
        }
    }

    /**
     * Get branch status information
     * @return Map containing branch status
     */
    private Map getBranchStatus() {
        try {
            def branches = Branch.list()
            def statusCounts = [:]
            
            branches.each { branch ->
                def status = branch.branchRunStatus?.name ?: "Unknown"
                statusCounts[status] = (statusCounts[status] ?: 0) + 1
            }
            
            return [
                totalBranches: branches.size(),
                statusCounts: statusCounts
            ]
        } catch (Exception e) {
            log.error("Error getting branch status: ${e.message}", e)
            return [error: "Unable to get branch status"]
        }
    }

    /**
     * Get active user count
     * @return Number of active users
     */
    private Integer getActiveUserCount() {
        try {
            return UserSession.countByLogout(null)
        } catch (Exception e) {
            log.error("Error getting active user count: ${e.message}", e)
            return 0
        }
    }

    /**
     * Check if system is locked
     * @return true if system is locked
     */
    private boolean isSystemLocked() {
        try {
            def lockParam = Institution.findByParamCode('GEN.10250')
            return lockParam?.paramValue == 'TRUE'
        } catch (Exception e) {
            log.error("Error checking system lock status: ${e.message}", e)
            return false
        }
    }

    /**
     * Check if tellering is active
     * @return true if tellering is active
     */
    private boolean isTelleringActive() {
        try {
            def branch = Branch.get(1)
            return branch?.isTelleringActive ?: false
        } catch (Exception e) {
            log.error("Error checking tellering status: ${e.message}", e)
            return false
        }
    }

    /**
     * Get last operation information
     * @return Map containing last operation details
     */
    private Map getLastOperation() {
        try {
            def lastLog = PeriodicOpsLog.createCriteria().get {
                order("runDate", "desc")
                order("startTime", "desc")
                maxResults(1)
            }
            
            if (lastLog) {
                return [
                    processType: lastLog.processType,
                    runDate: lastLog.runDate,
                    status: lastLog.status,
                    duration: calculateDuration(lastLog.startTime, lastLog.endTime)
                ]
            } else {
                return [message: "No operations found"]
            }
        } catch (Exception e) {
            log.error("Error getting last operation: ${e.message}", e)
            return [error: "Unable to get last operation"]
        }
    }

    /**
     * Set progress session for tracking progress
     * @param progress Progress percentage
     * @param message Progress message
     * @param flag Progress flag
     * @param logId Log ID
     */
    private void setProgressSession(String progress, String message, String flag, String logId) {
        try {
            session.progress = progress
            session.message = message
            session.flag = flag
            session.logId = logId
        } catch (Exception e) {
            log.error("Error setting progress session: ${e.message}", e)
        }
    }
}
