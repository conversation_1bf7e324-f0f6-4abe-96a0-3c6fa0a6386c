package org.icbs.periodicops

import grails.gorm.transactions.Transactional
import org.icbs.admin.Institution
import org.icbs.admin.Branch

/**
 * PERFORMANCE REFACTOR: Extracted system lock/unlock functionality from PeriodicOpsController
 * This focused controller handles only system locking operations
 */
class SystemLockController {

    def index() {
        def runDate = Branch.read(1).runDate
        render(view: "index", model: [startOfDayDate: runDate])
    }

    /**
     * PERFORMANCE OPTIMIZED: Lock system with proper error handling
     */
    @Transactional
    def lockSystem() {
        try {
            def lockMe = Institution.findByParamCode('GEN.10250')
            if (!lockMe) {
                flash.error = 'System lock configuration not found |error|alert'
                redirect(action: 'index')
                return
            }
            
            lockMe.paramValue = 'TRUE'
            lockMe.save(flush: true)
            
            flash.message = 'System Lock completed |success|alert'
            log.info("System locked by user: ${session.userMaster?.username}")
            
        } catch (Exception e) {
            log.error("Error locking system: ${e.message}", e)
            flash.error = 'Failed to lock system |error|alert'
        }
        
        def runDate = Branch.read(1).runDate
        render(view: "index", model: [startOfDayDate: runDate])
    }

    /**
     * PERFORMANCE OPTIMIZED: Unlock system with proper error handling
     */
    @Transactional
    def unlockSystem() {
        try {
            def lockMe = Institution.findByParamCode('GEN.10250')
            if (!lockMe) {
                flash.error = 'System lock configuration not found |error|alert'
                redirect(action: 'index')
                return
            }
            
            lockMe.paramValue = 'FALSE'
            lockMe.save(flush: true)
            
            flash.message = 'System unlock completed |success|alert'
            log.info("System unlocked by user: ${session.userMaster?.username}")
            
        } catch (Exception e) {
            log.error("Error unlocking system: ${e.message}", e)
            flash.error = 'Failed to unlock system |error|alert'
        }
        
        def runDate = Branch.read(1).runDate
        render(view: "index", model: [startOfDayDate: runDate])
    }

    /**
     * PERFORMANCE OPTIMIZED: Check system lock status
     */
    def getSystemLockStatus() {
        try {
            def lockConfig = Institution.findByParamCode('GEN.10250')
            def isLocked = lockConfig?.paramValue == 'TRUE'
            
            render(contentType: 'application/json') {
                [
                    locked: isLocked,
                    status: isLocked ? 'LOCKED' : 'UNLOCKED',
                    timestamp: new Date()
                ]
            }
        } catch (Exception e) {
            log.error("Error checking system lock status: ${e.message}", e)
            render(status: 500, contentType: 'application/json') {
                [error: 'Failed to check system status']
            }
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Toggle system lock status
     */
    @Transactional
    def toggleSystemLock() {
        try {
            def lockMe = Institution.findByParamCode('GEN.10250')
            if (!lockMe) {
                render(status: 404, contentType: 'application/json') {
                    [error: 'System lock configuration not found']
                }
                return
            }
            
            def currentlyLocked = lockMe.paramValue == 'TRUE'
            lockMe.paramValue = currentlyLocked ? 'FALSE' : 'TRUE'
            lockMe.save(flush: true)
            
            def action = currentlyLocked ? 'unlocked' : 'locked'
            log.info("System ${action} by user: ${session.userMaster?.username}")
            
            render(contentType: 'application/json') {
                [
                    success: true,
                    action: action,
                    locked: !currentlyLocked,
                    message: "System ${action} successfully"
                ]
            }
            
        } catch (Exception e) {
            log.error("Error toggling system lock: ${e.message}", e)
            render(status: 500, contentType: 'application/json') {
                [error: 'Failed to toggle system lock']
            }
        }
    }
}
