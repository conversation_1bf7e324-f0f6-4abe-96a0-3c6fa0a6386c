package org.icbs.periodicops

import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.grails.web.json.JSONObject
import groovy.time.TimeCategory
import org.icbs.admin.UserMaster
import java.nio.file.*
import org.icbs.admin.Branch
import org.icbs.admin.Holiday
import org.icbs.admin.UserMaster
import org.icbs.admin.CheckDepositClearingType
import org.icbs.admin.Institution
import org.icbs.admin.BranchHoliday
import org.icbs.security.UserSession
import org.icbs.lov.BranchRunStatus
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.gl.GlBatchHdr
import org.icbs.gl.GlBatch
import org.icbs.gl.GlDailyFile
import org.icbs.gl.GlMonthlyBalance
import org.icbs.gl.GlAccount
import org.icbs.deposit.Deposit
import org.icbs.periodicops.PeriodicOpsLog
import org.icbs.periodicops.DailyLoanRecoveries
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositStatus
import org.icbs.lov.DepositType
import org.icbs.loans.Loan
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanInstallmentStatus
import org.icbs.lov.GlBatchHdrStatus
import org.icbs.loans.Loan
import org.icbs.loans.LoanLedger
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnPassbookLine
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.cif.Customer
import grails.gorm.transactions.Transactional
import org.apache.commons.io.FileUtils
import org.apache.tools.zip.ZipEntry
import java.text.SimpleDateFormat
import java.util.zip.ZipOutputStream
import java.util.Calendar
import org.icbs.admin.UserMessage
import static grails.async.Promises.*
import groovy.sql.Sql
import org.icbs.tellering.TxnCOCI
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.lov.CheckStatus
import org.icbs.lov.TxnCheckStatus
import org.icbs.periodicops.DailyBalance
import org.icbs.periodicops.MonthlyBalance
import org.icbs.periodicops.MonthlyPointerLoan
import org.icbs.periodicops.MonthlyCustomer
import org.icbs.periodicops.DailyCheckClearing

/**
 * CheckClearingController - Handles check deposit clearing operations
 * 
 * This controller manages check clearing processes including:
 * - Check deposit clearing processing
 * - Check status updates
 * - Deposit account balance adjustments
 * - Daily check clearing records
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class CheckClearingController {
    
    // Service Dependencies
    def periodicOpsService
    def loanPeriodicOpsService
    def IsTelleringActiveService
    def depositPeriodicOpsService
    def jasperService
    def AuditLogService
    def GlTransactionService
    def UserMasterService
    def sessionFactory
    def dataSource
    def glPeriodicOpsService

    /**
     * Display the check clearing index page
     * @return rendered view with check clearing options
     */
    def index() {
        try {
            def runDate = Branch.read(1).runDate
            render(view: "index", model: [currentDate: runDate])
        } catch (Exception e) {
            log.error("Error loading check clearing index: ${e.message}", e)
            flash.message = "Error loading check clearing page |error|alert"
            redirect(controller: "periodicOps", action: "index")
        }
    }

    /**
     * Display the check deposit clearing form
     */
    def clearCheckDeposits() {
        try {
            def runDate = Branch.read(1).runDate
            def pendingChecks = getPendingChecks()
            
            render(view: "clearCheckDeposits", model: [
                currentDate: runDate,
                pendingChecks: pendingChecks
            ])
        } catch (Exception e) {
            log.error("Error loading check clearing form: ${e.message}", e)
            flash.message = "Error loading check clearing form |error|alert"
            redirect(action: "index")
        }
    }

    /**
     * Save and execute check deposit clearing
     */
    @Transactional
    def saveClearCheckDeposits() {
        try {
            log.info("Starting check deposit clearing process with params: ${params}")
            
            // Parse clearing date
            def clrDate = params.depClearingDate ? 
                new Date().parse("MM/dd/yyyy", params.depClearingDate) : null
            
            if (!clrDate) {
                flash.message = "Clearing date is required |error|alert"
                redirect(action: "clearCheckDeposits")
                return
            }
            
            // Perform check clearing
            def clearingResult = performCheckClearing(clrDate)
            
            if (clearingResult.success) {
                // Log the operation
                AuditLogService.insert('860', 'CHK00860', 
                    "Check deposit clearing completed for date: ${clrDate} - ${clearingResult.checksCleared} checks processed", 
                    'CheckClearing', null, null, null, null)
                
                flash.message = "Check deposit clearing completed successfully - ${clearingResult.checksCleared} checks processed |success|alert"
            } else {
                flash.message = "Check deposit clearing failed: ${clearingResult.message} |error|alert"
            }
            
        } catch (Exception e) {
            log.error("Error during check deposit clearing: ${e.message}", e)
            flash.message = "Check deposit clearing failed due to error: ${e.message} |error|alert"
        }
        
        redirect(action: "index")
    }

    /**
     * Get pending checks for clearing
     * @return JSON response with pending checks
     */
    def getPendingChecksForClearing() {
        try {
            def clearingDate = params.clearingDate ? 
                Date.parse("MM/dd/yyyy", params.clearingDate) : new Date()
            
            def pendingChecks = getPendingChecksForDate(clearingDate)
            
            render([
                success: true,
                clearingDate: clearingDate,
                pendingChecks: pendingChecks,
                totalAmount: pendingChecks.sum { it.checkAmt } ?: 0.00D,
                checkCount: pendingChecks.size()
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting pending checks: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get pending checks: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get check clearing status
     * @return JSON response with clearing status
     */
    def getCheckClearingStatus() {
        try {
            def currentDate = Branch.get(1).runDate
            def status = [
                currentDate: currentDate,
                pendingChecks: getPendingChecksCount(),
                clearedToday: getClearedTodayCount(),
                totalAmount: getTotalPendingAmount(),
                lastClearingDate: getLastClearingDate(),
                timestamp: new Date()
            ]
            
            render status as JSON
            
        } catch (Exception e) {
            log.error("Error getting check clearing status: ${e.message}", e)
            render([error: "Unable to get clearing status"] as JSON)
        }
    }

    /**
     * Clear specific check by ID
     */
    @Transactional
    def clearSpecificCheck() {
        try {
            def checkId = params.checkId?.toLong()
            if (!checkId) {
                render([
                    success: false,
                    error: "Check ID is required"
                ] as JSON)
                return
            }
            
            def check = TxnCOCI.get(checkId)
            if (!check) {
                render([
                    success: false,
                    error: "Check not found"
                ] as JSON)
                return
            }
            
            def clearingResult = clearSingleCheck(check)
            
            if (clearingResult.success) {
                AuditLogService.insert('861', 'CHK00861', 
                    "Individual check cleared - Check ID: ${checkId}, Amount: ${check.checkAmt}", 
                    'CheckClearing', null, null, null, checkId)
            }
            
            render clearingResult as JSON
            
        } catch (Exception e) {
            log.error("Error clearing specific check: ${e.message}", e)
            render([
                success: false,
                error: "Check clearing failed: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Perform check clearing for specified date
     * @param clearingDate The date for which to clear checks
     * @return Map containing clearing results
     */
    private Map performCheckClearing(Date clearingDate) {
        try {
            def db = new Sql(dataSource)
            
            // Get checks eligible for clearing
            def sqlstmt = "select A.id from txncoci A " +
                "inner join check_deposit_clearing_type B on A.check_type_id = B.id " +
                "where A.clearing_date = '" + clearingDate + "' " +
                "and A.check_status_id not in (4,5) " +
                "and B.is_on_us = false and A.id not in (select check_deposit_id from Daily_Check_Clearing)"
            
            def clearings = db.rows(sqlstmt)
            def checksCleared = 0
            def checksSkipped = 0
            def totalAmount = 0.00D
            
            for (checkRow in clearings) {
                try {
                    def check = TxnCOCI.get(checkRow.id)
                    def clearResult = clearSingleCheck(check)
                    
                    if (clearResult.success) {
                        checksCleared++
                        totalAmount += check.checkAmt
                    } else {
                        checksSkipped++
                        log.warn("Skipped check ${check.id}: ${clearResult.message}")
                    }
                    
                } catch (Exception e) {
                    log.error("Error clearing check ${checkRow.id}: ${e.message}", e)
                    checksSkipped++
                }
            }
            
            return [
                success: true,
                checksCleared: checksCleared,
                checksSkipped: checksSkipped,
                totalAmount: totalAmount,
                message: "Check clearing completed successfully"
            ]
            
        } catch (Exception e) {
            log.error("Error performing check clearing: ${e.message}", e)
            return [
                success: false,
                message: "Check clearing failed: ${e.message}"
            ]
        }
    }

    /**
     * Clear a single check
     * @param check The check to clear
     * @return Map containing clearing result
     */
    private Map clearSingleCheck(TxnCOCI check) {
        try {
            def deposit = check.depAcct
            
            if (!deposit) {
                return [
                    success: false,
                    message: "Deposit account not found for check"
                ]
            }
            
            // Update check status
            check.status = ConfigItemStatus.read(3)
            check.checkStatus = CheckStatus.read(5)
            check.cleared = 'TRUE'
            check.save(flush: true, failOnError: true)
            
            // Update deposit account balance
            if (deposit.ledgerBalAmt >= (deposit.availableBalAmt + check.checkAmt)) {
                deposit.availableBalAmt = deposit.availableBalAmt + check.checkAmt
                deposit.unclearedCheckBalAmt = deposit.unclearedCheckBalAmt - check.checkAmt
                deposit.save(flush: true, failOnError: true)
            } else {
                log.warn("Check ${check.id} not cleared - would result in available balance > ledger balance")
                return [
                    success: false,
                    message: "Check amount would exceed available balance"
                ]
            }
            
            // Create daily check clearing record
            def dailyCheckDeposit = new DailyCheckClearing(
                processDate: check.clearingDate,
                deposit: deposit,
                checkDeposit: check
            )
            dailyCheckDeposit.save(flush: true)
            
            return [
                success: true,
                message: "Check cleared successfully",
                checkId: check.id,
                amount: check.checkAmt
            ]
            
        } catch (Exception e) {
            log.error("Error clearing single check ${check.id}: ${e.message}", e)
            return [
                success: false,
                message: "Check clearing failed: ${e.message}"
            ]
        }
    }

    /**
     * Get pending checks for display
     * @return List of pending checks
     */
    private List getPendingChecks() {
        try {
            def currentDate = Branch.get(1).runDate
            return getPendingChecksForDate(currentDate)
        } catch (Exception e) {
            log.error("Error getting pending checks: ${e.message}", e)
            return []
        }
    }

    /**
     * Get pending checks for specific date
     * @param clearingDate The clearing date
     * @return List of pending checks
     */
    private List getPendingChecksForDate(Date clearingDate) {
        try {
            def db = new Sql(dataSource)
            
            def sqlstmt = "select A.id, A.check_amt, A.check_no, A.clearing_date, " +
                "D.acct_no as deposit_account, C.display_name as customer_name " +
                "from txncoci A " +
                "inner join check_deposit_clearing_type B on A.check_type_id = B.id " +
                "left join deposit D on A.dep_acct_id = D.id " +
                "left join customer C on D.customer_id = C.id " +
                "where A.clearing_date = '" + clearingDate + "' " +
                "and A.check_status_id not in (4,5) " +
                "and B.is_on_us = false and A.id not in (select check_deposit_id from Daily_Check_Clearing)"
            
            return db.rows(sqlstmt)
            
        } catch (Exception e) {
            log.error("Error getting pending checks for date ${clearingDate}: ${e.message}", e)
            return []
        }
    }

    /**
     * Get count of pending checks
     * @return Number of pending checks
     */
    private Integer getPendingChecksCount() {
        try {
            def currentDate = Branch.get(1).runDate
            def db = new Sql(dataSource)
            
            def sqlstmt = "select count(*) as count from txncoci A " +
                "inner join check_deposit_clearing_type B on A.check_type_id = B.id " +
                "where A.clearing_date = '" + currentDate + "' " +
                "and A.check_status_id not in (4,5) " +
                "and B.is_on_us = false and A.id not in (select check_deposit_id from Daily_Check_Clearing)"
            
            return db.firstRow(sqlstmt).count
            
        } catch (Exception e) {
            log.error("Error getting pending checks count: ${e.message}", e)
            return 0
        }
    }

    /**
     * Get count of checks cleared today
     * @return Number of checks cleared today
     */
    private Integer getClearedTodayCount() {
        try {
            def currentDate = Branch.get(1).runDate
            return DailyCheckClearing.countByProcessDate(currentDate)
        } catch (Exception e) {
            log.error("Error getting cleared today count: ${e.message}", e)
            return 0
        }
    }

    /**
     * Get total amount of pending checks
     * @return Total pending amount
     */
    private Double getTotalPendingAmount() {
        try {
            def currentDate = Branch.get(1).runDate
            def db = new Sql(dataSource)
            
            def sqlstmt = "select sum(A.check_amt) as total from txncoci A " +
                "inner join check_deposit_clearing_type B on A.check_type_id = B.id " +
                "where A.clearing_date = '" + currentDate + "' " +
                "and A.check_status_id not in (4,5) " +
                "and B.is_on_us = false and A.id not in (select check_deposit_id from Daily_Check_Clearing)"
            
            return db.firstRow(sqlstmt).total ?: 0.00D
            
        } catch (Exception e) {
            log.error("Error getting total pending amount: ${e.message}", e)
            return 0.00D
        }
    }

    /**
     * Get last clearing date
     * @return Last clearing date
     */
    private Date getLastClearingDate() {
        try {
            def lastClearing = DailyCheckClearing.createCriteria().get {
                projections {
                    max("processDate")
                }
            }
            return lastClearing
        } catch (Exception e) {
            log.error("Error getting last clearing date: ${e.message}", e)
            return null
        }
    }
}
