package org.icbs.loans

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.loans.TxnRopaDetails
import org.icbs.loans.LitigationExpense
import org.icbs.loans.LitigationDeficiency
import org.icbs.loans.ROPAExpense
import org.icbs.loans.ROPAExpenseAdjustment
import org.icbs.loans.ROPAExpenseCapitalization
import org.icbs.loans.ROPAExpenseCapitalizationAdjustment
import org.icbs.loans.ROPASellOff
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanSpecialType
import org.icbs.lov.TxnType
import org.icbs.gl.GlAccount

/**
 * LoanSpecialOperationsController - <PERSON>les special loan operations
 * 
 * This controller manages special loan operations including:
 * - Loan write-off operations
 * - ROPA (Real and Other Properties Acquired) transfers
 * - Litigation expense management
 * - Special loan status operations
 * - Loan termination and reopening
 * - Status update operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class LoanSpecialOperationsController {
    
    // Service Dependencies
    def loanService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        writeOff: "GET",
        transferToROPA: "POST",
        terminate: "POST",
        reopen: "GET",
        saveReopen: "POST",
        updateSpecial: "POST",
        saveLitigationExpense: "POST",
        saveLitigationDeficiency: "POST",
        saveROPAExpense: "POST",
        saveROPAExpenseAdjustment: "POST",
        saveROPAExpenseCapitalization: "POST",
        saveROPAExpenseCapitalizationAdjustment: "POST",
        saveROPASellOff: "POST"
    ]

    /**
     * Show write-off form
     */
    def writeOff(Loan loanInstance) {
        def module = getModule(request?.forwardURI)
        def title = getTitle(module)
        
        respond loanInstance, model:[module:module, title:title]
    }

    /**
     * Transfer loan to ROPA
     */
    @Transactional
    def transferToROPA() {
        def loanInstance = Loan.get(params.id)

        createLoanHistoryEntry(loanInstance)
        createSessionData(loanInstance)

        loanService.commitLoanHistoryEntry("ROPA")
        clearLoanData(loanInstance)

        loanService.ropa(loanInstance)

        def description = loanInstance.accountNo + ' was transferred to ROPA by ' + UserMaster.get(session.user_id).username
        auditLogService.insert('090', 'LON01000', description, 'Loan', null, null, null, loanInstance.id)

        request.withFormat {
            form multipartForm {
                flash.message = "Loan " + loanInstance.id + " transfered to ROPA"
                redirect controller:"loanROPA", action:'show', id:loanInstance?.id 
            }
            '*'{ render status: NO_CONTENT }
        }
    }

    /**
     * Terminate loan account
     */
    @Transactional
    def terminate(Loan loanInstance) {
        if (loanInstance == null) {
            notFound()
            return
        }

        createLoanHistoryEntry(loanInstance)
        createSessionData(loanInstance)

        loanService.commitLoanHistoryEntry("Terminate")
        clearLoanData(loanInstance)

        loanService.updateStatus(loanInstance, LoanAcctStatus.get(5)) // Terminated status

        def description = loanInstance.accountNo + ' was terminated by ' + UserMaster.get(session.user_id).username
        auditLogService.insert('090', 'LON01100', description, 'Loan', null, null, null, loanInstance.id)

        request.withFormat {
            form multipartForm {
                flash.message = "Loan account terminated successfully"
                redirect controller: "loanAccount", action: 'show', id: loanInstance.id
            }
            '*'{ render status: NO_CONTENT }
        }
    }

    /**
     * Show reopen form
     */
    def reopen() {
        if(params.id){
            def loanInstance = Loan.get(params.id)
            if (loanInstance == null) {
                notFound()
                return
            }
            render(view:'reopen/form', model: [loanInstance:loanInstance])
        } else {
            notFound()
        }
    }

    /**
     * Save loan reopen
     */
    @Transactional
    def saveReopen(Loan loanInstance) {
        createLoanHistoryEntry(loanInstance)
        createSessionData(loanInstance)

        loanService.commitLoanHistoryEntry("Reopen")
        clearLoanData(loanInstance)

        loanService.updateStatus(loanInstance, LoanAcctStatus.get(4)) // Active status

        def description = loanInstance.accountNo + ' was reopened by ' + UserMaster.get(session.user_id).username
        auditLogService.insert('090', 'LON01200', description, 'Loan', null, null, null, loanInstance.id)

        request.withFormat {
            form multipartForm {
                flash.message = "Loan account reopened successfully"
                redirect controller: "loanAccount", action: 'show', id: loanInstance.id
            }
            '*'{ render status: NO_CONTENT }
        }
    }

    /**
     * Show special operations form
     */
    def showSpecial(Loan loanInstance) {
        respond loanInstance
    }

    /**
     * Edit special operations
     */
    def editSpecial(Loan loanInstance) {
        respond loanInstance
    }

    /**
     * Update special loan type
     */
    @Transactional
    def updateSpecial(Loan loanInstance) {
        def type = LoanSpecialType.get(params?.type.id)
        def remarks = params?.remarks

        createLoanHistoryEntry(loanInstance)
        createSessionData(loanInstance)

        loanService.commitLoanHistoryEntry("Update Special Type")
        clearLoanData(loanInstance)

        loanService.updateSpecialType(loanInstance, type, remarks, UserMaster.get(session.user_id))

        def description = loanInstance.accountNo + ' special type was updated by ' + UserMaster.get(session.user_id).username
        auditLogService.insert('090', 'LON00700', description, 'Loan', null, null, null, loanInstance.id)

        request.withFormat {
            form multipartForm {
                flash.message = "Special type updated successfully"
                redirect action: 'showSpecial', id: loanInstance.id
            }
            '*'{ render status: NO_CONTENT }
        }
    }

    /**
     * Show litigation form
     */
    def litigation(Loan loanInstance) {
        params.max = Math.min(params?.max?.toInteger() ?: 10, 100)
        
        def litigationExpenses = []
        def litigationDeficiencies = []
        
        if (loanInstance.special) {
            litigationExpenses = loanInstance.special.litigationExpenses
            litigationDeficiencies = loanInstance.special.litigationDeficiencies
        }

        respond loanInstance, model:[
            litigationExpenses: litigationExpenses,
            litigationDeficiencies: litigationDeficiencies
        ]
    }

    /**
     * Save litigation expense
     */
    @Transactional
    def saveLitigationExpense(Loan loanInstance) {        
        def glAccount = GlAccount.get(params?.glAccount.id)        
        def type = TxnType.get(params?.type.id)        
        def amount = params?.amount?.toDouble() ?: 0

        def litigationExpense = new LitigationExpense(glAccount: glAccount, type: type, amount: amount)
        loanInstance.special.addToLitigationExpenses(litigationExpense)
        loanInstance.save flush:true

        def description = "Litigation expense added to loan " + loanInstance.accountNo
        auditLogService.insert('090', 'LON00800', description, 'Loan', null, null, null, loanInstance.id)

        redirect action: 'litigation', id: loanInstance.id
        return
    }

    /**
     * Save litigation deficiency
     */
    @Transactional
    def saveLitigationDeficiency(Loan loanInstance) {        
        def glAccount = GlAccount.get(params?.glAccount.id)        
        def type = TxnType.get(params?.type.id)        
        def amount = params?.amount?.toDouble() ?: 0

        def litigationDeficiency = new LitigationDeficiency(glAccount: glAccount, type: type, amount: amount)
        loanInstance.special.addToLitigationDeficiencies(litigationDeficiency)
        loanInstance.save flush:true

        def description = "Litigation deficiency added to loan " + loanInstance.accountNo
        auditLogService.insert('090', 'LON00800', description, 'Loan', null, null, null, loanInstance.id)

        redirect action: 'litigation', id: loanInstance.id
        return
    }

    /**
     * Show ROPA form
     */
    def ropa(Loan loanInstance) {            
        respond loanInstance
        return
    }

    /**
     * Save ROPA expense
     */
    @Transactional
    def saveROPAExpense(Loan loanInstance) {        
        def glAccount = GlAccount.get(params?.glAccount.id)        
        def type = TxnType.get(params?.type.id)        
        def amount = params?.amount?.toDouble() ?: 0

        def ropaExpense = new ROPAExpense(glAccount: glAccount, type: type, amount: amount)
        loanInstance.special.addToRopaExpenses(ropaExpense)
        loanInstance.save flush:true

        def description = "ROPA expense added to loan " + loanInstance.accountNo
        auditLogService.insert('090', 'LON00900', description, 'Loan', null, null, null, loanInstance.id)
        
        redirect action: 'ropa', id: loanInstance.id
        return
    }

    /**
     * Save ROPA expense adjustment
     */
    @Transactional
    def saveROPAExpenseAdjustment(Loan loanInstance) {        
        def glAccount = GlAccount.get(params?.glAccount.id)        
        def type = TxnType.get(params?.type.id)        
        def amount = params?.amount?.toDouble() ?: 0

        def ropaExpenseAdjustment = new ROPAExpenseAdjustment(glAccount: glAccount, type: type, amount: amount)
        loanInstance.special.addToRopaExpenseAdjustments(ropaExpenseAdjustment)
        loanInstance.save flush:true

        def description = "ROPA expense adjustment added to loan " + loanInstance.accountNo
        auditLogService.insert('090', 'LON00900', description, 'Loan', null, null, null, loanInstance.id)
        
        redirect action: 'ropa', id: loanInstance.id
        return
    }

    // Helper methods
    private def getModule(String url) {
        if (url =~ /^.*\/loanWriteOff.*$/) {
            return "loanWriteOff"       
        } else if (url =~ /^.*\/loanROPA.*$/) {
            return "loanROPA"       
        } else if (url =~ /^.*\/loanTermination.*$/) {
            return "loanTermination"       
        } else {
            return "loan"
        }
    }

    private def getTitle(String module) {
        if (module == "loanWriteOff") {
            return "Loan Account Write-Off"
        } else if (module == "loanROPA") {
            return "Loan ROPA"
        } else if (module == "loanTermination") {
            return "Loan Account Termination"
        } else {
            return "Loan Accounts"
        }
    }

    private def createLoanHistoryEntry(Loan loanInstance) {
        // Implementation for loan history entry
    }

    private def createSessionData(Loan loanInstance) {
        // Implementation for session data creation
    }

    private def clearLoanData(Loan loanInstance) {
        loanInstance.serviceCharges.clear()
        loanInstance.loanDeductions.clear()
        loanInstance.loanInstallments.clear()
        loanInstance.loanEIRSchedules.clear()
        loanInstance.sweepAccounts.clear()
    }

    protected def notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = message(code: 'default.not.found.message', args: [message(code: 'Loan.label', default: 'Loan'), params.id])
                redirect action: "index", controller: "loanAccount", method: "GET"
            }
            '*'{ render status: NOT_FOUND }
        }
    }
}
