package org.icbs.loans

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.admin.TxnTemplate
import org.icbs.tellering.TxnFile
import org.icbs.loans.LoanLedger
import org.icbs.lov.ConfigItemStatus
import org.icbs.gl.CfgAcctGlTemplate
import java.text.SimpleDateFormat

/**
 * LoanTransactionController - Handles loan transaction operations
 * 
 * This controller manages loan transaction operations including:
 * - Interest application and calculations
 * - Interest accrual start/stop operations
 * - Interest capitalization
 * - Interest rate updates
 * - GL classification updates
 * - Branch transfer operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class LoanTransactionController {
    
    // Service Dependencies
    def loanService
    def glTransactionService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        applyIntToDate: "POST",
        applyIntToMaturity: "POST",
        capitalizeAccruedInt: "POST",
        updateInterestRateAjax: "POST",
        startInterestAccrual: "POST",
        stopInterestAccrual: "POST",
        updateGLClassificationAjax: "POST",
        updateBranchAjax: "POST"
    ]

    /**
     * Apply interest to date
     */
    @Transactional
    def applyIntToDate(Loan loanInstance) { 
        def module = getModule(request?.forwardURI)
        def title = getTitle(module)

        if (loanInstance == null) {
            notFound()
            return
        }

        def totInt = loanService.getInterestToDate(loanInstance)
        
        if (totInt > 0) {
            def b = Branch.get(UserMaster.get(session.user_id).branchId)
            def tf = new TxnFile(
                txnCode: "INTCR",
                txnDescription: "Interest Credit",
                txnDate: b.runDate,
                currency: loanInstance.currency,
                txnAmt: totInt,
                txnRef: "Interest Credit to Date",
                status: ConfigItemStatus.get(2),
                branch: b,
                txnTimestamp: new Date().toTimestamp(),
                txnParticulars: "Interest Credit to Date for Loan " + loanInstance.accountNo,
                txnType: TxnTemplate.get(40).txnType,
                txnTemplate: TxnTemplate.get(40),
                user: UserMaster.get(session.user_id)
            )
            
            tf.save(flush:true)
            
            def ll = new LoanLedger(
                loan: loanInstance, 
                txnFile: tf, 
                txnDate: tf.txnDate, 
                txnTemplate: TxnTemplate.get(40), 
                interestCredit: totInt, 
                interestDebit: totInt, 
                txnRef: tf.txnRef,
                principalBalance: loanInstance.balanceAmount
            )
            ll.save(flush:true)
            
            loanInstance.interestBalanceAmount += totInt
            loanInstance.save(flush:true)
            
            flash.message = 'Interest Credit Completed!!!'
            
            session["transactionFileId"] = tf.id.toInteger()
            redirect(controller: "tellering", action: "txnSuccess")
        } else {
            redirect(action: "show", controller: "loanAccount", id: loanInstance.id, params: [loanInstance: loanInstance])
        }
    }

    /**
     * Apply interest to maturity
     */
    @Transactional
    def applyIntToMaturity(Loan loanInstance) { 
        def module = getModule(request?.forwardURI)
        def title = getTitle(module)

        if (loanInstance == null) {
            notFound()
            return
        }

        def totInt = loanService.getInterestToMaturity(loanInstance)
        
        if (totInt > 0) {
            def b = Branch.get(UserMaster.get(session.user_id).branchId)
            def tf = new TxnFile(
                txnCode: "INTCRM",
                txnDescription: "Interest Credit to Maturity",
                txnDate: b.runDate,
                currency: loanInstance.currency,
                txnAmt: totInt,
                txnRef: "Interest Credit to Maturity",
                status: ConfigItemStatus.get(2),
                branch: b,
                txnTimestamp: new Date().toTimestamp(),
                txnParticulars: "Interest Credit to Maturity for Loan " + loanInstance.accountNo,
                txnType: TxnTemplate.get(40).txnType,
                txnTemplate: TxnTemplate.get(40),
                user: UserMaster.get(session.user_id)
            )
            
            tf.save(flush:true)
            
            def ll = new LoanLedger(
                loan: loanInstance, 
                txnFile: tf, 
                txnDate: tf.txnDate, 
                txnTemplate: TxnTemplate.get(40), 
                interestCredit: totInt, 
                interestDebit: totInt, 
                txnRef: tf.txnRef,
                principalBalance: loanInstance.balanceAmount
            )
            ll.save(flush:true)
            
            loanInstance.interestBalanceAmount += totInt
            loanInstance.save(flush:true)
            
            flash.message = 'Interest Credit to Maturity Completed!!!'
            session["transactionFileId"] = tf.id.toInteger()
            redirect(controller: "tellering", action: "txnSuccess")
        } else {
            redirect(action: "show", controller: "loanAccount", id: loanInstance.id, params: [loanInstance: loanInstance])            
        }
    }

    /**
     * Capitalize accrued interest
     */
    @Transactional
    def capitalizeAccruedInt(Loan loanInstance) { 
        if (loanInstance == null) {
            notFound()
            return
        }

        def accruedInt = loanInstance.interestBalanceAmount
        
        if (accruedInt > 0) {
            // Create loan payment transaction
            def b = Branch.get(UserMaster.get(session.user_id).branchId)
            def tf = new TxnFile(
                txnCode: "INTCAP",
                txnDescription: "Interest Capitalization",
                txnDate: b.runDate,
                currency: loanInstance.currency,
                txnAmt: accruedInt,
                txnRef: "Interest Capitalization",
                status: ConfigItemStatus.get(2),
                branch: b,
                txnTimestamp: new Date().toTimestamp(),
                txnParticulars: "Interest Capitalization for Loan " + loanInstance.accountNo,
                txnType: TxnTemplate.get(41).txnType,
                txnTemplate: TxnTemplate.get(41),
                user: UserMaster.get(session.user_id)
            )
            tf.save(flush:true)

            // Update loan balances
            loanInstance.balanceAmount += accruedInt
            loanInstance.interestBalanceAmount = 0
            loanInstance.save(flush:true)

            def ll = new LoanLedger(
                loan: loanInstance, 
                txnFile: tf, 
                txnDate: tf.txnDate, 
                txnTemplate: TxnTemplate.get(41), 
                principalCredit: accruedInt,
                interestDebit: accruedInt,
                txnRef: tf.txnRef,
                principalBalance: loanInstance.balanceAmount
            )
            ll.save(flush:true)

            flash.message = 'Interest Capitalization Completed!!!'
            session["transactionFileId"] = tf.id.toInteger()
            redirect(controller: "tellering", action: "txnSuccess")
        } else {
            flash.message = 'Account Error - no interest to capitalize!|error|alert'
            redirect(action: "show", controller: "loanAccount", id: loanInstance.id, params: [loanInstance: loanInstance])            
        }      
    }

    /**
     * Show update interest rate form via AJAX
     */
    def showUpdateInterestRateAjax() {
        def loanInstance = Loan.get(params?.id)
        
        render(template:"interest/updateForm", model:[loanInstance:loanInstance]) as JSON
        return
    }

    /**
     * Update interest rate via AJAX
     */
    @Transactional
    def updateInterestRateAjax() {
        def loanInstance = Loan.get(params.id)

        createLoanHistoryEntry(loanInstance)
        createSessionData(loanInstance)

        loanService.commitLoanHistoryEntry("Update Interest Rate")
        clearLoanData(loanInstance)

        def newRate = params.interestRate.toDouble()
        loanService.updateInterestRate(loanInstance, newRate, UserMaster.get(session.user_id))

        def description = loanInstance.accountNo + ' interest rate was updated by ' + UserMaster.get(session.user_id).username
        auditLogService.insert('090', 'LON00600', description, 'Loan', null, null, null, loanInstance.id)

        def message = "Interest rate successfully updated"
        render(template:"interest/updateForm", model:[loanInstance:loanInstance, message:message]) as JSON

        return
    }

    /**
     * Start interest accrual
     */
    @Transactional
    def startInterestAccrual(Loan loanInstance) {
        if (loanInstance == null) {
            notFound()
            return
        }

        createLoanHistoryEntry(loanInstance)
        createSessionData(loanInstance)

        loanService.commitLoanHistoryEntry("Start Interest Accrual")
        clearLoanData(loanInstance)

        loanService.updateInterestAccrual(loanInstance, true)
        
        def description = loanInstance.accountNo + ' accrued interest start was started by ' + UserMaster.get(session.user_id).username
        auditLogService.insert('090', 'LON00500', description, 'Loan', null, null, null, loanInstance.id)

        request.withFormat {
            form multipartForm {
                flash.message = "Interest accrual started"
                redirect controller: "loanAccount", action: 'show', id: loanInstance.id
            }
            '*'{ render status: NO_CONTENT }
        }
    }

    /**
     * Stop interest accrual
     */
    @Transactional
    def stopInterestAccrual(Loan loanInstance) {
        if (loanInstance == null) {
            notFound()
            return
        }

        createLoanHistoryEntry(loanInstance)
        createSessionData(loanInstance)

        loanService.commitLoanHistoryEntry("Stop Interest Accrual")
        clearLoanData(loanInstance)

        loanService.updateInterestAccrual(loanInstance, false)

        def description = loanInstance.accountNo + ' accrued interest was stopped by ' + UserMaster.get(session.user_id).username
        auditLogService.insert('090', 'LON00500', description, 'Loan', null, null, null, loanInstance.id)

        request.withFormat {
            form multipartForm {
                flash.message = "Interest accrual stopped"
                redirect controller: "loanAccount", action: 'show', id: loanInstance.id
            }
            '*'{ render status: NO_CONTENT }
        }
    }

    // Helper methods
    private def getModule(String url) {
        if (url =~ /^.*\/loanInterestAccrual.*$/) {
            return "loanInterestAccrual"            
        } else {
            return "loan"
        }
    }

    private def getTitle(String module) {
        if (module == "loanInterestAccrual") {
            return "Loan Account Interest Accrual"
        } else {
            return "Loan Accounts"
        }
    }

    private def createLoanHistoryEntry(Loan loanInstance) {
        // Implementation for loan history entry
    }

    private def createSessionData(Loan loanInstance) {
        // Implementation for session data creation
    }

    private def clearLoanData(Loan loanInstance) {
        loanInstance.serviceCharges.clear()
        loanInstance.loanDeductions.clear()
        loanInstance.loanInstallments.clear()
        loanInstance.loanEIRSchedules.clear()
        loanInstance.sweepAccounts.clear()
    }

    protected def notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = message(code: 'default.not.found.message', args: [message(code: 'Loan.label', default: 'Loan'), params.id])
                redirect action: "index", controller: "loanAccount", method: "GET"
            }
            '*'{ render status: NOT_FOUND }
        }
    }
}
