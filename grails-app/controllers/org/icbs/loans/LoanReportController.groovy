package org.icbs.loans

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.reports.LoanListingEntry
import groovy.sql.Sql

/**
 * LoanReportController - <PERSON>les loan reporting and document generation
 * 
 * This controller manages loan reporting operations including:
 * - Loan installment schedule printing
 * - Disclosure statement generation
 * - Promissory note printing
 * - Loan inquiry reports
 * - Loan approval slip generation
 * - Loan listing reports
 * - Custom loan reports
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional(readOnly = true)
class LoanReportController {
    
    // Service Dependencies
    def jasperService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        generateReport: "POST"
    ]

    /**
     * Print loan installment schedule
     */
    def printLoanInstallment() {
        try {    
            params._name = "INSTALLMENT SCHEDULE FINAL VERSION"
            params._format = "PDF"
            params._file = "LOAN_INSTALLMENT_SCHEDULE_WITHOUT_EIR_NOLOGO.jasper"
            params.id = session["jrxmlTcId"]
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            response.setContentType('application/pdf')
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', 'inline; filename=loan_installment_schedule.pdf')
            response.setHeader('Expires', '0')
            response.setHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
            response.outputStream << bytes
            response.outputStream.flush()
            
            def description = "Loan installment schedule printed for loan ID: " + session["jrxmlTcId"]
            auditLogService.insert('090', 'LON02400', description, 'LoanReport', null, null, null, session["jrxmlTcId"])
            
        } catch(Exception e) {
            log.error("Error printing loan installment schedule", e)
            flash.message = "Error generating installment schedule: ${e.message}|error|alert"
            redirect(action: "reports")
        }        
    }

    /**
     * Print disclosure statement
     */
    def printDisclosure() {    
        try {    
            println("Printing disclosure for loan ID: " + session["jrxmlTcId"])
            params._name = "Disclosure Statement"
            params._format = "PDF"
            params._file = "Disclosure_Statement.jasper"
            params.id = session["jrxmlTcId"]
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            response.setContentType('application/pdf')
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', 'inline; filename=disclosure_statement.pdf')
            response.setHeader('Expires', '0')
            response.setHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
            response.outputStream << bytes
            response.outputStream.flush()
            
            def description = "Disclosure statement printed for loan ID: " + session["jrxmlTcId"]
            auditLogService.insert('090', 'LON02500', description, 'LoanReport', null, null, null, session["jrxmlTcId"])
            
        } catch(Exception e) {
            log.error("Error printing disclosure statement", e)
            flash.message = "Error generating disclosure statement: ${e.message}|error|alert"
            redirect(action: "reports")
        }
    }

    /**
     * Print promissory note
     */
    def printPromissory() {    
        try {    
            println("Printing promissory note")
            params._name = "Promissory"
            params._format = "PDF"
            params._file = "PN.jasper"
            params.id = session["jrxmlTcId"]
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            response.setContentType('application/pdf')
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', 'inline; filename=promissory_note.pdf')
            response.setHeader('Expires', '0')
            response.setHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
            response.outputStream << bytes
            response.outputStream.flush()
            
            def description = "Promissory note printed for loan ID: " + session["jrxmlTcId"]
            auditLogService.insert('090', 'LON02600', description, 'LoanReport', null, null, null, session["jrxmlTcId"])
            
        } catch(Exception e) {
            log.error("Error printing promissory note", e)
            flash.message = "Error generating promissory note: ${e.message}|error|alert"
            redirect(action: "reports")
        }
    }

    /**
     * Print loan inquiry report
     */
    def printLoanInquiry() {
        try {    
            println("Printing loan inquiry")
            params._name = "loan_inquiry"
            params._format = "PDF"
            params._file = "loan_inquiry.jrxml"
            params.id = session["jrxmlTcId"]
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            response.setContentType('application/pdf')
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', 'inline; filename=loan_inquiry.pdf')
            response.setHeader('Expires', '0')
            response.setHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
            response.outputStream << bytes
            response.outputStream.flush()
            
            def description = "Loan inquiry report printed for loan ID: " + session["jrxmlTcId"]
            auditLogService.insert('090', 'LON02700', description, 'LoanReport', null, null, null, session["jrxmlTcId"])
            
        } catch(Exception e) {
            log.error("Error printing loan inquiry", e)
            flash.message = "Error generating loan inquiry: ${e.message}|error|alert"
            redirect(action: "reports")
        }        
    }

    /**
     * Print loan approval slip
     */
    def printLoanApprovalSlip() {    
        try {    
            println("Printing loan approval slip")
            params._name = "LoanApprovalSlip"
            params._format = "PDF"
            params._file = "TransactionSlipLoanSample1.jrxml"
            params.id = session["jrxmlTcId"]
            
            def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), [])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            response.setContentType('application/pdf')
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', 'inline; filename=loan_approval_slip.pdf')
            response.setHeader('Expires', '0')
            response.setHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0')
            response.outputStream << bytes
            response.outputStream.flush()
            
            def description = "Loan approval slip printed for loan ID: " + session["jrxmlTcId"]
            auditLogService.insert('090', 'LON02800', description, 'LoanReport', null, null, null, session["jrxmlTcId"])
            
        } catch(Exception e) {
            log.error("Error printing loan approval slip", e)
            flash.message = "Error generating loan approval slip: ${e.message}|error|alert"
            redirect(action: "reports")
        }
    }

    /**
     * Show reports menu
     */
    def reports() {
        render(view:'reports/view')
    }

    /**
     * Generate custom loan reports
     */
    def generateReport() {
        try {
            if (params.type == "1") {
                // Loan Listing Report
                params._name = "Loan Listing"
                params._file = "loan_listing"

                def loans = Loan.list(fetch:[customer:"eager"])
                def loanListingEntries = []
                
                loans.each { loan ->
                    def entry = new LoanListingEntry()
                    entry.accountNo = loan.accountNo
                    entry.customerName = loan.customer?.displayName
                    entry.loanAmount = loan.grantedAmount
                    entry.balanceAmount = loan.balanceAmount
                    entry.interestRate = loan.interestRate
                    entry.maturityDate = loan.maturityDate
                    entry.status = loan.status?.description
                    loanListingEntries.add(entry)
                }

                def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), loanListingEntries)
                byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
                
                response.setContentType('application/pdf')
                response.setContentLength(bytes.length)
                response.setHeader('Content-disposition', 'inline; filename=loan_listing.pdf')
                response.outputStream << bytes
                response.outputStream.flush()

            } else if (params.type == "2") {
                // Loan Aging Report
                params._name = "Loan Aging Report"
                params._file = "loan_aging"

                def sql = new Sql(dataSource)
                def agingData = sql.rows("""
                    SELECT l.account_no, c.display_name, l.balance_amount, 
                           l.age_in_arrears, lps.description as performance_status
                    FROM loan l 
                    JOIN customer c ON l.customer_id = c.id
                    LEFT JOIN loan_performance_id lps ON l.loan_performance_id_id = lps.id
                    WHERE l.balance_amount > 0
                    ORDER BY l.age_in_arrears DESC
                """)

                def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), agingData)
                byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
                
                response.setContentType('application/pdf')
                response.setContentLength(bytes.length)
                response.setHeader('Content-disposition', 'inline; filename=loan_aging.pdf')
                response.outputStream << bytes
                response.outputStream.flush()

            } else if (params.type == "3") {
                // Loan Portfolio Report
                params._name = "Loan Portfolio Report"
                params._file = "loan_portfolio"

                def sql = new Sql(dataSource)
                def portfolioData = sql.rows("""
                    SELECT p.name as product_name, 
                           COUNT(l.id) as loan_count,
                           SUM(l.granted_amount) as total_granted,
                           SUM(l.balance_amount) as total_balance,
                           AVG(l.interest_rate) as avg_interest_rate
                    FROM loan l 
                    JOIN product p ON l.product_id = p.id
                    GROUP BY p.id, p.name
                    ORDER BY total_balance DESC
                """)

                def reportDef = jasperService.buildReportDefinition(params, request.getLocale(), portfolioData)
                byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
                
                response.setContentType('application/pdf')
                response.setContentLength(bytes.length)
                response.setHeader('Content-disposition', 'inline; filename=loan_portfolio.pdf')
                response.outputStream << bytes
                response.outputStream.flush()
            }

            def description = "Custom loan report generated - Type: " + params.type
            auditLogService.insert('090', 'LON02900', description, 'LoanReport', null, null, null, null)

        } catch(Exception e) {
            log.error("Error generating custom report", e)
            flash.message = "Error generating report: ${e.message}|error|alert"
            redirect(action: "reports")
        }
    }

    /**
     * Export loan data to Excel
     */
    def exportToExcel() {
        try {
            def loans = Loan.list()
            
            // Create Excel export logic here
            response.setContentType('application/vnd.ms-excel')
            response.setHeader('Content-disposition', 'attachment; filename=loan_export.xls')
            
            def description = "Loan data exported to Excel"
            auditLogService.insert('090', 'LON03000', description, 'LoanReport', null, null, null, null)
            
            // Excel generation logic would go here
            render "Excel export functionality"
            
        } catch(Exception e) {
            log.error("Error exporting to Excel", e)
            flash.message = "Error exporting data: ${e.message}|error|alert"
            redirect(action: "reports")
        }
    }

    /**
     * Generate loan summary report
     */
    def loanSummaryReport() {
        def sql = new Sql(dataSource)
        
        def summaryData = [:]
        summaryData.totalLoans = sql.firstRow("SELECT COUNT(*) as count FROM loan").count
        summaryData.totalAmount = sql.firstRow("SELECT SUM(granted_amount) as total FROM loan").total ?: 0
        summaryData.totalBalance = sql.firstRow("SELECT SUM(balance_amount) as total FROM loan").total ?: 0
        summaryData.activeLoans = sql.firstRow("SELECT COUNT(*) as count FROM loan WHERE status_id = 4").count
        summaryData.pastDueLoans = sql.firstRow("SELECT COUNT(*) as count FROM loan WHERE age_in_arrears > 0").count
        
        render(view: 'reports/summary', model: [summaryData: summaryData])
    }
}
