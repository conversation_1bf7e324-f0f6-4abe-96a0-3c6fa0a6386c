package org.icbs.loans

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.admin.Product
import org.icbs.loans.AmortizedChargeScheme
import org.icbs.loans.DeductionScheme
import org.icbs.loans.LoanServiceCharge
import org.icbs.loans.LoanDeduction
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.tellering.TxnFile
import org.icbs.admin.TxnTemplate
import org.icbs.lov.ConfigItemStatus

/**
 * LoanChargesController - Handles loan charges and deductions
 * 
 * This controller manages loan charges and deductions including:
 * - Service charges management (add, update, delete)
 * - Deductions management (add, update, delete)
 * - Charge scheme information retrieval
 * - Service charge credit/debit operations
 * - Deferred charge operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class LoanChargesController {
    
    // Service Dependencies
    def loanService
    def auditLogService
    def glTransactionService
    
    static allowedMethods = [
        addServiceChargeAjax: "POST",
        updateServiceChargeAjax: "POST",
        deleteServiceChargeAjax: "DELETE",
        addDeductionAjax: "POST",
        updateDeductionAjax: "POST",
        deleteDeductionAjax: "DELETE",
        loanServiceChargeCreditTransaction: "POST",
        loanServiceChargeDebitTransaction: "POST",
        deferredCredit: "POST",
        deferredDebit: "POST"
    ]

    /**
     * Show service charges via AJAX
     */
    def showServiceChargesAjax() {
        render(template:"serviceCharges/list") as JSON
        return
    }

    /**
     * Show add service charge form via AJAX
     */
    def showAddServiceChargeAjax() {
        def product = Product.get(params?.product)
        
        render(template:"serviceCharges/form", model:[product: product]) as JSON
        return
    }

    /**
     * Add service charge via AJAX
     */
    @Transactional
    def addServiceChargeAjax() {         
        def scheme = AmortizedChargeScheme.get(params?.scheme)
        def amount = params?.amount?.toDouble() ?: 0
        def percentage = params?.percentage?.toDouble() ?: 0
        def isPercentage = params?.isPercentage?.toBoolean() ?: false

        if (!session["serviceCharges"]) {
            session["serviceCharges"] = []
        }

        def serviceCharge = new LoanServiceCharge()
        serviceCharge.scheme = scheme
        serviceCharge.amount = amount
        serviceCharge.percentage = percentage
        serviceCharge.isPercentage = isPercentage
        serviceCharge.description = scheme?.description ?: "Service Charge"

        // Calculate actual amount if percentage
        if (isPercentage && params?.loanAmount) {
            def loanAmount = params.loanAmount.toDouble()
            serviceCharge.amount = (loanAmount * percentage) / 100
        }

        session["serviceCharges"].add(serviceCharge)

        render(template:"serviceCharges/list") as JSON
        return
    }

    /**
     * Show update service charge form via AJAX
     */
    def showUpdateServiceChargeAjax() {        
        def id = params?.id?.toInteger()
        def serviceCharge = null
        
        if (id != null && session["serviceCharges"] && id < session["serviceCharges"].size()) {
            serviceCharge = session["serviceCharges"].get(id)
        }

        render(template:"serviceCharges/updateForm", model:[serviceCharge: serviceCharge, id: id]) as JSON
        return
    }

    /**
     * Update service charge via AJAX
     */
    @Transactional
    def updateServiceChargeAjax() {   
        def id = params?.id?.toInteger()
        def amount = params?.amount?.toDouble() ?: 0
        def percentage = params?.percentage?.toDouble() ?: 0
        def isPercentage = params?.isPercentage?.toBoolean() ?: false

        if (id != null && session["serviceCharges"] && id < session["serviceCharges"].size()) {
            def serviceCharge = session["serviceCharges"].get(id)
            serviceCharge.amount = amount
            serviceCharge.percentage = percentage
            serviceCharge.isPercentage = isPercentage

            // Recalculate amount if percentage
            if (isPercentage && params?.loanAmount) {
                def loanAmount = params.loanAmount.toDouble()
                serviceCharge.amount = (loanAmount * percentage) / 100
            }
        }

        render(template:"serviceCharges/list") as JSON
        return
    }

    /**
     * Delete service charge via AJAX
     */
    @Transactional
    def deleteServiceChargeAjax() {
        def id = params?.id?.toInteger()
        
        if (id != null && session["serviceCharges"] && id < session["serviceCharges"].size()) {
            session["serviceCharges"].remove(id)
        }

        render "success"
        return
    }

    /**
     * Show deductions via AJAX
     */
    def showDeductionsAjax() {
        render(template:"deductions/list") as JSON
        return
    }

    /**
     * Show add deduction form via AJAX
     */
    def showAddDeductionAjax() {
        def product = Product.get(params?.product)
        
        render(template:"deductions/form", model:[product: product]) as JSON
        return
    }

    /**
     * Add deduction via AJAX
     */
    @Transactional
    def addDeductionAjax() {
        println("params: " + params)
        def scheme = DeductionScheme.get(params?.scheme)
        def amount = params?.amount?.toDouble() ?: 0
        def percentage = params?.percentage?.toDouble() ?: 0
        def isPercentage = params?.isPercentage?.toBoolean() ?: false

        if (!session["deductions"]) {
            session["deductions"] = []
        }

        def deduction = new LoanDeduction()
        deduction.scheme = scheme
        deduction.amount = amount
        deduction.percentage = percentage
        deduction.isPercentage = isPercentage
        deduction.description = scheme?.description ?: "Deduction"

        // Calculate actual amount if percentage
        if (isPercentage && params?.loanAmount) {
            def loanAmount = params.loanAmount.toDouble()
            deduction.amount = (loanAmount * percentage) / 100
        }

        session["deductions"].add(deduction)

        render(template:"deductions/list") as JSON
        return
    }

    /**
     * Show update deduction form via AJAX
     */
    def showUpdateDeductionAjax() {        
        def id = params?.id?.toInteger()
        def deduction = null
        
        if (id != null && session["deductions"] && id < session["deductions"].size()) {
            deduction = session["deductions"].get(id)
        }

        render(template:"deductions/updateForm", model:[deduction: deduction, id: id]) as JSON
        return
    }

    /**
     * Update deduction via AJAX
     */
    @Transactional
    def updateDeductionAjax() {   
        def id = params?.id?.toInteger()
        def amount = params?.amount?.toDouble() ?: 0
        def percentage = params?.percentage?.toDouble() ?: 0
        def isPercentage = params?.isPercentage?.toBoolean() ?: false

        if (id != null && session["deductions"] && id < session["deductions"].size()) {
            def deduction = session["deductions"].get(id)
            deduction.amount = amount
            deduction.percentage = percentage
            deduction.isPercentage = isPercentage

            // Recalculate amount if percentage
            if (isPercentage && params?.loanAmount) {
                def loanAmount = params.loanAmount.toDouble()
                deduction.amount = (loanAmount * percentage) / 100
            }
        }

        render(template:"deductions/list") as JSON
        return
    }

    /**
     * Delete deduction via AJAX
     */
    @Transactional
    def deleteDeductionAjax() {
        def id = params?.id?.toInteger()
        
        if (id != null && session["deductions"] && id < session["deductions"].size()) {
            session["deductions"].remove(id)
        }

        render "success"
        return
    }

    /**
     * Get amortized charge scheme info via AJAX
     */
    def getAmortizedChargeSchemeInfoAjax() {
        def amortizedChargeScheme = null
        if (params?.scheme) {
            amortizedChargeScheme = AmortizedChargeScheme.get(params.scheme)
        }

        render(template:"schemes/amortizedChargeInfo", model:[amortizedChargeScheme: amortizedChargeScheme]) as JSON
        return
    }

    /**
     * Get deduction scheme info via AJAX
     */
    def getDeductionSchemeInfoAjax() {
        def deductionScheme = null
        if (params?.scheme) {
            deductionScheme = DeductionScheme.get(params.scheme)
        }

        render(template:"schemes/deductionInfo", model:[deductionScheme: deductionScheme]) as JSON
        return
    }

    /**
     * Show loan service charge credit form
     */
    def loanServiceChargeCredit() {
        if(params.id){
            def loanInstance = Loan.get(params.id)
            if (loanInstance == null) {
                notFound()
                return
            }
            render(view:'loanLoss/loanServiceChargeCredit', model: [loanInstance:loanInstance])
        } else {
            notFound()
        } 
    }

    /**
     * Show loan service charge debit form
     */
    def loanServiceChargeDebit() {
        if(params.id){
            def loanInstance = Loan.get(params.id)
            if (loanInstance == null) {
                notFound()
                return
            }
            render(view:'loanLoss/loanServiceChargeDebit', model: [loanInstance:loanInstance])
        } else {
            notFound()
        } 
    }

    /**
     * Show loan deferred credit form
     */
    def loanDeferredCredit() {
        if(params.id){
            def loanInstance = Loan.get(params.id)
            if (loanInstance == null) {
                notFound()
                return
            }
            render(view:'loanLoss/loanDeferredCredit', model: [loanInstance:loanInstance])
        } else {
            notFound()
        }
    }

    /**
     * Show loan deferred debit form
     */
    def loanDeferredDebit() {
        if(params.id){
            def loanInstance = Loan.get(params.id)
            if (loanInstance == null) {
                notFound()
                return
            }
            render(view:'loanLoss/loanDeferredDebit', model: [loanInstance:loanInstance])
        } else {
            notFound()
        }
    }

    /**
     * Process service charge credit transaction
     */
    @Transactional
    def loanServiceChargeCreditTransaction() {
        def serviceChargeAmount = params.creditAmt.toString().replace(',','').toDouble()
        def loanInstance = Loan.get(params.serviceChargeCredit.toInteger())
        def b = Branch.get(1)
        def t = TxnTemplate.get(params.txnType.toInteger())
        
        def tx = new TxnFile(
            txnCode: t.code,
            txnDescription: t.description,
            txnDate: b.runDate,
            currency: loanInstance.currency,
            txnAmt: serviceChargeAmount,
            txnRef: params.reference,
            status: ConfigItemStatus.get(2),
            branch: loanInstance.branch,
            txnTimestamp: new Date().toTimestamp(),
            txnParticulars: params.particulars,
            txnType: t.txnType,
            txnTemplate: t,
            user: UserMaster.get(session.user_id)
        )
        tx.save(flush:true, failOnError:true)

        // Update loan service charge balance
        loanInstance.serviceChargeBalanceAmount += serviceChargeAmount
        loanInstance.save(flush:true)

        // Generate GL transactions
        glTransactionService.saveTxnBreakdown(tx.id)

        def description = "Service charge credit processed for loan " + loanInstance.accountNo
        auditLogService.insert('090', 'LON01800', description, 'Loan', null, null, null, loanInstance.id)

        redirect(action: "show", controller: "loanAccount", id: loanInstance.id)
    }

    /**
     * Process service charge debit transaction
     */
    @Transactional
    def loanServiceChargeDebitTransaction() {
        def serviceChargeAmount = params.debitAmt.toString().replace(',','').toDouble()
        def loanInstance = Loan.get(params.serviceChargeDebit.toInteger())
        def b = Branch.get(1)
        def t = TxnTemplate.get(params.txnType.toInteger())
        
        def tx = new TxnFile(
            txnCode: t.code,
            txnDescription: t.description,
            txnDate: b.runDate,
            currency: loanInstance.currency,
            txnAmt: serviceChargeAmount,
            txnRef: params.reference,
            status: ConfigItemStatus.get(2),
            branch: loanInstance.branch,
            txnTimestamp: new Date().toTimestamp(),
            txnParticulars: params.particulars,
            txnType: t.txnType,
            txnTemplate: t,
            user: UserMaster.get(session.user_id)
        )
        tx.save(flush:true, failOnError:true)

        // Update loan service charge balance
        loanInstance.serviceChargeBalanceAmount -= serviceChargeAmount
        loanInstance.save(flush:true)

        // Generate GL transactions
        glTransactionService.saveTxnBreakdown(tx.id)

        def description = "Service charge debit processed for loan " + loanInstance.accountNo
        auditLogService.insert('090', 'LON01800', description, 'Loan', null, null, null, loanInstance.id)

        redirect(action: "show", controller: "loanAccount", id: loanInstance.id)
    }

    // Helper methods
    protected def notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = message(code: 'default.not.found.message', args: [message(code: 'Loan.label', default: 'Loan'), params.id])
                redirect action: "index", controller: "loanAccount", method: "GET"
            }
            '*'{ render status: NOT_FOUND }
        }
    }
}
