package org.icbs.loans

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.loans.LoanGuaranteeDetail
import org.icbs.loans.LoanRediscountingStatus
import org.icbs.lov.Commodity
import org.icbs.lov.LoanRediscountingPartner
import org.icbs.admin.UserMaster
import groovy.sql.Sql

/**
 * LoanGuaranteeController - Handles loan guarantee and rediscounting operations
 * 
 * This controller manages loan guarantee operations including:
 * - Loan guarantee management and details
 * - AGFP (Agricultural Guarantee Fund Pool) information
 * - SBGFC (Small Business Guarantee and Finance Corporation) information
 * - HGC (Home Guaranty Corporation) information
 * - Loan rediscounting operations
 * - Commodity rate management
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class LoanGuaranteeController {
    
    // Service Dependencies
    def loanService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        saveGuarantee: "POST",
        saveAgfpInformation: "POST",
        saveSbgfcInformation: "POST",
        saveHgcInformation: "POST",
        saveLoanRediscounting: "POST"
    ]

    /**
     * Show loan guarantee details
     */
    def loanGurantee(Loan loanInstance) {
        if (loanInstance == null){
            notFound()
            return        
        }
        
        def loanGuaranteeInstance = LoanGuaranteeDetail.findByLoan(loanInstance)
        
        render(view:'loanGuarantee/loanGuaranteeShow', model: [
            loanInstance: loanInstance,
            loanGuaranteeInstance: loanGuaranteeInstance
        ])
    }

    /**
     * Show AGFP information form
     */
    def agfpInformation() {
        println("---->>rdmontana Loan Rediscounting<<-----")
        println("params: " + params)
        
        def loanInstance = Loan.get(params.id)
        if (loanInstance == null){
            notFound()
            return        
        }
        
        println("loanInstance: " + loanInstance)
        def loanGuaranteeInstance = LoanGuaranteeDetail.findByLoan(loanInstance)
        
        if (loanGuaranteeInstance == null) {
            loanGuaranteeInstance = new LoanGuaranteeDetail()
        } 
        
        render(view:'loanGuarantee/agfpInformation', model: [
            loanInstance: loanInstance,
            loanGuaranteeInstance: loanGuaranteeInstance
        ])
    }

    /**
     * Get commodity rate via AJAX
     */
    def commodityRateAjax() {
        def json = request.JSON
        def sql = new Sql(dataSource)
        def agfpCommodity = json.agfpCommodity.toString()
        println("agfpCommodity: " + json.agfpCommodity)
        
        def queryall = "select * from commodity where id='${agfpCommodity}'"
        def resultqueryall = sql.rows(queryall)
        println("return: " + resultqueryall)
        
        render resultqueryall as JSON
    }

    /**
     * Save AGFP information
     */
    @Transactional
    def saveAgfpInformation() {
        println("rdmontana ====> Pumasok na sa saving ng saveAgfpInformation")
        
        def loanInstance = Loan.get(params.id)
        def loanId = Loan.findById(Integer.parseInt(params.loanInstance.toString()))
        def loanGuaranteeInstance = LoanGuaranteeDetail.findByLoan(loanId)
        
        if (loanGuaranteeInstance == null) {
            loanGuaranteeInstance = new LoanGuaranteeDetail()
            loanGuaranteeInstance.loan = loanId
        }
        
        // Update AGFP fields
        loanGuaranteeInstance.agfpCommodity = Commodity.get(params.agfpCommodity)
        loanGuaranteeInstance.agfpCommodityRate = params.agfpCommodityRate?.toDouble() ?: 0
        loanGuaranteeInstance.agfpLoanAmount = params.agfpLoanAmount?.toDouble() ?: 0
        loanGuaranteeInstance.agfpGuaranteeAmount = params.agfpGuaranteeAmount?.toDouble() ?: 0
        loanGuaranteeInstance.agfpGuaranteeRate = params.agfpGuaranteeRate?.toDouble() ?: 0
        loanGuaranteeInstance.agfpRemarks = params.agfpRemarks
        
        loanGuaranteeInstance.save(flush: true, failOnError: true)

        def description = "AGFP information saved for loan " + loanInstance.accountNo
        auditLogService.insert('090', 'LON01900', description, 'Loan', null, null, null, loanInstance.id)

        redirect(action: "loanGurantee", id: loanInstance.id)
    }

    /**
     * Show SBGFC information form
     */
    def sbgfcInformation() {
        println("---->>rdmontana SBGFC Information<<-----")
        println("params: " + params)
        
        def loanInstance = Loan.get(params.id)
        if (loanInstance == null){
            notFound()
            return        
        }
        
        def loanGuaranteeInstance = LoanGuaranteeDetail.findByLoan(loanInstance)
        if (loanGuaranteeInstance == null) {
            loanGuaranteeInstance = new LoanGuaranteeDetail()
        } 
        
        render(view:'loanGuarantee/sbgfcInformation', model: [
            loanInstance: loanInstance,
            loanGuaranteeInstance: loanGuaranteeInstance
        ])
    }

    /**
     * Save SBGFC information
     */
    @Transactional
    def saveSbgfcInformation() {
        println("rdmontana ====> Pumasok na sa saving ng saveSbgfcInformation")
        
        def loanInstance = Loan.get(params.id)
        def loanId = Loan.findById(Integer.parseInt(params.loanInstance.toString()))
        def loanGuaranteeInstance = LoanGuaranteeDetail.findByLoan(loanId)
        
        if (loanGuaranteeInstance == null) {
            loanGuaranteeInstance = new LoanGuaranteeDetail()
            loanGuaranteeInstance.loan = loanId
        }
        
        // Update SBGFC fields
        loanGuaranteeInstance.sbgfcLoanAmount = params.sbgfcLoanAmount?.toDouble() ?: 0
        loanGuaranteeInstance.sbgfcGuaranteeAmount = params.sbgfcGuaranteeAmount?.toDouble() ?: 0
        loanGuaranteeInstance.sbgfcGuaranteeRate = params.sbgfcGuaranteeRate?.toDouble() ?: 0
        loanGuaranteeInstance.sbgfcRemarks = params.sbgfcRemarks
        
        loanGuaranteeInstance.save(flush: true, failOnError: true)

        def description = "SBGFC information saved for loan " + loanInstance.accountNo
        auditLogService.insert('090', 'LON02000', description, 'Loan', null, null, null, loanInstance.id)

        redirect(action: "loanGurantee", id: loanInstance.id)
    }

    /**
     * Show HGC information form
     */
    def hgcInformation() {
        println("---->>rdmontana HGC Information<<-----")
        println("params: " + params)
        
        def loanInstance = Loan.get(params.id)
        if (loanInstance == null){
            notFound()
            return        
        }
        
        def loanGuaranteeInstance = LoanGuaranteeDetail.findByLoan(loanInstance)
        if (loanGuaranteeInstance == null) {
            loanGuaranteeInstance = new LoanGuaranteeDetail()
        } 
        
        render(view:'loanGuarantee/hgcInformation', model: [
            loanInstance: loanInstance,
            loanGuaranteeInstance: loanGuaranteeInstance
        ])
    }

    /**
     * Save HGC information
     */
    @Transactional
    def saveHgcInformation() {
        println("rdmontana ====> Pumasok na sa saving ng saveHgcInformation")
        
        def loanInstance = Loan.get(params.id)
        def loanId = Loan.findById(Integer.parseInt(params.loanInstance.toString()))
        def loanGuaranteeInstance = LoanGuaranteeDetail.findByLoan(loanId)
        
        if (loanGuaranteeInstance == null) {
            loanGuaranteeInstance = new LoanGuaranteeDetail()
            loanGuaranteeInstance.loan = loanId
        }
        
        // Update HGC fields
        loanGuaranteeInstance.hgcLoanAmount = params.hgcLoanAmount?.toDouble() ?: 0
        loanGuaranteeInstance.hgcGuaranteeAmount = params.hgcGuaranteeAmount?.toDouble() ?: 0
        loanGuaranteeInstance.hgcGuaranteeRate = params.hgcGuaranteeRate?.toDouble() ?: 0
        loanGuaranteeInstance.hgcRemarks = params.hgcRemarks
        
        loanGuaranteeInstance.save(flush: true, failOnError: true)

        def description = "HGC information saved for loan " + loanInstance.accountNo
        auditLogService.insert('090', 'LON02100', description, 'Loan', null, null, null, loanInstance.id)

        redirect(action: "loanGurantee", id: loanInstance.id)
    }

    /**
     * Show loan guarantee details
     */
    def loanGuaranteeShow() {
        println("rdmontana ====> Pumasok na sa saving ng loanGuaranteeShow")
        def loanInstance = Loan.get(params.id)
        if (loanInstance == null) {
            notFound()
            return
        }
        
        def loanGuaranteeInstance = LoanGuaranteeDetail.findByLoan(loanInstance)
        
        render(view:'loanGuarantee/show', model: [
            loanInstance: loanInstance,
            loanGuaranteeInstance: loanGuaranteeInstance
        ])
    }

    /**
     * Show loan rediscounting form
     */
    def loanRediscountingGsp() {
        println("---->>rdmontana Loan Rediscounting GSP<<-----")
        def loanInstance = Loan.get(params.id)
        if (loanInstance == null) {
            notFound()
            return
        }
        
        def rediscountingPartners = LoanRediscountingPartner.list()
        
        render(view:'rediscounting/form', model: [
            loanInstance: loanInstance,
            rediscountingPartners: rediscountingPartners
        ])
    }

    /**
     * Save loan rediscounting
     */
    @Transactional
    def saveLoanRediscounting() {
        println("---->>rdmontana Loan Rediscounting CREATE<<-----")
        def loanInstance = Loan.get(params.loanId.toInteger())
        def partner = LoanRediscountingPartner.get(params.partnerId.toInteger())
        
        if (loanInstance == null || partner == null) {
            notFound()
            return
        }
        
        def rediscountingStatus = new LoanRediscountingStatus()
        rediscountingStatus.loan = loanInstance
        rediscountingStatus.partner = partner
        rediscountingStatus.rediscountingAmount = params.rediscountingAmount?.toDouble() ?: 0
        rediscountingStatus.rediscountingRate = params.rediscountingRate?.toDouble() ?: 0
        rediscountingStatus.rediscountingDate = params.rediscountingDate ? Date.parse("MM/dd/yyyy", params.rediscountingDate) : null
        rediscountingStatus.maturityDate = params.maturityDate ? Date.parse("MM/dd/yyyy", params.maturityDate) : null
        rediscountingStatus.remarks = params.remarks
        rediscountingStatus.createdBy = UserMaster.get(session.user_id)
        rediscountingStatus.dateCreated = new Date()
        
        rediscountingStatus.save(flush: true, failOnError: true)

        def description = "Loan rediscounting created for loan " + loanInstance.accountNo
        auditLogService.insert('090', 'LON02200', description, 'Loan', null, null, null, loanInstance.id)

        redirect(action: "show", controller: "loanAccount", id: loanInstance.id)
    }

    /**
     * Save guarantee details
     */
    @Transactional
    def saveGuarantee(Loan loanInstance) {
        if (loanInstance == null) {
            notFound()
            return
        }
        
        def loanGuaranteeInstance = LoanGuaranteeDetail.findByLoan(loanInstance)
        if (loanGuaranteeInstance == null) {
            loanGuaranteeInstance = new LoanGuaranteeDetail()
            loanGuaranteeInstance.loan = loanInstance
        }
        
        // Update general guarantee fields
        loanGuaranteeInstance.guaranteeType = params.guaranteeType
        loanGuaranteeInstance.guaranteeAmount = params.guaranteeAmount?.toDouble() ?: 0
        loanGuaranteeInstance.guaranteeRate = params.guaranteeRate?.toDouble() ?: 0
        loanGuaranteeInstance.guaranteeRemarks = params.guaranteeRemarks
        
        loanGuaranteeInstance.save(flush: true, failOnError: true)

        def description = "Guarantee details saved for loan " + loanInstance.accountNo
        auditLogService.insert('090', 'LON02300', description, 'Loan', null, null, null, loanInstance.id)

        redirect(action: "loanGurantee", id: loanInstance.id)
    }

    /**
     * Collection information via AJAX
     */
    def collectionInformation() {
        def json = request.JSON
        def sql = new Sql(dataSource)
        
        def loanId = json.loanId
        def collectionInfo = sql.rows("""
            SELECT * FROM loan_collection_info 
            WHERE loan_id = ?
            ORDER BY date_created DESC
        """, [loanId])
        
        render collectionInfo as JSON
    }

    // Helper methods
    protected def notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = message(code: 'default.not.found.message', args: [message(code: 'Loan.label', default: 'Loan'), params.id])
                redirect action: "index", controller: "loanAccount", method: "GET"
            }
            '*'{ render status: NOT_FOUND }
        }
    }
}
