package org.icbs.loans

import grails.converters.JSON

import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional

import org.apache.poi.ss.formula.functions.Irr
import org.apache.poi.ss.formula.functions.FinanceLib
import org.icbs.tellering.TxnLoanPaymentDetails
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.ObjectInputStream
import java.io.ObjectOutputStream
import org.icbs.lov.LoanProvisionBsp
import org.icbs.admin.Branch
import org.icbs.admin.Product
import org.icbs.admin.UserMaster
import org.icbs.deposit.Deposit
import org.icbs.admin.Currency
import org.icbs.admin.Institution
import org.icbs.loans.LoanApplication
import org.icbs.loans.LoanRemark
import org.icbs.loans.LoanApplicationComaker.*
import org.icbs.loans.TxnRopaDetails
import org.icbs.loans.GroupRecord	
import org.icbs.loans.LoanLossProvisionDetail	
import org.icbs.loans.PenaltyIncomeScheme
import org.icbs.loans.LoanGuaranteeDetail
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanSpecialType
import org.icbs.lov.TxnType
import org.icbs.admin.TxnTemplate
import org.icbs.deposit.Hold
import org.icbs.deposit.Deposit
import org.icbs.lov.HoldStatus
import org.icbs.lov.HoldType
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.SweepType
import org.icbs.lov.SweepRule
import org.icbs.lov.SweepStatus
import org.icbs.tellering.TxnFile
import org.icbs.gl.GlAccount
import org.icbs.gl.CfgAcctGlTemplate
import org.icbs.gl.CfgAcctGlTemplateDet
import org.hibernate.Session
import org.hibernate.SessionFactory
import org.icbs.reports.LoanListingEntry
import groovy.time.TimeCategory
import org.icbs.loans.LoanSweep
import groovy.sql.Sql
import org.icbs.tellering.TxnBreakdown
import org.icbs.loans.ROPA
import org.icbs.loans.ScrRopa
import org.icbs.loans.ROPALedger
//new imports
import java.text.SimpleDateFormat
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.web.multipart.MultipartFile
import java.lang.*

class ScrController {
    def jrxmlTcId
    def jasperService
    def transactionFileId 
    def dataSource
    def auditLogService
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]
    
    def loanService   
    def glTransactionService
    def index(Integer max) {
       // params.max = Math.min(max ?: 10, 100)   // no. of items on display
           params.max = Math.min(max ?: 25, 100)
        
        if (params.sort == null) {  // default ordering
           // params.sort = "id"
              params.sort  = "dateApproved" 
              params.order =  "desc"
        }

        if (params.query == null || params.query.trim() == "") {  // show all instances
            respond ROPA.list(params), model:[params:params, ropaInstanceCount: ROPA.count()]
        } else {    // show query results
            def result = ROPA.createCriteria().list(params) {
                or{
                    'customer'{
                        or{
                            ilike("displayName","%${params.query.trim()}%")
                        }
                    }
                    if(params.query.trim().isLong()){
                        idEq(params.query.trim().toLong())
                    }
                }
            }
            println result.totalCount
            respond result, model:[params:params, ropaInstanceCount: result.totalCount]
        }
        return
    } 

    def search(Integer max) {
     // params.max = Math.min(max ?: 10, 100) 
        params.max = Math.min(max ?: 25, 100)
  
         if (params.sort == null) {
            params.sort = "id"
        }

        if (params.query == null || params.query.trim() == "") {  // show all instances
            render(template:"search/searchRopa", model:[params:params, domainInstanceList:ROPA.list(params), domainInstanceCount:LoanApplication.count()]) as JSON
        } else {    // show query results
            def result = ROPA.createCriteria().list(params) {
                or{
                    'customerDisplayName'{
                        or{
                            ilike("customerDisplayName","%${params.query.trim()}%")
                        }
                    }
                    if(params.query.trim().isLong()){
                        idEq(params.query.trim().toLong())
                    }
                }
            }
            render(template:"search/searchRopa", model:[params:params, domainInstanceList:result, domainInstanceCount:result.totalCount]) as JSON
        }            
        return
    }
}
