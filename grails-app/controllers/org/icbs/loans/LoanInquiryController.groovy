package org.icbs.loans

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.audit.AuditLog
import org.icbs.admin.Branch
import org.icbs.admin.Module
import org.icbs.admin.Product
import org.icbs.admin.UserMaster
import org.icbs.loans.LoanApplication
import org.icbs.loans.LoanHistory
import org.icbs.loans.LoanWriteOffCollectionHist
import org.icbs.loans.ROPA
import org.icbs.lov.LoanAcctStatus
import groovy.sql.Sql

/**
 * LoanInquiryController - Handles loan inquiry and search operations
 * 
 * This controller manages loan inquiry operations including:
 * - Loan account search and filtering
 * - Loan details retrieval via AJAX
 * - Loan history viewing and tracking
 * - ROPA and write-off inquiry operations
 * - Loan payment history inquiry
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional(readOnly = true)
class LoanInquiryController {
    
    // Service Dependencies
    def loanService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        search: "GET",
        getLoanDetailsAjax: "GET",
        showHistory: "GET"
    ]

    /**
     * AJAX search for loan accounts
     */
    def search(Integer max) {
        def flags = params.flag
        if(!flags){
            flags = 0
        }
        params.max = Math.min(max ?: 25, 100)

        if (params.sort == null) {
            params.sort = "id"
        }
        
        if (params.query == null || params.query.trim() == "") {  
            // Show all instances
            render(template:"search/searchLoan", model:[params:params, domainInstanceList:Loan.list(params), domainInstanceCount:Loan.count()]) as JSON
        } else {    
            // Show query results
            def result = Loan.createCriteria().list(params){
                or{
                    ilike("accountNo","%${params.query.trim()}%")
                    'customer'{
                        or{
                            ilike("displayName","%${params.query.trim()}%")
                        }
                    }
                    if(params.query.trim().isLong()){
                        idEq(params.query.trim().toLong())
                    }
                }
            }
            
            render(template:"search/searchLoan", model:[params:params, domainInstanceList:result, domainInstanceCount:result.totalCount]) as JSON
        }            
        return
    }

    /**
     * Get loan details via AJAX
     */
    def getLoanDetailsAjax() {
        def loanInstance = null
        if (params?.id) {
            loanInstance = Loan.get(params.id)
        }

        render(template:"search/loanDetails", model:[loanInstance: loanInstance]) as JSON
        return
    }

    /**
     * Show loan history
     */
    def showHistory() {
        println("=======  SHOW HISTORY ============")
        def loanInstance = Loan.get(params.id)
        if (loanInstance == null) {
            notFound()
            return
        }

        def loanHistoryList = LoanHistory.findAllByAccountNo(loanInstance.accountNo)
        
        // Audit Logs
        def auditLogsList = AuditLog.findAllByRecordIdAndTableNameAndModuleBetween(
            loanInstance.id, 'Loan', Module.get(216), Module.get(236))

        render(view: 'history/show', model: [
            loanInstance: loanInstance,
            loanHistoryList: loanHistoryList.sort{it.id},
            auditLogsList: auditLogsList
        ])
    }

    /**
     * View ROPA details for loan
     */
    def viewRopa() {
        if(params.id){
            def loanInstance = Loan.get(params.id)
            if (loanInstance == null) {
                notFound()
                return
            }
            render(view:'ropa/view', model: [loanInstance:loanInstance])
        } else {
            notFound()
        } 
    }

    /**
     * View write-off details for loan
     */
    def viewWriteOff() {
        if(params.id){
            def loanInstance = Loan.get(params.id)
            if (loanInstance == null) {
                notFound()
                return
            }
            render(view:'writeOff/view', model: [loanInstance:loanInstance])
        } else {
            notFound()
        }
    }

    /**
     * View loan payment list
     */
    def viewLoanPaymentList(Integer max) {
        params.max = Math.min(max ?: 25, 100)
        
        if (params.sort == null) {
            params.sort = "id"
            params.order = "desc"
        }

        def loanInstance = Loan.get(params.id)
        if (loanInstance == null) {
            notFound()
            return
        }

        def sql = new Sql(dataSource)
        def paymentList = sql.rows("""
            SELECT * FROM txn_loan_payment_details 
            WHERE acct_id = ? 
            ORDER BY id DESC
        """, [loanInstance.id])

        render(view: 'payment/list', model: [
            loanInstance: loanInstance,
            paymentList: paymentList,
            params: params
        ])
    }

    /**
     * Show loan payment details
     */
    def showLoanPaymentDetails() {
        println("================ showLoanPaymentDetails =========")
        def paymentId = params.id
        if (!paymentId) {
            notFound()
            return
        }

        def sql = new Sql(dataSource)
        def paymentDetails = sql.firstRow("""
            SELECT * FROM txn_loan_payment_details 
            WHERE id = ?
        """, [paymentId])

        if (!paymentDetails) {
            notFound()
            return
        }

        render(view: 'payment/details', model: [
            paymentDetails: paymentDetails
        ])
    }

    /**
     * Loan write-off collection list
     */
    def loanWriteOffCollectionList() {
        println("============ loanWriteOffCollectionList ============")
        params.max = Math.min(params?.max?.toInteger() ?: 25, 100)
        
        if (params.sort == null) {
            params.sort = "id"
            params.order = "desc"
        }

        def writeOffList = LoanWriteOffCollectionHist.list(params)
        
        render(view: 'writeOff/collectionList', model: [
            writeOffList: writeOffList,
            writeOffCount: LoanWriteOffCollectionHist.count(),
            params: params
        ])
    }

    /**
     * Write-off collection details
     */
    def writeOffCollectionDetails() {
        println("========== writeOffCollectionDetails ===========")
        def writeOffInstance = LoanWriteOffCollectionHist.get(params.id)
        if (writeOffInstance == null) {
            notFound()
            return
        }

        render(view: 'writeOff/collectionDetails', model: [
            writeOffInstance: writeOffInstance
        ])
    }

    /**
     * Get product schemes via AJAX
     */
    def getProductSchemesAjax() {
        def loanInstance = null
        if (params?.id) {
            loanInstance = Loan.get(params.id)
        }

        def product = null
        if (params?.product) {
            product = Product.get(params.product)
        }

        def interestIncomeSchemes = []
        def amortizedChargeSchemes = []
        def deductionSchemes = []

        if (product) {
            interestIncomeSchemes = product.interestIncomeSchemes
            amortizedChargeSchemes = product.amortizedChargeSchemes  
            deductionSchemes = product.deductionSchemes
        }

        render(template:"schemes/list", model:[
            loanInstance: loanInstance,
            interestIncomeSchemes: interestIncomeSchemes,
            amortizedChargeSchemes: amortizedChargeSchemes,
            deductionSchemes: deductionSchemes
        ]) as JSON
        return
    }

    /**
     * Get scheme details via AJAX
     */
    def getSchemeDetailsAjax() {
        def interestIncomeScheme = null
        if (params?.scheme) {
            interestIncomeScheme = InterestIncomeScheme.get(params.scheme)
        }

        render(template:"schemes/details", model:[
            interestIncomeScheme: interestIncomeScheme
        ]) as JSON
        return
    }

    /**
     * Show loan application details via AJAX
     */
    def showLoanApplicationAjax() {
        println("pasok dito agad 1")
        def loanApplication = null
        if (params?.id) {
            loanApplication = LoanApplication.get(params.id)
        }

        render(template:"application/details", model:[
            loanApplication: loanApplication
        ]) as JSON
        return
    }

    // Helper methods
    protected def notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = message(code: 'default.not.found.message', args: [message(code: 'Loan.label', default: 'Loan'), params.id])
                redirect action: "index", method: "GET"
            }
            '*'{ render status: NOT_FOUND }
        }
    }
}
