package org.icbs.loans

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.audit.AuditLog
import org.icbs.admin.Branch
import org.icbs.admin.Module
import org.icbs.admin.Product
import org.icbs.admin.UserMaster
import org.icbs.admin.Currency
import org.icbs.loans.LoanApplication
import org.icbs.loans.LoanApplicationComaker
import org.icbs.loans.LoanHistory
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanProvisionBsp
import org.icbs.lov.ConfigItemStatus
import org.springframework.dao.DataIntegrityViolationException

/**
 * LoanAccountController - Handles core loan account operations
 * 
 * This controller manages basic loan account operations including:
 * - Loan account creation and setup
 * - Loan account viewing and display
 * - Loan account editing and updates
 * - Loan account deletion and status management
 * - Basic loan account inquiry operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class LoanAccountController {
    
    // Service Dependencies
    def loanService
    def auditLogService
    def dataSource
    
    static allowedMethods = [save: "POST", update: ["PUT","POST"], delete: "DELETE"]

    /**
     * Lists loan accounts with pagination and search
     */
    def index(Integer max) {
        def module = getModule(request?.forwardURI)
        def title = getTitle(module)

        params.max = Math.min(max ?: 25, 100)
        
        if (params.sort == null) {
            params.sort = "id"
        }

        if (params.query == null) {
            respond Loan.list(params), model:[params:params, LoanInstanceCount: Loan.count(), module:module, title:title]
        } else {
            def LoanList = Loan.createCriteria().list(params) {
               or{
                 ilike("accountNo","%${params.query.trim()}%")
                    'customer'{
                        or{
                            ilike("displayName","%${params.query.trim()}%")
                        }
                    }
                    if(params.query.trim().isLong()){
                        idEq(params.query.trim().toLong())
                    }
                }
            }
            
            respond LoanList, model:[params:params, LoanInstanceCount: LoanList.totalCount, module:module, title:title]
        }
        return
    }

    /**
     * Shows detailed loan account information
     */
    def show(Loan loanInstance) {
        println "id "+loanInstance.id
        def a = loanInstance.id.toInteger()
        session["jrxmlTcId"] = a
        def module = getModule(request?.forwardURI)
        def title = getTitle(module)

        def loanApplicationInstance = loanInstance.loanApplication
        def comakers = LoanApplicationComaker.findAllByLoanApplication(loanApplicationInstance)
        
        println("   comakerss: "+comakers)	
        def totalDeductions = 0
        for(loanDeduction in loanInstance?.loanDeductions) {
            totalDeductions += loanDeduction?.amount
        }

        def totalUID = 0
        def totalUIDServiceCharge = 0.00D
        for(loanEIRSchedules in loanInstance?.loanEIRSchedules) {
            totalUID += loanEIRSchedules?.uidAmount.round(2)
            if(!loanEIRSchedules?.serviceChargeAmount){
                loanEIRSchedules?.serviceChargeAmount = 0.00D
            }
            totalUIDServiceCharge += loanEIRSchedules?.serviceChargeAmount
        }

        // Calculate interest to date
        def intToDate = loanService.getInterestToDate(loanInstance)

        def accountOfficerInstance

        def loanHistoryList = LoanHistory.findAllByAccountNo(loanInstance.accountNo)   
        
        //Audit Logs
        def auditLogsList = AuditLog.findAllByRecordIdAndTableNameAndModuleBetween(loanInstance.id,'Loan',Module.get(216),Module.get(236))
        println("auditLogsList: " +auditLogsList)

        respond loanInstance, model:[loanApplicationInstance:loanApplicationInstance,accountOfficerInstance:accountOfficerInstance, totalDeductions: totalDeductions, 
           totalUID: totalUID,totalUIDServiceCharge:totalUIDServiceCharge, loanHistoryList: loanHistoryList.sort{it.id}, module:module, title:title, intToDate:intToDate, comakers:comakers, auditLogsList:auditLogsList]
    }

    /**
     * Creates new loan account form
     */
    def create() {
        // initialize session variables
        session["serviceCharges"] = []
        session["deductions"] = []
        session["installments"] = []        
        session["eirSchedules"] = []
        session["sweepAccounts"] = []
        session["pageAction"]=""
        session["pageAction"]="create"	

        def loanApplication = null
        if (params?.id) {
            loanApplication = LoanApplication.get(params?.id)
        }

        respond new Loan(params), model:[loanApplication:loanApplication]
    }

    /**
     * Saves new loan account
     */
    @Transactional
    def save(Loan loanInstance) {
        if (loanInstance.loanApplication == null) {
            notFound()
            return
        }

        def test = LoanApplication.get(params.loanApplication).approvalStatus
        if (test.id != 6 && test.id != 9 && test.id != 10 && test.id != 11){
            statusPending()
            return
        }
        
        if(loanInstance.ageInArrears == null){
            loanInstance.ageInArrears = 0 
        }
        if(loanInstance.loanProvisionBsp == null){
           loanInstance.loanProvisionBsp = LoanProvisionBsp.get(1) 
        }
       
        if (loanInstance.grantedAmount < 0){ 
            flash.message = 'Loan Amount Cannot be negative !|error|alert'
            render(view: '/loan/create', model: [loanInstance:loanInstance])
            return
        }

        if (loanInstance.hasErrors()) {
            respond loanInstance.errors, view:'create'
            return
        }

        loanService.initializeLoan(loanInstance)
        def installmentAmount = params?.installmentAmount ? (params?.installmentAmount.replaceAll(",","")).toDouble() : null
        loanService.saveLoan(loanInstance, installmentAmount)

        def description = 'Loan Account ' + loanInstance.accountNo + ' was created by ' + UserMaster.get(session.user_id).username
        auditLogService.insert('090', 'LON00100', description, 'Loan', null, null, null, loanInstance.id)

        request.withFormat {
            form multipartForm {
                flash.message = message(code: 'default.created.message', args: [message(code: 'Loan.label', default: 'Loan'), loanInstance.id])
                redirect action:'show', id:loanInstance?.id 
            }
            '*' { respond loanInstance, [status: CREATED] }
        }
    }

    /**
     * Shows edit form for loan account
     */
    def edit(Loan loanInstance) {
        session["pageAction"]=""
        session["pageAction"]="edit"
        
        createSessionData(loanInstance)
        
        def module = getModule(request?.forwardURI)
        def title = getTitle(module)
        
        respond loanInstance, model:[module:module, title:title]
    }

    /**
     * Updates loan account
     */
    @Transactional
    def update(Loan loanInstance) {
        if (loanInstance == null) {
            notFound()
            return
        }

        if (loanInstance.hasErrors()) {
            def module = getModule(request?.forwardURI)
            respond loanInstance.errors, view:'edit', model:[module:module]
            return
        }

        createLoanHistoryEntry(loanInstance)
        createSessionData(loanInstance)
        
        loanService.commitLoanHistoryEntry(params?.activity)
        clearLoanData(loanInstance)
        
        def installmentAmount = params?.installmentAmount ? (params?.installmentAmount.replaceAll(",","")).toDouble() : null        
        loanService.saveLoan(loanInstance, installmentAmount)

        def description = 'Loan Account ' +  loanInstance.accountNo + ' was edited by ' + UserMaster.get(session.user_id).username
        auditLogService.insert('090', 'LON00500', description, 'Loan', null, null, null, loanInstance.id)

        request.withFormat {
            form multipartForm {
                flash.message = message(code: 'default.updated.message', args: [message(code: 'Loan.label', default: 'Loan'), loanInstance.id])
                redirect action:'show', id:loanInstance?.id 
            }
            '*'{ respond loanInstance, [status: OK] }
        }
    }

    /**
     * Deletes loan account
     */
    @Transactional
    def delete(Loan loanInstance) {
        if (loanInstance == null) {
            notFound()
            return
        }

        try {
            loanInstance.delete flush:true
            request.withFormat {
                form multipartForm {
                    flash.message = message(code: 'default.deleted.message', args: [message(code: 'Loan.label', default: 'Loan'), loanInstance.id])
                    redirect action:"index", method:"GET"
                }
                '*'{ render status: NO_CONTENT }
            }
        }
        catch (DataIntegrityViolationException e) {
            request.withFormat {
                form multipartForm {
                    flash.message = message(code: 'default.not.deleted.message', args: [message(code: 'Loan.label', default: 'Loan'), loanInstance.id])
                    redirect action:"show", id:loanInstance.id
                }
                '*'{ render status: NO_CONTENT }
            }
        }
    }

    // Helper methods
    private def getModule(String url) {
        if (url =~ /^.*\/loanAmendment.*$/) {
            return "loanAmendment"
        } else if (url =~ /^.*\/loanApproval.*$/) {
            return "loanApproval"  
        } else {
            return "loan"
        }
    }

    private def getTitle(String module) {
        if (module == "loan") {
            return "Loan Accounts"
        } else if (module == "loanAmendment") {
            return "Loan Account Amendment"
        } else if (module == "loanApproval") {
            return "Loan Account Approval"
        }
    }

    private def createSessionData(Loan loanInstance) {
        // Implementation for session data creation
    }

    private def createLoanHistoryEntry(Loan loanInstance) {
        // Implementation for loan history entry
    }

    private def clearLoanData(Loan loanInstance) {
        loanInstance.serviceCharges.clear()
        loanInstance.loanDeductions.clear()
        loanInstance.loanInstallments.clear()
        loanInstance.loanEIRSchedules.clear()
        loanInstance.sweepAccounts.clear()
    }

    protected def notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = message(code: 'default.not.found.message', args: [message(code: 'Loan.label', default: 'Loan'), params.id])
                redirect action: "index", method: "GET"
            }
            '*'{ render status: NOT_FOUND }
        }
    }

    protected def statusPending() {
        request.withFormat {
            form multipartForm {
                flash.message = 'Loan Application is not yet approved!|error|alert'
                redirect action: "index", method: "GET"
            }
            '*'{ render status: FORBIDDEN }
        }
    }
}
