package org.icbs.loans

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.loans.ScrRopa
import org.icbs.loans.ROPA
import org.icbs.loans.ScrDiscountSchedule
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanPerformanceId
import org.icbs.lov.LoanProvisionBsp
import org.icbs.lov.ConfigItemStatus
import org.icbs.gl.CfgAcctGlTemplate
import groovy.sql.Sql

/**
 * LoanClassificationController - <PERSON>les loan classification and provisioning
 * 
 * This controller manages loan classification operations including:
 * - Loan performance classification updates
 * - GL classification management
 * - SCR (Sales Contract Receivable) maintenance
 * - Loan provisioning operations
 * - Risk classification updates
 * - Discount schedule management
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class LoanClassificationController {
    
    // Service Dependencies
    def loanService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        updateLoanPerformaceNow: "POST",
        updateGLClassificationAjax: "POST",
        saveScrMaintenance: "POST",
        saveScrDiscountSchedule: "POST"
    ]

    /**
     * Show loan reclassification form
     */
    def loanReclassification() {
        println("boom pumaosk na dito")
        def loanInstance = Loan.get(params.id)
        if (loanInstance == null) {
            notFound()
            return
        }
        
        render(view:'classification/reclassification', model: [loanInstance:loanInstance])
    }

    /**
     * Update loan performance classification
     */
    @Transactional
    def updateLoanPerformaceNow() {
        println("params: "+params)
        def loanInstance = Loan.get(params.loanId.toInteger())
        def performanceId = LoanPerformanceId.get(params.performanceId.toInteger())
        def provisionBsp = LoanProvisionBsp.get(params.provisionBsp.toInteger())
        
        if (loanInstance == null) {
            notFound()
            return
        }

        createLoanHistoryEntry(loanInstance)
        createSessionData(loanInstance)

        // Update loan classification
        loanInstance.loanPerformanceId = performanceId
        loanInstance.loanProvisionBsp = provisionBsp
        loanInstance.ageInArrears = params.ageInArrears?.toInteger() ?: 0
        
        loanInstance.save(flush: true, failOnError: true)

        loanService.commitLoanHistoryEntry("Update Loan Performance Classification")
        clearLoanData(loanInstance)

        def description = loanInstance.accountNo + ' performance classification was updated by ' + UserMaster.get(session.user_id).username
        auditLogService.insert('090', 'LON01300', description, 'Loan', null, null, null, loanInstance.id)

        println("nag save na: " + loanInstance)
        redirect(action: "show", controller: "loanAccount", id: loanInstance.id)
    }

    /**
     * Show GL classification update form via AJAX
     */
    def showUpdateGLClassificationAjax() {
        def loanInstance = Loan.get(params.id)
        
        render(template:"gl/form", model:[loanInstance:loanInstance]) as JSON
        return
    }

    /**
     * Update GL classification via AJAX
     */
    @Transactional
    def updateGLClassificationAjax() {
        def loanInstance = Loan.get(params.id)

        createLoanHistoryEntry(loanInstance)
        createSessionData(loanInstance)

        loanService.commitLoanHistoryEntry("Update GL Classification")
        clearLoanData(loanInstance)

        loanService.updateGLClassification(loanInstance, CfgAcctGlTemplate.get(params.glLink.id), UserMaster.get(session.user_id))

        def description = loanInstance.accountNo + ' GL classification was updated by ' + UserMaster.get(session.user_id).username
        auditLogService.insert('090', 'LON01400', description, 'Loan', null, null, null, loanInstance.id)

        def message = "GL classification successfully updated"
        render(template:"gl/form", model:[loanInstance:loanInstance, message:message]) as JSON

        return
    }

    /**
     * Show SCR maintenance form
     */
    def loanScrMaintenance() {
        if(params.id){
            def loanInstance = Loan.get(params.id)
            if (loanInstance == null) {
                notFound()
                return
            }
            render(view:'scr/scrMaintenance', model: [loanInstance:loanInstance])
        } else {
            notFound()
        } 
    }

    /**
     * Save SCR maintenance
     */
    @Transactional
    def saveScrMaintenance() {
        println("churva")
        def remarksIddididid = params.remarks
        def scrIdId = Loan.get(params.scrId.toInteger())
        def rororororpapapa = ROPA.get(params.ropaididid.toInteger())
        println("ropaniya:"+rororororpapapa)

        def scrTransaction = new ScrRopa()
        scrTransaction.loan = scrIdId
        scrTransaction.ropa = rororororpapapa
        scrTransaction.remarks = remarksIddididid
        
        println("loan:" + scrTransaction.loan)
        println("ropa:" + scrTransaction.ropa)
        println("remarks:" + scrTransaction.remarks)
        
        scrTransaction.save(flush:true, failOnError:true)
        println("save:"+scrTransaction )

        def description = "SCR maintenance performed for loan " + scrIdId.accountNo
        auditLogService.insert('090', 'LON01500', description, 'Loan', null, null, null, scrIdId.id)
        
        redirect(action: "show", controller: "loanAccount", id: scrIdId.id)
    }

    /**
     * Create SCR discount schedule
     */
    def scrCreateDiscountSchedule() {
        println("=========== scrCreateDiscountSchedule =================")
        def loanInstance = Loan.get(params.id)
        if (loanInstance == null) {
            notFound()
            return
        }
        
        render(view:'scr/discountSchedule', model: [loanInstance:loanInstance])
    }

    /**
     * Save SCR discount schedule
     */
    @Transactional
    def saveScrDiscountSchedule() {
        println(" ============= saveScrDiscountSchedule =============")
        def loanInstance = Loan.get(params.loanId.toInteger())
        def branch = Branch.get(UserMaster.get(session.user_id).branchId)
        
        def scrDiscountSchedule = new ScrDiscountSchedule()
        scrDiscountSchedule.loan = loanInstance
        scrDiscountSchedule.scrdateCreated = new Date()
        scrDiscountSchedule.branch = branch
        scrDiscountSchedule.debitAmt = params.debitAmt?.toDouble() ?: 0
        scrDiscountSchedule.creditAmt = params.creditAmt?.toDouble() ?: 0
        scrDiscountSchedule.scheduleDate = params.scheduleDate ? Date.parse("MM/dd/yyyy", params.scheduleDate) : null
        scrDiscountSchedule.reference = params.reference
        scrDiscountSchedule.particulars = params.particulars
        scrDiscountSchedule.createdBy = UserMaster.get(session.user_id)
        scrDiscountSchedule.status = ConfigItemStatus.get(2) // Active
        
        scrDiscountSchedule.save(flush: true, failOnError: true)

        def description = "SCR discount schedule created for loan " + loanInstance.accountNo
        auditLogService.insert('090', 'LON01600', description, 'Loan', null, null, null, loanInstance.id)

        redirect(action: "show", controller: "loanAccount", id: loanInstance.id)
    }

    /**
     * Show discount schedule
     */
    def showDiscountSchedule() {
        println("============ showDiscountSchedule ===============")
        def loanInstance = Loan.get(params.id)
        if (loanInstance == null) {
            notFound()
            return
        }

        def sql = new Sql(dataSource)
        def discountSchedules = sql.rows("""
            SELECT * FROM scr_discount_schedule 
            WHERE loan_id = ? 
            ORDER BY schedule_date DESC
        """, [loanInstance.id])

        render(view: 'scr/scheduleList', model: [
            loanInstance: loanInstance,
            discountSchedules: discountSchedules
        ])
    }

    /**
     * Show loan provision details
     */
    def showProvisionDetails() {
        def loanInstance = Loan.get(params.id)
        if (loanInstance == null) {
            notFound()
            return
        }

        def sql = new Sql(dataSource)
        def provisionDetails = sql.rows("""
            SELECT * FROM loan_loss_provision_detail 
            WHERE loan_id = ? 
            ORDER BY date_created DESC
        """, [loanInstance.id])

        render(view: 'provision/details', model: [
            loanInstance: loanInstance,
            provisionDetails: provisionDetails
        ])
    }

    /**
     * Update loan provision
     */
    @Transactional
    def updateProvision() {
        def loanInstance = Loan.get(params.id)
        if (loanInstance == null) {
            notFound()
            return
        }

        def provisionRate = params.provisionRate?.toDouble() ?: 0
        def provisionAmount = params.provisionAmount?.toDouble() ?: 0

        createLoanHistoryEntry(loanInstance)
        createSessionData(loanInstance)

        loanService.commitLoanHistoryEntry("Update Loan Provision")
        clearLoanData(loanInstance)

        loanService.updateProvision(loanInstance, provisionRate, provisionAmount, UserMaster.get(session.user_id))

        def description = loanInstance.accountNo + ' provision was updated by ' + UserMaster.get(session.user_id).username
        auditLogService.insert('090', 'LON01700', description, 'Loan', null, null, null, loanInstance.id)

        request.withFormat {
            form multipartForm {
                flash.message = "Loan provision updated successfully"
                redirect action: 'showProvisionDetails', id: loanInstance.id
            }
            '*'{ render status: OK }
        }
    }

    // Helper methods
    private def createLoanHistoryEntry(Loan loanInstance) {
        // Implementation for loan history entry
    }

    private def createSessionData(Loan loanInstance) {
        // Implementation for session data creation
    }

    private def clearLoanData(Loan loanInstance) {
        loanInstance.serviceCharges.clear()
        loanInstance.loanDeductions.clear()
        loanInstance.loanInstallments.clear()
        loanInstance.loanEIRSchedules.clear()
        loanInstance.sweepAccounts.clear()
    }

    protected def notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = message(code: 'default.not.found.message', args: [message(code: 'Loan.label', default: 'Loan'), params.id])
                redirect action: "index", controller: "loanAccount", method: "GET"
            }
            '*'{ render status: NOT_FOUND }
        }
    }
}
