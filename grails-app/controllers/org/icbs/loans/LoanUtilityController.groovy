package org.icbs.loans

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.loans.LoanSweep
import org.icbs.loans.LoanRemark
import org.icbs.loans.LoanRelief
import org.icbs.deposit.Deposit
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.lov.SweepType
import org.icbs.lov.SweepRule
import org.icbs.lov.SweepStatus
import org.icbs.lov.ConfigItemStatus

/**
 * LoanUtilityController - Handles loan utility operations and helpers
 * 
 * This controller manages utility operations including:
 * - Loan sweep account management
 * - Loan remarks and notes management
 * - Loan relief operations
 * - Utility AJAX operations
 * - Helper functions and validations
 * - Session management utilities
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class LoanUtilityController {
    
    // Service Dependencies
    def loanService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        addLoanSweepAccountAjaxx: "POST",
        editLoanSweepAccountAjaxx: "POST",
        removeLoanSweepAccountAjax: "DELETE",
        saveNewRemarks: "POST",
        deleteRemarksAjax: "DELETE",
        applyRelief: "POST",
        removeRelief: "POST"
    ]

    /**
     * Show sweep accounts via AJAX
     */
    def showSweepAccountsAjax() {
        render(template:"sweep/list") as JSON
        return
    }

    /**
     * Show add sweep account form via AJAX
     */
    def showAddSweepAccountAjax() {
        render(template:"sweep/form") as JSON
        return
    }

    /**
     * Add loan sweep account via AJAX
     */
    @Transactional
    def addLoanSweepAccountAjaxx() {
        def json = request.JSON
        println("JSON received: " + json)
        
        def depositAccount = Deposit.get(json.depositAcctId)
        if (!depositAccount) {
            render([success: false, message: "Invalid deposit account"] as JSON)
            return
        }

        if (!session["sweepAccounts"]) {
            session["sweepAccounts"] = []
        }

        def sweepAccount = new LoanSweep()
        sweepAccount.depositAccount = depositAccount
        sweepAccount.sweepType = SweepType.get(json.sweepTypeId)
        sweepAccount.sweepRule = SweepRule.get(json.sweepRuleId)
        sweepAccount.sweepStatus = SweepStatus.get(1) // Active
        sweepAccount.minimumAmount = json.minimumAmount?.toDouble() ?: 0
        sweepAccount.maximumAmount = json.maximumAmount?.toDouble() ?: 0
        sweepAccount.priority = json.priority?.toInteger() ?: 1
        sweepAccount.isActive = true

        session["sweepAccounts"].add(sweepAccount)

        render([success: true, message: "Sweep account added successfully"] as JSON)
        return
    }

    /**
     * Edit loan sweep account via AJAX
     */
    @Transactional
    def editLoanSweepAccountAjaxx() {
        def json = request.JSON
        def sweepPosition = json.sweepPosition?.toInteger()
        
        if (sweepPosition != null && session["sweepAccounts"] && sweepPosition < session["sweepAccounts"].size()) {
            def sweepAccount = session["sweepAccounts"].get(sweepPosition)
            
            sweepAccount.sweepType = SweepType.get(json.sweepTypeId)
            sweepAccount.sweepRule = SweepRule.get(json.sweepRuleId)
            sweepAccount.minimumAmount = json.minimumAmount?.toDouble() ?: 0
            sweepAccount.maximumAmount = json.maximumAmount?.toDouble() ?: 0
            sweepAccount.priority = json.priority?.toInteger() ?: 1
            
            render([success: true, message: "Sweep account updated successfully"] as JSON)
        } else {
            render([success: false, message: "Invalid sweep account position"] as JSON)
        }
        return
    }

    /**
     * Remove loan sweep account via AJAX
     */
    @Transactional
    def removeLoanSweepAccountAjax() {
        def json = request.JSON
        def sweepPosition = json.sweepPosition?.toInteger()
        
        if (sweepPosition != null && session["sweepAccounts"] && sweepPosition < session["sweepAccounts"].size()) {
            session["sweepAccounts"].remove(sweepPosition)
            render([success: true, message: "Sweep account removed successfully"] as JSON)
        } else {
            render([success: false, message: "Invalid sweep account position"] as JSON)
        }
        return
    }

    /**
     * Validate duplicate sweep deposit account via AJAX
     */
    def validateDuplicateSweepDepositAcctNoAjax() {
        def json = request.JSON
        def depositAcctNo = json.depositAcctNo
        
        def isDuplicate = false
        if (session["sweepAccounts"]) {
            isDuplicate = session["sweepAccounts"].any { sweep ->
                sweep.depositAccount?.accountNo == depositAcctNo
            }
        }
        
        render([isDuplicate: isDuplicate] as JSON)
        return
    }

    /**
     * Show deposit account information
     */
    def showDepositAccountInfo() {
        def depositAccount = null
        if (params?.accountNo) {
            depositAccount = Deposit.findByAccountNo(params.accountNo)
        }
        
        render(template:"deposit/accountInfo", model:[depositAccount: depositAccount]) as JSON
        return
    }

    /**
     * Edit sweep account
     */
    def editSweepAccount(Loan loanInstance) {
        println "id " + loanInstance.id
        def a = loanInstance.id.toInteger()
        session["jrxmlTcId"] = a
        
        createSessionData(loanInstance)
        
        respond loanInstance
    }

    /**
     * Show loan remarks index
     */
    def loanRemarkIndex(Loan loanInstance) {
       respond loanInstance    
    }

    /**
     * Show loan remarks
     */
    def loanRemarksShow(LoanRemark loanRemarkInstance) {
       respond loanRemarkInstance
    }

    /**
     * Show remarks index
     */
    def remarksIndex(Loan loanInstance) {
       respond loanInstance
    }

    /**
     * Create new remarks form
     */
    def createNewRemarks(Loan loanInstance) {
       respond loanInstance
    }

    /**
     * Save new remarks
     */
    @Transactional
    def saveNewRemarks(Loan loanInstance) {
        def remarks = params.remarks
        def isImportant = params.isImportant?.toBoolean() ?: false
        
        def loanRemark = new LoanRemark()
        loanRemark.loan = loanInstance
        loanRemark.remarks = remarks
        loanRemark.isImportant = isImportant
        loanRemark.createdBy = UserMaster.get(session.user_id)
        loanRemark.dateCreated = new Date()
        loanRemark.status = ConfigItemStatus.get(2) // Active
        
        loanRemark.save(flush: true, failOnError: true)

        def description = "Remarks added to loan " + loanInstance.accountNo
        auditLogService.insert('090', 'LON03100', description, 'Loan', null, null, null, loanInstance.id)

        redirect(action: "remarksIndex", id: loanInstance.id)
    }

    /**
     * Show remarks details
     */
    def showRemarksDetails(LoanRemark loanRemarkInstance) {
        respond loanRemarkInstance      
    }

    /**
     * Delete remarks via AJAX
     */
    @Transactional
    def deleteRemarksAjax() {
        def json = request.JSON
        def remarkId = json.remarkId?.toInteger()
        
        def loanRemark = LoanRemark.get(remarkId)
        if (loanRemark) {
            loanRemark.delete(flush: true)
            render([success: true, message: "Remark deleted successfully"] as JSON)
        } else {
            render([success: false, message: "Remark not found"] as JSON)
        }
        return
    }

    /**
     * Show loan relief form
     */
    def loanRelief() {
        if(params.id){
            def loanInstance = Loan.get(params.id)
            if (loanInstance == null) {
                notFound()
                return
            }
            def reliefInstance = LoanRelief.findByLoan(loanInstance)
            render(view:'relief/form', model: [loanInstance:loanInstance, reliefInstance:reliefInstance])
        } else {
            notFound()
        } 
    }

    /**
     * Apply loan relief
     */
    @Transactional
    def applyRelief() {
        if(params.id){
            def loanInstance = Loan.get(params.id)
            if (loanInstance == null) {
                notFound()
                return
            }
            
            def reliefInstance = LoanRelief.findByLoan(loanInstance)
            if (!reliefInstance) {
                reliefInstance = new LoanRelief()
                reliefInstance.loan = loanInstance
            }
            
            reliefInstance.reliefType = params.reliefType
            reliefInstance.reliefAmount = params.reliefAmount?.toDouble() ?: 0
            reliefInstance.reliefRate = params.reliefRate?.toDouble() ?: 0
            reliefInstance.reliefPeriod = params.reliefPeriod?.toInteger() ?: 0
            reliefInstance.startDate = params.startDate ? Date.parse("MM/dd/yyyy", params.startDate) : null
            reliefInstance.endDate = params.endDate ? Date.parse("MM/dd/yyyy", params.endDate) : null
            reliefInstance.remarks = params.remarks
            reliefInstance.appliedBy = UserMaster.get(session.user_id)
            reliefInstance.dateApplied = new Date()
            reliefInstance.status = ConfigItemStatus.get(2) // Active
            
            reliefInstance.save(flush: true, failOnError: true)

            def description = "Relief applied to loan " + loanInstance.accountNo
            auditLogService.insert('090', 'LON03200', description, 'Loan', null, null, null, loanInstance.id)

            redirect(action: "show", controller: "loanAccount", id: loanInstance.id)
        } else {
            notFound()
        }
    }

    /**
     * Remove loan relief
     */
    @Transactional
    def removeRelief() {
        if(params.id){
            def loanInstance = Loan.get(params.id)
            if (loanInstance == null) {
                notFound()
                return
            }
            
            def reliefInstance = LoanRelief.findByLoan(loanInstance)
            if (reliefInstance) {
                reliefInstance.status = ConfigItemStatus.get(3) // Inactive
                reliefInstance.removedBy = UserMaster.get(session.user_id)
                reliefInstance.dateRemoved = new Date()
                reliefInstance.save(flush: true)

                def description = "Relief removed from loan " + loanInstance.accountNo
                auditLogService.insert('090', 'LON03300', description, 'Loan', null, null, null, loanInstance.id)
            }
            
            redirect(action: "show", controller: "loanAccount", id: loanInstance.id)
        } else {
            notFound()
        }
    }

    /**
     * Account type validator
     */
    def accountTypeValidator() {
        // Validation logic for account types
        render([valid: true] as JSON)
    }

    /**
     * Application collect information
     */
    def applicationCollectInformation() {
        def json = request.JSON
        // Process application information collection
        render([success: true, data: json] as JSON)
    }

    // Helper methods
    private def createSessionData(Loan loanInstance) {
        // Duplicate service charges
        session["serviceCharges"] = []       
        for(serviceCharge in loanInstance?.serviceCharges) {
            def newServiceCharge = new LoanServiceCharge()
            LoanServiceCharge.constraints.each() { key, value ->            
                newServiceCharge."${key}" = serviceCharge."${key}"   
            }
            session["serviceCharges"].add(newServiceCharge)
        }

        // Duplicate deductions
        session["deductions"] = []       
        for(deduction in loanInstance?.loanDeductions) {
            def newDeduction = new LoanDeduction()
            LoanDeduction.constraints.each() { key, value ->            
                newDeduction."${key}" = deduction."${key}"   
            }
            session["deductions"].add(newDeduction)
        }

        // Duplicate installments
        session["installments"] = []       
        for(installment in loanInstance?.loanInstallments) {
            def newInstallment = new LoanInstallment()
            LoanInstallment.constraints.each() { key, value ->            
                newInstallment."${key}" = installment."${key}"   
            }
            session["installments"].add(newInstallment)
        }

        // Duplicate EIR schedules
        session["eirSchedules"] = []       
        for(eirSchedule in loanInstance?.loanEIRSchedules) {
            def newEIRSchedule = new LoanEIRSchedule()
            LoanEIRSchedule.constraints.each() { key, value ->            
                newEIRSchedule."${key}" = eirSchedule."${key}"   
            }
            session["eirSchedules"].add(newEIRSchedule)
        }

        // Duplicate sweep accounts
        session["sweepAccounts"] = []       
        for(sweepAccount in loanInstance?.sweepAccounts) {
            def newSweepAccount = new LoanSweep()
            LoanSweep.constraints.each() { key, value ->            
                newSweepAccount."${key}" = sweepAccount."${key}"   
            }
            session["sweepAccounts"].add(newSweepAccount)
        }       
    }

    protected def notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = message(code: 'default.not.found.message', args: [message(code: 'Loan.label', default: 'Loan'), params.id])
                redirect action: "index", controller: "loanAccount", method: "GET"
            }
            '*'{ render status: NOT_FOUND }
        }
    }
}
