package org.icbs.loans

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.loans.LoanInstallment
import org.icbs.loans.LoanEIRSchedule
import org.icbs.loans.InterestIncomeScheme
import org.springframework.web.multipart.MultipartFile
import java.text.SimpleDateFormat

/**
 * LoanScheduleController - Handles loan schedule and installment operations
 * 
 * This controller manages loan schedule operations including:
 * - Manual loan installment management
 * - EIR schedule operations
 * - Installment import from Excel/CSV
 * - Schedule calculations and updates
 * - Term calculations and adjustments
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class LoanScheduleController {
    
    // Service Dependencies
    def loanService
    def auditLogService
    
    static allowedMethods = [
        addInstallmentAjax: "POST",
        updateInstallmentAjax: "POST",
        deleteInstallmentAjax: "DELETE",
        importInstallmentss: "POST"
    ]

    /**
     * Show installments via AJAX
     */
    def showInstallmentsAjax() {
        def thepassedvalue = params?.onffvalue
        println("the parameter passed onoffvalue: "+thepassedvalue)
      
        render(template:"installments/list", model: [onoffplugvalue: thepassedvalue]) as JSON
        return
    }

    /**
     * Show add installment form via AJAX
     */
    def showAddInstallmentAjax() {    
        render(template:"installments/form") as JSON
        return
    }

    /**
     * Add installment via AJAX
     */
    @Transactional
    def addInstallmentAjax() {    
        def date = params?.date
        def principal = params?.principal?.toDouble() ?: 0
        def interest = params?.interest?.toDouble() ?: 0
        def serviceCharge = params?.serviceCharge?.toDouble() ?: 0

        if (!session["installments"]) {
            session["installments"] = []
        }

        def installment = new LoanInstallment()
        installment.installmentDate = date ? Date.parse("MM/dd/yyyy", date) : null
        installment.principalAmount = principal
        installment.interestAmount = interest
        installment.serviceChargeAmount = serviceCharge
        installment.totalAmount = principal + interest + serviceCharge

        session["installments"].add(installment)

        render(template:"installments/list") as JSON
        return
    }

    /**
     * Show update installment form via AJAX
     */
    def showUpdateInstallmentAjax() {   
        def id = params?.id?.toInteger()
        def installment = null
        
        if (id != null && session["installments"] && id < session["installments"].size()) {
            installment = session["installments"].get(id)
        }

        render(template:"installments/updateForm", model:[installment: installment, id: id]) as JSON
        return
    }

    /**
     * Update installment via AJAX
     */
    @Transactional
    def updateInstallmentAjax() {    
        def id = params?.id?.toInteger()
        def date = params?.date
        def principal = params?.principal?.toDouble() ?: 0
        def interest = params?.interest?.toDouble() ?: 0
        def serviceCharge = params?.serviceCharge?.toDouble() ?: 0

        if (id != null && session["installments"] && id < session["installments"].size()) {
            def installment = session["installments"].get(id)
            installment.installmentDate = date ? Date.parse("MM/dd/yyyy", date) : null
            installment.principalAmount = principal
            installment.interestAmount = interest
            installment.serviceChargeAmount = serviceCharge
            installment.totalAmount = principal + interest + serviceCharge
        }

        render(template:"installments/list") as JSON
        return
    }

    /**
     * Delete installment via AJAX
     */
    @Transactional
    def deleteInstallmentAjax() {
        def id = params?.id?.toInteger()
        
        if (id != null && session["installments"] && id < session["installments"].size()) {
            session["installments"].remove(id)
        }

        render "success"
        return
    }

    /**
     * Show import installment form via AJAX
     */
    def showImportInstallmentAjax() {    
        render(template:"installments/formupload") as JSON
        return
    }

    /**
     * Import installments from Excel/CSV file
     */
    @Transactional
    def importInstallmentss() {
        def installment_date
        def principal_amt
        def interest_value
        def service_charge
        int counter = 0
        int validateTitleExcelTitleHeader = 0
        
        println("from ajax value: " + params)
        session["installments"] = []
        session["onffvalue"] = ""
        
        MultipartFile file = request.getFile('file')
        if (!file || file.empty) {
            render([success: false, message: "No file uploaded"] as JSON)
            return
        }

        try {
            file.inputStream.eachCsvLine { row ->
                installment_date = row[0] ?: "NA"
                principal_amt = row[1] ?: "0.00"
                interest_value = row[2] ?: "0.00"
                service_charge = row[3] ?: "0.00"
                
                counter++
                
                if (counter == 1) {
                    // Skip header row
                    if (installment_date.toLowerCase().contains("date") || 
                        installment_date.toLowerCase().contains("installment")) {
                        validateTitleExcelTitleHeader = 1
                        return // Continue to next row
                    }
                }
                
                if (validateTitleExcelTitleHeader == 1 && counter == 2) {
                    return // Skip second row if header was found
                }
                
                if (installment_date != "NA" && installment_date.trim() != "") {
                    def installment = new LoanInstallment()
                    
                    try {
                        installment.installmentDate = Date.parse("MM/dd/yyyy", installment_date)
                        installment.principalAmount = Double.parseDouble(principal_amt.toString().replace(",", ""))
                        installment.interestAmount = Double.parseDouble(interest_value.toString().replace(",", ""))
                        installment.serviceChargeAmount = Double.parseDouble(service_charge.toString().replace(",", ""))
                        installment.totalAmount = installment.principalAmount + installment.interestAmount + installment.serviceChargeAmount
                        
                        session["installments"].add(installment)
                    } catch (Exception e) {
                        println("Error parsing row ${counter}: ${e.message}")
                        // Continue processing other rows
                    }
                }
            }
            
            session["onffvalue"] = "1" // Flag to indicate imported data
            
            render([
                success: true, 
                message: "Successfully imported ${session['installments'].size()} installments",
                count: session["installments"].size()
            ] as JSON)
            
        } catch (Exception e) {
            println("Error importing file: ${e.message}")
            render([success: false, message: "Error importing file: ${e.message}"] as JSON)
        }
    }

    /**
     * Get term calculation via AJAX
     */
    def getTermAjax() {
        def term = params?.term ? params.term.toInteger() : 0
        def numInstallments = params?.numInstallments ? params.numInstallments.toInteger() : 0
        def frequency = params?.frequency ? params.frequency.toInteger() : 0
        def openingDate = params?.openingDate ? new Date().parse("MM/dd/yyyy", params?.openingDate) : null
        def firstInstallmentDate = params?.firstInstallmentDate ? new Date().parse("MM/dd/yyyy", params?.firstInstallmentDate) : null

        def interestIncomeScheme = null    
        if (params?.interestIncomeScheme) {
            interestIncomeScheme = InterestIncomeScheme.get(params.interestIncomeScheme)
        }

        def gracePeriod = interestIncomeScheme?.gracePeriod ?: 0
        def installmentType = interestIncomeScheme?.installmentType?.id
        def installmentCalculation = interestIncomeScheme?.installmentCalcType?.id

        def dueDate
        def prevDueDate = firstInstallmentDate  
        def baseDate = firstInstallmentDate   

        if (installmentType != 5 && installmentCalculation != 1 && installmentCalculation != 6) {
            for(int i = 1; i <= gracePeriod; i++) {                      
                dueDate = loanService.getNextDueDate(prevDueDate, baseDate, frequency)
                prevDueDate = dueDate
                baseDate = baseDate
            }
        }

        if (installmentCalculation == 1) {  // single payment   
            term = term + gracePeriod            
            render(template:"search/term", model:[term: term]) as JSON
            return
        } else if (installmentType == 5 || installmentCalculation == 6) {  // manual
            def lastInstallment = session["installments"]?.get(session["installments"].size() - 1)
            if (lastInstallment) {
                dueDate = lastInstallment.installmentDate
            }
        } else {  // others
            for(int i = 1 + gracePeriod; i <= numInstallments + gracePeriod; i++) {
                if (i == 1 && firstInstallmentDate) {
                    dueDate = firstInstallmentDate
                } else {
                    dueDate = loanService.getNextDueDate(prevDueDate, baseDate, frequency)
                }
                prevDueDate = dueDate
                baseDate = baseDate
            }
        }

        if (openingDate && dueDate) {
            term = dueDate - openingDate
        }

        render(template:"search/term", model:[term: term]) as JSON
        return
    }

    /**
     * Show EIR schedules via AJAX
     */
    def showEIRSchedulesAjax() {
        render(template:"eir/list") as JSON
        return
    }

    /**
     * Calculate EIR schedule
     */
    def calculateEIRSchedule() {
        def loanAmount = params?.loanAmount?.toDouble() ?: 0
        def interestRate = params?.interestRate?.toDouble() ?: 0
        def term = params?.term?.toInteger() ?: 0
        def frequency = params?.frequency?.toInteger() ?: 0

        if (loanAmount <= 0 || interestRate <= 0 || term <= 0) {
            render([success: false, message: "Invalid parameters for EIR calculation"] as JSON)
            return
        }

        try {
            def eirSchedules = loanService.calculateEIRSchedule(loanAmount, interestRate, term, frequency)
            session["eirSchedules"] = eirSchedules
            
            render([
                success: true,
                schedules: eirSchedules,
                count: eirSchedules.size()
            ] as JSON)
            
        } catch (Exception e) {
            println("Error calculating EIR schedule: ${e.message}")
            render([success: false, message: "Error calculating EIR schedule: ${e.message}"] as JSON)
        }
    }

    /**
     * Validate installment schedule
     */
    def validateSchedule() {
        def installments = session["installments"]
        def errors = []
        
        if (!installments || installments.size() == 0) {
            errors.add("No installments defined")
        } else {
            // Validate installment dates are in sequence
            def previousDate = null
            installments.eachWithIndex { installment, index ->
                if (previousDate && installment.installmentDate <= previousDate) {
                    errors.add("Installment ${index + 1}: Date must be after previous installment")
                }
                previousDate = installment.installmentDate
                
                // Validate amounts
                if (installment.principalAmount < 0) {
                    errors.add("Installment ${index + 1}: Principal amount cannot be negative")
                }
                if (installment.interestAmount < 0) {
                    errors.add("Installment ${index + 1}: Interest amount cannot be negative")
                }
            }
        }
        
        render([
            valid: errors.size() == 0,
            errors: errors
        ] as JSON)
    }
}
