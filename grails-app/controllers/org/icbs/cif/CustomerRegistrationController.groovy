package org.icbs.cif

import org.icbs.lov.*
import org.icbs.admin.UserMaster
import org.icbs.admin.CustomerGroup
import static org.springframework.http.HttpStatus.*
import grails.converters.JSON
import org.grails.web.json.JSONObject
import java.text.DateFormat
import java.text.SimpleDateFormat
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import groovy.util.logging.Slf4j

/**
 * REFACTORED: Customer Registration Controller
 * Extracted from CustomerController.groovy (1,058+ lines)
 * Handles all customer registration and creation operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Slf4j
class CustomerRegistrationController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def customerService
    def policyService
    def auditLogService
    
    static allowedMethods = [
        create: "GET",
        save: "POST",
        customerVerificationAjax: "POST",
        customerOtherDetailsValidationAjax: "POST",
        customerContactInformationValidationAjax: "POST"
    ]
    
    // =====================================================
    // CUSTOMER REGISTRATION OPERATIONS
    // =====================================================
    
    /**
     * Create new customer form
     */
    def create() {
        log.info("Creating new customer form")
        
        try {
            session["customerpagevalidator"] = "create"
            
            if (!params.type?.id) {
                render(view: 'create', model: [
                    'customerInstance': new Customer(params),
                    'firstCreate': true
                ])
            } else {
                respond new Customer(params)
            }
            
            // Audit logging
            auditLogService.insert('120', 'CRC01100', 
                "Customer registration form accessed", 
                'CustomerRegistrationController', null, null, 'cif/create', session.user_id)
            
        } catch (Exception e) {
            log.error("Error creating customer form", e)
            flash.message = 'Error accessing customer registration form|error|alert'
            redirect(controller: 'customer', action: 'index')
        }
    }
    
    /**
     * Save new customer
     */
    def save(Customer customerInstance) {
        log.info("Saving new customer: ${customerInstance?.name1}")
        
        try {
            // 1. Process file attachments
            Map attachmentResult = processFileAttachments(request, params)
            
            // 2. Set system fields
            setSystemFields(params)
            
            // 3. Set default values
            setDefaultValues(params)
            
            // 4. Validate customer data
            Map validationResult = unifiedValidationService.validateCustomerRegistration(params)
            if (!validationResult.isValid) {
                customerInstance.errors = validationResult.errors
                respond customerInstance.errors, view: 'create'
                return
            }
            
            // 5. Save customer
            Map saveResult = customerService.saveCustomer(customerInstance, params)
            
            if (saveResult.success) {
                // 6. Process additional data
                processAdditionalCustomerData(saveResult.customer, params)
                
                // 7. Audit logging
                auditCustomerRegistration(saveResult.customer, 'SUCCESS')
                
                request.withFormat {
                    form multipartForm {
                        flash.message = 'Customer registered successfully|success|alert'
                        redirect(action: "show", id: saveResult.customer.id)
                    }
                    '*' { respond saveResult.customer, [status: CREATED] }
                }
            } else {
                customerInstance.errors = saveResult.errors
                respond customerInstance.errors, view: 'create'
            }
            
        } catch (Exception e) {
            log.error("Error saving customer", e)
            flash.message = 'Error saving customer|error|alert'
            respond customerInstance.errors, view: 'create'
        }
    }
    
    /**
     * Customer verification AJAX
     */
    def customerVerificationAjax(customerVerificationCommand cmd) {
        log.debug("Processing customer verification AJAX")
        
        try {
            JSONObject jsonObject = new JSONObject()
            
            if (cmd.hasErrors()) {
                jsonObject = jsonObject.put("html", 
                    g.render(template: 'form/customer/customerverification/private', 
                    model: [customerInstance: cmd, onsubmit: "true", isVerified: "false"]))
            } else {
                // Check for duplicates
                List duplicateList = checkForDuplicateCustomers(cmd)
                
                if (duplicateList) {
                    jsonObject = jsonObject.put("html", 
                        g.render(template: 'form/customer/customerverification/private', 
                        model: [customerInstance: cmd, duplicateList: duplicateList, 
                               onsubmit: "true", isVerified: "false"]))
                } else {
                    jsonObject = jsonObject.put("html", 
                        g.render(template: 'form/customer/customerverification/private', 
                        model: [customerInstance: cmd, duplicateList: duplicateList, 
                               onsubmit: "true", isVerified: "true"]))
                }
            }
            
            render jsonObject
            
        } catch (Exception e) {
            log.error("Error in customer verification AJAX", e)
            render([error: 'Error processing verification'] as JSON)
        }
    }
    
    /**
     * Customer other details validation AJAX
     */
    def customerOtherDetailsValidationAjax(customerOtherDetailsCommand cmd) {
        log.debug("Processing customer other details validation AJAX")
        
        try {
            render(template: 'form/customer/othercustomerinfo/otherCustomerInfo', 
                  model: ['customerInstance': cmd]) as JSON
                  
        } catch (Exception e) {
            log.error("Error in customer other details validation AJAX", e)
            render([error: 'Error processing other details validation'] as JSON)
        }
    }
    
    /**
     * Customer contact information validation AJAX
     */
    def customerContactInformationValidationAjax(customerContactsCommand cmd) {
        log.debug("Processing customer contact information validation AJAX")
        
        try {
            render(template: 'form/contact/contactInfo', 
                  model: ['customerInstance': cmd]) as JSON
                  
        } catch (Exception e) {
            log.error("Error in customer contact information validation AJAX", e)
            render([error: 'Error processing contact information validation'] as JSON)
        }
    }
    
    /**
     * Validate customer registration data
     */
    def validateRegistrationData() {
        log.debug("Validating customer registration data")
        
        try {
            Map validationResult = unifiedValidationService.validateCustomerRegistration(params)
            
            render([
                isValid: validationResult.isValid,
                errors: validationResult.errors,
                warnings: validationResult.warnings
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error validating registration data", e)
            render([isValid: false, errors: ['Error validating registration data']] as JSON)
        }
    }
    
    /**
     * Check customer eligibility
     */
    def checkCustomerEligibility() {
        log.debug("Checking customer eligibility")
        
        try {
            Map eligibilityResult = customerService.checkCustomerEligibility(params)
            
            render([
                isEligible: eligibilityResult.isEligible,
                reasons: eligibilityResult.reasons,
                requirements: eligibilityResult.requirements
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error checking customer eligibility", e)
            render([isEligible: false, reasons: ['Error checking eligibility']] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Process file attachments
     */
    private Map processFileAttachments(def request, Map params) {
        Map result = [success: true, errors: []]
        
        try {
            if (request instanceof MultipartHttpServletRequest) {
                request.fileNames.each { fileName ->
                    def uploadedFile = request.getFile(fileName)
                    String subscript = (fileName.split("\\.")[0])
                    
                    if (uploadedFile.getSize() < 1) {
                        if (!params[subscript + ".id"]) {
                            params.remove(subscript + '.fileName')
                        }
                    } else {
                        params[subscript + ".fileName"] = uploadedFile.getOriginalFilename()
                        params[subscript + ".fileType"] = uploadedFile.getContentType()
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("Error processing file attachments", e)
            result.success = false
            result.errors << "Error processing file attachments"
        }
        
        return result
    }
    
    /**
     * Set system fields
     */
    private void setSystemFields(Map params) {
        try {
            UserMaster currentUser = UserMaster.get(session.user_id)
            
            params.branch = currentUser?.branch?.id
            params.createdBy = currentUser
            params.lastUpdatedBy = currentUser
            
        } catch (Exception e) {
            log.error("Error setting system fields", e)
        }
    }
    
    /**
     * Set default values
     */
    private void setDefaultValues(Map params) {
        try {
            if (!params.dosriCode) {
                params.dosriCode = CustomerDosriCode.get(1)
            }
            
            // Set other default values as needed
            
        } catch (Exception e) {
            log.error("Error setting default values", e)
        }
    }
    
    /**
     * Check for duplicate customers
     */
    private List checkForDuplicateCustomers(customerVerificationCommand cmd) {
        try {
            return Customer.createCriteria().list {
                and {
                    eq("name1", cmd.name1)
                    eq("name2", cmd.name2)
                    eq("birthDate", cmd.birthDate)
                }
                maxResults(10)
            }
            
        } catch (Exception e) {
            log.error("Error checking for duplicate customers", e)
            return []
        }
    }
    
    /**
     * Process additional customer data
     */
    private void processAdditionalCustomerData(Customer customer, Map params) {
        try {
            // Process contacts, addresses, beneficiaries, etc.
            // This would integrate with other services
            
        } catch (Exception e) {
            log.error("Error processing additional customer data", e)
        }
    }
    
    /**
     * Audit customer registration
     */
    private void auditCustomerRegistration(Customer customer, String result) {
        try {
            auditLogService.insert('120', 'CRC01000', 
                "Customer registered - Name: ${customer.name1} ${customer.name2}, Result: ${result}", 
                'CustomerRegistrationController', null, null, null, customer.id)
        } catch (Exception e) {
            log.error("Error auditing customer registration", e)
        }
    }
}

// =====================================================
// COMMAND CLASSES (Moved from original controller)
// =====================================================

@grails.validation.Validateable
class customerVerificationCommand {
    CustomerType type
    int id
    CustomerGroup group
    String name1
    String name2
    String name3
    String name4
    Lov name5
    Lov title
    Gender gender
    Lov civilStatus
    Date birthDate
    String birthPlace
    Town custBirthPlace
    
    static constraints = {
        group nullable: false
        importFrom Customer
    }
}

@grails.validation.Validateable
class customerOtherDetailsCommand {
    CustomerType type
    String displayName
    ResidentType customerCode1
    RiskType customerCode2
    FirmSize customerCode3
    String sourceOfIncome
    Lov nationality
    CustomerDosriCode dosriCode
    String sssNo
    String gisNo
    String tinNo
    String passportNo
    String pepDescription
    String amla
    Integer noOfDependent
    String motherMaidenName
    String fatherName
    String spouseLastName
    String spouseFirstName
    String spouseMiddleName
    Date spouseBirthDate
    String spouseContactNo
    Religion religion
    
    static constraints = {
        importFrom Customer
    }
}

@grails.validation.Validateable
class customerContactsCommand {
    List<Contact> contacts = [].withLazyDefault { new Contact() }
    
    static constraints = {
        contacts validator: { val, obj, errors ->
            if (val) {
                for (int i = 0; i < val.size(); i++) {
                    if (!val[i]) { continue }
                    if (!val[i].validate()) {
                        val[i].errors.allErrors.each { error ->
                            errors.rejectValue(
                                "contacts[${i}]." + error.field,
                                error.getCode(),
                                error.getArguments(),
                                error.getDefaultMessage()
                            )
                        }
                    }
                }
            }
        }
    }
}
