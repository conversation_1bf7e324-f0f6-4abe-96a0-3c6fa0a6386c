package org.icbs.cif

import org.icbs.lov.*
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import static org.springframework.http.HttpStatus.*
import grails.converters.JSON
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import groovy.util.logging.Slf4j

/**
 * REFACTORED: Customer Report Controller
 * Extracted from CustomerController.groovy (1,058+ lines)
 * Handles all customer reporting operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Slf4j
class CustomerReportController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def customerService
    def jasperService
    def auditLogService
    
    static allowedMethods = [
        viewCustomerReports: "GET",
        createReport: "GET",
        generateCustomerList: "GET",
        generateCustomerProfile: "GET",
        generateCustomerSummary: "GET"
    ]
    
    // =====================================================
    // CUSTOMER REPORTING OPERATIONS
    // =====================================================
    
    /**
     * View customer reports page
     */
    def viewCustomerReports() {
        log.info("Accessing customer reports page")
        
        try {
            // Audit logging
            auditLogService.insert('120', 'CRC04100', 
                "Customer reports page accessed", 
                'CustomerReportController', null, null, 'cif/reports/view', session.user_id)
            
            render(view: 'reports/view')
            
        } catch (Exception e) {
            log.error("Error accessing customer reports page", e)
            flash.message = 'Error accessing customer reports|error|alert'
            redirect(controller: 'customer', action: 'index')
        }
    }
    
    /**
     * Create customer report
     */
    def createReport() {
        log.info("Creating customer report with params: ${params}")
        
        try {
            def list
            
            if (params.type == "individual") {
                list = Customer.get(params.customer.id)
            } else {
                list = Customer.list(fetch: [branch: "eager"])
            }
            
            // Audit logging
            auditLogService.insert('120', 'CRC04200', 
                "Customer report created - Type: ${params.type}", 
                'CustomerReportController', null, null, 'cif/createReport', session.user_id)
            
            chain(controller: 'jasper', action: 'index', model: [data: list], params: params)
            
        } catch (Exception e) {
            log.error("Error creating customer report", e)
            flash.message = 'Error creating customer report|error|alert'
            redirect(action: 'viewCustomerReports')
        }
    }
    
    /**
     * Generate customer list report
     */
    def generateCustomerList() {
        log.info("Generating customer list report")
        
        try {
            Date fromDate = params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 30
            Date toDate = params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date()
            
            Map reportData = generateCustomerListData(fromDate, toDate, params)
            
            Map reportParams = [:]
            reportParams._name = "Customer List Report"
            reportParams._format = params.format ?: "PDF"
            reportParams._file = "CustomerListReport.jasper"
            reportParams.fromDate = fromDate
            reportParams.toDate = toDate
            reportParams.branchId = params.branchId
            reportParams.customerType = params.customerType
            
            // Generate report
            def reportDef = jasperService.buildReportDefinition(reportParams, request.getLocale(), reportData.customers)
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            // Set response headers
            String contentType = params.format == 'EXCEL' ? 'application/vnd.ms-excel' : 'application/pdf'
            String fileName = "customer_list_report.${params.format?.toLowerCase() ?: 'pdf'}"
            
            response.setContentType(contentType)
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', "attachment; filename=${fileName}")
            
            response.outputStream << bytes
            response.outputStream.flush()
            
            // Audit logging
            auditLogService.insert('120', 'CRC04300', 
                "Customer list report generated - Count: ${reportData.totalCount}", 
                'CustomerReportController', null, null, 'cif/generateCustomerList', session.user_id)
            
        } catch (Exception e) {
            log.error("Error generating customer list report", e)
            response.sendError(500, "Error generating customer list report")
        }
    }
    
    /**
     * Generate customer profile report
     */
    def generateCustomerProfile() {
        log.info("Generating customer profile report for customer: ${params.customerId}")
        
        try {
            if (!params.customerId) {
                render([error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([error: 'Customer not found'] as JSON)
                return
            }
            
            Map profileData = generateCustomerProfileData(customer)
            
            Map reportParams = [:]
            reportParams._name = "Customer Profile Report"
            reportParams._format = params.format ?: "PDF"
            reportParams._file = "CustomerProfileReport.jasper"
            reportParams.customerId = customer.id
            reportParams.customerName = customer.displayName ?: "${customer.name1} ${customer.name2}".trim()
            
            // Generate report
            def reportDef = jasperService.buildReportDefinition(reportParams, request.getLocale(), [profileData])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            // Set response headers
            String contentType = params.format == 'EXCEL' ? 'application/vnd.ms-excel' : 'application/pdf'
            String fileName = "customer_profile_${customer.id}.${params.format?.toLowerCase() ?: 'pdf'}"
            
            response.setContentType(contentType)
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', "attachment; filename=${fileName}")
            
            response.outputStream << bytes
            response.outputStream.flush()
            
            // Audit logging
            auditLogService.insert('120', 'CRC04400', 
                "Customer profile report generated - Customer: ${customer.id}", 
                'CustomerReportController', null, null, 'cif/generateCustomerProfile', customer.id)
            
        } catch (Exception e) {
            log.error("Error generating customer profile report", e)
            response.sendError(500, "Error generating customer profile report")
        }
    }
    
    /**
     * Generate customer summary report
     */
    def generateCustomerSummary() {
        log.info("Generating customer summary report")
        
        try {
            Date asOfDate = params.asOfDate ? Date.parse('yyyy-MM-dd', params.asOfDate) : new Date()
            
            Map summaryData = generateCustomerSummaryData(asOfDate, params)
            
            Map reportParams = [:]
            reportParams._name = "Customer Summary Report"
            reportParams._format = params.format ?: "PDF"
            reportParams._file = "CustomerSummaryReport.jasper"
            reportParams.asOfDate = asOfDate
            reportParams.branchId = params.branchId
            
            // Generate report
            def reportDef = jasperService.buildReportDefinition(reportParams, request.getLocale(), [summaryData])
            byte[] bytes = jasperService.generateReport(reportDef).toByteArray()
            
            // Set response headers
            String contentType = params.format == 'EXCEL' ? 'application/vnd.ms-excel' : 'application/pdf'
            String fileName = "customer_summary_report.${params.format?.toLowerCase() ?: 'pdf'}"
            
            response.setContentType(contentType)
            response.setContentLength(bytes.length)
            response.setHeader('Content-disposition', "attachment; filename=${fileName}")
            
            response.outputStream << bytes
            response.outputStream.flush()
            
            // Audit logging
            auditLogService.insert('120', 'CRC04500', 
                "Customer summary report generated", 
                'CustomerReportController', null, null, 'cif/generateCustomerSummary', session.user_id)
            
        } catch (Exception e) {
            log.error("Error generating customer summary report", e)
            response.sendError(500, "Error generating customer summary report")
        }
    }
    
    /**
     * Generate customer demographics report
     */
    def generateCustomerDemographics() {
        log.info("Generating customer demographics report")
        
        try {
            Map demographicsData = generateCustomerDemographicsData(params)
            
            render(demographicsData as JSON)
            
        } catch (Exception e) {
            log.error("Error generating customer demographics report", e)
            render([error: 'Error generating customer demographics report'] as JSON)
        }
    }
    
    /**
     * Generate customer growth report
     */
    def generateCustomerGrowthReport() {
        log.info("Generating customer growth report")
        
        try {
            Date fromDate = params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 365
            Date toDate = params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date()
            
            Map growthData = generateCustomerGrowthData(fromDate, toDate, params)
            
            render(growthData as JSON)
            
        } catch (Exception e) {
            log.error("Error generating customer growth report", e)
            render([error: 'Error generating customer growth report'] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Generate customer list data
     */
    private Map generateCustomerListData(Date fromDate, Date toDate, Map filters) {
        Map data = [customers: [], totalCount: 0]
        
        try {
            def criteria = Customer.createCriteria()
            def customers = criteria.list {
                between("dateCreated", fromDate, toDate)
                
                if (filters.branchId) {
                    eq("branch", Branch.get(filters.branchId))
                }
                
                if (filters.customerType) {
                    eq("type", CustomerType.get(filters.customerType))
                }
                
                if (filters.status) {
                    eq("status", ConfigItemStatus.get(filters.status))
                }
                
                order("dateCreated", "desc")
            }
            
            data.customers = customers.collect { customer ->
                [
                    id: customer.id,
                    name: customer.displayName ?: "${customer.name1} ${customer.name2}".trim(),
                    type: customer.type?.description,
                    status: customer.status?.description,
                    branch: customer.branch?.name,
                    dateCreated: customer.dateCreated
                ]
            }
            
            data.totalCount = customers.size()
            
        } catch (Exception e) {
            log.error("Error generating customer list data", e)
            data.error = "Error generating customer list data"
        }
        
        return data
    }
    
    /**
     * Generate customer profile data
     */
    private Map generateCustomerProfileData(Customer customer) {
        Map data = [:]
        
        try {
            // Basic information
            data.basicInfo = [
                id: customer.id,
                name: customer.displayName ?: "${customer.name1} ${customer.name2}".trim(),
                type: customer.type?.description,
                group: customer.group?.description,
                status: customer.status?.description,
                branch: customer.branch?.name,
                dateCreated: customer.dateCreated
            ]
            
            // Contact information
            data.contacts = customer.contacts?.findAll { it.status?.id == 2 }?.collect {
                [type: it.type?.description, value: it.contactValue, isPrimary: it.isPrimaryPhone]
            }
            
            // Address information
            data.addresses = customer.addresses?.findAll { it.status?.id == 2 }?.collect {
                [type: it.type?.description, address: it.fullAddress]
            }
            
            // Account summary
            data.accounts = generateAccountsSummary(customer)
            
            // Employment information
            data.employments = customer.employments?.findAll { it.status?.id == 2 }?.collect {
                [employer: it.employer, position: it.position, monthlyIncome: it.monthlyIncome]
            }
            
        } catch (Exception e) {
            log.error("Error generating customer profile data", e)
            data.error = "Error generating customer profile data"
        }
        
        return data
    }
    
    /**
     * Generate customer summary data
     */
    private Map generateCustomerSummaryData(Date asOfDate, Map filters) {
        Map data = [:]
        
        try {
            // Total customers
            def totalCustomers = Customer.count()
            data.totalCustomers = totalCustomers
            
            // By type
            data.byType = CustomerType.list().collect { type ->
                [
                    type: type.description,
                    count: Customer.countByType(type)
                ]
            }
            
            // By status
            data.byStatus = ConfigItemStatus.list().collect { status ->
                [
                    status: status.description,
                    count: Customer.countByStatus(status)
                ]
            }
            
            // By branch
            if (!filters.branchId) {
                data.byBranch = Branch.list().collect { branch ->
                    [
                        branch: branch.name,
                        count: Customer.countByBranch(branch)
                    ]
                }
            }
            
            // Growth metrics
            Date lastMonth = asOfDate - 30
            data.newCustomersThisMonth = Customer.countByDateCreatedBetween(lastMonth, asOfDate)
            
        } catch (Exception e) {
            log.error("Error generating customer summary data", e)
            data.error = "Error generating customer summary data"
        }
        
        return data
    }
    
    /**
     * Generate customer demographics data
     */
    private Map generateCustomerDemographicsData(Map filters) {
        Map data = [:]
        
        try {
            // Age distribution
            data.ageDistribution = [
                '18-25': Customer.createCriteria().count {
                    between("birthDate", new Date() - (25 * 365), new Date() - (18 * 365))
                },
                '26-35': Customer.createCriteria().count {
                    between("birthDate", new Date() - (35 * 365), new Date() - (26 * 365))
                },
                '36-50': Customer.createCriteria().count {
                    between("birthDate", new Date() - (50 * 365), new Date() - (36 * 365))
                },
                '51+': Customer.createCriteria().count {
                    lt("birthDate", new Date() - (51 * 365))
                }
            ]
            
            // Gender distribution
            data.genderDistribution = Gender.list().collect { gender ->
                [
                    gender: gender.description,
                    count: Customer.countByGender(gender)
                ]
            }
            
            // Geographic distribution
            data.geographicDistribution = Branch.list().collect { branch ->
                [
                    location: branch.name,
                    count: Customer.countByBranch(branch)
                ]
            }
            
        } catch (Exception e) {
            log.error("Error generating customer demographics data", e)
            data.error = "Error generating customer demographics data"
        }
        
        return data
    }
    
    /**
     * Generate customer growth data
     */
    private Map generateCustomerGrowthData(Date fromDate, Date toDate, Map filters) {
        Map data = [:]
        
        try {
            // Monthly growth
            data.monthlyGrowth = []
            
            Calendar cal = Calendar.getInstance()
            cal.setTime(fromDate)
            
            while (cal.getTime() <= toDate) {
                Date monthStart = cal.getTime()
                cal.add(Calendar.MONTH, 1)
                Date monthEnd = cal.getTime()
                
                Integer newCustomers = Customer.countByDateCreatedBetween(monthStart, monthEnd)
                
                data.monthlyGrowth << [
                    month: monthStart.format('yyyy-MM'),
                    newCustomers: newCustomers
                ]
            }
            
            // Growth by type
            data.growthByType = CustomerType.list().collect { type ->
                [
                    type: type.description,
                    count: Customer.countByTypeAndDateCreatedBetween(type, fromDate, toDate)
                ]
            }
            
        } catch (Exception e) {
            log.error("Error generating customer growth data", e)
            data.error = "Error generating customer growth data"
        }
        
        return data
    }
    
    /**
     * Generate accounts summary
     */
    private Map generateAccountsSummary(Customer customer) {
        Map summary = [:]
        
        try {
            // Loan accounts
            def loans = Loan.findAllByCustomer(customer)
            summary.loans = [
                count: loans.size(),
                totalBalance: loans.sum { it.balanceAmount } ?: 0,
                active: loans.count { it.status?.id in [2, 3] }
            ]
            
            // Deposit accounts
            def deposits = Deposit.findAllByCustomer(customer)
            summary.deposits = [
                count: deposits.size(),
                totalBalance: deposits.sum { it.balanceAmount } ?: 0,
                active: deposits.count { it.statusId in [2, 3] }
            ]
            
            summary.totalRelationship = (summary.loans.totalBalance ?: 0) + (summary.deposits.totalBalance ?: 0)
            
        } catch (Exception e) {
            log.error("Error generating accounts summary", e)
            summary.error = "Error generating accounts summary"
        }
        
        return summary
    }
}
