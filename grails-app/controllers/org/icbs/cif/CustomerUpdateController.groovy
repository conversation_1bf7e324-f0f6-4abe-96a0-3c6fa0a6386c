package org.icbs.cif

import org.icbs.lov.*
import org.icbs.admin.UserMaster
import static org.springframework.http.HttpStatus.*
import grails.converters.JSON
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import groovy.util.logging.Slf4j

/**
 * REFACTORED: Customer Update Controller
 * Extracted from CustomerController.groovy (1,058+ lines)
 * Handles all customer update and modification operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Slf4j
class CustomerUpdateController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def customerService
    def policyService
    def auditLogService
    
    static allowedMethods = [
        edit: "GET",
        update: ["PUT", "POST"],
        delete: "DELETE",
        updateStatus: "POST",
        updateContactInfo: "POST",
        updateAddressInfo: "POST"
    ]
    
    // =====================================================
    // CUSTOMER UPDATE OPERATIONS
    // =====================================================
    
    /**
     * Edit customer form
     */
    def edit(Customer customerInstance) {
        log.info("Editing customer: ${customerInstance?.id}")
        
        try {
            if (!customerInstance) {
                notFound()
                return
            }
            
            session["customerpagevalidator"] = "edit"
            
            // Check edit permissions
            Map permissionResult = checkEditPermissions(customerInstance)
            if (!permissionResult.canEdit) {
                flash.message = permissionResult.message + '|warning|alert'
                redirect(action: 'show', id: customerInstance.id)
                return
            }
            
            // Audit logging
            auditLogService.insert('120', 'CUC03100', 
                "Customer edit form accessed - Customer: ${customerInstance.id}", 
                'CustomerUpdateController', null, null, 'cif/edit', customerInstance.id)
            
            respond customerInstance
            
        } catch (Exception e) {
            log.error("Error accessing customer edit form", e)
            flash.message = 'Error accessing customer edit form|error|alert'
            redirect(action: 'show', id: customerInstance?.id)
        }
    }
    
    /**
     * Update customer
     */
    def update(Customer customerInstance) {
        log.info("Updating customer: ${customerInstance?.id}")
        
        try {
            if (!customerInstance) {
                notFound()
                return
            }
            
            // 1. Process file attachments
            Map attachmentResult = processFileAttachments(request, params)
            
            // 2. Set system fields
            setUpdateSystemFields(params)
            
            // 3. Validate update permissions
            Map permissionResult = checkUpdatePermissions(customerInstance, params)
            if (!permissionResult.canUpdate) {
                flash.message = permissionResult.message + '|warning|alert'
                redirect(action: 'edit', id: customerInstance.id)
                return
            }
            
            // 4. Validate customer data
            Map validationResult = unifiedValidationService.validateCustomerUpdate(customerInstance, params)
            if (!validationResult.isValid) {
                def c = Customer.read(params.id)
                c.properties = params
                c.errors = validationResult.errors
                render view: "edit", model: [customerInstance: c]
                return
            }
            
            // 5. Update customer
            Map updateResult = customerService.updateCustomer(customerInstance, params)
            
            if (updateResult.success) {
                // 6. Process additional updates
                processAdditionalUpdates(updateResult.customer, params)
                
                // 7. Audit logging
                auditCustomerUpdate(updateResult.customer, 'SUCCESS')
                
                request.withFormat {
                    form multipartForm {
                        flash.message = 'Customer updated successfully|success|alert'
                        redirect(action: "show", id: updateResult.customer.id)
                    }
                    '*' { respond updateResult.customer, [status: OK] }
                }
            } else {
                def c = Customer.read(params.id)
                c.properties = params
                c.errors = updateResult.errors
                render view: "edit", model: [customerInstance: c]
            }
            
        } catch (Exception e) {
            log.error("Error updating customer", e)
            flash.message = 'Error updating customer|error|alert'
            def c = Customer.read(params.id)
            c.properties = params
            render view: "edit", model: [customerInstance: c]
        }
    }
    
    /**
     * Delete customer
     */
    def delete(Customer customerInstance) {
        log.info("Deleting customer: ${customerInstance?.id}")
        
        try {
            if (!customerInstance) {
                notFound()
                return
            }
            
            // Check delete permissions
            Map permissionResult = checkDeletePermissions(customerInstance)
            if (!permissionResult.canDelete) {
                flash.message = permissionResult.message + '|warning|alert'
                redirect(action: 'show', id: customerInstance.id)
                return
            }
            
            // Perform soft delete
            Map deleteResult = customerService.deleteCustomer(customerInstance)
            
            if (deleteResult.success) {
                // Audit logging
                auditCustomerDeletion(customerInstance, 'SUCCESS')
                
                request.withFormat {
                    form multipartForm {
                        flash.message = 'Customer deleted successfully|success|alert'
                        redirect action: "index", method: "GET"
                    }
                    '*' { render status: NO_CONTENT }
                }
            } else {
                flash.message = deleteResult.message + '|error|alert'
                redirect(action: 'show', id: customerInstance.id)
            }
            
        } catch (Exception e) {
            log.error("Error deleting customer", e)
            flash.message = 'Error deleting customer|error|alert'
            redirect(action: 'show', id: customerInstance?.id)
        }
    }
    
    /**
     * Update customer status
     */
    def updateStatus() {
        log.info("Updating customer status: ${params.customerId}")
        
        try {
            if (!params.customerId || !params.newStatus) {
                render([success: false, error: 'Customer ID and new status are required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([success: false, error: 'Customer not found'] as JSON)
                return
            }
            
            // Validate status change
            Map validationResult = validateStatusChange(customer, params.newStatus)
            if (!validationResult.isValid) {
                render([success: false, error: validationResult.message] as JSON)
                return
            }
            
            // Update status
            Map updateResult = customerService.updateCustomerStatus(customer, params.newStatus, params.reason)
            
            if (updateResult.success) {
                // Audit logging
                auditStatusChange(customer, params.newStatus, 'SUCCESS')
                
                render([success: true, message: 'Customer status updated successfully'] as JSON)
            } else {
                render([success: false, error: updateResult.message] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error updating customer status", e)
            render([success: false, error: 'Error updating customer status'] as JSON)
        }
    }
    
    /**
     * Update contact information
     */
    def updateContactInfo() {
        log.info("Updating customer contact info: ${params.customerId}")
        
        try {
            if (!params.customerId) {
                render([success: false, error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([success: false, error: 'Customer not found'] as JSON)
                return
            }
            
            // Update contact information
            Map updateResult = customerService.updateContactInformation(customer, params)
            
            if (updateResult.success) {
                // Audit logging
                auditContactUpdate(customer, 'SUCCESS')
                
                render([success: true, message: 'Contact information updated successfully'] as JSON)
            } else {
                render([success: false, error: updateResult.message] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error updating contact information", e)
            render([success: false, error: 'Error updating contact information'] as JSON)
        }
    }
    
    /**
     * Update address information
     */
    def updateAddressInfo() {
        log.info("Updating customer address info: ${params.customerId}")
        
        try {
            if (!params.customerId) {
                render([success: false, error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([success: false, error: 'Customer not found'] as JSON)
                return
            }
            
            // Update address information
            Map updateResult = customerService.updateAddressInformation(customer, params)
            
            if (updateResult.success) {
                // Audit logging
                auditAddressUpdate(customer, 'SUCCESS')
                
                render([success: true, message: 'Address information updated successfully'] as JSON)
            } else {
                render([success: false, error: updateResult.message] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error updating address information", e)
            render([success: false, error: 'Error updating address information'] as JSON)
        }
    }
    
    /**
     * Bulk update customers
     */
    def bulkUpdate() {
        log.info("Performing bulk customer update")
        
        try {
            if (!params.customerIds || !params.updateType) {
                render([success: false, error: 'Customer IDs and update type are required'] as JSON)
                return
            }
            
            List<Long> customerIds = params.customerIds.split(',').collect { it.toLong() }
            Map bulkResult = customerService.bulkUpdateCustomers(customerIds, params)
            
            // Audit logging
            auditBulkUpdate(customerIds, params.updateType, bulkResult.success ? 'SUCCESS' : 'FAILED')
            
            render(bulkResult as JSON)
            
        } catch (Exception e) {
            log.error("Error performing bulk update", e)
            render([success: false, error: 'Error performing bulk update'] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Process file attachments
     */
    private Map processFileAttachments(def request, Map params) {
        Map result = [success: true, errors: []]
        
        try {
            if (request instanceof MultipartHttpServletRequest) {
                request.fileNames.each { fileName ->
                    def uploadedFile = request.getFile(fileName)
                    String subscript = (fileName.split("\\.")[0])
                    
                    if (uploadedFile.getSize() < 1) {
                        if (!params[subscript + ".id"]) {
                            params.remove(subscript + '.fileName')
                        }
                    } else {
                        params[subscript + ".fileName"] = uploadedFile.getOriginalFilename()
                        params[subscript + ".fileType"] = uploadedFile.getContentType()
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("Error processing file attachments", e)
            result.success = false
            result.errors << "Error processing file attachments"
        }
        
        return result
    }
    
    /**
     * Set update system fields
     */
    private void setUpdateSystemFields(Map params) {
        try {
            UserMaster currentUser = UserMaster.get(session.user_id)
            params.lastUpdatedBy = currentUser
            
        } catch (Exception e) {
            log.error("Error setting update system fields", e)
        }
    }
    
    /**
     * Check edit permissions
     */
    private Map checkEditPermissions(Customer customer) {
        Map result = [canEdit: true, message: '']
        
        try {
            // Check if customer is active
            if (customer.status?.id == 4) { // Inactive
                result.canEdit = false
                result.message = 'Cannot edit inactive customer'
                return result
            }
            
            // Check user permissions
            // Add additional permission checks as needed
            
        } catch (Exception e) {
            log.error("Error checking edit permissions", e)
            result.canEdit = false
            result.message = 'Error checking edit permissions'
        }
        
        return result
    }
    
    /**
     * Check update permissions
     */
    private Map checkUpdatePermissions(Customer customer, Map params) {
        Map result = [canUpdate: true, message: '']
        
        try {
            // Check if critical fields are being changed
            if (params.type?.id != customer.type?.id) {
                // Customer type change requires special permission
                result.canUpdate = false
                result.message = 'Customer type change requires supervisor approval'
                return result
            }
            
            // Add additional permission checks as needed
            
        } catch (Exception e) {
            log.error("Error checking update permissions", e)
            result.canUpdate = false
            result.message = 'Error checking update permissions'
        }
        
        return result
    }
    
    /**
     * Check delete permissions
     */
    private Map checkDeletePermissions(Customer customer) {
        Map result = [canDelete: true, message: '']
        
        try {
            // Check if customer has active accounts
            def activeLoans = Loan.countByCustomerAndStatusInList(customer, [
                LoanAcctStatus.get(2), LoanAcctStatus.get(3)
            ])
            
            if (activeLoans > 0) {
                result.canDelete = false
                result.message = 'Cannot delete customer with active loan accounts'
                return result
            }
            
            // Check for active deposits
            def activeDeposits = Deposit.countByCustomerAndStatusIdInList(customer, [2, 3])
            
            if (activeDeposits > 0) {
                result.canDelete = false
                result.message = 'Cannot delete customer with active deposit accounts'
                return result
            }
            
        } catch (Exception e) {
            log.error("Error checking delete permissions", e)
            result.canDelete = false
            result.message = 'Error checking delete permissions'
        }
        
        return result
    }
    
    /**
     * Validate status change
     */
    private Map validateStatusChange(Customer customer, String newStatus) {
        Map result = [isValid: true, message: '']
        
        try {
            ConfigItemStatus newStatusObj = ConfigItemStatus.get(newStatus.toLong())
            
            if (!newStatusObj) {
                result.isValid = false
                result.message = 'Invalid status'
                return result
            }
            
            // Add business rule validations for status changes
            
        } catch (Exception e) {
            log.error("Error validating status change", e)
            result.isValid = false
            result.message = 'Error validating status change'
        }
        
        return result
    }
    
    /**
     * Process additional updates
     */
    private void processAdditionalUpdates(Customer customer, Map params) {
        try {
            // Process any additional updates like contacts, addresses, etc.
            // This would integrate with other services
            
        } catch (Exception e) {
            log.error("Error processing additional updates", e)
        }
    }
    
    /**
     * Handle not found
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = message(code: 'default.not.found.message', 
                                      args: [message(code: 'customer.label', default: 'Customer'), params.id])
                redirect action: "index", method: "GET"
            }
            '*' { render status: NOT_FOUND }
        }
    }
    
    // Audit methods
    private void auditCustomerUpdate(Customer customer, String result) {
        try {
            auditLogService.insert('120', 'CUC03000', 
                "Customer updated - Name: ${customer.name1} ${customer.name2}, Result: ${result}", 
                'CustomerUpdateController', null, null, null, customer.id)
        } catch (Exception e) {
            log.error("Error auditing customer update", e)
        }
    }
    
    private void auditCustomerDeletion(Customer customer, String result) {
        try {
            auditLogService.insert('120', 'CUC03200', 
                "Customer deleted - Name: ${customer.name1} ${customer.name2}, Result: ${result}", 
                'CustomerUpdateController', null, null, null, customer.id)
        } catch (Exception e) {
            log.error("Error auditing customer deletion", e)
        }
    }
    
    private void auditStatusChange(Customer customer, String newStatus, String result) {
        try {
            auditLogService.insert('120', 'CUC03300', 
                "Customer status changed - Customer: ${customer.id}, New Status: ${newStatus}, Result: ${result}", 
                'CustomerUpdateController', null, null, null, customer.id)
        } catch (Exception e) {
            log.error("Error auditing status change", e)
        }
    }
    
    private void auditContactUpdate(Customer customer, String result) {
        try {
            auditLogService.insert('120', 'CUC03400', 
                "Customer contact updated - Customer: ${customer.id}, Result: ${result}", 
                'CustomerUpdateController', null, null, null, customer.id)
        } catch (Exception e) {
            log.error("Error auditing contact update", e)
        }
    }
    
    private void auditAddressUpdate(Customer customer, String result) {
        try {
            auditLogService.insert('120', 'CUC03500', 
                "Customer address updated - Customer: ${customer.id}, Result: ${result}", 
                'CustomerUpdateController', null, null, null, customer.id)
        } catch (Exception e) {
            log.error("Error auditing address update", e)
        }
    }
    
    private void auditBulkUpdate(List<Long> customerIds, String updateType, String result) {
        try {
            auditLogService.insert('120', 'CUC03600', 
                "Bulk customer update - Count: ${customerIds.size()}, Type: ${updateType}, Result: ${result}", 
                'CustomerUpdateController', null, null, null, session.user_id)
        } catch (Exception e) {
            log.error("Error auditing bulk update", e)
        }
    }
}
