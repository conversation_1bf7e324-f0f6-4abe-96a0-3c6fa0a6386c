package org.icbs.cif

import org.icbs.lov.*
import org.icbs.admin.UserMaster
import org.icbs.admin.DocumentType
import static org.springframework.http.HttpStatus.*
import grails.converters.JSON
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import groovy.util.logging.Slf4j

/**
 * REFACTORED: Customer Document Controller
 * Extracted from CustomerController.groovy (1,058+ lines)
 * Handles all customer document management operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Slf4j
class CustomerDocumentController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def customerService
    def fileStorageService
    def auditLogService
    
    static allowedMethods = [
        uploadDocument: "POST",
        downloadDocument: "GET",
        deleteDocument: "DELETE",
        getDocumentList: "GET",
        getDocumentChecklist: "GET",
        updateDocumentStatus: "POST"
    ]
    
    // =====================================================
    // CUSTOMER DOCUMENT OPERATIONS
    // =====================================================
    
    /**
     * Upload customer document
     */
    def uploadDocument() {
        log.info("Uploading customer document")
        
        try {
            if (!params.customerId) {
                render([success: false, error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([success: false, error: 'Customer not found'] as JSON)
                return
            }
            
            // Process file upload
            Map uploadResult = processDocumentUpload(request, params, customer)
            
            if (uploadResult.success) {
                // Audit logging
                auditDocumentUpload(customer, uploadResult.document, 'SUCCESS')
                
                render([
                    success: true,
                    message: 'Document uploaded successfully',
                    document: [
                        id: uploadResult.document.id,
                        fileName: uploadResult.document.fileName,
                        documentType: uploadResult.document.documentType?.description,
                        uploadDate: uploadResult.document.uploadDate
                    ]
                ] as JSON)
            } else {
                render([success: false, error: uploadResult.message] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error uploading customer document", e)
            render([success: false, error: 'Error uploading document'] as JSON)
        }
    }
    
    /**
     * Download customer document
     */
    def downloadDocument() {
        log.info("Downloading customer document: ${params.documentId}")
        
        try {
            if (!params.documentId) {
                response.sendError(400, "Document ID is required")
                return
            }
            
            CustomerDocument document = CustomerDocument.get(params.documentId)
            if (!document) {
                response.sendError(404, "Document not found")
                return
            }
            
            // Check access permissions
            Map accessResult = checkDocumentAccess(document)
            if (!accessResult.hasAccess) {
                response.sendError(403, "Access denied")
                return
            }
            
            // Retrieve file content
            Map fileResult = fileStorageService.retrieveFile(document.filePath)
            if (!fileResult.success) {
                response.sendError(500, "Error retrieving document")
                return
            }
            
            // Set response headers
            response.setContentType(document.contentType ?: 'application/octet-stream')
            response.setContentLength(fileResult.content.length)
            response.setHeader('Content-disposition', "attachment; filename=${document.fileName}")
            
            response.outputStream << fileResult.content
            response.outputStream.flush()
            
            // Audit logging
            auditDocumentDownload(document, 'SUCCESS')
            
        } catch (Exception e) {
            log.error("Error downloading customer document", e)
            response.sendError(500, "Error downloading document")
        }
    }
    
    /**
     * Delete customer document
     */
    def deleteDocument() {
        log.info("Deleting customer document: ${params.documentId}")
        
        try {
            if (!params.documentId) {
                render([success: false, error: 'Document ID is required'] as JSON)
                return
            }
            
            CustomerDocument document = CustomerDocument.get(params.documentId)
            if (!document) {
                render([success: false, error: 'Document not found'] as JSON)
                return
            }
            
            // Check deletion permissions
            Map permissionResult = checkDeletionPermission(document)
            if (!permissionResult.canDelete) {
                render([success: false, error: permissionResult.reason] as JSON)
                return
            }
            
            // Perform soft delete
            Map deleteResult = customerService.deleteCustomerDocument(document, params.reason)
            
            if (deleteResult.success) {
                // Audit logging
                auditDocumentDeletion(document, params.reason, 'SUCCESS')
                
                render([success: true, message: 'Document deleted successfully'] as JSON)
            } else {
                render([success: false, error: deleteResult.message] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error deleting customer document", e)
            render([success: false, error: 'Error deleting document'] as JSON)
        }
    }
    
    /**
     * Get customer document list
     */
    def getDocumentList() {
        log.debug("Getting customer document list: ${params.customerId}")
        
        try {
            if (!params.customerId) {
                render([error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([error: 'Customer not found'] as JSON)
                return
            }
            
            List<CustomerDocument> documents = CustomerDocument.createCriteria().list {
                eq("customer", customer)
                eq("isDeleted", false)
                order("uploadDate", "desc")
            }
            
            List<Map> documentList = documents.collect { doc ->
                [
                    id: doc.id,
                    fileName: doc.fileName,
                    documentType: doc.documentType?.description,
                    uploadDate: doc.uploadDate,
                    fileSize: doc.fileSize,
                    status: doc.status?.description,
                    expiryDate: doc.expiryDate,
                    isExpired: doc.expiryDate && doc.expiryDate < new Date()
                ]
            }
            
            render([
                success: true,
                documents: documentList,
                totalCount: documentList.size()
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting customer document list", e)
            render([error: 'Error getting document list'] as JSON)
        }
    }
    
    /**
     * Get customer document checklist
     */
    def getDocumentChecklist() {
        log.debug("Getting customer document checklist: ${params.customerId}")
        
        try {
            if (!params.customerId) {
                render([error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([error: 'Customer not found'] as JSON)
                return
            }
            
            Map checklist = generateDocumentChecklist(customer)
            
            render([
                success: true,
                checklist: checklist
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting customer document checklist", e)
            render([error: 'Error getting document checklist'] as JSON)
        }
    }
    
    /**
     * Update document status
     */
    def updateDocumentStatus() {
        log.info("Updating document status: ${params.documentId}")
        
        try {
            if (!params.documentId || !params.newStatus) {
                render([success: false, error: 'Document ID and new status are required'] as JSON)
                return
            }
            
            CustomerDocument document = CustomerDocument.get(params.documentId)
            if (!document) {
                render([success: false, error: 'Document not found'] as JSON)
                return
            }
            
            // Update document status
            Map updateResult = customerService.updateDocumentStatus(document, params.newStatus, params.comments)
            
            if (updateResult.success) {
                // Audit logging
                auditDocumentStatusUpdate(document, params.newStatus, 'SUCCESS')
                
                render([success: true, message: 'Document status updated successfully'] as JSON)
            } else {
                render([success: false, error: updateResult.message] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error updating document status", e)
            render([success: false, error: 'Error updating document status'] as JSON)
        }
    }
    
    /**
     * Bulk upload documents
     */
    def bulkUploadDocuments() {
        log.info("Bulk uploading customer documents")
        
        try {
            if (!params.customerId) {
                render([success: false, error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([success: false, error: 'Customer not found'] as JSON)
                return
            }
            
            Map bulkResult = processBulkDocumentUpload(request, params, customer)
            
            // Audit logging
            auditBulkDocumentUpload(customer, bulkResult.uploadedCount, bulkResult.success ? 'SUCCESS' : 'PARTIAL')
            
            render(bulkResult as JSON)
            
        } catch (Exception e) {
            log.error("Error bulk uploading documents", e)
            render([success: false, error: 'Error bulk uploading documents'] as JSON)
        }
    }
    
    /**
     * Generate document report
     */
    def generateDocumentReport() {
        log.info("Generating customer document report")
        
        try {
            Date fromDate = params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 30
            Date toDate = params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date()
            
            Map reportData = generateDocumentReportData(fromDate, toDate, params)
            
            render(reportData as JSON)
            
        } catch (Exception e) {
            log.error("Error generating document report", e)
            render([error: 'Error generating document report'] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Process document upload
     */
    private Map processDocumentUpload(def request, Map params, Customer customer) {
        Map result = [success: false, message: '', document: null]
        
        try {
            if (!(request instanceof MultipartHttpServletRequest)) {
                result.message = 'No file uploaded'
                return result
            }
            
            MultipartFile uploadedFile = request.getFile('documentFile')
            if (!uploadedFile || uploadedFile.empty) {
                result.message = 'No file selected'
                return result
            }
            
            // Validate file
            Map fileValidation = validateUploadedFile(uploadedFile)
            if (!fileValidation.isValid) {
                result.message = fileValidation.errors.join(', ')
                return result
            }
            
            // Store file
            String fileName = generateUniqueFileName(uploadedFile.originalFilename)
            Map storageResult = fileStorageService.storeFile(uploadedFile, fileName, 'customer-documents')
            
            if (!storageResult.success) {
                result.message = 'Error storing file'
                return result
            }
            
            // Create document record
            CustomerDocument document = new CustomerDocument(
                customer: customer,
                documentType: DocumentType.get(params.documentTypeId),
                fileName: fileName,
                originalFileName: uploadedFile.originalFilename,
                filePath: storageResult.filePath,
                fileSize: uploadedFile.size,
                contentType: uploadedFile.contentType,
                uploadDate: new Date(),
                uploadedBy: UserMaster.get(session.user_id),
                description: params.description,
                expiryDate: params.expiryDate ? Date.parse('yyyy-MM-dd', params.expiryDate) : null,
                status: ConfigItemStatus.get(2) // Active
            )
            
            document.save(flush: true, failOnError: true)
            
            result.success = true
            result.document = document
            result.message = 'Document uploaded successfully'
            
        } catch (Exception e) {
            log.error("Error processing document upload", e)
            result.message = 'Error processing document upload'
        }
        
        return result
    }
    
    /**
     * Process bulk document upload
     */
    private Map processBulkDocumentUpload(def request, Map params, Customer customer) {
        Map result = [success: true, uploadedCount: 0, failedCount: 0, errors: []]
        
        try {
            if (!(request instanceof MultipartHttpServletRequest)) {
                result.success = false
                result.errors << 'No files uploaded'
                return result
            }
            
            request.fileNames.each { fileName ->
                try {
                    MultipartFile uploadedFile = request.getFile(fileName)
                    if (uploadedFile && !uploadedFile.empty) {
                        Map uploadResult = processDocumentUpload(request, params, customer)
                        if (uploadResult.success) {
                            result.uploadedCount++
                        } else {
                            result.failedCount++
                            result.errors << "File ${uploadedFile.originalFilename}: ${uploadResult.message}"
                        }
                    }
                } catch (Exception e) {
                    result.failedCount++
                    result.errors << "Error processing file: ${e.message}"
                }
            }
            
            result.success = result.failedCount == 0
            
        } catch (Exception e) {
            log.error("Error processing bulk document upload", e)
            result.success = false
            result.errors << 'Error processing bulk upload'
        }
        
        return result
    }
    
    /**
     * Generate document checklist
     */
    private Map generateDocumentChecklist(Customer customer) {
        Map checklist = [required: [], optional: [], submitted: [], completionStatus: [:]]
        
        try {
            // Get required documents based on customer type
            checklist.required = getRequiredDocuments(customer)
            
            // Get optional documents
            checklist.optional = getOptionalDocuments(customer)
            
            // Get submitted documents
            checklist.submitted = customer.documents?.findAll { !it.isDeleted }?.collect {
                [
                    id: it.id,
                    documentType: it.documentType?.description,
                    fileName: it.fileName,
                    uploadDate: it.uploadDate,
                    status: it.status?.description,
                    isExpired: it.expiryDate && it.expiryDate < new Date()
                ]
            } ?: []
            
            // Calculate completion status
            checklist.completionStatus = calculateCompletionStatus(checklist)
            
        } catch (Exception e) {
            log.error("Error generating document checklist", e)
            checklist.error = "Error generating document checklist"
        }
        
        return checklist
    }
    
    /**
     * Get required documents for customer
     */
    private List getRequiredDocuments(Customer customer) {
        try {
            return DocumentType.createCriteria().list {
                eq("isRequired", true)
                eq("entityType", "CUSTOMER")
                eq("isActive", true)
                // Add customer type specific filtering if needed
            }
        } catch (Exception e) {
            log.error("Error getting required documents", e)
            return []
        }
    }
    
    /**
     * Get optional documents for customer
     */
    private List getOptionalDocuments(Customer customer) {
        try {
            return DocumentType.createCriteria().list {
                eq("isRequired", false)
                eq("entityType", "CUSTOMER")
                eq("isActive", true)
            }
        } catch (Exception e) {
            log.error("Error getting optional documents", e)
            return []
        }
    }
    
    /**
     * Calculate completion status
     */
    private Map calculateCompletionStatus(Map checklist) {
        Map status = [:]
        
        try {
            Integer requiredCount = checklist.required.size()
            Integer submittedRequiredCount = 0
            
            checklist.required.each { requiredDoc ->
                boolean isSubmitted = checklist.submitted.any { 
                    it.documentType == requiredDoc.description 
                }
                if (isSubmitted) {
                    submittedRequiredCount++
                }
            }
            
            status.totalRequired = requiredCount
            status.submittedRequired = submittedRequiredCount
            status.completionPercentage = requiredCount > 0 ? 
                (submittedRequiredCount / requiredCount * 100).setScale(2) : 100
            status.isComplete = submittedRequiredCount == requiredCount
            
        } catch (Exception e) {
            log.error("Error calculating completion status", e)
            status.error = "Error calculating completion status"
        }
        
        return status
    }
    
    /**
     * Validate uploaded file
     */
    private Map validateUploadedFile(MultipartFile file) {
        Map validation = [isValid: true, errors: []]
        
        try {
            // Check file size (10MB limit)
            if (file.size > 10 * 1024 * 1024) {
                validation.isValid = false
                validation.errors << "File size exceeds 10MB limit"
            }
            
            // Check file type
            List allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/gif', 'application/msword']
            if (!allowedTypes.contains(file.contentType)) {
                validation.isValid = false
                validation.errors << "File type not allowed"
            }
            
        } catch (Exception e) {
            log.error("Error validating uploaded file", e)
            validation.isValid = false
            validation.errors << "Error validating file"
        }
        
        return validation
    }
    
    /**
     * Generate unique file name
     */
    private String generateUniqueFileName(String originalFileName) {
        try {
            String extension = originalFileName.substring(originalFileName.lastIndexOf('.'))
            String timestamp = new Date().format('yyyyMMdd_HHmmss')
            String randomString = UUID.randomUUID().toString().substring(0, 8)
            return "customer_doc_${timestamp}_${randomString}${extension}"
        } catch (Exception e) {
            log.error("Error generating unique file name", e)
            return "customer_doc_${System.currentTimeMillis()}.pdf"
        }
    }
    
    /**
     * Check document access permissions
     */
    private Map checkDocumentAccess(CustomerDocument document) {
        Map access = [hasAccess: true, reason: '']
        
        try {
            // Implement access control logic
            // For now, allowing all access
            
        } catch (Exception e) {
            log.error("Error checking document access", e)
            access.hasAccess = false
            access.reason = "Error checking access permissions"
        }
        
        return access
    }
    
    /**
     * Check deletion permission
     */
    private Map checkDeletionPermission(CustomerDocument document) {
        Map permission = [canDelete: true, reason: '']
        
        try {
            // Check if document is required and customer is active
            if (document.documentType?.isRequired && document.customer?.status?.id == 2) {
                permission.canDelete = false
                permission.reason = "Cannot delete required document for active customer"
            }
            
        } catch (Exception e) {
            log.error("Error checking deletion permission", e)
            permission.canDelete = false
            permission.reason = "Error checking deletion permission"
        }
        
        return permission
    }
    
    /**
     * Generate document report data
     */
    private Map generateDocumentReportData(Date fromDate, Date toDate, Map filters) {
        Map data = [:]
        
        try {
            // Document upload statistics
            data.uploadStats = [
                totalUploads: CustomerDocument.countByUploadDateBetween(fromDate, toDate),
                byDocumentType: DocumentType.list().collect { type ->
                    [
                        type: type.description,
                        count: CustomerDocument.countByDocumentTypeAndUploadDateBetween(type, fromDate, toDate)
                    ]
                }
            ]
            
            // Compliance statistics
            data.complianceStats = [
                compliantCustomers: 0, // Calculate based on document completeness
                nonCompliantCustomers: 0,
                expiredDocuments: CustomerDocument.countByExpiryDateLessThan(new Date())
            ]
            
        } catch (Exception e) {
            log.error("Error generating document report data", e)
            data.error = "Error generating document report data"
        }
        
        return data
    }
    
    // Audit methods
    private void auditDocumentUpload(Customer customer, CustomerDocument document, String result) {
        try {
            auditLogService.insert('120', 'CDC05100', 
                "Customer document uploaded - Customer: ${customer.id}, Document: ${document.fileName}, Result: ${result}", 
                'CustomerDocumentController', null, null, null, customer.id)
        } catch (Exception e) {
            log.error("Error auditing document upload", e)
        }
    }
    
    private void auditDocumentDownload(CustomerDocument document, String result) {
        try {
            auditLogService.insert('120', 'CDC05200', 
                "Customer document downloaded - Document: ${document.fileName}, Result: ${result}", 
                'CustomerDocumentController', null, null, null, document.id)
        } catch (Exception e) {
            log.error("Error auditing document download", e)
        }
    }
    
    private void auditDocumentDeletion(CustomerDocument document, String reason, String result) {
        try {
            auditLogService.insert('120', 'CDC05300', 
                "Customer document deleted - Document: ${document.fileName}, Reason: ${reason}, Result: ${result}", 
                'CustomerDocumentController', null, null, null, document.id)
        } catch (Exception e) {
            log.error("Error auditing document deletion", e)
        }
    }
    
    private void auditDocumentStatusUpdate(CustomerDocument document, String newStatus, String result) {
        try {
            auditLogService.insert('120', 'CDC05400', 
                "Customer document status updated - Document: ${document.fileName}, Status: ${newStatus}, Result: ${result}", 
                'CustomerDocumentController', null, null, null, document.id)
        } catch (Exception e) {
            log.error("Error auditing document status update", e)
        }
    }
    
    private void auditBulkDocumentUpload(Customer customer, Integer uploadedCount, String result) {
        try {
            auditLogService.insert('120', 'CDC05500', 
                "Bulk customer documents uploaded - Customer: ${customer.id}, Count: ${uploadedCount}, Result: ${result}", 
                'CustomerDocumentController', null, null, null, customer.id)
        } catch (Exception e) {
            log.error("Error auditing bulk document upload", e)
        }
    }
}
