package org.icbs.cif

import org.icbs.lov.*
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import static org.springframework.http.HttpStatus.*
import grails.converters.JSON
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import groovy.util.logging.Slf4j

/**
 * REFACTORED: Customer Utility Controller
 * Extracted from CustomerController.groovy (1,058+ lines)
 * Handles utility operations and helper functions with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Slf4j
class CustomerUtilityController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def customerService
    def auditLogService
    
    static allowedMethods = [
        getCustomerTypes: "GET",
        getCustomerGroups: "GET",
        getDocumentTypes: "GET",
        generateCustomerNumber: "GET",
        formatCustomerData: "POST",
        exportCustomerData: "GET"
    ]
    
    // =====================================================
    // UTILITY OPERATIONS
    // =====================================================
    
    /**
     * Get customer types
     */
    def getCustomerTypes() {
        log.debug("Getting customer types")
        
        try {
            List<CustomerType> customerTypes = CustomerType.createCriteria().list {
                eq("isActive", true)
                order("description", "asc")
            }
            
            List<Map> typeList = customerTypes.collect {
                [
                    id: it.id,
                    code: it.code,
                    description: it.description,
                    isIndividual: it.isIndividual,
                    isCorporate: it.isCorporate
                ]
            }
            
            render([
                success: true,
                customerTypes: typeList
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting customer types", e)
            render([error: 'Error getting customer types'] as JSON)
        }
    }
    
    /**
     * Get customer groups
     */
    def getCustomerGroups() {
        log.debug("Getting customer groups")
        
        try {
            List<CustomerGroup> customerGroups = CustomerGroup.createCriteria().list {
                eq("isActive", true)
                order("description", "asc")
            }
            
            List<Map> groupList = customerGroups.collect {
                [
                    id: it.id,
                    code: it.code,
                    description: it.description,
                    riskLevel: it.riskLevel
                ]
            }
            
            render([
                success: true,
                customerGroups: groupList
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting customer groups", e)
            render([error: 'Error getting customer groups'] as JSON)
        }
    }
    
    /**
     * Get document types
     */
    def getDocumentTypes() {
        log.debug("Getting document types")
        
        try {
            List<DocumentType> documentTypes = DocumentType.createCriteria().list {
                eq("isActive", true)
                eq("entityType", "CUSTOMER")
                order("description", "asc")
            }
            
            List<Map> typeList = documentTypes.collect {
                [
                    id: it.id,
                    code: it.code,
                    description: it.description,
                    isRequired: it.isRequired,
                    hasExpiry: it.hasExpiry
                ]
            }
            
            render([
                success: true,
                documentTypes: typeList
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting document types", e)
            render([error: 'Error getting document types'] as JSON)
        }
    }
    
    /**
     * Generate customer number
     */
    def generateCustomerNumber() {
        log.debug("Generating customer number")
        
        try {
            String customerNumber = customerService.generateCustomerNumber(params)
            
            render([
                success: true,
                customerNumber: customerNumber
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error generating customer number", e)
            render([error: 'Error generating customer number'] as JSON)
        }
    }
    
    /**
     * Format customer data
     */
    def formatCustomerData() {
        log.debug("Formatting customer data")
        
        try {
            Map formattedData = customerService.formatCustomerData(params)
            
            render([
                success: true,
                formattedData: formattedData
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error formatting customer data", e)
            render([error: 'Error formatting customer data'] as JSON)
        }
    }
    
    /**
     * Export customer data
     */
    def exportCustomerData() {
        log.info("Exporting customer data")
        
        try {
            if (!params.customerId) {
                response.sendError(400, "Customer ID is required")
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                response.sendError(404, "Customer not found")
                return
            }
            
            String format = params.format ?: 'JSON'
            Map exportData = customerService.exportCustomerData(customer, format)
            
            if (exportData.success) {
                // Set response headers
                String contentType = getContentTypeForFormat(format)
                String fileName = "customer_${customer.id}_export.${format.toLowerCase()}"
                
                response.setContentType(contentType)
                response.setHeader('Content-disposition', "attachment; filename=${fileName}")
                
                if (format == 'JSON') {
                    render(exportData.data as JSON)
                } else {
                    response.outputStream << exportData.content
                    response.outputStream.flush()
                }
                
                // Audit logging
                auditCustomerDataExport(customer, format, 'SUCCESS')
                
            } else {
                response.sendError(500, "Error exporting customer data")
            }
            
        } catch (Exception e) {
            log.error("Error exporting customer data", e)
            response.sendError(500, "Error exporting customer data")
        }
    }
    
    /**
     * Get customer statistics
     */
    def getCustomerStatistics() {
        log.debug("Getting customer statistics")
        
        try {
            Map statistics = generateCustomerStatistics(params)
            
            render([
                success: true,
                statistics: statistics
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting customer statistics", e)
            render([error: 'Error getting customer statistics'] as JSON)
        }
    }
    
    /**
     * Validate customer business rules
     */
    def validateBusinessRules() {
        log.debug("Validating customer business rules")
        
        try {
            Map validationResult = customerService.validateBusinessRules(params)
            
            render([
                isValid: validationResult.isValid,
                violations: validationResult.violations,
                warnings: validationResult.warnings
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error validating business rules", e)
            render([isValid: false, violations: ['Error validating business rules']] as JSON)
        }
    }
    
    /**
     * Get customer lookup data
     */
    def getCustomerLookupData() {
        log.debug("Getting customer lookup data")
        
        try {
            Map lookupData = [:]
            
            // Customer types
            lookupData.customerTypes = CustomerType.createCriteria().list {
                eq("isActive", true)
                order("description", "asc")
            }.collect { [id: it.id, description: it.description] }
            
            // Customer groups
            lookupData.customerGroups = CustomerGroup.createCriteria().list {
                eq("isActive", true)
                order("description", "asc")
            }.collect { [id: it.id, description: it.description] }
            
            // Genders
            lookupData.genders = Gender.list().collect { [id: it.id, description: it.description] }
            
            // Civil statuses
            lookupData.civilStatuses = Lov.createCriteria().list {
                eq("type", "CIVIL_STATUS")
                eq("isActive", true)
                order("description", "asc")
            }.collect { [id: it.id, description: it.description] }
            
            // Nationalities
            lookupData.nationalities = Lov.createCriteria().list {
                eq("type", "NATIONALITY")
                eq("isActive", true)
                order("description", "asc")
            }.collect { [id: it.id, description: it.description] }
            
            // Branches
            lookupData.branches = Branch.createCriteria().list {
                eq("isActive", true)
                order("name", "asc")
            }.collect { [id: it.id, name: it.name, code: it.code] }
            
            render([
                success: true,
                lookupData: lookupData
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting customer lookup data", e)
            render([error: 'Error getting customer lookup data'] as JSON)
        }
    }
    
    /**
     * Calculate customer age
     */
    def calculateCustomerAge() {
        log.debug("Calculating customer age")
        
        try {
            if (!params.birthDate) {
                render([error: 'Birth date is required'] as JSON)
                return
            }
            
            Date birthDate = Date.parse('yyyy-MM-dd', params.birthDate)
            Integer age = calculateAge(birthDate)
            
            render([
                success: true,
                age: age,
                ageGroup: getAgeGroup(age),
                isMinor: age < 18,
                isSenior: age >= 60
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error calculating customer age", e)
            render([error: 'Error calculating customer age'] as JSON)
        }
    }
    
    /**
     * Generate customer summary
     */
    def generateCustomerSummary() {
        log.debug("Generating customer summary")
        
        try {
            if (!params.customerId) {
                render([error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([error: 'Customer not found'] as JSON)
                return
            }
            
            Map summary = customerService.generateCustomerSummary(customer)
            
            render([
                success: true,
                summary: summary
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error generating customer summary", e)
            render([error: 'Error generating customer summary'] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Generate customer statistics
     */
    private Map generateCustomerStatistics(Map filters) {
        Map statistics = [:]
        
        try {
            // Total customers
            statistics.totalCustomers = Customer.count()
            
            // By type
            statistics.byType = CustomerType.list().collect { type ->
                [
                    type: type.description,
                    count: Customer.countByType(type)
                ]
            }
            
            // By status
            statistics.byStatus = ConfigItemStatus.list().collect { status ->
                [
                    status: status.description,
                    count: Customer.countByStatus(status)
                ]
            }
            
            // By branch
            if (!filters.branchId) {
                statistics.byBranch = Branch.list().collect { branch ->
                    [
                        branch: branch.name,
                        count: Customer.countByBranch(branch)
                    ]
                }
            }
            
            // Recent registrations
            Date lastWeek = new Date() - 7
            statistics.newCustomersThisWeek = Customer.countByDateCreatedGreaterThan(lastWeek)
            
            Date lastMonth = new Date() - 30
            statistics.newCustomersThisMonth = Customer.countByDateCreatedGreaterThan(lastMonth)
            
        } catch (Exception e) {
            log.error("Error generating customer statistics", e)
            statistics.error = "Error generating customer statistics"
        }
        
        return statistics
    }
    
    /**
     * Get content type for export format
     */
    private String getContentTypeForFormat(String format) {
        switch (format.toUpperCase()) {
            case 'JSON':
                return 'application/json'
            case 'XML':
                return 'application/xml'
            case 'CSV':
                return 'text/csv'
            case 'EXCEL':
                return 'application/vnd.ms-excel'
            case 'PDF':
                return 'application/pdf'
            default:
                return 'application/octet-stream'
        }
    }
    
    /**
     * Calculate age from birth date
     */
    private Integer calculateAge(Date birthDate) {
        try {
            if (!birthDate) {
                return 0
            }
            
            Calendar birthCalendar = Calendar.getInstance()
            birthCalendar.setTime(birthDate)
            
            Calendar todayCalendar = Calendar.getInstance()
            
            int age = todayCalendar.get(Calendar.YEAR) - birthCalendar.get(Calendar.YEAR)
            
            if (todayCalendar.get(Calendar.DAY_OF_YEAR) < birthCalendar.get(Calendar.DAY_OF_YEAR)) {
                age--
            }
            
            return age
            
        } catch (Exception e) {
            log.error("Error calculating age", e)
            return 0
        }
    }
    
    /**
     * Get age group
     */
    private String getAgeGroup(Integer age) {
        if (age < 18) {
            return 'MINOR'
        } else if (age < 25) {
            return 'YOUNG_ADULT'
        } else if (age < 35) {
            return 'ADULT'
        } else if (age < 50) {
            return 'MIDDLE_AGED'
        } else if (age < 65) {
            return 'MATURE'
        } else {
            return 'SENIOR'
        }
    }
    
    /**
     * Audit customer data export
     */
    private void auditCustomerDataExport(Customer customer, String format, String result) {
        try {
            auditLogService.insert('120', 'CUC07000', 
                "Customer data exported - Customer: ${customer.id}, Format: ${format}, Result: ${result}", 
                'CustomerUtilityController', null, null, null, customer.id)
        } catch (Exception e) {
            log.error("Error auditing customer data export", e)
        }
    }
}
