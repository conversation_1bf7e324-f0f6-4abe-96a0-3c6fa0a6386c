package org.icbs.cif

import org.icbs.lov.*
import org.icbs.admin.UserMaster
import org.icbs.loans.Loan
import org.icbs.deposit.Deposit
import static org.springframework.http.HttpStatus.*
import grails.converters.JSON
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import groovy.util.logging.Slf4j
import groovy.sql.Sql

/**
 * REFACTORED: Customer Relationship Controller
 * Extracted from CustomerController.groovy (1,058+ lines)
 * Handles all customer relationship management operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Slf4j
class CustomerRelationshipController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def customerService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        getCustomerRelationship: "GET",
        getAccountSummary: "GET",
        getTransactionHistory: "GET",
        getCustomerHierarchy: "GET",
        linkCustomers: "POST",
        unlinkCustomers: "POST"
    ]
    
    // =====================================================
    // CUSTOMER RELATIONSHIP OPERATIONS
    // =====================================================
    
    /**
     * Get customer relationship overview
     */
    def getCustomerRelationship() {
        log.info("Getting customer relationship overview: ${params.customerId}")
        
        try {
            if (!params.customerId) {
                render([error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([error: 'Customer not found'] as JSON)
                return
            }
            
            Map relationshipData = generateRelationshipOverview(customer)
            
            // Audit logging
            auditLogService.insert('120', 'CRC06100', 
                "Customer relationship overview accessed - Customer: ${customer.id}", 
                'CustomerRelationshipController', null, null, 'cif/getCustomerRelationship', customer.id)
            
            render([
                success: true,
                customer: [
                    id: customer.id,
                    name: customer.displayName ?: "${customer.name1} ${customer.name2}".trim(),
                    type: customer.type?.description,
                    status: customer.status?.description
                ],
                relationship: relationshipData
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting customer relationship", e)
            render([error: 'Error getting customer relationship'] as JSON)
        }
    }
    
    /**
     * Get customer account summary
     */
    def getAccountSummary() {
        log.debug("Getting customer account summary: ${params.customerId}")
        
        try {
            if (!params.customerId) {
                render([error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([error: 'Customer not found'] as JSON)
                return
            }
            
            Map accountSummary = generateAccountSummary(customer)
            
            render([
                success: true,
                accountSummary: accountSummary
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting customer account summary", e)
            render([error: 'Error getting account summary'] as JSON)
        }
    }
    
    /**
     * Get customer transaction history
     */
    def getTransactionHistory() {
        log.debug("Getting customer transaction history: ${params.customerId}")
        
        try {
            if (!params.customerId) {
                render([error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([error: 'Customer not found'] as JSON)
                return
            }
            
            Date fromDate = params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 30
            Date toDate = params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date()
            Integer limit = params.limit ? params.limit.toInteger() : 50
            
            Map transactionHistory = generateTransactionHistory(customer, fromDate, toDate, limit)
            
            render([
                success: true,
                transactionHistory: transactionHistory
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting customer transaction history", e)
            render([error: 'Error getting transaction history'] as JSON)
        }
    }
    
    /**
     * Get customer hierarchy
     */
    def getCustomerHierarchy() {
        log.debug("Getting customer hierarchy: ${params.customerId}")
        
        try {
            if (!params.customerId) {
                render([error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([error: 'Customer not found'] as JSON)
                return
            }
            
            Map hierarchy = generateCustomerHierarchy(customer)
            
            render([
                success: true,
                hierarchy: hierarchy
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting customer hierarchy", e)
            render([error: 'Error getting customer hierarchy'] as JSON)
        }
    }
    
    /**
     * Link customers
     */
    def linkCustomers() {
        log.info("Linking customers: ${params.primaryCustomerId} -> ${params.secondaryCustomerId}")
        
        try {
            if (!params.primaryCustomerId || !params.secondaryCustomerId) {
                render([success: false, error: 'Primary and secondary customer IDs are required'] as JSON)
                return
            }
            
            Customer primaryCustomer = Customer.get(params.primaryCustomerId)
            Customer secondaryCustomer = Customer.get(params.secondaryCustomerId)
            
            if (!primaryCustomer || !secondaryCustomer) {
                render([success: false, error: 'One or both customers not found'] as JSON)
                return
            }
            
            // Validate linking
            Map validationResult = validateCustomerLinking(primaryCustomer, secondaryCustomer, params.relationshipType)
            if (!validationResult.isValid) {
                render([success: false, error: validationResult.message] as JSON)
                return
            }
            
            // Create customer relationship
            Map linkResult = customerService.linkCustomers(primaryCustomer, secondaryCustomer, params.relationshipType, params.comments)
            
            if (linkResult.success) {
                // Audit logging
                auditCustomerLinking(primaryCustomer, secondaryCustomer, params.relationshipType, 'SUCCESS')
                
                render([success: true, message: 'Customers linked successfully'] as JSON)
            } else {
                render([success: false, error: linkResult.message] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error linking customers", e)
            render([success: false, error: 'Error linking customers'] as JSON)
        }
    }
    
    /**
     * Unlink customers
     */
    def unlinkCustomers() {
        log.info("Unlinking customers: ${params.relationshipId}")
        
        try {
            if (!params.relationshipId) {
                render([success: false, error: 'Relationship ID is required'] as JSON)
                return
            }
            
            CustomerRelationship relationship = CustomerRelationship.get(params.relationshipId)
            if (!relationship) {
                render([success: false, error: 'Relationship not found'] as JSON)
                return
            }
            
            // Validate unlinking
            Map validationResult = validateCustomerUnlinking(relationship)
            if (!validationResult.isValid) {
                render([success: false, error: validationResult.message] as JSON)
                return
            }
            
            // Remove customer relationship
            Map unlinkResult = customerService.unlinkCustomers(relationship, params.reason)
            
            if (unlinkResult.success) {
                // Audit logging
                auditCustomerUnlinking(relationship, params.reason, 'SUCCESS')
                
                render([success: true, message: 'Customers unlinked successfully'] as JSON)
            } else {
                render([success: false, error: unlinkResult.message] as JSON)
            }
            
        } catch (Exception e) {
            log.error("Error unlinking customers", e)
            render([success: false, error: 'Error unlinking customers'] as JSON)
        }
    }
    
    /**
     * Get customer profitability analysis
     */
    def getCustomerProfitability() {
        log.debug("Getting customer profitability analysis: ${params.customerId}")
        
        try {
            if (!params.customerId) {
                render([error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([error: 'Customer not found'] as JSON)
                return
            }
            
            Date fromDate = params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 365
            Date toDate = params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date()
            
            Map profitabilityAnalysis = generateProfitabilityAnalysis(customer, fromDate, toDate)
            
            render([
                success: true,
                profitability: profitabilityAnalysis
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting customer profitability", e)
            render([error: 'Error getting customer profitability'] as JSON)
        }
    }
    
    /**
     * Get customer risk assessment
     */
    def getCustomerRiskAssessment() {
        log.debug("Getting customer risk assessment: ${params.customerId}")
        
        try {
            if (!params.customerId) {
                render([error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([error: 'Customer not found'] as JSON)
                return
            }
            
            Map riskAssessment = generateRiskAssessment(customer)
            
            render([
                success: true,
                riskAssessment: riskAssessment
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting customer risk assessment", e)
            render([error: 'Error getting customer risk assessment'] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Generate relationship overview
     */
    private Map generateRelationshipOverview(Customer customer) {
        Map overview = [:]
        
        try {
            // Account summary
            overview.accounts = generateAccountSummary(customer)
            
            // Relationship value
            overview.relationshipValue = calculateRelationshipValue(customer)
            
            // Customer tenure
            overview.tenure = calculateCustomerTenure(customer)
            
            // Product penetration
            overview.productPenetration = calculateProductPenetration(customer)
            
            // Related customers
            overview.relatedCustomers = getRelatedCustomers(customer)
            
            // Recent activity
            overview.recentActivity = getRecentActivity(customer)
            
        } catch (Exception e) {
            log.error("Error generating relationship overview", e)
            overview.error = "Error generating relationship overview"
        }
        
        return overview
    }
    
    /**
     * Generate account summary
     */
    private Map generateAccountSummary(Customer customer) {
        Map summary = [:]
        
        try {
            // Loan accounts
            def loans = Loan.findAllByCustomer(customer)
            summary.loans = [
                count: loans.size(),
                totalBalance: loans.sum { it.balanceAmount } ?: 0,
                active: loans.count { it.status?.id in [2, 3] },
                overdue: loans.count { it.status?.id == 5 }
            ]
            
            // Deposit accounts
            def deposits = Deposit.findAllByCustomer(customer)
            summary.deposits = [
                count: deposits.size(),
                totalBalance: deposits.sum { it.balanceAmount } ?: 0,
                active: deposits.count { it.statusId in [2, 3] }
            ]
            
            // Total relationship
            summary.totalRelationship = (summary.loans.totalBalance ?: 0) + (summary.deposits.totalBalance ?: 0)
            
            // Account details
            summary.loanDetails = loans.collect {
                [
                    id: it.id,
                    accountNo: it.accountNo,
                    product: it.product?.name,
                    balance: it.balanceAmount,
                    status: it.status?.description
                ]
            }
            
            summary.depositDetails = deposits.collect {
                [
                    id: it.id,
                    accountNo: it.accountNo,
                    product: it.product?.name,
                    balance: it.balanceAmount,
                    status: it.status?.description
                ]
            }
            
        } catch (Exception e) {
            log.error("Error generating account summary", e)
            summary.error = "Error generating account summary"
        }
        
        return summary
    }
    
    /**
     * Generate transaction history
     */
    private Map generateTransactionHistory(Customer customer, Date fromDate, Date toDate, Integer limit) {
        Map history = [:]
        
        try {
            def sql = new Sql(dataSource)
            
            def query = """
                SELECT t.txn_date, t.txn_amt, t.txn_particulars, 
                       tt.description as txn_type, t.txn_ref, t.status_id,
                       CASE WHEN l.id IS NOT NULL THEN l.account_no
                            WHEN d.id IS NOT NULL THEN d.account_no
                            ELSE 'N/A' END as account_no
                FROM txn_file t
                JOIN txn_type tt ON t.txn_type_id = tt.id
                LEFT JOIN loan l ON t.loan_acct_id = l.id
                LEFT JOIN deposit d ON t.deposit_acct_id = d.id
                WHERE t.customer_id = ? 
                  AND t.txn_date BETWEEN ? AND ?
                ORDER BY t.txn_date DESC
                LIMIT ?
            """
            
            def transactions = sql.rows(query, [customer.id, fromDate, toDate, limit])
            
            history.transactions = transactions.collect {
                [
                    date: it.txn_date,
                    amount: it.txn_amt,
                    particulars: it.txn_particulars,
                    type: it.txn_type,
                    reference: it.txn_ref,
                    accountNo: it.account_no,
                    status: it.status_id == 2 ? 'Completed' : 'Pending'
                ]
            }
            
            history.totalCount = transactions.size()
            history.totalAmount = transactions.sum { it.txn_amt } ?: 0
            
            // Transaction summary by type
            history.byType = transactions.groupBy { it.txn_type }.collectEntries { type, txns ->
                [type, [count: txns.size(), amount: txns.sum { it.txn_amt } ?: 0]]
            }
            
        } catch (Exception e) {
            log.error("Error generating transaction history", e)
            history.error = "Error generating transaction history"
        }
        
        return history
    }
    
    /**
     * Generate customer hierarchy
     */
    private Map generateCustomerHierarchy(Customer customer) {
        Map hierarchy = [:]
        
        try {
            // Primary relationships (where this customer is primary)
            def primaryRelationships = CustomerRelationship.findAllByPrimaryCustomer(customer)
            hierarchy.dependents = primaryRelationships.collect {
                [
                    id: it.secondaryCustomer.id,
                    name: "${it.secondaryCustomer.name1} ${it.secondaryCustomer.name2}".trim(),
                    relationshipType: it.relationshipType?.description,
                    status: it.status?.description
                ]
            }
            
            // Secondary relationships (where this customer is secondary)
            def secondaryRelationships = CustomerRelationship.findAllBySecondaryCustomer(customer)
            hierarchy.guardians = secondaryRelationships.collect {
                [
                    id: it.primaryCustomer.id,
                    name: "${it.primaryCustomer.name1} ${it.primaryCustomer.name2}".trim(),
                    relationshipType: it.relationshipType?.description,
                    status: it.status?.description
                ]
            }
            
            // Joint account holders
            hierarchy.jointAccountHolders = getJointAccountHolders(customer)
            
            // Guarantors and guarantees
            hierarchy.guarantors = getGuarantors(customer)
            hierarchy.guarantees = getGuarantees(customer)
            
        } catch (Exception e) {
            log.error("Error generating customer hierarchy", e)
            hierarchy.error = "Error generating customer hierarchy"
        }
        
        return hierarchy
    }
    
    /**
     * Calculate relationship value
     */
    private BigDecimal calculateRelationshipValue(Customer customer) {
        try {
            BigDecimal loanBalance = Loan.findAllByCustomer(customer).sum { it.balanceAmount } ?: 0
            BigDecimal depositBalance = Deposit.findAllByCustomer(customer).sum { it.balanceAmount } ?: 0
            return loanBalance + depositBalance
        } catch (Exception e) {
            log.error("Error calculating relationship value", e)
            return BigDecimal.ZERO
        }
    }
    
    /**
     * Calculate customer tenure
     */
    private Map calculateCustomerTenure(Customer customer) {
        Map tenure = [:]
        
        try {
            Date today = new Date()
            long diffInMillis = today.time - customer.dateCreated.time
            int days = (int) (diffInMillis / (24 * 60 * 60 * 1000))
            int years = days / 365
            int months = (days % 365) / 30
            
            tenure.totalDays = days
            tenure.years = years
            tenure.months = months
            tenure.description = "${years} years, ${months} months"
            
        } catch (Exception e) {
            log.error("Error calculating customer tenure", e)
            tenure.error = "Error calculating customer tenure"
        }
        
        return tenure
    }
    
    /**
     * Calculate product penetration
     */
    private Map calculateProductPenetration(Customer customer) {
        Map penetration = [:]
        
        try {
            def totalProducts = Product.count()
            def customerProducts = []
            
            // Add loan products
            Loan.findAllByCustomer(customer).each {
                if (it.product && !customerProducts.contains(it.product.id)) {
                    customerProducts << it.product.id
                }
            }
            
            // Add deposit products
            Deposit.findAllByCustomer(customer).each {
                if (it.product && !customerProducts.contains(it.product.id)) {
                    customerProducts << it.product.id
                }
            }
            
            penetration.totalProducts = totalProducts
            penetration.customerProducts = customerProducts.size()
            penetration.penetrationRate = totalProducts > 0 ? 
                (customerProducts.size() / totalProducts * 100).setScale(2) : 0
            
        } catch (Exception e) {
            log.error("Error calculating product penetration", e)
            penetration.error = "Error calculating product penetration"
        }
        
        return penetration
    }
    
    /**
     * Get related customers
     */
    private List getRelatedCustomers(Customer customer) {
        try {
            def related = []
            
            // Primary relationships
            CustomerRelationship.findAllByPrimaryCustomer(customer).each {
                related << [
                    id: it.secondaryCustomer.id,
                    name: "${it.secondaryCustomer.name1} ${it.secondaryCustomer.name2}".trim(),
                    relationship: it.relationshipType?.description,
                    role: 'Dependent'
                ]
            }
            
            // Secondary relationships
            CustomerRelationship.findAllBySecondaryCustomer(customer).each {
                related << [
                    id: it.primaryCustomer.id,
                    name: "${it.primaryCustomer.name1} ${it.primaryCustomer.name2}".trim(),
                    relationship: it.relationshipType?.description,
                    role: 'Guardian'
                ]
            }
            
            return related
            
        } catch (Exception e) {
            log.error("Error getting related customers", e)
            return []
        }
    }
    
    /**
     * Get recent activity
     */
    private List getRecentActivity(Customer customer) {
        try {
            def sql = new Sql(dataSource)
            
            def query = """
                SELECT t.txn_date, t.txn_particulars, tt.description as txn_type
                FROM txn_file t
                JOIN txn_type tt ON t.txn_type_id = tt.id
                WHERE t.customer_id = ?
                ORDER BY t.txn_date DESC
                LIMIT 5
            """
            
            return sql.rows(query, [customer.id])
            
        } catch (Exception e) {
            log.error("Error getting recent activity", e)
            return []
        }
    }
    
    // Additional helper methods
    private List getJointAccountHolders(Customer customer) { return [] }
    private List getGuarantors(Customer customer) { return [] }
    private List getGuarantees(Customer customer) { return [] }
    
    private Map generateProfitabilityAnalysis(Customer customer, Date fromDate, Date toDate) {
        // Implement profitability analysis logic
        return [totalRevenue: 0, totalCosts: 0, netProfit: 0]
    }
    
    private Map generateRiskAssessment(Customer customer) {
        // Implement risk assessment logic
        return [riskScore: 50, riskLevel: 'MEDIUM', factors: []]
    }
    
    private Map validateCustomerLinking(Customer primary, Customer secondary, String relationshipType) {
        return [isValid: true, message: '']
    }
    
    private Map validateCustomerUnlinking(CustomerRelationship relationship) {
        return [isValid: true, message: '']
    }
    
    // Audit methods
    private void auditCustomerLinking(Customer primary, Customer secondary, String relationshipType, String result) {
        try {
            auditLogService.insert('120', 'CRC06000', 
                "Customers linked - Primary: ${primary.id}, Secondary: ${secondary.id}, Type: ${relationshipType}, Result: ${result}", 
                'CustomerRelationshipController', null, null, null, primary.id)
        } catch (Exception e) {
            log.error("Error auditing customer linking", e)
        }
    }
    
    private void auditCustomerUnlinking(CustomerRelationship relationship, String reason, String result) {
        try {
            auditLogService.insert('120', 'CRC06100', 
                "Customers unlinked - Relationship: ${relationship.id}, Reason: ${reason}, Result: ${result}", 
                'CustomerRelationshipController', null, null, null, relationship.id)
        } catch (Exception e) {
            log.error("Error auditing customer unlinking", e)
        }
    }
}
