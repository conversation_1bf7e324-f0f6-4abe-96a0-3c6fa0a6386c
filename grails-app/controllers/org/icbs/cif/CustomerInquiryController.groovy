package org.icbs.cif

import org.icbs.lov.*
import org.icbs.admin.UserMaster
import static org.springframework.http.HttpStatus.*
import grails.converters.JSON
import groovy.sql.Sql
import org.icbs.loans.Loan
import org.icbs.deposit.Deposit
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import groovy.util.logging.Slf4j

/**
 * REFACTORED: Customer Inquiry Controller
 * Extracted from CustomerController.groovy (1,058+ lines)
 * Handles all customer search and inquiry operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Slf4j
class CustomerInquiryController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def customerService
    def auditLogService
    def dataSource
    
    static allowedMethods = [
        index: "GET",
        show: "GET",
        customerSearch: "GET",
        search: "GET",
        customerInfobase: "GET"
    ]
    
    // =====================================================
    // CUSTOMER INQUIRY OPERATIONS
    // =====================================================
    
    /**
     * Customer listing page
     */
    def index(Integer max) {
        log.info("Accessing customer listing page")
        
        try {
            params.max = Math.min(max ?: 25, 100)
            
            if (params.sort == null) {
                params.sort = "id"
                params.order = "desc"
            }
            
            List<Customer> customers
            Integer customerCount
            
            if (params.query == null || params.query.trim() == "") {
                // Show all customers
                customers = Customer.list(params)
                customerCount = Customer.count()
            } else {
                // Show search results
                Map searchResult = performCustomerSearch(params.query, params)
                customers = searchResult.customers
                customerCount = searchResult.totalCount
            }
            
            // Audit logging
            auditLogService.insert('120', 'CIC02100', 
                "Customer listing accessed - Count: ${customerCount}", 
                'CustomerInquiryController', null, null, 'cif/index', session.user_id)
            
            respond customers, model: [
                params: params, 
                customerInstanceCount: customerCount
            ]
            
        } catch (Exception e) {
            log.error("Error accessing customer listing", e)
            flash.message = 'Error accessing customer listing|error|alert'
            render(view: '/error')
        }
    }
    
    /**
     * Show customer details
     */
    def show(Customer customerInstance) {
        log.info("Showing customer details: ${customerInstance?.id}")
        
        try {
            if (!customerInstance) {
                notFound()
                return
            }
            
            // Get customer summary data
            Map customerSummary = generateCustomerSummary(customerInstance)
            
            // Audit logging
            auditLogService.insert('120', 'CIC02200', 
                "Customer details viewed - Customer: ${customerInstance.id}", 
                'CustomerInquiryController', null, null, 'cif/show', customerInstance.id)
            
            respond customerInstance, model: [
                customerSummary: customerSummary
            ]
            
        } catch (Exception e) {
            log.error("Error showing customer details", e)
            flash.message = 'Error accessing customer details|error|alert'
            redirect(action: 'index')
        }
    }
    
    /**
     * Customer search page
     */
    def customerSearch() {
        log.info("Accessing customer search page")
        
        try {
            params.actionTemplate = 'customerInquirySearchAction'
            
            // Audit logging
            auditLogService.insert('120', 'CIC02300', 
                "Customer search page accessed", 
                'CustomerInquiryController', null, null, 'cif/customerSearch', session.user_id)
            
            render(view: 'search/customerSearch', model: [params: params])
            
        } catch (Exception e) {
            log.error("Error accessing customer search page", e)
            flash.message = 'Error accessing customer search|error|alert'
            redirect(action: 'index')
        }
    }
    
    /**
     * Advanced customer search
     */
    def search(Integer max) {
        log.debug("Performing advanced customer search")
        
        try {
            params.max = Math.min(max ?: 25, 100)
            
            Map searchResult = performAdvancedCustomerSearch(params)
            
            // Audit logging
            auditLogService.insert('120', 'CIC02400', 
                "Advanced customer search performed - Results: ${searchResult.totalCount}", 
                'CustomerInquiryController', null, null, 'cif/search', session.user_id)
            
            respond searchResult.customers, model: [
                params: params,
                customerInstanceCount: searchResult.totalCount,
                searchCriteria: searchResult.criteria
            ]
            
        } catch (Exception e) {
            log.error("Error performing customer search", e)
            flash.message = 'Error performing customer search|error|alert'
            redirect(action: 'index')
        }
    }
    
    /**
     * Customer infobase
     */
    def customerInfobase(Customer customerInstance) {
        log.info("Accessing customer infobase: ${customerInstance?.id}")
        
        try {
            if (!customerInstance) {
                notFound()
                return
            }
            
            def result = CustomerInfobase.createCriteria().list {
                and {
                    eq("status", ConfigItemStatus.get(2))
                    eq("customer", customerInstance)
                }
            }
            
            // Audit logging
            auditLogService.insert('120', 'CIC02500', 
                "Customer infobase accessed - Customer: ${customerInstance.id}", 
                'CustomerInquiryController', null, null, 'cif/customerInfobase', customerInstance.id)
            
            render(view: 'customerInfobase', model: [
                customerInstance: customerInstance,
                result: result
            ])
            
        } catch (Exception e) {
            log.error("Error accessing customer infobase", e)
            flash.message = 'Error accessing customer infobase|error|alert'
            redirect(action: 'show', id: customerInstance?.id)
        }
    }
    
    /**
     * Get customer accounts summary
     */
    def getCustomerAccountsSummary() {
        log.debug("Getting customer accounts summary")
        
        try {
            if (!params.customerId) {
                render([error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([error: 'Customer not found'] as JSON)
                return
            }
            
            Map accountsSummary = generateAccountsSummary(customer)
            
            render(accountsSummary as JSON)
            
        } catch (Exception e) {
            log.error("Error getting customer accounts summary", e)
            render([error: 'Error getting accounts summary'] as JSON)
        }
    }
    
    /**
     * Get customer transaction history
     */
    def getCustomerTransactionHistory() {
        log.debug("Getting customer transaction history")
        
        try {
            if (!params.customerId) {
                render([error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([error: 'Customer not found'] as JSON)
                return
            }
            
            Date fromDate = params.fromDate ? Date.parse('yyyy-MM-dd', params.fromDate) : new Date() - 30
            Date toDate = params.toDate ? Date.parse('yyyy-MM-dd', params.toDate) : new Date()
            
            Map transactionHistory = generateTransactionHistory(customer, fromDate, toDate)
            
            render(transactionHistory as JSON)
            
        } catch (Exception e) {
            log.error("Error getting customer transaction history", e)
            render([error: 'Error getting transaction history'] as JSON)
        }
    }
    
    /**
     * Quick customer lookup
     */
    def quickLookup() {
        log.debug("Performing quick customer lookup")
        
        try {
            if (!params.query || params.query.trim().length() < 2) {
                render([customers: []] as JSON)
                return
            }
            
            List<Customer> customers = Customer.createCriteria().list {
                or {
                    ilike("name1", "%${params.query.trim()}%")
                    ilike("name2", "%${params.query.trim()}%")
                    ilike("displayName", "%${params.query.trim()}%")
                    if (params.query.trim().isNumber()) {
                        eq("id", params.query.trim().toLong())
                    }
                }
                maxResults(10)
                order("name1", "asc")
            }
            
            List<Map> customerData = customers.collect { customer ->
                [
                    id: customer.id,
                    name: customer.displayName ?: "${customer.name1} ${customer.name2}".trim(),
                    type: customer.type?.description,
                    status: customer.status?.description
                ]
            }
            
            render([customers: customerData] as JSON)
            
        } catch (Exception e) {
            log.error("Error performing quick customer lookup", e)
            render([customers: []] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Perform basic customer search
     */
    private Map performCustomerSearch(String query, Map params) {
        Map result = [customers: [], totalCount: 0]
        
        try {
            def criteria = Customer.createCriteria()
            def customers = criteria.list(params) {
                or {
                    ilike("name1", "%${query.trim()}%")
                    ilike("name2", "%${query.trim()}%")
                    ilike("name3", "%${query.trim()}%")
                    ilike("displayName", "%${query.trim()}%")
                    if (query.trim().isNumber()) {
                        eq("id", query.trim().toLong())
                    }
                }
            }
            
            result.customers = customers
            result.totalCount = customers.totalCount
            
        } catch (Exception e) {
            log.error("Error performing customer search", e)
        }
        
        return result
    }
    
    /**
     * Perform advanced customer search
     */
    private Map performAdvancedCustomerSearch(Map params) {
        Map result = [customers: [], totalCount: 0, criteria: [:]]
        
        try {
            def criteria = Customer.createCriteria()
            def customers = criteria.list(params) {
                if (params.name1) {
                    ilike("name1", "%${params.name1}%")
                }
                if (params.name2) {
                    ilike("name2", "%${params.name2}%")
                }
                if (params.customerType) {
                    eq("type", CustomerType.get(params.customerType))
                }
                if (params.customerGroup) {
                    eq("group", CustomerGroup.get(params.customerGroup))
                }
                if (params.status) {
                    eq("status", ConfigItemStatus.get(params.status))
                }
                if (params.branch) {
                    eq("branch", Branch.get(params.branch))
                }
                if (params.fromDate && params.toDate) {
                    between("dateCreated", 
                           Date.parse('yyyy-MM-dd', params.fromDate),
                           Date.parse('yyyy-MM-dd', params.toDate))
                }
            }
            
            result.customers = customers
            result.totalCount = customers.totalCount
            result.criteria = params.subMap(['name1', 'name2', 'customerType', 'customerGroup', 'status', 'branch'])
            
        } catch (Exception e) {
            log.error("Error performing advanced customer search", e)
        }
        
        return result
    }
    
    /**
     * Generate customer summary
     */
    private Map generateCustomerSummary(Customer customer) {
        Map summary = [:]
        
        try {
            // Basic information
            summary.basicInfo = [
                id: customer.id,
                name: customer.displayName ?: "${customer.name1} ${customer.name2}".trim(),
                type: customer.type?.description,
                group: customer.group?.description,
                status: customer.status?.description,
                branch: customer.branch?.name
            ]
            
            // Account counts
            summary.accounts = generateAccountsSummary(customer)
            
            // Contact information
            summary.contacts = customer.contacts?.findAll { it.status?.id == 2 }?.collect {
                [type: it.type?.description, value: it.contactValue, isPrimary: it.isPrimaryPhone]
            }
            
            // Address information
            summary.addresses = customer.addresses?.findAll { it.status?.id == 2 }?.collect {
                [type: it.type?.description, address: it.fullAddress]
            }
            
        } catch (Exception e) {
            log.error("Error generating customer summary", e)
            summary.error = "Error generating customer summary"
        }
        
        return summary
    }
    
    /**
     * Generate accounts summary
     */
    private Map generateAccountsSummary(Customer customer) {
        Map summary = [:]
        
        try {
            // Loan accounts
            def loans = Loan.findAllByCustomer(customer)
            summary.loans = [
                count: loans.size(),
                totalBalance: loans.sum { it.balanceAmount } ?: 0,
                active: loans.count { it.status?.id in [2, 3] }
            ]
            
            // Deposit accounts
            def deposits = Deposit.findAllByCustomer(customer)
            summary.deposits = [
                count: deposits.size(),
                totalBalance: deposits.sum { it.balanceAmount } ?: 0,
                active: deposits.count { it.statusId in [2, 3] }
            ]
            
            summary.totalRelationship = (summary.loans.totalBalance ?: 0) + (summary.deposits.totalBalance ?: 0)
            
        } catch (Exception e) {
            log.error("Error generating accounts summary", e)
            summary.error = "Error generating accounts summary"
        }
        
        return summary
    }
    
    /**
     * Generate transaction history
     */
    private Map generateTransactionHistory(Customer customer, Date fromDate, Date toDate) {
        Map history = [:]
        
        try {
            def sql = new Sql(dataSource)
            
            // Get recent transactions
            def query = """
                SELECT t.txn_date, t.txn_amt, t.txn_particulars, 
                       tt.description as txn_type, t.txn_ref
                FROM txn_file t
                JOIN txn_type tt ON t.txn_type_id = tt.id
                WHERE t.customer_id = ? 
                  AND t.txn_date BETWEEN ? AND ?
                ORDER BY t.txn_date DESC
                LIMIT 50
            """
            
            def transactions = sql.rows(query, [customer.id, fromDate, toDate])
            
            history.transactions = transactions
            history.totalCount = transactions.size()
            history.totalAmount = transactions.sum { it.txn_amt } ?: 0
            
        } catch (Exception e) {
            log.error("Error generating transaction history", e)
            history.error = "Error generating transaction history"
        }
        
        return history
    }
    
    /**
     * Handle not found
     */
    protected void notFound() {
        request.withFormat {
            form multipartForm {
                flash.message = message(code: 'default.not.found.message', 
                                      args: [message(code: 'customer.label', default: 'Customer'), params.id])
                redirect action: "index", method: "GET"
            }
            '*' { render status: NOT_FOUND }
        }
    }
}
