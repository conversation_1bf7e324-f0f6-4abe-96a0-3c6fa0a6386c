package org.icbs.cif

import org.icbs.lov.*
import org.icbs.admin.UserMaster
import static org.springframework.http.HttpStatus.*
import grails.converters.JSON
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import groovy.util.logging.Slf4j

/**
 * REFACTORED: Customer Validation Controller
 * Extracted from CustomerController.groovy (1,058+ lines)
 * Handles all customer validation operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase III
 * @since Grails 6.2.3
 */
@Slf4j
class CustomerValidationController {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def customerService
    def auditLogService
    
    static allowedMethods = [
        validateCustomerData: "POST",
        validateDuplicateCustomer: "POST",
        validateIdNumber: "POST",
        validateContactInfo: "POST",
        validateEmploymentInfo: "POST",
        validateKycCompliance: "POST"
    ]
    
    // =====================================================
    // CUSTOMER VALIDATION OPERATIONS
    // =====================================================
    
    /**
     * Validate customer data
     */
    def validateCustomerData() {
        log.debug("Validating customer data")
        
        try {
            Map validationResult = unifiedValidationService.validateCustomerData(params)
            
            render([
                isValid: validationResult.isValid,
                errors: validationResult.errors,
                warnings: validationResult.warnings,
                fieldErrors: validationResult.fieldErrors
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error validating customer data", e)
            render([isValid: false, errors: ['Error validating customer data']] as JSON)
        }
    }
    
    /**
     * Validate duplicate customer
     */
    def validateDuplicateCustomer() {
        log.debug("Validating duplicate customer")
        
        try {
            Map duplicateResult = checkForDuplicateCustomers(params)
            
            render([
                hasDuplicates: duplicateResult.hasDuplicates,
                duplicates: duplicateResult.duplicates,
                matchScore: duplicateResult.matchScore,
                recommendations: duplicateResult.recommendations
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error validating duplicate customer", e)
            render([hasDuplicates: false, error: 'Error checking for duplicates'] as JSON)
        }
    }
    
    /**
     * Validate ID number
     */
    def validateIdNumber() {
        log.debug("Validating ID number: ${params.idNumber}")
        
        try {
            if (!params.idNumber || !params.idType) {
                render([isValid: false, error: 'ID number and type are required'] as JSON)
                return
            }
            
            Map validationResult = validateIdNumberFormat(params.idNumber, params.idType)
            
            if (validationResult.isValid) {
                // Check for existing customers with same ID
                Map duplicateCheck = checkIdNumberDuplicates(params.idNumber, params.idType)
                validationResult.putAll(duplicateCheck)
            }
            
            render(validationResult as JSON)
            
        } catch (Exception e) {
            log.error("Error validating ID number", e)
            render([isValid: false, error: 'Error validating ID number'] as JSON)
        }
    }
    
    /**
     * Validate contact information
     */
    def validateContactInfo() {
        log.debug("Validating contact information")
        
        try {
            Map validationResult = validateContactInformation(params)
            
            render([
                isValid: validationResult.isValid,
                errors: validationResult.errors,
                warnings: validationResult.warnings,
                suggestions: validationResult.suggestions
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error validating contact information", e)
            render([isValid: false, errors: ['Error validating contact information']] as JSON)
        }
    }
    
    /**
     * Validate employment information
     */
    def validateEmploymentInfo() {
        log.debug("Validating employment information")
        
        try {
            Map validationResult = validateEmploymentInformation(params)
            
            render([
                isValid: validationResult.isValid,
                errors: validationResult.errors,
                warnings: validationResult.warnings,
                creditworthiness: validationResult.creditworthiness
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error validating employment information", e)
            render([isValid: false, errors: ['Error validating employment information']] as JSON)
        }
    }
    
    /**
     * Validate KYC compliance
     */
    def validateKycCompliance() {
        log.debug("Validating KYC compliance for customer: ${params.customerId}")
        
        try {
            if (!params.customerId) {
                render([isCompliant: false, error: 'Customer ID is required'] as JSON)
                return
            }
            
            Customer customer = Customer.get(params.customerId)
            if (!customer) {
                render([isCompliant: false, error: 'Customer not found'] as JSON)
                return
            }
            
            Map kycResult = validateKycComplianceForCustomer(customer)
            
            render([
                isCompliant: kycResult.isCompliant,
                completionPercentage: kycResult.completionPercentage,
                missingDocuments: kycResult.missingDocuments,
                expiredDocuments: kycResult.expiredDocuments,
                recommendations: kycResult.recommendations
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error validating KYC compliance", e)
            render([isCompliant: false, error: 'Error validating KYC compliance'] as JSON)
        }
    }
    
    /**
     * Validate customer eligibility
     */
    def validateCustomerEligibility() {
        log.debug("Validating customer eligibility")
        
        try {
            Map eligibilityResult = validateEligibilityForProducts(params)
            
            render([
                isEligible: eligibilityResult.isEligible,
                eligibleProducts: eligibilityResult.eligibleProducts,
                ineligibleProducts: eligibilityResult.ineligibleProducts,
                requirements: eligibilityResult.requirements
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error validating customer eligibility", e)
            render([isEligible: false, error: 'Error validating customer eligibility'] as JSON)
        }
    }
    
    /**
     * Validate address information
     */
    def validateAddressInfo() {
        log.debug("Validating address information")
        
        try {
            Map validationResult = validateAddressInformation(params)
            
            render([
                isValid: validationResult.isValid,
                errors: validationResult.errors,
                warnings: validationResult.warnings,
                standardizedAddress: validationResult.standardizedAddress
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error validating address information", e)
            render([isValid: false, errors: ['Error validating address information']] as JSON)
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Check for duplicate customers
     */
    private Map checkForDuplicateCustomers(Map params) {
        Map result = [hasDuplicates: false, duplicates: [], matchScore: 0, recommendations: []]
        
        try {
            // Exact name match
            List<Customer> exactMatches = Customer.createCriteria().list {
                and {
                    eq("name1", params.name1)
                    eq("name2", params.name2)
                    if (params.birthDate) {
                        eq("birthDate", Date.parse('yyyy-MM-dd', params.birthDate))
                    }
                }
                maxResults(10)
            }
            
            if (exactMatches) {
                result.hasDuplicates = true
                result.matchScore = 100
                result.duplicates = exactMatches.collect { customer ->
                    [
                        id: customer.id,
                        name: "${customer.name1} ${customer.name2}".trim(),
                        birthDate: customer.birthDate,
                        status: customer.status?.description,
                        matchType: 'EXACT_MATCH'
                    ]
                }
                result.recommendations << "Exact match found. Please verify if this is the same customer."
            }
            
            // Fuzzy name match
            if (!result.hasDuplicates) {
                List<Customer> fuzzyMatches = findFuzzyMatches(params)
                if (fuzzyMatches) {
                    result.hasDuplicates = true
                    result.matchScore = 75
                    result.duplicates = fuzzyMatches.collect { customer ->
                        [
                            id: customer.id,
                            name: "${customer.name1} ${customer.name2}".trim(),
                            birthDate: customer.birthDate,
                            status: customer.status?.description,
                            matchType: 'FUZZY_MATCH'
                        ]
                    }
                    result.recommendations << "Similar customer found. Please verify if this is the same person."
                }
            }
            
        } catch (Exception e) {
            log.error("Error checking for duplicate customers", e)
        }
        
        return result
    }
    
    /**
     * Validate ID number format
     */
    private Map validateIdNumberFormat(String idNumber, String idType) {
        Map result = [isValid: true, errors: [], formattedId: '']
        
        try {
            switch (idType) {
                case 'CNIC':
                    result = validateCNICFormat(idNumber)
                    break
                case 'PASSPORT':
                    result = validatePassportFormat(idNumber)
                    break
                case 'DRIVING_LICENSE':
                    result = validateDrivingLicenseFormat(idNumber)
                    break
                default:
                    result = validateGenericIdFormat(idNumber)
            }
            
        } catch (Exception e) {
            log.error("Error validating ID number format", e)
            result.isValid = false
            result.errors << "Error validating ID number format"
        }
        
        return result
    }
    
    /**
     * Check ID number duplicates
     */
    private Map checkIdNumberDuplicates(String idNumber, String idType) {
        Map result = [hasDuplicates: false, existingCustomers: []]
        
        try {
            List<Customer> existingCustomers = []
            
            switch (idType) {
                case 'CNIC':
                    existingCustomers = Customer.findAllByCnicNo(idNumber)
                    break
                case 'PASSPORT':
                    existingCustomers = Customer.findAllByPassportNo(idNumber)
                    break
                case 'DRIVING_LICENSE':
                    existingCustomers = Customer.findAllByDrivingLicenseNo(idNumber)
                    break
            }
            
            if (existingCustomers) {
                result.hasDuplicates = true
                result.existingCustomers = existingCustomers.collect { customer ->
                    [
                        id: customer.id,
                        name: "${customer.name1} ${customer.name2}".trim(),
                        status: customer.status?.description
                    ]
                }
            }
            
        } catch (Exception e) {
            log.error("Error checking ID number duplicates", e)
        }
        
        return result
    }
    
    /**
     * Validate contact information
     */
    private Map validateContactInformation(Map params) {
        Map result = [isValid: true, errors: [], warnings: [], suggestions: []]
        
        try {
            // Validate phone numbers
            if (params.phoneNumber) {
                if (!isValidPhoneNumber(params.phoneNumber)) {
                    result.isValid = false
                    result.errors << "Invalid phone number format"
                }
            }
            
            // Validate email
            if (params.email) {
                if (!isValidEmail(params.email)) {
                    result.isValid = false
                    result.errors << "Invalid email format"
                }
            }
            
            // Check for duplicate contact information
            if (params.phoneNumber) {
                def existingCustomers = Customer.createCriteria().list {
                    contacts {
                        eq("contactValue", params.phoneNumber)
                    }
                }
                
                if (existingCustomers) {
                    result.warnings << "Phone number already exists for another customer"
                }
            }
            
        } catch (Exception e) {
            log.error("Error validating contact information", e)
            result.isValid = false
            result.errors << "Error validating contact information"
        }
        
        return result
    }
    
    /**
     * Validate employment information
     */
    private Map validateEmploymentInformation(Map params) {
        Map result = [isValid: true, errors: [], warnings: [], creditworthiness: 'UNKNOWN']
        
        try {
            // Validate monthly income
            if (params.monthlyIncome) {
                BigDecimal income = new BigDecimal(params.monthlyIncome)
                
                if (income <= 0) {
                    result.isValid = false
                    result.errors << "Monthly income must be greater than zero"
                } else {
                    // Assess creditworthiness based on income
                    if (income >= 100000) {
                        result.creditworthiness = 'HIGH'
                    } else if (income >= 50000) {
                        result.creditworthiness = 'MEDIUM'
                    } else {
                        result.creditworthiness = 'LOW'
                    }
                }
            }
            
            // Validate employment duration
            if (params.employmentStartDate) {
                Date startDate = Date.parse('yyyy-MM-dd', params.employmentStartDate)
                Date today = new Date()
                
                long diffInMillis = today.time - startDate.time
                int monthsEmployed = (int) (diffInMillis / (30L * 24 * 60 * 60 * 1000))
                
                if (monthsEmployed < 6) {
                    result.warnings << "Employment duration is less than 6 months"
                }
            }
            
        } catch (Exception e) {
            log.error("Error validating employment information", e)
            result.isValid = false
            result.errors << "Error validating employment information"
        }
        
        return result
    }
    
    /**
     * Validate KYC compliance for customer
     */
    private Map validateKycComplianceForCustomer(Customer customer) {
        Map result = [isCompliant: true, completionPercentage: 0, missingDocuments: [], expiredDocuments: [], recommendations: []]
        
        try {
            // Check required documents
            List<String> requiredDocs = ['CNIC_COPY', 'PHOTO', 'INCOME_PROOF', 'ADDRESS_PROOF']
            List<String> submittedDocs = customer.documents?.collect { it.documentType?.code } ?: []
            
            result.missingDocuments = requiredDocs - submittedDocs
            
            // Check expired documents
            customer.documents?.each { doc ->
                if (doc.expiryDate && doc.expiryDate < new Date()) {
                    result.expiredDocuments << doc.documentType?.description
                }
            }
            
            // Calculate completion percentage
            int totalRequired = requiredDocs.size()
            int submitted = submittedDocs.size()
            result.completionPercentage = totalRequired > 0 ? (submitted / totalRequired * 100).intValue() : 0
            
            // Determine compliance
            result.isCompliant = result.missingDocuments.isEmpty() && result.expiredDocuments.isEmpty()
            
            // Generate recommendations
            if (!result.isCompliant) {
                if (result.missingDocuments) {
                    result.recommendations << "Submit missing documents: ${result.missingDocuments.join(', ')}"
                }
                if (result.expiredDocuments) {
                    result.recommendations << "Renew expired documents: ${result.expiredDocuments.join(', ')}"
                }
            }
            
        } catch (Exception e) {
            log.error("Error validating KYC compliance", e)
            result.isCompliant = false
            result.recommendations << "Error validating KYC compliance"
        }
        
        return result
    }
    
    /**
     * Validate eligibility for products
     */
    private Map validateEligibilityForProducts(Map params) {
        Map result = [isEligible: true, eligibleProducts: [], ineligibleProducts: [], requirements: []]
        
        try {
            // This would contain business logic for product eligibility
            // Based on customer age, income, credit score, etc.
            
            result.eligibleProducts = ['SAVINGS_ACCOUNT', 'CURRENT_ACCOUNT']
            result.ineligibleProducts = ['CREDIT_CARD', 'PERSONAL_LOAN']
            result.requirements = ['Minimum age 18 years', 'Valid ID document', 'Proof of income']
            
        } catch (Exception e) {
            log.error("Error validating customer eligibility", e)
            result.isEligible = false
            result.requirements << "Error validating customer eligibility"
        }
        
        return result
    }
    
    /**
     * Validate address information
     */
    private Map validateAddressInformation(Map params) {
        Map result = [isValid: true, errors: [], warnings: [], standardizedAddress: '']
        
        try {
            // Validate required address fields
            if (!params.address1) {
                result.isValid = false
                result.errors << "Address line 1 is required"
            }
            
            if (!params.city) {
                result.isValid = false
                result.errors << "City is required"
            }
            
            // Standardize address format
            if (result.isValid) {
                result.standardizedAddress = "${params.address1}, ${params.city}".trim()
                if (params.state) {
                    result.standardizedAddress += ", ${params.state}"
                }
                if (params.postalCode) {
                    result.standardizedAddress += " ${params.postalCode}"
                }
            }
            
        } catch (Exception e) {
            log.error("Error validating address information", e)
            result.isValid = false
            result.errors << "Error validating address information"
        }
        
        return result
    }
    
    // Helper validation methods
    private List<Customer> findFuzzyMatches(Map params) {
        // Implement fuzzy matching logic
        return []
    }
    
    private Map validateCNICFormat(String cnic) {
        Map result = [isValid: true, errors: [], formattedId: '']
        
        String cleanCnic = cnic.replaceAll(/\D/, '')
        if (cleanCnic.length() != 13) {
            result.isValid = false
            result.errors << "CNIC must be 13 digits"
        } else {
            result.formattedId = "${cleanCnic.substring(0, 5)}-${cleanCnic.substring(5, 12)}-${cleanCnic.substring(12)}"
        }
        
        return result
    }
    
    private Map validatePassportFormat(String passport) {
        Map result = [isValid: true, errors: [], formattedId: passport?.toUpperCase()]
        
        if (!passport || passport.length() < 6 || passport.length() > 12) {
            result.isValid = false
            result.errors << "Passport number must be 6-12 characters"
        }
        
        return result
    }
    
    private Map validateDrivingLicenseFormat(String license) {
        Map result = [isValid: true, errors: [], formattedId: license?.toUpperCase()]
        
        if (!license || license.length() < 8 || license.length() > 15) {
            result.isValid = false
            result.errors << "Driving license must be 8-15 characters"
        }
        
        return result
    }
    
    private Map validateGenericIdFormat(String id) {
        Map result = [isValid: true, errors: [], formattedId: id]
        
        if (!id || id.trim().length() < 3) {
            result.isValid = false
            result.errors << "ID must be at least 3 characters"
        }
        
        return result
    }
    
    private boolean isValidPhoneNumber(String phoneNumber) {
        if (!phoneNumber) return false
        String cleanNumber = phoneNumber.replaceAll(/\D/, '')
        return cleanNumber.length() >= 10 && cleanNumber.length() <= 15
    }
    
    private boolean isValidEmail(String email) {
        if (!email) return false
        String emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
        return email.matches(emailPattern)
    }
}
