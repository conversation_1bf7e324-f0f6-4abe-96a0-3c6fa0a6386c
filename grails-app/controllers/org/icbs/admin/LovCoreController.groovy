package org.icbs.admin

import grails.converters.JSON
import static org.springframework.http.HttpStatus.*
import grails.gorm.transactions.Transactional
import org.icbs.lov.*

/**
 * LovCoreController - Core LOV (List of Values) maintenance controller
 * 
 * This controller manages core LOV operations including:
 * - Main LOV index and navigation
 * - Common LOV operations and utilities
 * - LOV search and inquiry functions
 * - Cross-LOV validation and helpers
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class LovCoreController {
    
    // Service Dependencies
    def auditLogService
    
    static allowedMethods = [
        index: "GET",
        search: "GET"
    ]

    /**
     * Main LOV maintenance index
     */
    def index() {
        def lovCategories = [
            [name: "Firm Size", controller: "firmSize", action: "sizeOfFirmIndex", count: FirmSize.count()],
            [name: "Business Type", controller: "businessType", action: "businessTypeIndex", count: BusinessType.count()],
            [name: "Civil Status", controller: "civilStatus", action: "civilStatusIndex", count: CivilStatus.count()],
            [name: "Educational Attainment", controller: "educationalAttainment", action: "educationalAttainmentIndex", count: EducationalAttainment.count()],
            [name: "Employment Status", controller: "employmentStatus", action: "employmentStatusIndex", count: EmploymentStatus.count()],
            [name: "Gender", controller: "gender", action: "genderIndex", count: Gender.count()],
            [name: "Loan Purpose", controller: "loanPurpose", action: "loanPurposeIndex", count: LoanPurpose.count()],
            [name: "Occupation", controller: "occupation", action: "occupationIndex", count: Occupation.count()],
            [name: "Relationship", controller: "relationship", action: "relationshipIndex", count: Relationship.count()],
            [name: "Source of Income", controller: "sourceOfIncome", action: "sourceOfIncomeIndex", count: SourceOfIncome.count()]
        ]
        
        def description = "LOV maintenance index accessed"
        auditLogService.insert('140', 'ADM00500', description, 'LovCore', null, null, null, null)
        
        render(view: "index", model: [lovCategories: lovCategories])
    }
    
    /**
     * Search across all LOV types
     */
    def search() {
        def query = params.query?.trim()
        def results = [:]
        
        if (query) {
            // Search Firm Size
            results.firmSize = FirmSize.createCriteria().list {
                or {
                    ilike("code", "%${query}%")
                    ilike("description", "%${query}%")
                }
                maxResults(5)
            }
            
            // Search Business Type
            results.businessType = BusinessType.createCriteria().list {
                or {
                    ilike("code", "%${query}%")
                    ilike("description", "%${query}%")
                }
                maxResults(5)
            }
            
            // Search Civil Status
            results.civilStatus = CivilStatus.createCriteria().list {
                or {
                    ilike("code", "%${query}%")
                    ilike("description", "%${query}%")
                }
                maxResults(5)
            }
            
            // Search Educational Attainment
            results.educationalAttainment = EducationalAttainment.createCriteria().list {
                or {
                    ilike("code", "%${query}%")
                    ilike("description", "%${query}%")
                }
                maxResults(5)
            }
            
            // Search Employment Status
            results.employmentStatus = EmploymentStatus.createCriteria().list {
                or {
                    ilike("code", "%${query}%")
                    ilike("description", "%${query}%")
                }
                maxResults(5)
            }
            
            // Search Gender
            results.gender = Gender.createCriteria().list {
                or {
                    ilike("code", "%${query}%")
                    ilike("description", "%${query}%")
                }
                maxResults(5)
            }
            
            // Search Loan Purpose
            results.loanPurpose = LoanPurpose.createCriteria().list {
                or {
                    ilike("code", "%${query}%")
                    ilike("description", "%${query}%")
                }
                maxResults(5)
            }
            
            // Search Occupation
            results.occupation = Occupation.createCriteria().list {
                or {
                    ilike("code", "%${query}%")
                    ilike("description", "%${query}%")
                }
                maxResults(5)
            }
            
            // Search Relationship
            results.relationship = Relationship.createCriteria().list {
                or {
                    ilike("code", "%${query}%")
                    ilike("description", "%${query}%")
                }
                maxResults(5)
            }
            
            // Search Source of Income
            results.sourceOfIncome = SourceOfIncome.createCriteria().list {
                or {
                    ilike("code", "%${query}%")
                    ilike("description", "%${query}%")
                }
                maxResults(5)
            }
        }
        
        render(view: "searchResults", model: [results: results, query: query])
    }
    
    /**
     * Get LOV statistics
     */
    def getStatistics() {
        def stats = [
            firmSize: FirmSize.count(),
            businessType: BusinessType.count(),
            civilStatus: CivilStatus.count(),
            educationalAttainment: EducationalAttainment.count(),
            employmentStatus: EmploymentStatus.count(),
            gender: Gender.count(),
            loanPurpose: LoanPurpose.count(),
            occupation: Occupation.count(),
            relationship: Relationship.count(),
            sourceOfIncome: SourceOfIncome.count()
        ]
        
        stats.total = stats.values().sum()
        
        render(stats as JSON)
        return
    }
    
    /**
     * Validate LOV code across all types
     */
    def validateCodeGlobal() {
        def code = params.code?.trim()
        def type = params.type
        def id = params.id?.toLong()
        
        if (!code || !type) {
            render([valid: false, message: "Code and type are required"] as JSON)
            return
        }
        
        def existing = null
        
        switch (type) {
            case "firmSize":
                existing = FirmSize.createCriteria().get {
                    eq("code", code)
                    if (id) ne("id", id)
                }
                break
            case "businessType":
                existing = BusinessType.createCriteria().get {
                    eq("code", code)
                    if (id) ne("id", id)
                }
                break
            case "civilStatus":
                existing = CivilStatus.createCriteria().get {
                    eq("code", code)
                    if (id) ne("id", id)
                }
                break
            case "educationalAttainment":
                existing = EducationalAttainment.createCriteria().get {
                    eq("code", code)
                    if (id) ne("id", id)
                }
                break
            case "employmentStatus":
                existing = EmploymentStatus.createCriteria().get {
                    eq("code", code)
                    if (id) ne("id", id)
                }
                break
            case "gender":
                existing = Gender.createCriteria().get {
                    eq("code", code)
                    if (id) ne("id", id)
                }
                break
            case "loanPurpose":
                existing = LoanPurpose.createCriteria().get {
                    eq("code", code)
                    if (id) ne("id", id)
                }
                break
            case "occupation":
                existing = Occupation.createCriteria().get {
                    eq("code", code)
                    if (id) ne("id", id)
                }
                break
            case "relationship":
                existing = Relationship.createCriteria().get {
                    eq("code", code)
                    if (id) ne("id", id)
                }
                break
            case "sourceOfIncome":
                existing = SourceOfIncome.createCriteria().get {
                    eq("code", code)
                    if (id) ne("id", id)
                }
                break
        }
        
        if (existing) {
            render([valid: false, message: "Code already exists in ${type}"] as JSON)
        } else {
            render([valid: true, message: "Code is available"] as JSON)
        }
        return
    }
    
    /**
     * Export LOV data
     */
    def exportData() {
        def type = params.type
        def format = params.format ?: "json"
        
        def data = []
        
        switch (type) {
            case "firmSize":
                data = FirmSize.list().collect { [id: it.id, code: it.code, description: it.description] }
                break
            case "businessType":
                data = BusinessType.list().collect { [id: it.id, code: it.code, description: it.description] }
                break
            case "civilStatus":
                data = CivilStatus.list().collect { [id: it.id, code: it.code, description: it.description] }
                break
            case "educationalAttainment":
                data = EducationalAttainment.list().collect { [id: it.id, code: it.code, description: it.description] }
                break
            case "employmentStatus":
                data = EmploymentStatus.list().collect { [id: it.id, code: it.code, description: it.description] }
                break
            case "gender":
                data = Gender.list().collect { [id: it.id, code: it.code, description: it.description] }
                break
            case "loanPurpose":
                data = LoanPurpose.list().collect { [id: it.id, code: it.code, description: it.description] }
                break
            case "occupation":
                data = Occupation.list().collect { [id: it.id, code: it.code, description: it.description] }
                break
            case "relationship":
                data = Relationship.list().collect { [id: it.id, code: it.code, description: it.description] }
                break
            case "sourceOfIncome":
                data = SourceOfIncome.list().collect { [id: it.id, code: it.code, description: it.description] }
                break
            default:
                data = [error: "Invalid type specified"]
        }
        
        if (format == "csv") {
            response.setContentType("text/csv")
            response.setHeader("Content-disposition", "attachment; filename=${type}_export.csv")
            
            def csv = "ID,Code,Description\n"
            data.each { item ->
                csv += "${item.id},${item.code},\"${item.description}\"\n"
            }
            render csv
        } else {
            render(data as JSON)
        }
        return
    }
}
