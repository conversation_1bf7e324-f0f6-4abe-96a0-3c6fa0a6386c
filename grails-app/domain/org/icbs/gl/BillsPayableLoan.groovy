package org.icbs.gl

import org.icbs.loans.Loan
import org.icbs.admin.UserMaster
import org.icbs.lov.ConfigItemStatus

class BillsPayableLoan {
    BillsPayable billsPayable
    Loan loan
    Date linkDate
    UserMaster user
    ConfigItemStatus status
    
    static constraints = {
        billsPayable nullable:true
        loan nullable:true
        linkDate nullable:true
        user nullable:true
        status nullable:true
    }
    
    static mapping = {
      id sqlType: "int", generator: "increment"
    }     
}
