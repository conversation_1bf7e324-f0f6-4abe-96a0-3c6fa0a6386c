package org.icbs.gl
import org.icbs.cif.Customer
import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.admin.Currency
import org.icbs.lov.ConfigItemStatus

class AccountsPayable {
    Customer customer
    Branch branch
    Currency currency
    String glContra
    Date bookingDate
    String payable
    String particulars
    Double balance
    Date dateCreated
    Date apCreatedDate
    UserMaster user
    String reference
    ConfigItemStatus status
    String acctNo
    static constraints = {
        acctNo nullable: true
        customer nullable: true  
        branch nullable:true
        currency nullable:true
        glContra nullable:true
        bookingDate nullable:true
        payable nullable:true, maxSize:50
        particulars nullable:true
        balance mid:0D, scale:2, nullable:true
        dateCreated nullable:true
        user nullable:true
        status nullable:true
        reference nullable:true
        apCreatedDate nullable:true
    }
    
    static mapping = {
      id sqlType: "int", generator: "increment"
    }    
}
