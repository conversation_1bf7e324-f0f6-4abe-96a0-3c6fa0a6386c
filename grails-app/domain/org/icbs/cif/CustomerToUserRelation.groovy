package org.icbs.cif
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.Lov
import org.icbs.lov.CustomerType
import org.icbs.lov.CustomerStatus
import org.icbs.lov.Gender
import org.icbs.admin.UserMaster
import org.icbs.cif.Customer
class CustomerToUserRelation {
 
    Customer customer
    UserMaster relateToUser
    Lov type
    ConfigItemStatus status
    
    static constraints = {
    customer nullable: true
    relateToUser nullable: true
    type nullable: true
    status nullable: true
    }
    static mapping = {
        id sqlType: "int", generator: "increment"
    }
}
