package org.icbs.cif
import org.icbs.admin.UserMaster
import org.icbs.lov.ConfigItemStatus
import org.icbs.cif.Customer


class CustomerInfobase {
    
    Customer customer
    String infoMessage
    Date refDate
    UserMaster user
    ConfigItemStatus status
    
    static constraints = {
        infoMessage nullable:false
        refDate nullable:false
        customer nullable:false
        user nullable:false
        status nullable:false
    }
    static mapping = {
        id sqlType: "int", generator: "increment"
    }      
}
