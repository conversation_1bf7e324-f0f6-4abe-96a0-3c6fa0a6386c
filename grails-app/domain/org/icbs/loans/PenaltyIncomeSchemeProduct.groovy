package org.icbs.loans

import org.icbs.admin.Product
import org.icbs.lov.ConfigItemStatus

class PenaltyIncomeSchemeProduct {	
	PenaltyIncomeScheme penaltyIncomeScheme
	Product product

	//ConfigItemStatus status

	//static belongsTo = [penaltyIncomeScheme: PenaltyIncomeScheme , product: Product]

    static mapping = {
    	version false
        id sqlType:'int', generator:'increment'
    	//id composite: ["penaltyIncomeScheme", "product"]
    	penaltyIncomeScheme sqlType: "int"
    	product sqlType: "int"
    }
}
