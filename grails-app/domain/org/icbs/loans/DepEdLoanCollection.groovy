package org.icbs.loans
import org.icbs.loans.Loan
import org.icbs.cif.Customer
import org.icbs.loans.DepEdLoanRemDet
import org.icbs.admin.Branch

class DepEdLoanCollection {
    String batchSerial
    DepEdLoanRemDet depEdLoanRemDet
    Loan loan
    Branch branch
    Customer customer
    Double paymentAmt
    
    static constraints = {
        batchSerial nullable:false
        depEdLoanRemDet nullable:false
        loan nullable:false
        branch nullable:false
        customer nullable:false
        paymentAmt nullable:true, min:0d, scale:2
    }
}
