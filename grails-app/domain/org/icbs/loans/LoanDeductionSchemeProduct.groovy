package org.icbs.loans

import org.icbs.admin.Product
import org.icbs.lov.ConfigItemStatus

class LoanDeductionSchemeProduct {	
	LoanDeductionScheme loanDeductionScheme
	Product product

	//ConfigItemStatus status

	//static belongsTo = [loanDeductionScheme: LoanDeductionScheme , product: Product]

    static mapping = {
    	version false
        id sqlType:'int', generator:'increment'
    	//id composite: ["loanDeductionScheme", "product"]
    	loanDeductionScheme sqlType: "int"
    	product sqlType: "int"
    }
}
