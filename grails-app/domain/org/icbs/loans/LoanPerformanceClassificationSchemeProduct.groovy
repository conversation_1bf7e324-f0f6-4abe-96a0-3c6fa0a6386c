package org.icbs.loans

import org.icbs.admin.Product
import org.icbs.lov.ConfigItemStatus

class LoanPerformanceClassificationSchemeProduct {	
	LoanPerformanceClassificationScheme loanPerformanceClassificationScheme
	Product product

	//ConfigItemStatus status

	//static belongsTo = [loanPerformanceClassification: LoanPerformanceClassification , product: Product]

    static mapping = {
    	version false
        id sqlType:'int', generator:'increment'
    	//id composite: ["loanPerformanceClassification", "product"]
    	loanPerformanceClassificationScheme sqlType: "int"
    	product sqlType: "int"
    }
}
