package org.icbs.loans

import org.icbs.deposit.Deposit
import org.icbs.admin.UserMaster
import org.icbs.lov.SweepType
import org.icbs.lov.SweepRule
import org.icbs.lov.SweepStatus
import org.icbs.loans.Loan

class LoanRecovery {
    Deposit depositAccount
    SweepType type
    SweepRule rule
    Double fundLimitAmt
    Double fundLimitPercentage
    
    String remarks
    SweepStatus status
    Loan fundedLoan
    Date dateCreated
    UserMaster createdBy

    static constraints = {
        depositAccount nullable:false
        type nullable:false
        rule nullable:false
        fundLimitAmt nullable:true, min: 0d, scale:2
        fundLimitPercentage nullable:true, min: 0d, scale:5
        remarks nullable:true
        status nullable:true
        fundedLoan nullable:true
        dateCreated nullable:true
        createdBy nullable:true
    }

    static mapping = {
    	id sqlType: "int", generator: "increment"
    }   
}
