package org.icbs.loans
import org.icbs.loans.Loan
import org.icbs.admin.TxnTemplate
import org.icbs.admin.UserMaster
import org.icbs.admin.Currency
import org.icbs.admin.Branch
import org.icbs.tellering.TxnFile
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.LoanAcctStatus
import java.sql.Timestamp
import org.icbs.loans.LoanWriteOffCollectors
import org.icbs.lov.WriteOffCollectionType

class LoanWriteOffCollectionHist {
    
    TxnFile txnFile
    Branch branch
    Currency currency
    Loan loan
    String txnDescription
    UserMaster transactBy
    Timestamp txnTimestamp
    ConfigItemStatus status
    Double txnAmount
    UserMaster collectedBy
    Date txnDate
    WriteOffCollectionType writeOffCollectionType
    static constraints = {
        txnFile nullable:true
        branch nullable:true
        currency nullable:true
        loan nullable:true
        txnDescription nullable:true
        transactBy nullable:true
        txnTimestamp nullable:true
        status nullable:true
        txnAmount nullable:true
        collectedBy nullable:true
        txnDate nullable:true
        writeOffCollectionType nullable:true
    }
    static mapping = {
        id sqlType: 'int', generator: 'increment'
        
    }
    
}
