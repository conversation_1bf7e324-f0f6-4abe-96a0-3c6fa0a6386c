package org.icbs.loans

import org.icbs.gl.GlAccount
import org.icbs.lov.LoanSpecialType

class LoanSpecial {
	LoanSpecialType type
	String action
	Date transferDate

	static hasMany = [litigationExpenses: LitigationExpense,
					  litigationDeficiencies: LitigationDeficiency,
					  ropaExpenses: ROPAExpense,
					  ropaExpenseAdjustments: ROPAExpenseAdjustment,
					  ropaExpenseCapitalizations: ROPAExpenseCapitalization,
					  ropaExpenseCapitalizationAdjustments: ROPAExpenseCapitalizationAdjustment,
					  ropaSellOffs: ROPASellOff]

	static constraints = {    	
		type nullable:true
		action nullable:true
		transferDate nullable:true
	}

    static mapping = {
		id sqlType: "int", generator: "increment"
	}
}