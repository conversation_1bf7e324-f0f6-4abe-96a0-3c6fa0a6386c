package org.icbs.loans

import org.icbs.loans.Loan
import org.icbs.cif.Customer
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.Currency
import org.icbs.lov.ConfigItemStatus

class ScrDiscountSchedule {
    
    Loan loan
    Date scrdateCreated
    Branch branch
    Double debitAmt
    Double creditAmt
    Date scheduleDate
    String reference
    String particulars
    UserMaster createdBy
    ConfigItemStatus status
    
    static constraints = {
        loan nullable:true
        scrdateCreated nullable:true
        branch nullable:true
        debitAmt nullable:true
        creditAmt nullable:true
        scheduleDate nullable:true
        reference nullable:true
        particulars nullable:true
        createdBy nullable:true
        status nullable:true
    }
    
    static mapping = {
        id sqlType: "int", generator: "increment"
    }
}
