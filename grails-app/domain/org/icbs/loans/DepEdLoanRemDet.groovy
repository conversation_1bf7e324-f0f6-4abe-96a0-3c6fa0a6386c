package org.icbs.loans

import org.icbs.cif.Customer
import org.icbs.loans.Loan
import org.icbs.admin.Branch
import org.icbs.gl.GlBatch

class DepEdLoanRemDet {
    
    Customer customer
    String batchSerial
    Double remAmount
    
    static constraints = {

        customer nullable:true
        batchSerial nullable:true
        remAmount nullable:true
    
    }
    
    static mapping = {
        id sqlType:'int', generator:'increment'
    }
}
