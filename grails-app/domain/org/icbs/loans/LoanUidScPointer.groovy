package org.icbs.loans

import org.icbs.loans.LoanDeductionScheme
import org.icbs.lov.ConfigItemStatus
class LoanUidScPointer {

    String description
    LoanDeductionScheme loanDeductionScheme
    ConfigItemStatus status
    
    static constraints = {
        description nullable:true
        loanDeductionScheme nullable:true
        status nullable:true
    }
    
    static mapping = {
      id sqlType: "int", generator: "increment"
    } 
}
