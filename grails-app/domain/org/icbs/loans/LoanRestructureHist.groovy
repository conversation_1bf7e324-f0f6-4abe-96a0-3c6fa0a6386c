package org.icbs.loans
import org.icbs.loans.Loan
import org.icbs.lov.ConfigItemStatus
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
class LoanRestructureHist {
    
    Loan loan
    Date restructuredDate
    Double amount
    ConfigItemStatus status
    UserMaster restructuredBy
    Branch branch
    
    static constraints = {
        loan nullable:true
        restructuredDate nullable:true
        amount nullable:true
        status nullable:true
        restructuredBy nullable:true
        branch nullable:true
    }
    static mapping = {
        id sqlType: "int", generator: "increment"
    }
}
