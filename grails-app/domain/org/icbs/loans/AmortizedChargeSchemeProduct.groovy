package org.icbs.loans

import org.icbs.admin.Product
import org.icbs.lov.ConfigItemStatus

class AmortizedChargeSchemeProduct {	
	AmortizedChargeScheme amortizedChargeScheme
	Product product

	//ConfigItemStatus status

	//static belongsTo = [amortizedChargeScheme: AmortizedChargeScheme , product: Product]

    static mapping = {
    	version false
    	//id composite: ["amortizedChargeScheme", "product"]
        id sqlType:'int', generator:'increment'
    	amortizedChargeScheme sqlType: "int"
    	product sqlType: "int"
    }
}
