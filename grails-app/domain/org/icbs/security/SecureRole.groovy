package org.icbs.security

import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString

/**
 * Modern Spring Security Role Domain Class
 * Implements role-based access control for banking systems
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 7.0
 */
// @Entity and @Table annotations removed for Grails 6.2.3 compatibility
@EqualsAndHashCode(includes='authority')
@ToString(includes='authority', includeNames=true, includePackage=false)
class SecureRole implements Serializable {
    
    private static final long serialVersionUID = 1
    
    // Core role fields
    String authority
    String description
    String category
    
    // Role hierarchy and permissions
    Integer level = 1
    Set<String> permissions = []
    Set<String> restrictions = []
    
    // Audit fields
    Date dateCreated
    Date lastUpdated
    String createdBy
    String updatedBy
    
    // Status fields
    boolean enabled = true
    boolean systemRole = false
    
    static hasMany = [
        users: SecureUser,
        childRoles: SecureRole
    ]
    
    static belongsTo = [parentRole: SecureRole]
    
    static constraints = {
        // Authority constraints (Spring Security standard)
        authority nullable: false, blank: false, unique: true, maxSize: 50,
                 matches: /^ROLE_[A-Z_]+$/,
                 validator: { val, obj ->
                     if (val && !val.startsWith('ROLE_')) {
                         return 'authority.must.start.with.role'
                     }
                     if (val && val.length() < 6) { // ROLE_ + at least 1 char
                         return 'authority.too.short'
                     }
                 }
        
        // Description constraints
        description nullable: false, blank: false, maxSize: 255
        
        // Category constraints for role organization
        category nullable: true, maxSize: 50,
                inList: [
                    'SYSTEM',           // System administration roles
                    'MANAGEMENT',       // Management roles
                    'OPERATIONS',       // Operational roles
                    'CUSTOMER_SERVICE', // Customer service roles
                    'TELLER',          // Teller roles
                    'LOAN_OFFICER',    // Loan officer roles
                    'AUDIT',           // Audit roles
                    'COMPLIANCE',      // Compliance roles
                    'IT',              // IT roles
                    'SECURITY'         // Security roles
                ]
        
        // Hierarchy constraints
        level min: 1, max: 10
        parentRole nullable: true,
                  validator: { val, obj ->
                      if (val && val.id == obj.id) {
                          return 'role.cannot.be.parent.of.itself'
                      }
                      if (val && val.level >= obj.level) {
                          return 'parent.role.level.must.be.lower'
                      }
                  }
        
        // Audit constraints
        createdBy nullable: true, maxSize: 50
        updatedBy nullable: true, maxSize: 50
        
        // Collections constraints
        permissions nullable: true
        restrictions nullable: true
    }
    
    static mapping = {
        table 'secure_role'
        id generator: 'identity'
        
        // Relationship mappings
        users joinTable: [
            name: 'secure_user_role',
            key: 'role_id',
            column: 'user_id'
        ]
        
        childRoles cascade: 'none' // Prevent cascade deletion
        
        // Performance indexes
        authority index: 'idx_secure_role_authority'
        category index: 'idx_secure_role_category'
        enabled index: 'idx_secure_role_enabled'
        level index: 'idx_secure_role_level'
        
        // Composite indexes
        indexes = [
            [name: 'idx_role_category_level', columnNames: ['category', 'level']],
            [name: 'idx_role_status', columnNames: ['enabled', 'system_role']]
        ]
        
        version false
    }
    
    /**
     * Get all permissions including inherited from parent roles
     */
    Set<String> getAllPermissions() {
        Set<String> allPermissions = new HashSet<>(permissions ?: [])
        
        // Add permissions from parent role
        if (parentRole) {
            allPermissions.addAll(parentRole.getAllPermissions())
        }
        
        return allPermissions
    }
    
    /**
     * Get all restrictions including inherited from parent roles
     */
    Set<String> getAllRestrictions() {
        Set<String> allRestrictions = new HashSet<>(restrictions ?: [])
        
        // Add restrictions from parent role
        if (parentRole) {
            allRestrictions.addAll(parentRole.getAllRestrictions())
        }
        
        return allRestrictions
    }
    
    /**
     * Check if role has specific permission
     */
    boolean hasPermission(String permission) {
        return getAllPermissions().contains(permission)
    }
    
    /**
     * Check if role has restriction
     */
    boolean hasRestriction(String restriction) {
        return getAllRestrictions().contains(restriction)
    }
    
    /**
     * Add permission to role
     */
    void addPermission(String permission) {
        if (!permissions) {
            permissions = [] as Set
        }
        permissions.add(permission)
    }
    
    /**
     * Remove permission from role
     */
    void removePermission(String permission) {
        permissions?.remove(permission)
    }
    
    /**
     * Add restriction to role
     */
    void addRestriction(String restriction) {
        if (!restrictions) {
            restrictions = [] as Set
        }
        restrictions.add(restriction)
    }
    
    /**
     * Remove restriction from role
     */
    void removeRestriction(String restriction) {
        restrictions?.remove(restriction)
    }
    
    /**
     * Get role hierarchy path
     */
    List<SecureRole> getHierarchyPath() {
        List<SecureRole> path = []
        SecureRole current = this
        
        while (current) {
            path.add(0, current) // Add to beginning
            current = current.parentRole
        }
        
        return path
    }
    
    /**
     * Check if this role is ancestor of another role
     */
    boolean isAncestorOf(SecureRole role) {
        SecureRole current = role.parentRole
        
        while (current) {
            if (current.id == this.id) {
                return true
            }
            current = current.parentRole
        }
        
        return false
    }
    
    /**
     * Check if this role is descendant of another role
     */
    boolean isDescendantOf(SecureRole role) {
        return role.isAncestorOf(this)
    }
    
    /**
     * Get all child roles recursively
     */
    Set<SecureRole> getAllChildRoles() {
        Set<SecureRole> allChildren = new HashSet<>()
        
        childRoles?.each { child ->
            allChildren.add(child)
            allChildren.addAll(child.getAllChildRoles())
        }
        
        return allChildren
    }
    
    /**
     * Get display name for UI
     */
    String getDisplayName() {
        return authority.replaceAll('ROLE_', '').replaceAll('_', ' ').toLowerCase().split(' ').collect {
            it.capitalize()
        }.join(' ')
    }
    
    /**
     * Get role color for UI based on category
     */
    String getRoleColor() {
        switch (category) {
            case 'SYSTEM':
                return '#dc3545' // Red
            case 'MANAGEMENT':
                return '#6f42c1' // Purple
            case 'OPERATIONS':
                return '#007bff' // Blue
            case 'CUSTOMER_SERVICE':
                return '#28a745' // Green
            case 'TELLER':
                return '#17a2b8' // Cyan
            case 'LOAN_OFFICER':
                return '#fd7e14' // Orange
            case 'AUDIT':
                return '#6c757d' // Gray
            case 'COMPLIANCE':
                return '#e83e8c' // Pink
            case 'IT':
                return '#20c997' // Teal
            case 'SECURITY':
                return '#ffc107' // Yellow
            default:
                return '#6c757d' // Default gray
        }
    }
    
    /**
     * Check if role can be deleted
     */
    boolean canBeDeleted() {
        // System roles cannot be deleted
        if (systemRole) {
            return false
        }
        
        // Roles with users cannot be deleted
        if (SecureUserRole.countBySecureRole(this) > 0) {
            return false
        }
        
        // Roles with child roles cannot be deleted
        if (childRoles && !childRoles.isEmpty()) {
            return false
        }
        
        return true
    }
    
    /**
     * Get user count for this role
     */
    int getUserCount() {
        return SecureUserRole.countBySecureRole(this)
    }
    
    /**
     * Create standard banking roles
     */
    static void createStandardRoles() {
        // System roles
        createRoleIfNotExists('ROLE_SUPER_ADMIN', 'Super Administrator', 'SYSTEM', 1, true)
        createRoleIfNotExists('ROLE_SYSTEM_ADMIN', 'System Administrator', 'SYSTEM', 2, true)
        
        // Management roles
        createRoleIfNotExists('ROLE_BRANCH_MANAGER', 'Branch Manager', 'MANAGEMENT', 3, false)
        createRoleIfNotExists('ROLE_OPERATIONS_MANAGER', 'Operations Manager', 'MANAGEMENT', 4, false)
        
        // Operational roles
        createRoleIfNotExists('ROLE_SENIOR_TELLER', 'Senior Teller', 'TELLER', 5, false)
        createRoleIfNotExists('ROLE_TELLER', 'Teller', 'TELLER', 6, false)
        createRoleIfNotExists('ROLE_LOAN_OFFICER', 'Loan Officer', 'LOAN_OFFICER', 5, false)
        createRoleIfNotExists('ROLE_CUSTOMER_SERVICE', 'Customer Service Representative', 'CUSTOMER_SERVICE', 6, false)
        
        // Specialized roles
        createRoleIfNotExists('ROLE_AUDITOR', 'Auditor', 'AUDIT', 4, false)
        createRoleIfNotExists('ROLE_COMPLIANCE_OFFICER', 'Compliance Officer', 'COMPLIANCE', 4, false)
        createRoleIfNotExists('ROLE_SECURITY_OFFICER', 'Security Officer', 'SECURITY', 4, false)
        createRoleIfNotExists('ROLE_IT_SUPPORT', 'IT Support', 'IT', 5, false)
    }
    
    private static void createRoleIfNotExists(String authority, String description, String category, int level, boolean systemRole) {
        if (!SecureRole.findByAuthority(authority)) {
            new SecureRole(
                authority: authority,
                description: description,
                category: category,
                level: level,
                systemRole: systemRole,
                enabled: true,
                createdBy: 'SYSTEM'
            ).save(failOnError: true)
        }
    }
}
