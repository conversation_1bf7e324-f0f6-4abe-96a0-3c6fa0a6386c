package org.icbs.tellering

import org.icbs.cif.Customer
import org.icbs.admin.Currency
import org.icbs.admin.Branch
import org.icbs.lov.ConfigItemStatus
import org.icbs.tellering.TxnCOCI

class TxnBillsPayment {

    static belongsTo=[beneficiary:Customer];
    Branch branch
    static hasMany=[checks:TxnCOCI]
    Double txnAmt
    String txnRef
    String billsPaymentId
    String billsRef
    String txnParticulars
    ConfigItemStatus status
    String billsMerchantId
    Date txnDate
    Currency currency
    TxnFile txnFile
    
    static constraints = {
        txnAmt nullable:false, min:0d, scale:2
        beneficiary nullable:true
        txnRef nullable:false
        billsPaymentId nullable:true
        branch nullable:true
        billsRef nullable:true, maxSize:50
        txnParticulars nullable:false, maxSize:100
        status nullable:true
        billsMerchantId nullable:true
        txnDate nullable:true
        currency nullable:true
        txnFile nullable:true
        checks nullable:true
    }
    
    static mapping = {
        id sqlType: 'int', generator: 'increment'
        txnRef sqlType: 'varchar'
        billsRef sqlType: 'varchar'
        txnParticulars sqlType: 'varchar'
    }
}
