package org.icbs.admin

import org.icbs.lov.Gender
import org.icbs.lov.Designation
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.Lov
import org.icbs.gl.GlAccount

class UserMaster {

	String username
	String password
	String passwordType = 'MD5' // Track password type during migration
	Date passwordMigrated // Track when password was migrated
	Date lastLoginAttempt // Track login attempts
	Integer failedLoginCount = 0 // Track failed login attempts
    // String confirmPassword
	String name1
	String name2
	String name3
	String name4
	Date birthdate
	Gender gender
	String address1
	String address2
	Lov province
	String zipCode
	String email
	String contact
	Date dateHired
	Designation designation
	Branch branch
    GlAccount cash
    GlAccount coci
	Lov employmentType
	Date expiryDate
        Date expiryPwdDate
    boolean isLocked
    boolean hasExceededMaxLogin
    boolean isFirstLogin
    boolean isTellerBalanced
	ConfigItemStatus configItemStatus

    String getName() {
        "${name1} ${name2 ?: ''} ${name3}"
    }

    // SECURITY METHODS - CRITICAL SECURITY FIXES
    def securePasswordService

    boolean validatePassword(String plainPassword) {
        if (passwordType == 'BCRYPT' || password?.startsWith('$2a$')) {
            return securePasswordService?.verifyPassword(plainPassword, password) ?: false
        } else {
            // Legacy MD5 validation during migration period
            return password == plainPassword?.encodeAsMD5()
        }
    }

    void migrateToSecurePassword(String plainPassword) {
        if (securePasswordService) {
            this.password = securePasswordService.hashPassword(plainPassword)
            this.passwordType = 'BCRYPT'
            this.passwordMigrated = new Date()
            this.save(flush: true)
        }
    }

    boolean isPasswordExpired() {
        if (!expiryPwdDate) return false
        return new Date() > expiryPwdDate
    }

    boolean isAccountLocked() {
        return isLocked || failedLoginCount >= 5
    }

    static belongsTo = [Role]

    static hasMany = [roles: Role]

    static constraints = {
    	username maxSize:20, unique:true
    	password nullable:false, validator: { val, obj ->
    	    if (obj.passwordType == 'BCRYPT' || val?.startsWith('$2a$')) {
    	        return true // BCrypt passwords are already validated
    	    }
    	    return obj.securePasswordService?.isValidPassword(val) ?: true
    	}
    	passwordType nullable:true, inList:['MD5', 'BCRYPT', 'RESET_REQUIRED']
    	passwordMigrated nullable:true
    	lastLoginAttempt nullable:true
    	failedLoginCount nullable:true, min:0, max:10
        // confirmPassword blank:true
    	name1 maxSize:50
    	name2 maxSize:50, nullable:true  
    	name3 maxSize:50, nullable:false
    	name4 maxSize:50, nullable:true
    	birthdate nullable:false
    	gender nullable:false
    	address1 maxSize:100
    	address2 maxSize:100, nullable:true
    	province nullable:true
    	zipCode maxSize:10
    	email maxSize:100
    	contact maxSize:30
    	dateHired nullable:true
    	designation nullable:false
    	branch nullable:false
        cash nullable:true
        coci nullable:true
    	employmentType nullable:false
    	expiryDate nullable:false
        expiryPwdDate nullable:true
    	configItemStatus blank:true
    }

    static mapping = {
        roles joinTable: [name:'user_role', key:'user_master_id']
    	id sqlType:'int', generator:'increment'
    	gender sqlType:'smallint'
    	designation sqlType:'smallint'
    	branch sqlType:'int'
    	configItemStatus sqlType:'smallint'
        province sqlType:'int'
        employmentType sqlType:'int'
    }

    static transients = ['name']

    def beforeInsert() {
        if (password && !password.startsWith('$2a$') && passwordType != 'MD5') {
            // Hash new passwords with BCrypt
            if (securePasswordService) {
                password = securePasswordService.hashPassword(password)
                passwordType = 'BCRYPT'
                passwordMigrated = new Date()
            }
        }
    }

    def beforeUpdate() {
        if (isDirty('password') && password && !password.startsWith('$2a$') && passwordType != 'MD5') {
            // Hash updated passwords with BCrypt
            if (securePasswordService) {
                password = securePasswordService.hashPassword(password)
                passwordType = 'BCRYPT'
                passwordMigrated = new Date()
            }
        }
    }
}
