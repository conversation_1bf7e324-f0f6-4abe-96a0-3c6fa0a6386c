package org.icbs.domain.events

import org.springframework.context.ApplicationEvent
import org.icbs.cif.Customer

/**
 * ARCHITECTURE MODERNIZATION: Domain Event for Customer Creation
 * Published when a new customer is created in the system
 */
class CustomerCreatedEvent extends ApplicationEvent {
    
    final Customer customer
    final Date timestamp
    final String eventType = 'CUSTOMER_CREATED'
    
    CustomerCreatedEvent(Customer customer) {
        super(customer)
        this.customer = customer
        this.timestamp = new Date()
    }
    
    /**
     * Get event data for serialization
     */
    Map getEventData() {
        return [
            eventType: eventType,
            timestamp: timestamp,
            customerId: customer.id,
            customerNumber: customer.customerId,
            customerName: customer.displayName,
            branchId: customer.branch?.id,
            branchName: customer.branch?.branchName,
            customerType: customer.type?.itemValue,
            status: customer.status?.itemValue,
            createdBy: customer.createdBy
        ]
    }
    
    @Override
    String toString() {
        return "CustomerCreatedEvent{customerId=${customer.id}, customerNumber=${customer.customerId}, timestamp=${timestamp}}"
    }
}

/**
 * ARCHITECTURE MODERNIZATION: Domain Event for Customer Updates
 */
class CustomerUpdatedEvent extends ApplicationEvent {
    
    final Customer customer
    final Customer originalState
    final Date timestamp
    final String eventType = 'CUSTOMER_UPDATED'
    
    CustomerUpdatedEvent(Customer customer, Customer originalState) {
        super(customer)
        this.customer = customer
        this.originalState = originalState
        this.timestamp = new Date()
    }
    
    /**
     * Get changes made to the customer
     */
    Map getChanges() {
        def changes = [:]
        
        if (customer.name1 != originalState.name1) {
            changes.firstName = [from: originalState.name1, to: customer.name1]
        }
        if (customer.name2 != originalState.name2) {
            changes.middleName = [from: originalState.name2, to: customer.name2]
        }
        if (customer.name3 != originalState.name3) {
            changes.lastName = [from: originalState.name3, to: customer.name3]
        }
        if (customer.email != originalState.email) {
            changes.email = [from: originalState.email, to: customer.email]
        }
        if (customer.status?.id != originalState.status?.id) {
            changes.status = [from: originalState.status?.itemValue, to: customer.status?.itemValue]
        }
        
        return changes
    }
    
    /**
     * Get event data for serialization
     */
    Map getEventData() {
        return [
            eventType: eventType,
            timestamp: timestamp,
            customerId: customer.id,
            customerNumber: customer.customerId,
            changes: getChanges(),
            updatedBy: customer.lastUpdatedBy
        ]
    }
    
    @Override
    String toString() {
        return "CustomerUpdatedEvent{customerId=${customer.id}, changes=${getChanges().keySet()}, timestamp=${timestamp}}"
    }
}

/**
 * ARCHITECTURE MODERNIZATION: Domain Event for Customer Activation
 */
class CustomerActivatedEvent extends ApplicationEvent {
    
    final Customer customer
    final Date timestamp
    final String eventType = 'CUSTOMER_ACTIVATED'
    
    CustomerActivatedEvent(Customer customer) {
        super(customer)
        this.customer = customer
        this.timestamp = new Date()
    }
    
    /**
     * Get event data for serialization
     */
    Map getEventData() {
        return [
            eventType: eventType,
            timestamp: timestamp,
            customerId: customer.id,
            customerNumber: customer.customerId,
            customerName: customer.displayName,
            branchId: customer.branch?.id,
            activatedBy: customer.activatedBy,
            activationDate: customer.dateActivated
        ]
    }
    
    @Override
    String toString() {
        return "CustomerActivatedEvent{customerId=${customer.id}, customerNumber=${customer.customerId}, timestamp=${timestamp}}"
    }
}

/**
 * ARCHITECTURE MODERNIZATION: Domain Event for Customer Deactivation
 */
class CustomerDeactivatedEvent extends ApplicationEvent {
    
    final Customer customer
    final String reason
    final Date timestamp
    final String eventType = 'CUSTOMER_DEACTIVATED'
    
    CustomerDeactivatedEvent(Customer customer, String reason) {
        super(customer)
        this.customer = customer
        this.reason = reason
        this.timestamp = new Date()
    }
    
    /**
     * Get event data for serialization
     */
    Map getEventData() {
        return [
            eventType: eventType,
            timestamp: timestamp,
            customerId: customer.id,
            customerNumber: customer.customerId,
            customerName: customer.displayName,
            branchId: customer.branch?.id,
            reason: reason,
            deactivatedBy: customer.deactivatedBy,
            deactivationDate: customer.dateDeactivated
        ]
    }
    
    @Override
    String toString() {
        return "CustomerDeactivatedEvent{customerId=${customer.id}, reason=${reason}, timestamp=${timestamp}}"
    }
}
