package org.icbs.domain.valueobjects

import java.math.RoundingMode

/**
 * ARCHITECTURE MODERNIZATION: Value Object for Money
 * Encapsulates monetary amounts with currency and precision handling
 */
class Money {
    
    final BigDecimal amount
    final String currency
    
    private static final int DEFAULT_SCALE = 2
    private static final RoundingMode DEFAULT_ROUNDING = RoundingMode.HALF_UP
    
    Money(BigDecimal amount, String currency = 'PHP') {
        if (amount == null) {
            throw new IllegalArgumentException("Amount cannot be null")
        }
        if (!currency || currency.trim().isEmpty()) {
            throw new IllegalArgumentException("Currency cannot be null or empty")
        }
        
        this.amount = amount.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING)
        this.currency = currency.toUpperCase()
    }
    
    Money(Double amount, String currency = 'PHP') {
        this(new BigDecimal(amount.toString()), currency)
    }
    
    Money(String amount, String currency = 'PHP') {
        this(new BigDecimal(amount), currency)
    }
    
    /**
     * Add two money amounts (must be same currency)
     */
    Money add(Money other) {
        validateSameCurrency(other)
        return new Money(this.amount.add(other.amount), this.currency)
    }
    
    /**
     * Subtract two money amounts (must be same currency)
     */
    Money subtract(Money other) {
        validateSameCurrency(other)
        return new Money(this.amount.subtract(other.amount), this.currency)
    }
    
    /**
     * Multiply money by a factor
     */
    Money multiply(BigDecimal factor) {
        return new Money(this.amount.multiply(factor), this.currency)
    }
    
    Money multiply(Double factor) {
        return multiply(new BigDecimal(factor.toString()))
    }
    
    /**
     * Divide money by a factor
     */
    Money divide(BigDecimal divisor) {
        if (divisor.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("Cannot divide by zero")
        }
        return new Money(this.amount.divide(divisor, DEFAULT_SCALE, DEFAULT_ROUNDING), this.currency)
    }
    
    Money divide(Double divisor) {
        return divide(new BigDecimal(divisor.toString()))
    }
    
    /**
     * Check if amount is positive
     */
    boolean isPositive() {
        return amount.compareTo(BigDecimal.ZERO) > 0
    }
    
    /**
     * Check if amount is negative
     */
    boolean isNegative() {
        return amount.compareTo(BigDecimal.ZERO) < 0
    }
    
    /**
     * Check if amount is zero
     */
    boolean isZero() {
        return amount.compareTo(BigDecimal.ZERO) == 0
    }
    
    /**
     * Get absolute value
     */
    Money abs() {
        return new Money(amount.abs(), currency)
    }
    
    /**
     * Negate the amount
     */
    Money negate() {
        return new Money(amount.negate(), currency)
    }
    
    /**
     * Compare with another money amount
     */
    int compareTo(Money other) {
        validateSameCurrency(other)
        return this.amount.compareTo(other.amount)
    }
    
    /**
     * Check if greater than another amount
     */
    boolean greaterThan(Money other) {
        return compareTo(other) > 0
    }
    
    /**
     * Check if less than another amount
     */
    boolean lessThan(Money other) {
        return compareTo(other) < 0
    }
    
    /**
     * Check if greater than or equal to another amount
     */
    boolean greaterThanOrEqual(Money other) {
        return compareTo(other) >= 0
    }
    
    /**
     * Check if less than or equal to another amount
     */
    boolean lessThanOrEqual(Money other) {
        return compareTo(other) <= 0
    }
    
    /**
     * Format money for display
     */
    String getFormattedAmount() {
        return String.format("%,.2f", amount)
    }
    
    /**
     * Get display string with currency
     */
    String getDisplayValue() {
        return "${currency} ${formattedAmount}"
    }
    
    /**
     * Convert to double (use with caution for calculations)
     */
    Double toDouble() {
        return amount.doubleValue()
    }
    
    /**
     * Convert to BigDecimal
     */
    BigDecimal toBigDecimal() {
        return amount
    }
    
    /**
     * Create zero money
     */
    static Money zero(String currency = 'PHP') {
        return new Money(BigDecimal.ZERO, currency)
    }
    
    /**
     * Create money from cents/centavos
     */
    static Money fromCents(Long cents, String currency = 'PHP') {
        return new Money(new BigDecimal(cents).divide(new BigDecimal(100)), currency)
    }
    
    /**
     * Get amount in cents/centavos
     */
    Long toCents() {
        return amount.multiply(new BigDecimal(100)).longValue()
    }
    
    /**
     * Validate that two money objects have the same currency
     */
    private void validateSameCurrency(Money other) {
        if (this.currency != other.currency) {
            throw new IllegalArgumentException("Cannot perform operation on different currencies: ${this.currency} and ${other.currency}")
        }
    }
    
    @Override
    boolean equals(Object obj) {
        if (this.is(obj)) return true
        if (!(obj instanceof Money)) return false
        Money other = (Money) obj
        return amount.equals(other.amount) && currency.equals(other.currency)
    }
    
    @Override
    int hashCode() {
        return Objects.hash(amount, currency)
    }
    
    @Override
    String toString() {
        return displayValue
    }
}
