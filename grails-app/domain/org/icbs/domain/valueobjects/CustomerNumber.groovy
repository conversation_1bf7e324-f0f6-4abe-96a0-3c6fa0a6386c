package org.icbs.domain.valueobjects

/**
 * ARCHITECTURE MODERNIZATION: Value Object for Customer Number
 * Encapsulates customer number validation and formatting logic
 */
class CustomerNumber {
    
    final String value
    
    CustomerNumber(String value) {
        if (!isValid(value)) {
            throw new IllegalArgumentException("Invalid customer number format: ${value}")
        }
        this.value = value
    }
    
    /**
     * Validate customer number format
     * Format: BBBBYYYYNNNNNN (4-digit branch + 4-digit year + 6-digit sequence)
     */
    private static boolean isValid(String customerNumber) {
        if (!customerNumber || customerNumber.length() != 14) {
            return false
        }
        
        // Check if all characters are digits
        if (!customerNumber.matches(/^\d{14}$/)) {
            return false
        }
        
        // Extract and validate year
        def year = customerNumber.substring(4, 8)
        def currentYear = new Date().format('yyyy')
        def yearInt = Integer.parseInt(year)
        def currentYearInt = Integer.parseInt(currentYear)
        
        // Year should be within reasonable range (not future, not too old)
        if (yearInt > currentYearInt || yearInt < (currentYearInt - 50)) {
            return false
        }
        
        return true
    }
    
    /**
     * Get branch code from customer number
     */
    String getBranchCode() {
        return value.substring(0, 4)
    }
    
    /**
     * Get year from customer number
     */
    String getYear() {
        return value.substring(4, 8)
    }
    
    /**
     * Get sequence number from customer number
     */
    String getSequence() {
        return value.substring(8, 14)
    }
    
    /**
     * Format customer number for display
     */
    String getFormattedValue() {
        return "${branchCode}-${year}-${sequence}"
    }
    
    @Override
    boolean equals(Object obj) {
        if (this.is(obj)) return true
        if (!(obj instanceof CustomerNumber)) return false
        CustomerNumber other = (CustomerNumber) obj
        return value == other.value
    }
    
    @Override
    int hashCode() {
        return value.hashCode()
    }
    
    @Override
    String toString() {
        return value
    }
}
