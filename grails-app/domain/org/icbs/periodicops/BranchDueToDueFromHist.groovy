package org.icbs.periodicops

import org.icbs.lov.BranchRunStatus
import org.icbs.lov.BranchStatus
import org.icbs.lov.Lov
import org.icbs.gl.GlAccount
import org.icbs.admin.Branch

class BranchDueToDueFromHist {
    
    Branch branch
    String branchName
    GlAccount dueToGl
    GlAccount dueFromGl
    GlAccount yearEndClosingGl
    GlAccount iccContra
    
    static constraints = {
        branch nullable:true
        branchName nullable:true
        dueToGl nullable:true
        dueFromGl nullable:true
        yearEndClosingGl nullable:true
        iccContra nullable:true
    }
    static mapping = {
    	id sqlType:'int', generator:'increment'
    }
}
