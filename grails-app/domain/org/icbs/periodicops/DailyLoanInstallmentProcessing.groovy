package org.icbs.periodicops

import org.icbs.loans.LoanInstallment
import org.icbs.loans.Loan

class DailyLoanInstallmentProcessing {
    Date processDate
    Integer loanInstallment
    Integer loan
    
    static constraints = {
        loanInstallment nullable:true
        loan nullable:true
    }
    static mapping = {
	id sqlType:'int', generator:'increment'
	processDate sqlType:'date'
    }      
}
