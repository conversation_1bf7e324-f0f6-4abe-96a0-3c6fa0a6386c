package org.icbs.periodicops
import org.icbs.cif.Customer
import org.icbs.admin.Branch
import org.icbs.lov.CustomerDosriCode
import org.icbs.lov.CustomerStatus
import org.icbs.lov.CustomerType
import org.icbs.lov.FirmSize
import org.icbs.lov.Gender
import org.icbs.lov.Lov
import org.icbs.lov.ResidentType
import org.icbs.lov.RiskType
import org.icbs.admin.CustomerGroup

class MonthlyCustomer {
    Date refDate
    Customer customer
    CustomerType type//lov
    Branch branch
    String cid// changed 2/5/15
    String name1
    String name2
    String name3
    String name4
    String displayName
    String shortAddress
    String pepDescription
    String amla
    Date birthDate
    Lov title
    Gender gender//lov
    Lov civilStatus
    String birthPlace
    boolean isTaxable
    double creditLimit
    ResidentType customerCode1
    RiskType customerCode2
    
    FirmSize customerCode3
    Lov nationality
    String sourceOfIncome
    CustomerDosriCode dosriCode//lov
    //EXTRA FROM SB.200010B
    //citizenship
    String sssNo
    String gisNo
    String tinNo
    String passportNo
    String remarks
    CustomerGroup group
    CustomerStatus status//lov

    static constraints = {
        customer nullable:true
        type nullable:true
        branch nullable:true
        cid nullable:true
        name1 nullable:true
        name2 nullable:true
        name3 nullable:true
        name4 nullable:true
        displayName nullable:true
        shortAddress nullable:true
        pepDescription nullable:true
        amla nullable:true
        birthDate nullable:true
        title nullable:true
        gender nullable:true
        civilStatus nullable:true
        birthPlace nullable:true
        isTaxable nullable:true
        creditLimit nullable:true
        customerCode1 nullable:true
        customerCode2 nullable:true
    
        customerCode3 nullable:true
        nationality nullable:true
        sourceOfIncome nullable:true
        dosriCode nullable:true
        //EXTRA FROM SB.200010B
        //citizenship
        sssNo nullable:true
        gisNo nullable:true
        tinNo nullable:true
        passportNo nullable:true
        remarks nullable:true
        group nullable:true
        status nullable:true
    }
    static mapping = {
		id sqlType: "int", generator: "increment"
    }
}
