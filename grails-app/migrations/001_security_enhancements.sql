-- QwikBanka Database Migration Script
-- Phase 1: Security Enhancements
-- Version: 1.0.0
-- Date: 2025-01-27

-- =====================================================
-- SECURITY ENHANCEMENT MIGRATIONS
-- =====================================================

-- Add password security fields to user_master table
ALTER TABLE user_master 
ADD COLUMN password_type VARCHAR(20) DEFAULT 'MD5',
ADD COLUMN password_migrated TIMESTAMP NULL,
ADD COLUMN last_login_attempt TIMESTAMP NULL,
ADD COLUMN failed_login_count INTEGER DEFAULT 0;

-- Add indexes for performance
CREATE INDEX idx_user_master_password_type ON user_master(password_type);
CREATE INDEX idx_user_master_failed_login ON user_master(failed_login_count);
CREATE INDEX idx_user_master_last_login ON user_master(last_login_attempt);

-- Update existing users to track migration status
UPDATE user_master 
SET password_type = 'MD5', 
    failed_login_count = 0 
WHERE password_type IS NULL;

-- =====================================================
-- AUDIT TRAIL ENHANCEMENTS
-- =====================================================

-- Create audit log table for security events
CREATE TABLE security_audit_log (
    id BIGSERIAL PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    user_id BIGINT,
    username VARCHAR(50),
    ip_address VARCHAR(45),
    user_agent TEXT,
    event_data JSONB,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_security_audit_user FOREIGN KEY (user_id) 
        REFERENCES user_master(id) ON DELETE SET NULL
);

-- Add indexes for audit log
CREATE INDEX idx_security_audit_event_type ON security_audit_log(event_type);
CREATE INDEX idx_security_audit_user_id ON security_audit_log(user_id);
CREATE INDEX idx_security_audit_created_at ON security_audit_log(created_at);
CREATE INDEX idx_security_audit_success ON security_audit_log(success);

-- =====================================================
-- SESSION MANAGEMENT ENHANCEMENTS
-- =====================================================

-- Add session tracking fields to user_session table
ALTER TABLE user_session 
ADD COLUMN session_token VARCHAR(255),
ADD COLUMN ip_address VARCHAR(45),
ADD COLUMN user_agent TEXT,
ADD COLUMN last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN expires_at TIMESTAMP,
ADD COLUMN is_active BOOLEAN DEFAULT TRUE;

-- Add indexes for session management
CREATE INDEX idx_user_session_token ON user_session(session_token);
CREATE INDEX idx_user_session_active ON user_session(is_active);
CREATE INDEX idx_user_session_expires ON user_session(expires_at);
CREATE INDEX idx_user_session_last_activity ON user_session(last_activity);

-- =====================================================
-- CONFIGURATION SECURITY
-- =====================================================

-- Create secure configuration table
CREATE TABLE secure_configuration (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    description TEXT,
    created_by VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(50),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default security configurations
INSERT INTO secure_configuration (config_key, config_value, description, created_by) VALUES
('SECURITY.PASSWORD_MIN_LENGTH', '8', 'Minimum password length requirement', 'system'),
('SECURITY.PASSWORD_COMPLEXITY', 'true', 'Require complex passwords', 'system'),
('SECURITY.MAX_LOGIN_ATTEMPTS', '5', 'Maximum failed login attempts before lockout', 'system'),
('SECURITY.SESSION_TIMEOUT', '1800', 'Session timeout in seconds (30 minutes)', 'system'),
('SECURITY.PASSWORD_EXPIRY_DAYS', '90', 'Password expiry period in days', 'system'),
('SECURITY.ENABLE_2FA', 'false', 'Enable two-factor authentication', 'system');

-- =====================================================
-- DATA INTEGRITY CONSTRAINTS
-- =====================================================

-- Add constraints for data integrity
ALTER TABLE user_master 
ADD CONSTRAINT chk_failed_login_count CHECK (failed_login_count >= 0 AND failed_login_count <= 10);

ALTER TABLE user_master 
ADD CONSTRAINT chk_password_type CHECK (password_type IN ('MD5', 'BCRYPT', 'RESET_REQUIRED'));

-- =====================================================
-- PERFORMANCE OPTIMIZATIONS
-- =====================================================

-- Add composite indexes for common queries
CREATE INDEX idx_user_master_username_status ON user_master(username, is_locked);
CREATE INDEX idx_user_master_branch_status ON user_master(branch_id, is_locked);

-- =====================================================
-- MIGRATION VERIFICATION
-- =====================================================

-- Create migration log table
CREATE TABLE migration_log (
    id BIGSERIAL PRIMARY KEY,
    migration_name VARCHAR(100) NOT NULL,
    migration_version VARCHAR(20) NOT NULL,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    execution_time_ms INTEGER,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    executed_by VARCHAR(50) DEFAULT 'system'
);

-- Log this migration
INSERT INTO migration_log (migration_name, migration_version, execution_time_ms, executed_by) 
VALUES ('001_security_enhancements', '1.0.0', 0, 'system');

-- =====================================================
-- ROLLBACK SCRIPT (for emergency use)
-- =====================================================

/*
-- ROLLBACK SCRIPT - USE ONLY IN EMERGENCY
-- This script will undo the security enhancements

-- Remove added columns from user_master
ALTER TABLE user_master 
DROP COLUMN IF EXISTS password_type,
DROP COLUMN IF EXISTS password_migrated,
DROP COLUMN IF EXISTS last_login_attempt,
DROP COLUMN IF EXISTS failed_login_count;

-- Remove added columns from user_session
ALTER TABLE user_session 
DROP COLUMN IF EXISTS session_token,
DROP COLUMN IF EXISTS ip_address,
DROP COLUMN IF EXISTS user_agent,
DROP COLUMN IF EXISTS last_activity,
DROP COLUMN IF EXISTS expires_at,
DROP COLUMN IF EXISTS is_active;

-- Drop created tables
DROP TABLE IF EXISTS security_audit_log;
DROP TABLE IF EXISTS secure_configuration;
DROP TABLE IF EXISTS migration_log;

-- Note: Indexes will be automatically dropped with columns/tables
*/

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify migration success
SELECT 
    'user_master' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN password_type IS NOT NULL THEN 1 END) as records_with_password_type,
    COUNT(CASE WHEN failed_login_count IS NOT NULL THEN 1 END) as records_with_login_count
FROM user_master;

SELECT 
    'security_audit_log' as table_name,
    COUNT(*) as total_records
FROM security_audit_log;

SELECT 
    'secure_configuration' as table_name,
    COUNT(*) as total_records
FROM secure_configuration;

-- Check indexes
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('user_master', 'user_session', 'security_audit_log', 'secure_configuration')
ORDER BY tablename, indexname;

COMMIT;
