-- QwikBanka Database Migration Script
-- Phase 2: Performance Optimizations
-- Version: 1.0.0
-- Date: 2025-01-27

-- =====================================================
-- PERFORMANCE OPTIMIZATION MIGRATIONS
-- =====================================================

-- Add performance tracking fields to customer table
ALTER TABLE customer 
ADD COLUMN last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN access_count INTEGER DEFAULT 0,
ADD COLUMN cache_version INTEGER DEFAULT 1;

-- Add performance tracking to deposit table
ALTER TABLE deposit 
ADD COLUMN last_transaction_date TIMESTAMP,
ADD COLUMN transaction_count INTEGER DEFAULT 0,
ADD COLUMN balance_last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Add performance tracking to loan table
ALTER TABLE loan 
ADD COLUMN last_payment_date TIMESTAMP,
ADD COLUMN payment_count INTEGER DEFAULT 0,
ADD COLUMN balance_last_calculated TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- =====================================================
-- INDEXING STRATEGY FOR N+1 QUERY PREVENTION
-- =====================================================

-- Customer table indexes
CREATE INDEX idx_customer_branch_type ON customer(branch_id, type_id);
CREATE INDEX idx_customer_status_branch ON customer(status_id, branch_id);
CREATE INDEX idx_customer_display_name ON customer(display_name);
CREATE INDEX idx_customer_customer_id ON customer(customer_id);
CREATE INDEX idx_customer_last_updated ON customer(last_updated_at);

-- Deposit table indexes
CREATE INDEX idx_deposit_customer_status ON deposit(customer_id, status_id);
CREATE INDEX idx_deposit_branch_type ON deposit(branch_id, type_id);
CREATE INDEX idx_deposit_acct_no ON deposit(acct_no);
CREATE INDEX idx_deposit_maturity_date ON deposit(maturity_date);
CREATE INDEX idx_deposit_last_txn_date ON deposit(last_transaction_date);

-- Loan table indexes
CREATE INDEX idx_loan_customer_status ON loan(customer_id, status_id);
CREATE INDEX idx_loan_branch_product ON loan(branch_id, product_id);
CREATE INDEX idx_loan_maturity_date ON loan(maturity_date);
CREATE INDEX idx_loan_last_payment ON loan(last_payment_date);

-- Address table indexes (for customer relationships)
CREATE INDEX idx_address_customer_type ON address(customer_id, type_id);

-- Contact table indexes (for customer relationships)
CREATE INDEX idx_contact_customer_type ON contact(customer_id, type_id);

-- =====================================================
-- MATERIALIZED VIEWS FOR REPORTING
-- =====================================================

-- Customer summary materialized view
CREATE MATERIALIZED VIEW mv_customer_summary AS
SELECT 
    c.id as customer_id,
    c.customer_id as customer_number,
    c.display_name,
    c.branch_id,
    b.branch_name,
    c.type_id,
    ct.item_value as customer_type,
    c.status_id,
    cs.item_value as customer_status,
    COUNT(DISTINCT d.id) as deposit_count,
    COALESCE(SUM(d.ledger_bal_amt), 0) as total_deposit_balance,
    COUNT(DISTINCT l.id) as loan_count,
    COALESCE(SUM(l.outstanding_principal_balance), 0) as total_loan_balance,
    c.date_created,
    c.last_updated_at
FROM customer c
LEFT JOIN branch b ON c.branch_id = b.id
LEFT JOIN lov_item ct ON c.type_id = ct.id
LEFT JOIN lov_item cs ON c.status_id = cs.id
LEFT JOIN deposit d ON c.id = d.customer_id AND d.status_id = 1
LEFT JOIN loan l ON c.id = l.customer_id AND l.status_id IN (3, 4, 5)
GROUP BY c.id, c.customer_id, c.display_name, c.branch_id, b.branch_name, 
         c.type_id, ct.item_value, c.status_id, cs.item_value, c.date_created, c.last_updated_at;

-- Create unique index on materialized view
CREATE UNIQUE INDEX idx_mv_customer_summary_id ON mv_customer_summary(customer_id);
CREATE INDEX idx_mv_customer_summary_branch ON mv_customer_summary(branch_id);
CREATE INDEX idx_mv_customer_summary_status ON mv_customer_summary(customer_status);

-- Branch summary materialized view
CREATE MATERIALIZED VIEW mv_branch_summary AS
SELECT 
    b.id as branch_id,
    b.branch_name,
    b.branch_code,
    COUNT(DISTINCT c.id) as total_customers,
    COUNT(DISTINCT CASE WHEN c.status_id = 1 THEN c.id END) as active_customers,
    COUNT(DISTINCT d.id) as total_deposits,
    COALESCE(SUM(d.ledger_bal_amt), 0) as total_deposit_balance,
    COUNT(DISTINCT l.id) as total_loans,
    COALESCE(SUM(l.outstanding_principal_balance), 0) as total_loan_balance,
    CURRENT_TIMESTAMP as last_updated
FROM branch b
LEFT JOIN customer c ON b.id = c.branch_id
LEFT JOIN deposit d ON b.id = d.branch_id AND d.status_id = 1
LEFT JOIN loan l ON b.id = l.branch_id AND l.status_id IN (3, 4, 5)
GROUP BY b.id, b.branch_name, b.branch_code;

-- Create unique index on branch summary
CREATE UNIQUE INDEX idx_mv_branch_summary_id ON mv_branch_summary(branch_id);

-- =====================================================
-- PARTITIONING FOR LARGE TABLES
-- =====================================================

-- Create partitioned transaction tables for better performance
-- (This is a preparation for future transaction volume)

-- Create transaction log partition table
CREATE TABLE txn_log_partitioned (
    id BIGSERIAL,
    txn_date DATE NOT NULL,
    txn_type VARCHAR(50),
    customer_id BIGINT,
    account_id BIGINT,
    amount DECIMAL(15,2),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    branch_id BIGINT
) PARTITION BY RANGE (txn_date);

-- Create partitions for current and future months
CREATE TABLE txn_log_2025_01 PARTITION OF txn_log_partitioned
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE txn_log_2025_02 PARTITION OF txn_log_partitioned
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

CREATE TABLE txn_log_2025_03 PARTITION OF txn_log_partitioned
    FOR VALUES FROM ('2025-03-01') TO ('2025-04-01');

-- Add indexes to partitions
CREATE INDEX idx_txn_log_2025_01_customer ON txn_log_2025_01(customer_id);
CREATE INDEX idx_txn_log_2025_01_account ON txn_log_2025_01(account_id);
CREATE INDEX idx_txn_log_2025_02_customer ON txn_log_2025_02(customer_id);
CREATE INDEX idx_txn_log_2025_02_account ON txn_log_2025_02(account_id);
CREATE INDEX idx_txn_log_2025_03_customer ON txn_log_2025_03(customer_id);
CREATE INDEX idx_txn_log_2025_03_account ON txn_log_2025_03(account_id);

-- =====================================================
-- CACHING SUPPORT TABLES
-- =====================================================

-- Create cache metadata table
CREATE TABLE cache_metadata (
    id BIGSERIAL PRIMARY KEY,
    cache_key VARCHAR(255) NOT NULL UNIQUE,
    cache_type VARCHAR(50) NOT NULL,
    entity_type VARCHAR(100),
    entity_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    hit_count INTEGER DEFAULT 0,
    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for cache management
CREATE INDEX idx_cache_metadata_type ON cache_metadata(cache_type);
CREATE INDEX idx_cache_metadata_entity ON cache_metadata(entity_type, entity_id);
CREATE INDEX idx_cache_metadata_expires ON cache_metadata(expires_at);

-- =====================================================
-- PERFORMANCE MONITORING TABLES
-- =====================================================

-- Create query performance log
CREATE TABLE query_performance_log (
    id BIGSERIAL PRIMARY KEY,
    query_type VARCHAR(100) NOT NULL,
    execution_time_ms INTEGER NOT NULL,
    rows_affected INTEGER,
    query_hash VARCHAR(64),
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id BIGINT,
    session_id VARCHAR(100)
);

-- Add indexes for performance monitoring
CREATE INDEX idx_query_perf_type ON query_performance_log(query_type);
CREATE INDEX idx_query_perf_time ON query_performance_log(execution_time_ms);
CREATE INDEX idx_query_perf_executed ON query_performance_log(executed_at);

-- =====================================================
-- STORED PROCEDURES FOR MAINTENANCE
-- =====================================================

-- Function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_materialized_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_customer_summary;
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_branch_summary;
    
    -- Log the refresh
    INSERT INTO query_performance_log (query_type, execution_time_ms, rows_affected)
    VALUES ('REFRESH_MATERIALIZED_VIEWS', 0, 0);
END;
$$ LANGUAGE plpgsql;

-- Function to update access statistics
CREATE OR REPLACE FUNCTION update_access_stats(
    p_table_name VARCHAR(50),
    p_entity_id BIGINT
)
RETURNS void AS $$
BEGIN
    IF p_table_name = 'customer' THEN
        UPDATE customer 
        SET last_accessed = CURRENT_TIMESTAMP,
            access_count = access_count + 1
        WHERE id = p_entity_id;
    ELSIF p_table_name = 'deposit' THEN
        UPDATE deposit 
        SET last_accessed = CURRENT_TIMESTAMP
        WHERE id = p_entity_id;
    ELSIF p_table_name = 'loan' THEN
        UPDATE loan 
        SET last_accessed = CURRENT_TIMESTAMP
        WHERE id = p_entity_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CLEANUP PROCEDURES
-- =====================================================

-- Function to clean old performance logs
CREATE OR REPLACE FUNCTION cleanup_performance_logs()
RETURNS void AS $$
BEGIN
    -- Keep only last 30 days of query performance logs
    DELETE FROM query_performance_log 
    WHERE executed_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
    
    -- Clean expired cache metadata
    DELETE FROM cache_metadata 
    WHERE expires_at < CURRENT_TIMESTAMP;
    
    -- Log cleanup
    INSERT INTO query_performance_log (query_type, execution_time_ms, rows_affected)
    VALUES ('CLEANUP_PERFORMANCE_LOGS', 0, 0);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- MIGRATION COMPLETION
-- =====================================================

-- Update migration log
INSERT INTO migration_log (migration_name, migration_version, execution_time_ms, executed_by) 
VALUES ('002_performance_optimizations', '1.0.0', 0, 'system');

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify new indexes
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('customer', 'deposit', 'loan', 'address', 'contact')
  AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

-- Verify materialized views
SELECT 
    schemaname,
    matviewname,
    definition
FROM pg_matviews
WHERE matviewname LIKE 'mv_%';

-- Check table sizes after optimization
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE tablename IN ('customer', 'deposit', 'loan', 'mv_customer_summary', 'mv_branch_summary')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

COMMIT;
