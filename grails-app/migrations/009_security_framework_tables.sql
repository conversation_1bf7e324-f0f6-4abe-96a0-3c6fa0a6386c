-- QwikBanka Database Migration Script
-- Phase 1.2: Security Framework Tables
-- Version: 1.0.0
-- Date: 2025-01-27

-- =====================================================
-- SECURITY FRAMEWORK TABLES CREATION
-- =====================================================

-- Create secure_user table
CREATE TABLE IF NOT EXISTS secure_user (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    account_expired BOOLEAN DEFAULT FALSE,
    account_locked BOOLEAN DEFAULT FALSE,
    password_expired BOOLEAN DEFAULT FALSE,
    date_created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    last_login_ip VARCHAR(45),
    failed_login_attempts INTEGER DEFAULT 0,
    last_failed_login TIMESTAMP,
    password_last_changed TIMESTAMP,
    employee_id VARCHAR(20) UNIQUE,
    branch_id BIGINT,
    department_id BIGINT,
    permissions JSONB,
    mfa_secret VARCHAR(32),
    mfa_enabled BOOLEAN DEFAULT FALSE,
    recovery_email VARCHAR(100),
    phone_number VARCHAR(20),
    last_password_change TIMESTAMP,
    password_version INTEGER DEFAULT 1,
    current_session_id VARCHAR(128),
    session_start_time TIMESTAMP,
    max_concurrent_sessions INTEGER DEFAULT 1,
    
    CONSTRAINT chk_failed_login_count CHECK (failed_login_attempts >= 0 AND failed_login_attempts <= 10),
    CONSTRAINT chk_max_sessions CHECK (max_concurrent_sessions >= 1 AND max_concurrent_sessions <= 5),
    CONSTRAINT chk_password_version CHECK (password_version >= 1),
    CONSTRAINT fk_secure_user_branch FOREIGN KEY (branch_id) REFERENCES branch(id),
    CONSTRAINT fk_secure_user_department FOREIGN KEY (department_id) REFERENCES department(id)
);

-- Create secure_role table
CREATE TABLE IF NOT EXISTS secure_role (
    id BIGSERIAL PRIMARY KEY,
    authority VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(255) NOT NULL,
    category VARCHAR(50),
    level INTEGER DEFAULT 1,
    permissions JSONB,
    restrictions JSONB,
    date_created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    enabled BOOLEAN DEFAULT TRUE,
    system_role BOOLEAN DEFAULT FALSE,
    parent_role_id BIGINT,
    
    CONSTRAINT chk_role_level CHECK (level >= 1 AND level <= 10),
    CONSTRAINT chk_role_authority_format CHECK (authority ~ '^ROLE_[A-Z_]+$'),
    CONSTRAINT fk_secure_role_parent FOREIGN KEY (parent_role_id) REFERENCES secure_role(id)
);

-- Create secure_user_role junction table
CREATE TABLE IF NOT EXISTS secure_user_role (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    date_created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by VARCHAR(50),
    assignment_reason VARCHAR(255),
    valid_from TIMESTAMP,
    valid_to TIMESTAMP,
    active BOOLEAN DEFAULT TRUE,
    
    PRIMARY KEY (user_id, role_id),
    CONSTRAINT fk_user_role_user FOREIGN KEY (user_id) REFERENCES secure_user(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_role_role FOREIGN KEY (role_id) REFERENCES secure_role(id) ON DELETE CASCADE,
    CONSTRAINT chk_valid_dates CHECK (valid_to IS NULL OR valid_from IS NULL OR valid_to >= valid_from)
);

-- Create security_audit_log table
CREATE TABLE IF NOT EXISTS security_audit_log (
    id BIGSERIAL PRIMARY KEY,
    event_id VARCHAR(36) NOT NULL UNIQUE,
    event_type VARCHAR(50) NOT NULL,
    event_category VARCHAR(50),
    event_description VARCHAR(500) NOT NULL,
    username VARCHAR(50),
    user_id VARCHAR(50),
    session_id VARCHAR(128),
    user_agent VARCHAR(500),
    ip_address VARCHAR(45),
    remote_host VARCHAR(255),
    forwarded_for VARCHAR(255),
    request_uri VARCHAR(500),
    request_method VARCHAR(10),
    request_parameters TEXT,
    referer VARCHAR(500),
    security_level VARCHAR(20),
    risk_score VARCHAR(10),
    threat_indicators VARCHAR(500),
    event_data JSONB,
    before_state TEXT,
    after_state TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_time BIGINT,
    result VARCHAR(20),
    result_code VARCHAR(20),
    error_message VARCHAR(1000),
    compliance_flags VARCHAR(200),
    regulatory_category VARCHAR(50),
    requires_review BOOLEAN DEFAULT FALSE,
    user_ref_id BIGINT,
    
    CONSTRAINT fk_audit_log_user FOREIGN KEY (user_ref_id) REFERENCES secure_user(id)
);

-- Create password_history table
CREATE TABLE IF NOT EXISTS password_history (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    hash_algorithm VARCHAR(20) DEFAULT 'BCRYPT',
    password_version INTEGER DEFAULT 1,
    date_created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    change_reason VARCHAR(100),
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    forced_change BOOLEAN DEFAULT FALSE,
    password_strength_score INTEGER,
    compliance_flags VARCHAR(200),
    
    CONSTRAINT fk_password_history_user FOREIGN KEY (user_id) REFERENCES secure_user(id) ON DELETE CASCADE,
    CONSTRAINT chk_password_version CHECK (password_version >= 1),
    CONSTRAINT chk_password_strength CHECK (password_strength_score IS NULL OR (password_strength_score >= 0 AND password_strength_score <= 100))
);

-- Update user_session table for enhanced session management
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS session_id VARCHAR(128);
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS username VARCHAR(50);
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS user_id VARCHAR(50);
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS session_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS session_end TIMESTAMP;
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS timeout_minutes INTEGER DEFAULT 30;
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE;
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS session_status VARCHAR(20) DEFAULT 'ACTIVE';
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS termination_reason VARCHAR(100);
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS ip_address VARCHAR(45);
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS remote_host VARCHAR(255);
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS user_agent VARCHAR(500);
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS browser_info VARCHAR(200);
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS device_info VARCHAR(200);
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS authentication_method VARCHAR(20) DEFAULT 'PASSWORD';
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS mfa_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS security_level VARCHAR(20) DEFAULT 'STANDARD';
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS granted_permissions JSONB;
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS session_data JSONB;
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS last_accessed_url VARCHAR(500);
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS request_count INTEGER DEFAULT 0;
ALTER TABLE user_session ADD COLUMN IF NOT EXISTS secure_user_id BIGINT;

-- Add foreign key constraint for secure_user
ALTER TABLE user_session ADD CONSTRAINT IF NOT EXISTS fk_user_session_secure_user 
    FOREIGN KEY (secure_user_id) REFERENCES secure_user(id);

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Indexes for secure_user table
CREATE INDEX IF NOT EXISTS idx_secure_user_username ON secure_user(username);
CREATE INDEX IF NOT EXISTS idx_secure_user_email ON secure_user(email);
CREATE INDEX IF NOT EXISTS idx_secure_user_employee_id ON secure_user(employee_id);
CREATE INDEX IF NOT EXISTS idx_secure_user_last_login ON secure_user(last_login);
CREATE INDEX IF NOT EXISTS idx_secure_user_enabled ON secure_user(enabled);
CREATE INDEX IF NOT EXISTS idx_secure_user_branch_dept ON secure_user(branch_id, department_id);
CREATE INDEX IF NOT EXISTS idx_secure_user_status ON secure_user(enabled, account_locked, account_expired);
CREATE INDEX IF NOT EXISTS idx_secure_user_security ON secure_user(failed_login_attempts, last_failed_login);

-- Indexes for secure_role table
CREATE INDEX IF NOT EXISTS idx_secure_role_authority ON secure_role(authority);
CREATE INDEX IF NOT EXISTS idx_secure_role_category ON secure_role(category);
CREATE INDEX IF NOT EXISTS idx_secure_role_enabled ON secure_role(enabled);
CREATE INDEX IF NOT EXISTS idx_secure_role_level ON secure_role(level);
CREATE INDEX IF NOT EXISTS idx_secure_role_category_level ON secure_role(category, level);
CREATE INDEX IF NOT EXISTS idx_secure_role_status ON secure_role(enabled, system_role);

-- Indexes for secure_user_role table
CREATE INDEX IF NOT EXISTS idx_user_role_user ON secure_user_role(user_id);
CREATE INDEX IF NOT EXISTS idx_user_role_role ON secure_user_role(role_id);
CREATE INDEX IF NOT EXISTS idx_user_role_active ON secure_user_role(active);
CREATE INDEX IF NOT EXISTS idx_user_role_valid_from ON secure_user_role(valid_from);
CREATE INDEX IF NOT EXISTS idx_user_role_valid_to ON secure_user_role(valid_to);
CREATE INDEX IF NOT EXISTS idx_user_role_validity ON secure_user_role(valid_from, valid_to, active);
CREATE INDEX IF NOT EXISTS idx_user_role_assignment ON secure_user_role(assigned_by, date_created);

-- Indexes for security_audit_log table
CREATE INDEX IF NOT EXISTS idx_audit_event_type ON security_audit_log(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_event_category ON security_audit_log(event_category);
CREATE INDEX IF NOT EXISTS idx_audit_username ON security_audit_log(username);
CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON security_audit_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_ip_address ON security_audit_log(ip_address);
CREATE INDEX IF NOT EXISTS idx_audit_result ON security_audit_log(result);
CREATE INDEX IF NOT EXISTS idx_audit_security_level ON security_audit_log(security_level);
CREATE INDEX IF NOT EXISTS idx_audit_requires_review ON security_audit_log(requires_review);
CREATE INDEX IF NOT EXISTS idx_audit_user_time ON security_audit_log(username, timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_type_time ON security_audit_log(event_type, timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_security_time ON security_audit_log(security_level, timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_compliance ON security_audit_log(regulatory_category, requires_review);
CREATE INDEX IF NOT EXISTS idx_audit_session ON security_audit_log(session_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_ip_time ON security_audit_log(ip_address, timestamp);

-- Indexes for password_history table
CREATE INDEX IF NOT EXISTS idx_password_history_user ON password_history(user_id);
CREATE INDEX IF NOT EXISTS idx_password_history_hash ON password_history(password_hash);
CREATE INDEX IF NOT EXISTS idx_password_history_date ON password_history(date_created);
CREATE INDEX IF NOT EXISTS idx_password_history_algorithm ON password_history(hash_algorithm);
CREATE INDEX IF NOT EXISTS idx_password_user_date ON password_history(user_id, date_created);
CREATE INDEX IF NOT EXISTS idx_password_user_hash ON password_history(user_id, password_hash);
CREATE INDEX IF NOT EXISTS idx_password_compliance ON password_history(compliance_flags, date_created);

-- Enhanced indexes for user_session table
CREATE INDEX IF NOT EXISTS idx_session_id ON user_session(session_id);
CREATE INDEX IF NOT EXISTS idx_session_username ON user_session(username);
CREATE INDEX IF NOT EXISTS idx_session_active ON user_session(is_active);
CREATE INDEX IF NOT EXISTS idx_session_status ON user_session(session_status);
CREATE INDEX IF NOT EXISTS idx_session_last_activity ON user_session(last_activity);
CREATE INDEX IF NOT EXISTS idx_session_ip ON user_session(ip_address);
CREATE INDEX IF NOT EXISTS idx_session_user_active ON user_session(username, is_active);
CREATE INDEX IF NOT EXISTS idx_session_activity_status ON user_session(last_activity, session_status);
CREATE INDEX IF NOT EXISTS idx_session_security ON user_session(security_level, mfa_verified);
CREATE INDEX IF NOT EXISTS idx_session_timing ON user_session(session_start, session_end);

-- =====================================================
-- INSERT DEFAULT SECURITY ROLES
-- =====================================================

-- Insert system roles
INSERT INTO secure_role (authority, description, category, level, system_role, enabled, created_by) VALUES
('ROLE_SUPER_ADMIN', 'Super Administrator', 'SYSTEM', 1, TRUE, TRUE, 'SYSTEM'),
('ROLE_SYSTEM_ADMIN', 'System Administrator', 'SYSTEM', 2, TRUE, TRUE, 'SYSTEM'),
('ROLE_BRANCH_MANAGER', 'Branch Manager', 'MANAGEMENT', 3, FALSE, TRUE, 'SYSTEM'),
('ROLE_OPERATIONS_MANAGER', 'Operations Manager', 'MANAGEMENT', 4, FALSE, TRUE, 'SYSTEM'),
('ROLE_SENIOR_TELLER', 'Senior Teller', 'TELLER', 5, FALSE, TRUE, 'SYSTEM'),
('ROLE_TELLER', 'Teller', 'TELLER', 6, FALSE, TRUE, 'SYSTEM'),
('ROLE_LOAN_OFFICER', 'Loan Officer', 'LOAN_OFFICER', 5, FALSE, TRUE, 'SYSTEM'),
('ROLE_CUSTOMER_SERVICE', 'Customer Service Representative', 'CUSTOMER_SERVICE', 6, FALSE, TRUE, 'SYSTEM'),
('ROLE_AUDITOR', 'Auditor', 'AUDIT', 4, FALSE, TRUE, 'SYSTEM'),
('ROLE_COMPLIANCE_OFFICER', 'Compliance Officer', 'COMPLIANCE', 4, FALSE, TRUE, 'SYSTEM'),
('ROLE_SECURITY_OFFICER', 'Security Officer', 'SECURITY', 4, FALSE, TRUE, 'SYSTEM'),
('ROLE_IT_SUPPORT', 'IT Support', 'IT', 5, FALSE, TRUE, 'SYSTEM')
ON CONFLICT (authority) DO NOTHING;

-- =====================================================
-- SECURITY CONFIGURATION TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS security_configuration (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value VARCHAR(500) NOT NULL,
    description VARCHAR(255),
    category VARCHAR(50) DEFAULT 'GENERAL',
    is_encrypted BOOLEAN DEFAULT FALSE,
    is_system BOOLEAN DEFAULT FALSE,
    date_created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50)
);

-- Insert default security configurations
INSERT INTO security_configuration (config_key, config_value, description, category, is_system, created_by) VALUES
('SECURITY.PASSWORD_MIN_LENGTH', '8', 'Minimum password length requirement', 'PASSWORD_POLICY', TRUE, 'SYSTEM'),
('SECURITY.PASSWORD_COMPLEXITY', 'true', 'Require complex passwords', 'PASSWORD_POLICY', TRUE, 'SYSTEM'),
('SECURITY.MAX_LOGIN_ATTEMPTS', '5', 'Maximum failed login attempts before lockout', 'AUTHENTICATION', TRUE, 'SYSTEM'),
('SECURITY.SESSION_TIMEOUT', '1800', 'Session timeout in seconds (30 minutes)', 'SESSION', TRUE, 'SYSTEM'),
('SECURITY.PASSWORD_EXPIRY_DAYS', '90', 'Password expiry period in days', 'PASSWORD_POLICY', TRUE, 'SYSTEM'),
('SECURITY.ENABLE_2FA', 'false', 'Enable two-factor authentication', 'AUTHENTICATION', TRUE, 'SYSTEM'),
('SECURITY.PASSWORD_HISTORY_COUNT', '12', 'Number of previous passwords to remember', 'PASSWORD_POLICY', TRUE, 'SYSTEM'),
('SECURITY.ACCOUNT_LOCKOUT_DURATION', '900', 'Account lockout duration in seconds (15 minutes)', 'AUTHENTICATION', TRUE, 'SYSTEM'),
('SECURITY.JWT_EXPIRATION', '86400', 'JWT token expiration in seconds (24 hours)', 'JWT', TRUE, 'SYSTEM'),
('SECURITY.JWT_REFRESH_EXPIRATION', '604800', 'JWT refresh token expiration in seconds (7 days)', 'JWT', TRUE, 'SYSTEM')
ON CONFLICT (config_key) DO NOTHING;

-- Create index for security configuration
CREATE INDEX IF NOT EXISTS idx_security_config_key ON security_configuration(config_key);
CREATE INDEX IF NOT EXISTS idx_security_config_category ON security_configuration(category);

-- =====================================================
-- MIGRATION COMPLETION LOG
-- =====================================================

INSERT INTO migration_log (migration_name, executed_at, status, description) VALUES
('009_security_framework_tables', CURRENT_TIMESTAMP, 'SUCCESS', 'Created comprehensive security framework tables with modern authentication and authorization features')
ON CONFLICT (migration_name) DO UPDATE SET 
    executed_at = CURRENT_TIMESTAMP,
    status = 'SUCCESS',
    description = 'Updated comprehensive security framework tables with modern authentication and authorization features';

COMMIT;
