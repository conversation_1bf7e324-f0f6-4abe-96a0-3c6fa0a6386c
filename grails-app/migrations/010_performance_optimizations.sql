-- QwikBanka Database Migration Script
-- Phase 1.3: Performance Optimizations
-- Version: 1.0.0
-- Date: 2025-01-27

-- =====================================================
-- PERFORMANCE OPTIMIZATION INDEXES
-- =====================================================

-- Customer table optimizations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_search_name 
ON customer USING gin(to_tsvector('english', display_name));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_branch_status 
ON customer(branch_id, status_id) 
WHERE status_id IN (1, 2, 5); -- Active statuses only

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_created_date 
ON customer(date_created);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_updated_date 
ON customer(last_updated);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_type_branch 
ON customer(customer_type_id, branch_id);

-- Deposit table optimizations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deposit_account_lookup 
ON deposit(acct_no) 
WHERE status_id = 1; -- Active deposits only

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deposit_customer_type 
ON deposit(customer_id, type_id, status_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deposit_balance_range 
ON deposit(available_balance) 
WHERE available_balance > 0;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deposit_maturity_date 
ON deposit(maturity_date) 
WHERE maturity_date >= CURRENT_DATE;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deposit_branch_type 
ON deposit(branch_id, type_id, status_id);

-- Loan table optimizations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_loan_account_lookup 
ON loan(account_no) 
WHERE status_id IN (1, 2, 3); -- Active loan statuses

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_loan_maturity_date 
ON loan(maturity_date) 
WHERE maturity_date >= CURRENT_DATE;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_loan_customer_status 
ON loan(customer_id, status_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_loan_balance_amount 
ON loan(balance_amount) 
WHERE balance_amount > 0;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_loan_performance_class 
ON loan(performance_classification_id, status_id);

-- Transaction file optimizations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_txn_file_date_branch 
ON txn_file(txn_date, branch_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_txn_file_amount_type 
ON txn_file(amount, txn_template_id) 
WHERE amount > 10000; -- Large transactions

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_txn_file_customer_date 
ON txn_file(customer_id, txn_date) 
WHERE customer_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_txn_file_status_date 
ON txn_file(status, txn_date);

-- GL Transaction optimizations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_gl_txn_date_branch 
ON gl_transaction(txn_date, branch_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_gl_txn_account_date 
ON gl_transaction(gl_account_id, txn_date);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_gl_txn_amount_range 
ON gl_transaction(amount) 
WHERE amount != 0;

-- User and security optimizations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_master_branch_active 
ON user_master(branch_id, enabled) 
WHERE enabled = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_master_last_login 
ON user_master(last_login) 
WHERE last_login IS NOT NULL;

-- =====================================================
-- TABLE PARTITIONING FOR LARGE DATASETS
-- =====================================================

-- Create partitioned transaction table for better performance
CREATE TABLE IF NOT EXISTS txn_file_partitioned (
    LIKE txn_file INCLUDING ALL
) PARTITION BY RANGE (txn_date);

-- Create monthly partitions for current and next 12 months
DO $$
DECLARE
    start_date DATE;
    end_date DATE;
    partition_name TEXT;
BEGIN
    FOR i IN 0..12 LOOP
        start_date := DATE_TRUNC('month', CURRENT_DATE) + (i || ' months')::INTERVAL;
        end_date := start_date + '1 month'::INTERVAL;
        partition_name := 'txn_file_' || TO_CHAR(start_date, 'YYYY_MM');
        
        -- Check if partition already exists
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = partition_name
        ) THEN
            EXECUTE format('CREATE TABLE %I PARTITION OF txn_file_partitioned 
                           FOR VALUES FROM (%L) TO (%L)', 
                           partition_name, start_date, end_date);
        END IF;
    END LOOP;
END $$;

-- Create partitioned audit log table
CREATE TABLE IF NOT EXISTS security_audit_log_partitioned (
    LIKE security_audit_log INCLUDING ALL
) PARTITION BY RANGE (timestamp);

-- Create monthly partitions for audit logs
DO $$
DECLARE
    start_date DATE;
    end_date DATE;
    partition_name TEXT;
BEGIN
    FOR i IN 0..12 LOOP
        start_date := DATE_TRUNC('month', CURRENT_DATE) + (i || ' months')::INTERVAL;
        end_date := start_date + '1 month'::INTERVAL;
        partition_name := 'security_audit_log_' || TO_CHAR(start_date, 'YYYY_MM');
        
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = partition_name
        ) THEN
            EXECUTE format('CREATE TABLE %I PARTITION OF security_audit_log_partitioned 
                           FOR VALUES FROM (%L) TO (%L)', 
                           partition_name, start_date, end_date);
        END IF;
    END LOOP;
END $$;

-- =====================================================
-- MATERIALIZED VIEWS FOR REPORTING
-- =====================================================

-- Customer summary materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_customer_summary AS
SELECT 
    c.id,
    c.customer_id,
    c.display_name,
    c.branch_id,
    b.name as branch_name,
    COUNT(d.id) as deposit_count,
    COALESCE(SUM(d.available_balance), 0) as total_deposits,
    COUNT(l.id) as loan_count,
    COALESCE(SUM(l.balance_amount), 0) as total_loans,
    c.date_created,
    c.last_updated
FROM customer c
LEFT JOIN branch b ON c.branch_id = b.id
LEFT JOIN deposit d ON c.id = d.customer_id AND d.status_id = 1
LEFT JOIN loan l ON c.id = l.customer_id AND l.status_id IN (1, 2, 3)
WHERE c.status_id IN (1, 2, 5)
GROUP BY c.id, c.customer_id, c.display_name, c.branch_id, b.name, c.date_created, c.last_updated;

-- Create unique index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_customer_summary_id 
ON mv_customer_summary(id);

-- Branch performance materialized view
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_branch_performance AS
SELECT 
    b.id,
    b.code,
    b.name,
    COUNT(DISTINCT c.id) as customer_count,
    COUNT(DISTINCT d.id) as deposit_count,
    COALESCE(SUM(d.available_balance), 0) as total_deposits,
    COUNT(DISTINCT l.id) as loan_count,
    COALESCE(SUM(l.balance_amount), 0) as total_loans,
    CURRENT_DATE as report_date
FROM branch b
LEFT JOIN customer c ON b.id = c.branch_id AND c.status_id IN (1, 2, 5)
LEFT JOIN deposit d ON c.id = d.customer_id AND d.status_id = 1
LEFT JOIN loan l ON c.id = l.customer_id AND l.status_id IN (1, 2, 3)
WHERE b.enabled = true
GROUP BY b.id, b.code, b.name;

-- Create unique index on branch performance view
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_branch_performance_id 
ON mv_branch_performance(id);

-- =====================================================
-- STORED PROCEDURES FOR COMMON OPERATIONS
-- =====================================================

-- Function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_performance_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_customer_summary;
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_branch_performance;
END;
$$ LANGUAGE plpgsql;

-- Function to get customer balance summary
CREATE OR REPLACE FUNCTION get_customer_balance_summary(customer_id_param BIGINT)
RETURNS TABLE(
    total_deposits DECIMAL(15,2),
    total_loans DECIMAL(15,2),
    net_position DECIMAL(15,2),
    deposit_count INTEGER,
    loan_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(SUM(d.available_balance), 0) as total_deposits,
        COALESCE(SUM(l.balance_amount), 0) as total_loans,
        COALESCE(SUM(d.available_balance), 0) - COALESCE(SUM(l.balance_amount), 0) as net_position,
        COUNT(d.id)::INTEGER as deposit_count,
        COUNT(l.id)::INTEGER as loan_count
    FROM customer c
    LEFT JOIN deposit d ON c.id = d.customer_id AND d.status_id = 1
    LEFT JOIN loan l ON c.id = l.customer_id AND l.status_id IN (1, 2, 3)
    WHERE c.id = customer_id_param;
END;
$$ LANGUAGE plpgsql;

-- Function for transaction search with pagination
CREATE OR REPLACE FUNCTION search_transactions(
    customer_id_param BIGINT DEFAULT NULL,
    branch_id_param BIGINT DEFAULT NULL,
    start_date DATE DEFAULT NULL,
    end_date DATE DEFAULT NULL,
    min_amount DECIMAL DEFAULT NULL,
    max_amount DECIMAL DEFAULT NULL,
    page_size INTEGER DEFAULT 50,
    page_offset INTEGER DEFAULT 0
)
RETURNS TABLE(
    id BIGINT,
    txn_date DATE,
    amount DECIMAL(15,2),
    description TEXT,
    customer_name VARCHAR(255),
    branch_name VARCHAR(255)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id,
        t.txn_date,
        t.amount,
        t.description,
        c.display_name as customer_name,
        b.name as branch_name
    FROM txn_file t
    LEFT JOIN customer c ON t.customer_id = c.id
    LEFT JOIN branch b ON t.branch_id = b.id
    WHERE 
        (customer_id_param IS NULL OR t.customer_id = customer_id_param)
        AND (branch_id_param IS NULL OR t.branch_id = branch_id_param)
        AND (start_date IS NULL OR t.txn_date >= start_date)
        AND (end_date IS NULL OR t.txn_date <= end_date)
        AND (min_amount IS NULL OR t.amount >= min_amount)
        AND (max_amount IS NULL OR t.amount <= max_amount)
    ORDER BY t.txn_date DESC, t.id DESC
    LIMIT page_size OFFSET page_offset;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- AUTOMATED MAINTENANCE PROCEDURES
-- =====================================================

-- Function to cleanup old audit logs
CREATE OR REPLACE FUNCTION cleanup_old_audit_logs(retention_days INTEGER DEFAULT 365)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM security_audit_log 
    WHERE timestamp < CURRENT_DATE - INTERVAL '1 day' * retention_days;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to update table statistics
CREATE OR REPLACE FUNCTION update_table_statistics()
RETURNS void AS $$
BEGIN
    ANALYZE customer;
    ANALYZE deposit;
    ANALYZE loan;
    ANALYZE txn_file;
    ANALYZE gl_transaction;
    ANALYZE security_audit_log;
    ANALYZE user_session;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- PERFORMANCE MONITORING VIEWS
-- =====================================================

-- View for slow queries monitoring
CREATE OR REPLACE VIEW v_slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE mean_time > 100 -- Queries taking more than 100ms on average
ORDER BY mean_time DESC;

-- View for table size monitoring
CREATE OR REPLACE VIEW v_table_sizes AS
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_stats 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- =====================================================
-- SCHEDULED MAINTENANCE JOBS
-- =====================================================

-- Create extension for cron jobs if not exists
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Schedule materialized view refresh (every hour)
SELECT cron.schedule('refresh-mv', '0 * * * *', 'SELECT refresh_performance_views();');

-- Schedule statistics update (daily at 2 AM)
SELECT cron.schedule('update-stats', '0 2 * * *', 'SELECT update_table_statistics();');

-- Schedule old audit log cleanup (weekly on Sunday at 3 AM)
SELECT cron.schedule('cleanup-audit', '0 3 * * 0', 'SELECT cleanup_old_audit_logs(365);');

-- =====================================================
-- MIGRATION COMPLETION LOG
-- =====================================================

INSERT INTO migration_log (migration_name, executed_at, status, description) VALUES
('010_performance_optimizations', CURRENT_TIMESTAMP, 'SUCCESS', 'Applied comprehensive performance optimizations including indexes, partitioning, materialized views, and stored procedures')
ON CONFLICT (migration_name) DO UPDATE SET 
    executed_at = CURRENT_TIMESTAMP,
    status = 'SUCCESS',
    description = 'Updated comprehensive performance optimizations including indexes, partitioning, materialized views, and stored procedures';

COMMIT;
