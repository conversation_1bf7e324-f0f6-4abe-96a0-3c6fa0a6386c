package org.icbs.validation

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import groovy.sql.Sql
import org.icbs.common.CommonUtilityService
import org.icbs.admin.TxnTemplate
import org.icbs.gl.CfgAcctGlTemplateDet
import org.icbs.gl.GlSortCode
import org.icbs.admin.Holiday

/**
 * Unified Validation Service
 * Consolidates all validation logic across the application
 * Replaces duplicate validation methods in multiple controllers
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class UnifiedValidationService {
    
    CommonUtilityService commonUtilityService
    def dataSource
    
    // =====================================================
    // DUPLICATE VALIDATION (CONSOLIDATED)
    // =====================================================
    
    /**
     * Validate transaction template code uniqueness
     * Replaces TxnTemplateController.validateExistingCodeAjax()
     */
    Map validateTxnTemplateCode(String code, Long txnTypeId, Long excludeId = null) {
        try {
            def existing = TxnTemplate.createCriteria().list {
                eq('code', code)
                eq('txnType.id', txnTypeId)
                if (excludeId) {
                    ne('id', excludeId)
                }
            }
            
            return [
                isValid: existing.isEmpty(),
                isDuplicate: !existing.isEmpty(),
                existingRecords: existing,
                message: existing.isEmpty() ? 'Code is available' : 'Code already exists for this transaction type'
            ]
            
        } catch (Exception e) {
            log.error("Error validating transaction template code", e)
            return [
                isValid: false,
                error: true,
                message: "Validation error: ${e.message}"
            ]
        }
    }
    
    /**
     * Validate GL template detail duplicates
     * Replaces CfgAcctGlTemplateDetController.checkduplicatesCfgglAcctDetAjax()
     */
    Map validateGlTemplateDetail(Long templateId, String ordinalValue, Long statusType, Long transactionType, Long excludeId = null) {
        try {
            Sql sql = new Sql(dataSource)
            
            String query = """
                SELECT * FROM cfg_acct_gl_template_det 
                WHERE gl_template_id = ? 
                AND ordinal_pos = ? 
                AND status = ? 
                AND transaction_type = ?
            """
            
            List<Object> params = [templateId, ordinalValue, statusType, transactionType]
            
            if (excludeId) {
                query += " AND id != ?"
                params.add(excludeId)
            }
            
            def results = sql.rows(query, params)
            sql.close()
            
            return [
                isValid: results.isEmpty(),
                isDuplicate: !results.isEmpty(),
                existingRecords: results,
                message: results.isEmpty() ? 'Configuration is valid' : 'Duplicate configuration found'
            ]
            
        } catch (Exception e) {
            log.error("Error validating GL template detail", e)
            return [
                isValid: false,
                error: true,
                message: "Validation error: ${e.message}"
            ]
        }
    }
    
    /**
     * Validate GL Sort Code uniqueness
     * Replaces duplicate logic in GlSortCodeController.save()
     */
    Map validateGlSortCode(String sortCode, Long excludeId = null) {
        try {
            def existing = GlSortCode.createCriteria().list {
                eq('sort_code', sortCode)
                if (excludeId) {
                    ne('id', excludeId)
                }
            }
            
            return [
                isValid: existing.isEmpty(),
                isDuplicate: !existing.isEmpty(),
                existingRecords: existing,
                message: existing.isEmpty() ? 'Sort code is available' : 'Duplicate sort code'
            ]
            
        } catch (Exception e) {
            log.error("Error validating GL sort code", e)
            return [
                isValid: false,
                error: true,
                message: "Validation error: ${e.message}"
            ]
        }
    }
    
    /**
     * Validate Holiday uniqueness and business rules
     * Replaces duplicate logic in HolidayController.save()
     */
    Map validateHoliday(String description, Date holidayDate, Long excludeId = null) {
        return commonUtilityService.validateHoliday(description, holidayDate, excludeId)
    }
    
    // =====================================================
    // CUSTOMER VALIDATION (CONSOLIDATED)
    // =====================================================
    
    /**
     * Comprehensive customer validation
     * Replaces CustomerController.customerVerificationValidationAjax()
     */
    Map validateCustomer(Map customerData, Long excludeId = null) {
        Map result = [
            isValid: true,
            errors: [],
            warnings: [],
            possibleDuplicates: []
        ]
        
        try {
            // Check for potential duplicates
            def duplicates = commonUtilityService.findDuplicateCustomers(customerData, excludeId)
            if (duplicates) {
                result.possibleDuplicates = duplicates
                result.warnings << "Potential duplicate customers found"
            }
            
            // Validate required fields
            if (!customerData.name1 || customerData.name1.toString().trim().isEmpty()) {
                result.isValid = false
                result.errors << "First name is required"
            }
            
            if (!customerData.lastName || customerData.lastName.toString().trim().isEmpty()) {
                result.isValid = false
                result.errors << "Last name is required"
            }
            
            // Validate email format if provided
            if (customerData.email && !isValidEmail(customerData.email.toString())) {
                result.isValid = false
                result.errors << "Invalid email format"
            }
            
            // Validate phone format if provided
            if (customerData.phone && !isValidPhone(customerData.phone.toString())) {
                result.warnings << "Phone number format may be invalid"
            }
            
            // Validate birth date
            if (customerData.birthDate) {
                Date birthDate = customerData.birthDate instanceof Date ? 
                    customerData.birthDate : Date.parse('yyyy-MM-dd', customerData.birthDate.toString())
                
                if (birthDate.after(new Date())) {
                    result.isValid = false
                    result.errors << "Birth date cannot be in the future"
                }
                
                // Check minimum age (18 years)
                Calendar cal = Calendar.getInstance()
                cal.add(Calendar.YEAR, -18)
                if (birthDate.after(cal.time)) {
                    result.warnings << "Customer appears to be under 18 years old"
                }
            }
            
        } catch (Exception e) {
            log.error("Error validating customer", e)
            result.isValid = false
            result.errors << "Validation error: ${e.message}"
        }
        
        return result
    }
    
    // =====================================================
    // BUSINESS RULE VALIDATION
    // =====================================================
    
    /**
     * Validate account number format
     */
    boolean validateAccountNumber(String accountNumber, String accountType = null) {
        if (!accountNumber) return false
        
        // Basic format validation
        if (!accountNumber.matches(/^\d{10,16}$/)) {
            return false
        }
        
        // Account type specific validation
        switch (accountType?.toUpperCase()) {
            case 'SAVINGS':
                return accountNumber.startsWith('1')
            case 'CHECKING':
                return accountNumber.startsWith('2')
            case 'LOAN':
                return accountNumber.startsWith('3')
            default:
                return true // Generic validation passed
        }
    }
    
    /**
     * Validate transaction amount
     */
    Map validateTransactionAmount(BigDecimal amount, String transactionType, Map accountDetails = [:]) {
        Map result = [isValid: true, errors: [], warnings: []]
        
        try {
            // Basic amount validation
            if (!amount || amount <= 0) {
                result.isValid = false
                result.errors << "Amount must be greater than zero"
                return result
            }
            
            // Transaction type specific validation
            switch (transactionType?.toUpperCase()) {
                case 'WITHDRAWAL':
                    if (accountDetails.availableBalance && amount > accountDetails.availableBalance) {
                        result.isValid = false
                        result.errors << "Insufficient funds"
                    }
                    break
                    
                case 'TRANSFER':
                    if (accountDetails.dailyTransferLimit && amount > accountDetails.dailyTransferLimit) {
                        result.isValid = false
                        result.errors << "Amount exceeds daily transfer limit"
                    }
                    break
                    
                case 'DEPOSIT':
                    if (amount > 10000) {
                        result.warnings << "Large deposit amount - may require additional documentation"
                    }
                    break
            }
            
            // General large amount warning
            if (amount > 50000) {
                result.warnings << "Large transaction amount - supervisor approval may be required"
            }
            
        } catch (Exception e) {
            log.error("Error validating transaction amount", e)
            result.isValid = false
            result.errors << "Amount validation error: ${e.message}"
        }
        
        return result
    }
    
    /**
     * Validate date range for reports
     */
    Map validateDateRange(Date startDate, Date endDate, int maxDaysRange = 365) {
        Map result = [isValid: true, errors: [], warnings: []]
        
        try {
            if (!startDate || !endDate) {
                result.isValid = false
                result.errors << "Both start and end dates are required"
                return result
            }
            
            if (startDate.after(endDate)) {
                result.isValid = false
                result.errors << "Start date cannot be after end date"
                return result
            }
            
            if (endDate.after(new Date())) {
                result.isValid = false
                result.errors << "End date cannot be in the future"
                return result
            }
            
            // Check range limit
            long daysDiff = (endDate.time - startDate.time) / (24 * 60 * 60 * 1000)
            if (daysDiff > maxDaysRange) {
                result.warnings << "Date range exceeds ${maxDaysRange} days - report may take longer to generate"
            }
            
        } catch (Exception e) {
            log.error("Error validating date range", e)
            result.isValid = false
            result.errors << "Date validation error: ${e.message}"
        }
        
        return result
    }
    
    // =====================================================
    // UTILITY VALIDATION METHODS
    // =====================================================
    
    /**
     * Validate email format
     */
    boolean isValidEmail(String email) {
        if (!email) return false
        return email.matches(/^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\.[A-Za-z]{2,})$/)
    }
    
    /**
     * Validate phone number format
     */
    boolean isValidPhone(String phone) {
        if (!phone) return false
        // Remove all non-digits
        String digitsOnly = phone.replaceAll(/\D/, '')
        // Check if it's a valid length (10-15 digits)
        return digitsOnly.length() >= 10 && digitsOnly.length() <= 15
    }
    
    /**
     * Validate currency code
     */
    boolean isValidCurrencyCode(String currencyCode) {
        if (!currencyCode) return false
        return currencyCode.matches(/^[A-Z]{3}$/)
    }
    
    /**
     * Validate routing number
     */
    boolean isValidRoutingNumber(String routingNumber) {
        if (!routingNumber || routingNumber.length() != 9) return false
        
        // Check if all digits
        if (!routingNumber.matches(/^\d{9}$/)) return false
        
        // Validate checksum (simplified)
        int[] digits = routingNumber.chars.collect { (it as char) - '0' as char }
        int checksum = (3 * (digits[0] + digits[3] + digits[6]) +
                       7 * (digits[1] + digits[4] + digits[7]) +
                       (digits[2] + digits[5] + digits[8])) % 10
        
        return checksum == 0
    }
    
    /**
     * Generic field validation
     */
    Map validateField(String fieldName, Object value, Map constraints = [:]) {
        Map result = [isValid: true, errors: []]
        
        try {
            // Required validation
            if (constraints.required && (!value || value.toString().trim().isEmpty())) {
                result.isValid = false
                result.errors << "${fieldName} is required"
                return result
            }
            
            if (!value) return result // Skip other validations if value is null/empty and not required
            
            String stringValue = value.toString().trim()
            
            // Length validation
            if (constraints.minLength && stringValue.length() < constraints.minLength) {
                result.isValid = false
                result.errors << "${fieldName} must be at least ${constraints.minLength} characters"
            }
            
            if (constraints.maxLength && stringValue.length() > constraints.maxLength) {
                result.isValid = false
                result.errors << "${fieldName} cannot exceed ${constraints.maxLength} characters"
            }
            
            // Pattern validation
            if (constraints.pattern && !stringValue.matches(constraints.pattern)) {
                result.isValid = false
                result.errors << "${fieldName} format is invalid"
            }
            
            // Numeric validation
            if (constraints.numeric) {
                try {
                    BigDecimal numValue = new BigDecimal(stringValue)
                    if (constraints.min && numValue < constraints.min) {
                        result.isValid = false
                        result.errors << "${fieldName} must be at least ${constraints.min}"
                    }
                    if (constraints.max && numValue > constraints.max) {
                        result.isValid = false
                        result.errors << "${fieldName} cannot exceed ${constraints.max}"
                    }
                } catch (NumberFormatException e) {
                    result.isValid = false
                    result.errors << "${fieldName} must be a valid number"
                }
            }
            
        } catch (Exception e) {
            log.error("Error validating field ${fieldName}", e)
            result.isValid = false
            result.errors << "Validation error for ${fieldName}: ${e.message}"
        }
        
        return result
    }
}
