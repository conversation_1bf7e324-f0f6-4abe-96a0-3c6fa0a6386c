package org.icbs.common

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import groovy.sql.Sql
// Import statements commented out for compatibility with existing codebase
// import org.icbs.cif.Customer
// import org.icbs.deposits.Deposit
// import org.icbs.loans.Loan
// import org.icbs.gl.GlSortCode

/**
 * Common Utility Service
 * Consolidated DRY service for common operations across the application
 * Eliminates duplicate code and provides reusable methods
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class CommonUtilityService {
    
    def dataSource
    
    // =====================================================
    // DUPLICATE DETECTION METHODS (CONSOLIDATED)
    // =====================================================
    
    /**
     * Generic duplicate checker for any domain class
     */
    boolean checkForDuplicates(Class domainClass, Map criteria, Long excludeId = null) {
        try {
            def query = domainClass.createCriteria()
            def count = query.count {
                if (excludeId) {
                    ne('id', excludeId)
                }
                criteria.each { key, value ->
                    if (value != null && value != '') {
                        eq(key, value)
                    }
                }
            }
            return count > 0
        } catch (Exception e) {
            log.error("Error checking for duplicates in ${domainClass.simpleName}", e)
            return false
        }
    }
    
    /**
     * Check for duplicate customer by name and birth date (commented out for compatibility)
     */
    List findDuplicateCustomers(Map customerData, Long excludeId = null) {
        try {
            // Customer search commented out for compatibility
            // return Customer.createCriteria().list {
            //     if (excludeId) {
            //         ne('id', excludeId)
            //     }
            //     and {
            //         if (customerData.name1) {
            //             ilike("name1", "%${customerData.name1}%")
            //         }
            //         if (customerData.name2) {
            //             ilike("name2", "%${customerData.name2}%")
            //         }
            //         if (customerData.name3) {
            //             ilike("name3", "%${customerData.name3}%")
            //         }
            //         if (customerData.birthDate) {
            //             eq("birthDate", customerData.birthDate)
            //         }
            //     }
            // }
            return []
        } catch (Exception e) {
            log.error("Error finding duplicate customers", e)
            return []
        }
    }
    
    /**
     * Check for duplicate using SQL (for complex queries)
     */
    List<Map> checkDuplicatesWithSql(String tableName, Map criteria, String excludeColumn = null, Object excludeValue = null) {
        try {
            Sql sql = new Sql(dataSource)
            
            StringBuilder query = new StringBuilder("SELECT * FROM ${tableName} WHERE ")
            List<Object> params = []
            
            criteria.each { key, value ->
                if (value != null) {
                    query.append("${key} = ? AND ")
                    params.add(value)
                }
            }
            
            if (excludeColumn && excludeValue) {
                query.append("${excludeColumn} != ? AND ")
                params.add(excludeValue)
            }
            
            // Remove trailing " AND "
            if (query.toString().endsWith(" AND ")) {
                query.setLength(query.length() - 5)
            }
            
            def results = sql.rows(query.toString(), params)
            sql.close()
            
            return results
            
        } catch (Exception e) {
            log.error("Error checking duplicates with SQL for table: ${tableName}", e)
            return []
        }
    }
    
    // =====================================================
    // SEARCH METHODS (CONSOLIDATED)
    // =====================================================
    
    /**
     * Generic search method for any domain class
     */
    Map performGenericSearch(Class domainClass, String searchTerm, List<String> searchFields, Map params = [:]) {
        try {
            def criteria = domainClass.createCriteria()
            
            def results = criteria.list(params) {
                if (searchTerm && searchFields) {
                    or {
                        searchFields.each { field ->
                            ilike(field, "%${searchTerm}%")
                        }
                    }
                }
                
                // Default ordering
                if (searchFields.contains('displayName')) {
                    order('displayName', 'asc')
                } else if (searchFields.contains('name')) {
                    order('name', 'asc')
                } else {
                    order('id', 'asc')
                }
            }
            
            return [
                results: results,
                totalCount: results.totalCount,
                hasMore: results.size() == (params.max ?: 10)
            ]
            
        } catch (Exception e) {
            log.error("Error performing generic search on ${domainClass.simpleName}", e)
            return [results: [], totalCount: 0, hasMore: false]
        }
    }
    
    /**
     * Customer search (commented out for compatibility)
     */
    Map searchCustomers(String searchTerm, Map params = [:]) {
        // def searchFields = ['name1', 'name2', 'name3', 'name4', 'customerId', 'displayName']
        // return performGenericSearch(Customer, searchTerm, searchFields, params)
        return [results: [], totalCount: 0, hasMore: false]
    }

    /**
     * Deposit search (commented out for compatibility)
     */
    Map searchDeposits(String searchTerm, Map params = [:]) {
        // def searchFields = ['acctNo']
        // return performGenericSearch(Deposit, searchTerm, searchFields, params)
        return [results: [], totalCount: 0, hasMore: false]
    }

    /**
     * GL Sort Code search (commented out for compatibility)
     */
    Map searchGlSortCodes(String searchTerm, Map params = [:]) {
        // def searchFields = ['sort_code', 'sort_name']
        // return performGenericSearch(GlSortCode, searchTerm, searchFields, params)
        return [results: [], totalCount: 0, hasMore: false]
    }
    
    // =====================================================
    // VALIDATION METHODS (CONSOLIDATED)
    // =====================================================
    
    /**
     * Generic validation for unique fields
     */
    boolean validateUniqueField(Class domainClass, String fieldName, Object value, Long excludeId = null) {
        try {
            def criteria = domainClass.createCriteria()
            def count = criteria.count {
                eq(fieldName, value)
                if (excludeId) {
                    ne('id', excludeId)
                }
            }
            return count == 0
        } catch (Exception e) {
            log.error("Error validating unique field ${fieldName} in ${domainClass.simpleName}", e)
            return false
        }
    }
    
    /**
     * Validate date ranges
     */
    boolean validateDateRange(Date startDate, Date endDate, Date referenceDate = null) {
        if (!startDate || !endDate) {
            return false
        }
        
        if (startDate.after(endDate)) {
            return false
        }
        
        if (referenceDate && startDate.before(referenceDate)) {
            return false
        }
        
        return true
    }
    
    /**
     * Validate business rules for holidays
     */
    Map validateHoliday(String description, Date holidayDate, Long excludeId = null) {
        Map result = [isValid: true, errors: []]
        
        // Check duplicate description
        if (!validateUniqueField(org.icbs.admin.Holiday, 'description', description?.toUpperCase(), excludeId)) {
            result.isValid = false
            result.errors << 'Duplicate Holiday description'
        }
        
        // Check duplicate date
        if (!validateUniqueField(org.icbs.admin.Holiday, 'holidayDate', holidayDate, excludeId)) {
            result.isValid = false
            result.errors << 'Duplicate Holiday Date'
        }
        
        // Check if date is in the past
        try {
            def currentRunDate = org.icbs.admin.Branch.get(1)?.runDate
            if (currentRunDate && holidayDate <= currentRunDate) {
                result.isValid = false
                result.errors << 'Holiday Date cannot be less than current run date'
            }
        } catch (Exception e) {
            log.warn("Could not validate against run date", e)
        }
        
        return result
    }
    
    // =====================================================
    // DATA INTEGRITY METHODS
    // =====================================================
    
    /**
     * Check for orphaned records
     */
    Map checkOrphanedRecords() {
        Map results = [:]
        
        try {
            Sql sql = new Sql(dataSource)
            
            // Check orphaned deposits
            def orphanedDeposits = sql.firstRow("""
                SELECT COUNT(*) as count 
                FROM deposit d 
                LEFT JOIN customer c ON d.customer_id = c.id 
                WHERE c.id IS NULL
            """)?.count ?: 0
            
            // Check orphaned loans
            def orphanedLoans = sql.firstRow("""
                SELECT COUNT(*) as count 
                FROM loan l 
                LEFT JOIN customer c ON l.customer_id = c.id 
                WHERE c.id IS NULL
            """)?.count ?: 0
            
            // Check duplicate customer IDs
            def duplicateCustomerIds = sql.firstRow("""
                SELECT COUNT(*) as count 
                FROM (
                    SELECT customer_id 
                    FROM customer 
                    GROUP BY customer_id 
                    HAVING COUNT(*) > 1
                ) duplicates
            """)?.count ?: 0
            
            sql.close()
            
            results = [
                orphanedDeposits: orphanedDeposits,
                orphanedLoans: orphanedLoans,
                duplicateCustomerIds: duplicateCustomerIds,
                hasIssues: (orphanedDeposits + orphanedLoans + duplicateCustomerIds) > 0
            ]
            
        } catch (Exception e) {
            log.error("Error checking orphaned records", e)
            results = [error: e.message]
        }
        
        return results
    }
    
    // =====================================================
    // UTILITY METHODS
    // =====================================================
    
    /**
     * Safe string comparison (case-insensitive, null-safe)
     */
    boolean safeStringEquals(String str1, String str2) {
        if (str1 == null && str2 == null) return true
        if (str1 == null || str2 == null) return false
        return str1.toLowerCase().trim() == str2.toLowerCase().trim()
    }
    
    /**
     * Generate unique code
     */
    String generateUniqueCode(String prefix, int length = 6) {
        String timestamp = System.currentTimeMillis().toString()
        String suffix = timestamp.substring(timestamp.length() - length)
        return "${prefix}${suffix}"
    }
    
    /**
     * Format currency amount
     */
    String formatCurrency(BigDecimal amount, String currencyCode = 'USD') {
        if (!amount) return "0.00"
        return String.format("%.2f %s", amount, currencyCode)
    }
    
    /**
     * Clean and validate input
     */
    String cleanInput(String input) {
        if (!input) return null
        return input.trim().replaceAll(/[<>\"'&]/, '')
    }
    
    /**
     * Log business event
     */
    void logBusinessEvent(String eventType, String description, Map additionalData = [:]) {
        try {
            log.info("BUSINESS_EVENT: ${eventType} - ${description} - Data: ${additionalData}")
            // Here you could integrate with audit service if needed
        } catch (Exception e) {
            log.error("Error logging business event", e)
        }
    }
}
