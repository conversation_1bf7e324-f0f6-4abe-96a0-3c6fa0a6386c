package org.icbs.microservices

import grails.gorm.transactions.Transactional
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.beans.factory.annotation.Value
import java.util.concurrent.ConcurrentHashMap

/**
 * ARCHITECTURE MODERNIZATION: Service Discovery for Microservices
 * Manages service registration, discovery, and health monitoring
 */
@Transactional
class ServiceDiscoveryService {

    @Value('${app.service.name:qwikbanka-core}')
    String serviceName

    @Value('${app.service.version:1.0.0}')
    String serviceVersion

    @Value('${server.port:8080}')
    Integer serverPort

    private final Map<String, ServiceInstance> serviceRegistry = new ConcurrentHashMap<>()
    private final Map<String, Date> lastHealthCheck = new ConcurrentHashMap<>()

    /**
     * Register a service instance
     */
    void registerService(ServiceRegistration registration) {
        try {
            def instance = new ServiceInstance(
                serviceId: registration.serviceId,
                serviceName: registration.serviceName,
                host: registration.host,
                port: registration.port,
                version: registration.version,
                metadata: registration.metadata,
                healthCheckUrl: registration.healthCheckUrl,
                status: ServiceStatus.UP,
                registeredAt: new Date()
            )
            
            serviceRegistry.put(registration.serviceId, instance)
            lastHealthCheck.put(registration.serviceId, new Date())
            
            log.info("Service registered: ${registration.serviceName} (${registration.serviceId}) at ${registration.host}:${registration.port}")
            
        } catch (Exception e) {
            log.error("Error registering service: ${e.message}", e)
            throw new ServiceRegistrationException("Failed to register service: ${e.message}")
        }
    }

    /**
     * Deregister a service instance
     */
    void deregisterService(String serviceId) {
        try {
            def instance = serviceRegistry.remove(serviceId)
            lastHealthCheck.remove(serviceId)
            
            if (instance) {
                log.info("Service deregistered: ${instance.serviceName} (${serviceId})")
            } else {
                log.warn("Attempted to deregister unknown service: ${serviceId}")
            }
            
        } catch (Exception e) {
            log.error("Error deregistering service: ${e.message}", e)
        }
    }

    /**
     * Discover services by name
     */
    List<ServiceInstance> discoverServices(String serviceName) {
        try {
            return serviceRegistry.values()
                .findAll { it.serviceName == serviceName && it.status == ServiceStatus.UP }
                .sort { it.registeredAt }
            
        } catch (Exception e) {
            log.error("Error discovering services: ${e.message}", e)
            return []
        }
    }

    /**
     * Get a specific service instance
     */
    ServiceInstance getService(String serviceId) {
        return serviceRegistry.get(serviceId)
    }

    /**
     * Get all registered services
     */
    Map<String, ServiceInstance> getAllServices() {
        return new HashMap<>(serviceRegistry)
    }

    /**
     * Get healthy services only
     */
    List<ServiceInstance> getHealthyServices() {
        return serviceRegistry.values()
            .findAll { it.status == ServiceStatus.UP }
            .sort { it.serviceName }
    }

    /**
     * Load balance service selection (round-robin)
     */
    ServiceInstance selectService(String serviceName) {
        def services = discoverServices(serviceName)
        
        if (services.isEmpty()) {
            throw new ServiceNotFoundException("No healthy instances found for service: ${serviceName}")
        }
        
        // Simple round-robin selection
        def index = Math.abs(serviceName.hashCode() + System.currentTimeMillis()) % services.size()
        return services[index]
    }

    /**
     * Register current application as a service
     */
    void registerSelf() {
        try {
            def hostname = InetAddress.getLocalHost().getHostName()
            def registration = new ServiceRegistration(
                serviceId: "${serviceName}-${hostname}-${serverPort}",
                serviceName: serviceName,
                host: hostname,
                port: serverPort,
                version: serviceVersion,
                metadata: [
                    startTime: new Date().toString(),
                    environment: System.getProperty('grails.env', 'development'),
                    jvmVersion: System.getProperty('java.version')
                ],
                healthCheckUrl: "http://${hostname}:${serverPort}/actuator/health"
            )
            
            registerService(registration)
            
        } catch (Exception e) {
            log.error("Error registering self: ${e.message}", e)
        }
    }

    /**
     * Scheduled health check for all registered services
     */
    @Scheduled(fixedRate = 30000L) // Every 30 seconds
    void performHealthChecks() {
        try {
            serviceRegistry.values().each { instance ->
                checkServiceHealth(instance)
            }
            
            // Remove services that haven't been checked in 5 minutes
            def cutoffTime = new Date() - (5 * 60 * 1000) // 5 minutes ago
            def staleServices = lastHealthCheck.findAll { serviceId, lastCheck ->
                lastCheck < cutoffTime
            }
            
            staleServices.each { serviceId, lastCheck ->
                log.warn("Removing stale service: ${serviceId} (last check: ${lastCheck})")
                deregisterService(serviceId)
            }
            
        } catch (Exception e) {
            log.error("Error performing health checks: ${e.message}", e)
        }
    }

    /**
     * Check health of a specific service
     */
    private void checkServiceHealth(ServiceInstance instance) {
        try {
            if (!instance.healthCheckUrl) {
                // No health check URL, assume healthy
                instance.status = ServiceStatus.UP
                lastHealthCheck.put(instance.serviceId, new Date())
                return
            }
            
            def url = new URL(instance.healthCheckUrl)
            def connection = url.openConnection()
            connection.setConnectTimeout(5000) // 5 seconds
            connection.setReadTimeout(5000)
            
            def responseCode = connection.getResponseCode()
            
            if (responseCode == 200) {
                instance.status = ServiceStatus.UP
                instance.lastHealthCheck = new Date()
            } else {
                instance.status = ServiceStatus.DOWN
                log.warn("Service health check failed: ${instance.serviceName} (${instance.serviceId}) - HTTP ${responseCode}")
            }
            
            lastHealthCheck.put(instance.serviceId, new Date())
            
        } catch (Exception e) {
            instance.status = ServiceStatus.DOWN
            log.warn("Service health check failed: ${instance.serviceName} (${instance.serviceId}) - ${e.message}")
            lastHealthCheck.put(instance.serviceId, new Date())
        }
    }

    /**
     * Get service statistics
     */
    Map getServiceStatistics() {
        def stats = [
            totalServices: serviceRegistry.size(),
            healthyServices: serviceRegistry.values().count { it.status == ServiceStatus.UP },
            unhealthyServices: serviceRegistry.values().count { it.status == ServiceStatus.DOWN },
            servicesByName: [:],
            lastUpdated: new Date()
        ]
        
        // Group by service name
        serviceRegistry.values().groupBy { it.serviceName }.each { serviceName, instances ->
            stats.servicesByName[serviceName] = [
                totalInstances: instances.size(),
                healthyInstances: instances.count { it.status == ServiceStatus.UP },
                instances: instances.collect { [
                    serviceId: it.serviceId,
                    host: it.host,
                    port: it.port,
                    status: it.status.toString(),
                    version: it.version,
                    registeredAt: it.registeredAt
                ]}
            ]
        }
        
        return stats
    }

    /**
     * Update service metadata
     */
    void updateServiceMetadata(String serviceId, Map<String, Object> metadata) {
        def instance = serviceRegistry.get(serviceId)
        if (instance) {
            instance.metadata.putAll(metadata)
            instance.lastUpdated = new Date()
            log.info("Updated metadata for service: ${serviceId}")
        } else {
            log.warn("Attempted to update metadata for unknown service: ${serviceId}")
        }
    }

    /**
     * Get services by tag
     */
    List<ServiceInstance> getServicesByTag(String tag) {
        return serviceRegistry.values()
            .findAll { instance ->
                instance.metadata?.tags?.contains(tag) && instance.status == ServiceStatus.UP
            }
    }
}

/**
 * Service registration data
 */
class ServiceRegistration {
    String serviceId
    String serviceName
    String host
    Integer port
    String version
    Map<String, Object> metadata = [:]
    String healthCheckUrl
}

/**
 * Service instance representation
 */
class ServiceInstance {
    String serviceId
    String serviceName
    String host
    Integer port
    String version
    Map<String, Object> metadata = [:]
    String healthCheckUrl
    ServiceStatus status
    Date registeredAt
    Date lastHealthCheck
    Date lastUpdated
}

/**
 * Service status enumeration
 */
enum ServiceStatus {
    UP, DOWN, STARTING, STOPPING, UNKNOWN
}

/**
 * Service-related exceptions
 */
class ServiceRegistrationException extends RuntimeException {
    ServiceRegistrationException(String message) { super(message) }
}

class ServiceNotFoundException extends RuntimeException {
    ServiceNotFoundException(String message) { super(message) }
}
