package org.icbs.monitoring

import grails.gorm.transactions.Transactional
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.scheduling.annotation.Async
import java.lang.management.ManagementFactory
import java.lang.management.MemoryMXBean
import java.lang.management.ThreadMXBean
import java.lang.management.GarbageCollectorMXBean
import javax.sql.DataSource
import groovy.sql.Sql

/**
 * PERFORMANCE OPTIMIZATION: Service for monitoring application performance metrics
 */
@Transactional
class PerformanceMonitoringService {

    def dataSource
    def cacheManagementService

    private final Map<String, Long> requestTimes = [:]
    private final Map<String, Integer> requestCounts = [:]
    private final List<Map> performanceHistory = []

    /**
     * PERFORMANCE OPTIMIZATION: Record request performance
     */
    void recordRequest(String endpoint, long executionTime) {
        synchronized (requestTimes) {
            requestTimes[endpoint] = (requestTimes[endpoint] ?: 0) + executionTime
            requestCounts[endpoint] = (requestCounts[endpoint] ?: 0) + 1
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get current performance metrics
     */
    Map getCurrentMetrics() {
        try {
            def metrics = [:]
            
            // JVM Memory metrics
            metrics.memory = getMemoryMetrics()
            
            // Thread metrics
            metrics.threads = getThreadMetrics()
            
            // Garbage Collection metrics
            metrics.gc = getGarbageCollectionMetrics()
            
            // Database connection metrics
            metrics.database = getDatabaseMetrics()
            
            // Cache metrics
            metrics.cache = cacheManagementService.getCacheSummary()
            
            // Request performance metrics
            metrics.requests = getRequestMetrics()
            
            // System metrics
            metrics.system = getSystemMetrics()
            
            metrics.timestamp = new Date()
            
            return metrics
            
        } catch (Exception e) {
            log.error("Error getting current metrics: ${e.message}", e)
            return [error: e.message, timestamp: new Date()]
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get memory usage metrics
     */
    private Map getMemoryMetrics() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean()
        def heapMemory = memoryBean.heapMemoryUsage
        def nonHeapMemory = memoryBean.nonHeapMemoryUsage
        
        return [
            heap: [
                used: heapMemory.used,
                max: heapMemory.max,
                committed: heapMemory.committed,
                usagePercentage: heapMemory.max > 0 ? (heapMemory.used * 100.0 / heapMemory.max).round(2) : 0
            ],
            nonHeap: [
                used: nonHeapMemory.used,
                max: nonHeapMemory.max,
                committed: nonHeapMemory.committed,
                usagePercentage: nonHeapMemory.max > 0 ? (nonHeapMemory.used * 100.0 / nonHeapMemory.max).round(2) : 0
            ]
        ]
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get thread metrics
     */
    private Map getThreadMetrics() {
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean()
        
        return [
            total: threadBean.threadCount,
            daemon: threadBean.daemonThreadCount,
            peak: threadBean.peakThreadCount,
            started: threadBean.totalStartedThreadCount
        ]
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get garbage collection metrics
     */
    private Map getGarbageCollectionMetrics() {
        def gcBeans = ManagementFactory.getGarbageCollectorMXBeans()
        def gcMetrics = [:]
        
        gcBeans.each { gcBean ->
            gcMetrics[gcBean.name] = [
                collectionCount: gcBean.collectionCount,
                collectionTime: gcBean.collectionTime
            ]
        }
        
        return gcMetrics
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get database connection metrics
     */
    private Map getDatabaseMetrics() {
        try {
            def sql = new Sql(dataSource)
            def startTime = System.currentTimeMillis()
            
            // Test database connectivity and response time
            sql.eachRow("SELECT 1") { row -> }
            
            def responseTime = System.currentTimeMillis() - startTime
            
            // Get connection pool information if available
            def poolMetrics = [:]
            if (dataSource.respondsTo('getHikariPoolMXBean')) {
                def poolBean = dataSource.getHikariPoolMXBean()
                poolMetrics = [
                    active: poolBean.activeConnections,
                    idle: poolBean.idleConnections,
                    total: poolBean.totalConnections,
                    waiting: poolBean.threadsAwaitingConnection
                ]
            }
            
            sql.close()
            
            return [
                responseTime: responseTime,
                connectionPool: poolMetrics,
                status: 'HEALTHY'
            ]
            
        } catch (Exception e) {
            log.error("Error getting database metrics: ${e.message}", e)
            return [
                status: 'ERROR',
                error: e.message
            ]
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get request performance metrics
     */
    private Map getRequestMetrics() {
        synchronized (requestTimes) {
            def metrics = [:]
            
            requestTimes.each { endpoint, totalTime ->
                def count = requestCounts[endpoint] ?: 1
                metrics[endpoint] = [
                    totalTime: totalTime,
                    requestCount: count,
                    averageTime: totalTime / count
                ]
            }
            
            return metrics
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get system metrics
     */
    private Map getSystemMetrics() {
        def runtime = Runtime.getRuntime()
        
        return [
            availableProcessors: runtime.availableProcessors(),
            freeMemory: runtime.freeMemory(),
            totalMemory: runtime.totalMemory(),
            maxMemory: runtime.maxMemory(),
            uptime: ManagementFactory.getRuntimeMXBean().uptime
        ]
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get performance trends
     */
    Map getPerformanceTrends(int hours = 24) {
        try {
            def cutoffTime = new Date() - hours / 24
            def recentHistory = performanceHistory.findAll { it.timestamp > cutoffTime }
            
            if (recentHistory.isEmpty()) {
                return [message: 'No performance data available for the specified period']
            }
            
            def trends = [:]
            
            // Memory usage trend
            trends.memoryUsage = recentHistory.collect { 
                [
                    timestamp: it.timestamp,
                    heapUsage: it.memory?.heap?.usagePercentage ?: 0
                ]
            }
            
            // Response time trend
            trends.responseTime = recentHistory.collect {
                [
                    timestamp: it.timestamp,
                    avgResponseTime: it.requests?.values()?.collect { it.averageTime }?.sum() / (it.requests?.size() ?: 1)
                ]
            }
            
            // Cache hit rate trend
            trends.cacheHitRate = recentHistory.collect {
                [
                    timestamp: it.timestamp,
                    hitRate: it.cache?.overallHitRate ?: 0
                ]
            }
            
            return trends
            
        } catch (Exception e) {
            log.error("Error getting performance trends: ${e.message}", e)
            return [error: e.message]
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get performance alerts
     */
    List getPerformanceAlerts() {
        def alerts = []
        def currentMetrics = getCurrentMetrics()
        
        try {
            // Memory usage alerts
            if (currentMetrics.memory?.heap?.usagePercentage > 85) {
                alerts << [
                    type: 'CRITICAL',
                    category: 'MEMORY',
                    message: "High heap memory usage: ${currentMetrics.memory.heap.usagePercentage}%",
                    timestamp: new Date()
                ]
            } else if (currentMetrics.memory?.heap?.usagePercentage > 70) {
                alerts << [
                    type: 'WARNING',
                    category: 'MEMORY',
                    message: "Elevated heap memory usage: ${currentMetrics.memory.heap.usagePercentage}%",
                    timestamp: new Date()
                ]
            }
            
            // Database response time alerts
            if (currentMetrics.database?.responseTime > 1000) {
                alerts << [
                    type: 'CRITICAL',
                    category: 'DATABASE',
                    message: "Slow database response time: ${currentMetrics.database.responseTime}ms",
                    timestamp: new Date()
                ]
            } else if (currentMetrics.database?.responseTime > 500) {
                alerts << [
                    type: 'WARNING',
                    category: 'DATABASE',
                    message: "Elevated database response time: ${currentMetrics.database.responseTime}ms",
                    timestamp: new Date()
                ]
            }
            
            // Cache performance alerts
            if (currentMetrics.cache?.overallHitRate < 0.5) {
                alerts << [
                    type: 'WARNING',
                    category: 'CACHE',
                    message: "Low cache hit rate: ${(currentMetrics.cache.overallHitRate * 100).round(2)}%",
                    timestamp: new Date()
                ]
            }
            
            // Thread count alerts
            if (currentMetrics.threads?.total > 200) {
                alerts << [
                    type: 'WARNING',
                    category: 'THREADS',
                    message: "High thread count: ${currentMetrics.threads.total}",
                    timestamp: new Date()
                ]
            }
            
        } catch (Exception e) {
            log.error("Error generating performance alerts: ${e.message}", e)
            alerts << [
                type: 'ERROR',
                category: 'MONITORING',
                message: "Error generating alerts: ${e.message}",
                timestamp: new Date()
            ]
        }
        
        return alerts
    }

    /**
     * PERFORMANCE OPTIMIZATION: Scheduled task to collect performance metrics
     */
    @Scheduled(fixedRate = 300000L) // Every 5 minutes
    @Async
    void collectPerformanceMetrics() {
        try {
            def metrics = getCurrentMetrics()
            
            synchronized (performanceHistory) {
                performanceHistory << metrics
                
                // Keep only last 24 hours of data
                def cutoffTime = new Date() - 1
                performanceHistory.removeAll { it.timestamp < cutoffTime }
            }
            
            // Check for performance issues
            def alerts = getPerformanceAlerts()
            if (alerts.any { it.type == 'CRITICAL' }) {
                log.warn("Critical performance issues detected: ${alerts.findAll { it.type == 'CRITICAL' }}")
            }
            
        } catch (Exception e) {
            log.error("Error collecting performance metrics: ${e.message}", e)
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Reset request metrics
     */
    void resetRequestMetrics() {
        synchronized (requestTimes) {
            requestTimes.clear()
            requestCounts.clear()
        }
        log.info("Request metrics reset")
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get performance summary
     */
    Map getPerformanceSummary() {
        def currentMetrics = getCurrentMetrics()
        def alerts = getPerformanceAlerts()
        
        return [
            status: alerts.any { it.type == 'CRITICAL' } ? 'CRITICAL' : 
                   alerts.any { it.type == 'WARNING' } ? 'WARNING' : 'HEALTHY',
            memoryUsage: currentMetrics.memory?.heap?.usagePercentage ?: 0,
            databaseResponseTime: currentMetrics.database?.responseTime ?: 0,
            cacheHitRate: currentMetrics.cache?.overallHitRate ?: 0,
            threadCount: currentMetrics.threads?.total ?: 0,
            alertCount: alerts.size(),
            criticalAlerts: alerts.count { it.type == 'CRITICAL' },
            warningAlerts: alerts.count { it.type == 'WARNING' },
            timestamp: new Date()
        ]
    }
}
