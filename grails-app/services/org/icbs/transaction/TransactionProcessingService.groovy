package org.icbs.transaction

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TellerBalance
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.Currency
import org.icbs.cif.Customer
import org.icbs.common.CommonUtilityService
import org.icbs.security.SecurityAuditService

/**
 * Comprehensive Transaction Processing Service
 * Handles all banking transaction business logic with proper validation,
 * security, and compliance checks
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class TransactionProcessingService {
    
    CommonUtilityService commonUtilityService
    SecurityAuditService securityAuditService
    def policyService
    def glTransactionService
    def userMasterService
    
    // =====================================================
    // TRANSACTION PROCESSING BUSINESS LOGIC
    // =====================================================
    
    /**
     * Process cash transfer transaction with comprehensive validation
     */
    Map processCashTransfer(Map params, Long userId) {
        return TxnFile.withTransaction { status ->
            Map result = [success: false, errors: [], txnFile: null]
            
            try {
                // 1. Validate transaction parameters
                Map validation = validateTransactionParams(params)
                if (!validation.isValid) {
                    result.errors.addAll(validation.errors)
                    return result
                }
                
                // 2. Create transaction file
                TxnFile txnFile = createTransactionFile(params, userId)
                if (!txnFile.validate()) {
                    result.errors = extractValidationErrors(txnFile)
                    return result
                }
                
                // 3. Apply business rules and policy checks
                Map policyCheck = applyTransactionPolicies(txnFile)
                if (!policyCheck.allowed) {
                    result.errors << policyCheck.reason
                    createPolicyException(txnFile, policyCheck.code)
                    return result
                }
                
                // 4. Process teller balance updates
                Map balanceUpdate = updateTellerBalance(txnFile, userId)
                if (!balanceUpdate.success) {
                    result.errors.addAll(balanceUpdate.errors)
                    return result
                }
                
                // 5. Generate GL transactions
                Map glResult = generateGLTransactions(txnFile)
                if (!glResult.success) {
                    result.errors.addAll(glResult.errors)
                    return result
                }
                
                // 6. Finalize transaction
                txnFile.status = ConfigItemStatus.get(2) // Completed
                txnFile.save(flush: true, failOnError: true)
                
                // 7. Audit logging
                securityAuditService.logSecurityEvent([
                    eventType: 'TRANSACTION_PROCESSED',
                    eventDescription: "Cash transfer transaction processed successfully",
                    username: UserMaster.get(userId)?.username,
                    transactionId: txnFile.id,
                    amount: txnFile.txnAmt,
                    result: 'SUCCESS'
                ])
                
                result.success = true
                result.txnFile = txnFile
                result.message = "Transaction processed successfully"
                
            } catch (Exception e) {
                log.error("Error processing cash transfer transaction", e)
                status.setRollbackOnly()
                result.errors << "Transaction processing failed: ${e.message}"
                
                securityAuditService.logSecurityEvent([
                    eventType: 'TRANSACTION_ERROR',
                    eventDescription: "Cash transfer transaction failed",
                    username: UserMaster.get(userId)?.username,
                    result: 'FAILURE',
                    errorMessage: e.message
                ])
            }
            
            return result
        }
    }
    
    /**
     * Process check deposit transaction
     */
    Map processCheckDeposit(Map params, Long userId) {
        return TxnFile.withTransaction { status ->
            Map result = [success: false, errors: [], txnFile: null]
            
            try {
                // 1. Validate check-specific parameters
                Map validation = validateCheckParams(params)
                if (!validation.isValid) {
                    result.errors.addAll(validation.errors)
                    return result
                }
                
                // 2. Create check transaction
                TxnFile txnFile = createCheckTransaction(params, userId)
                
                // 3. Apply check hold policies
                Map holdPolicy = applyCheckHoldPolicy(txnFile)
                if (holdPolicy.holdRequired) {
                    txnFile.holdUntilDate = holdPolicy.holdUntilDate
                    txnFile.holdReason = holdPolicy.reason
                }
                
                // 4. Process transaction
                Map processResult = processTransaction(txnFile, userId)
                if (!processResult.success) {
                    result.errors.addAll(processResult.errors)
                    return result
                }
                
                result.success = true
                result.txnFile = txnFile
                result.holdInfo = holdPolicy
                
            } catch (Exception e) {
                log.error("Error processing check deposit", e)
                status.setRollbackOnly()
                result.errors << "Check deposit processing failed: ${e.message}"
            }
            
            return result
        }
    }
    
    /**
     * Validate transaction parameters
     */
    private Map validateTransactionParams(Map params) {
        Map result = [isValid: true, errors: []]
        
        // Amount validation
        if (!params.txnAmt || params.txnAmt <= 0) {
            result.isValid = false
            result.errors << "Transaction amount must be greater than zero"
        }
        
        // Customer validation
        if (!params.customer?.id) {
            result.isValid = false
            result.errors << "Customer is required"
        } else {
            Customer customer = Customer.get(params.customer.id)
            if (!customer) {
                result.isValid = false
                result.errors << "Invalid customer"
            } else if (!customer.status?.id || customer.status.id != 1) {
                result.isValid = false
                result.errors << "Customer account is not active"
            }
        }
        
        // Transaction template validation
        if (!params.txnTemplate?.id) {
            result.isValid = false
            result.errors << "Transaction template is required"
        }
        
        // Daily transaction limit check
        if (params.txnAmt && params.customer?.id) {
            BigDecimal dailyTotal = calculateDailyTransactionTotal(params.customer.id)
            BigDecimal dailyLimit = getDailyTransactionLimit(params.customer.id)
            
            if ((dailyTotal + params.txnAmt) > dailyLimit) {
                result.isValid = false
                result.errors << "Transaction exceeds daily limit"
            }
        }
        
        return result
    }
    
    /**
     * Create transaction file with proper initialization
     */
    private TxnFile createTransactionFile(Map params, Long userId) {
        TxnFile txnFile = new TxnFile()
        
        // Basic transaction details
        txnFile.txnAmt = params.txnAmt as BigDecimal
        txnFile.txnParticulars = params.txnParticulars
        txnFile.txnDate = new Date()
        txnFile.valueDate = params.valueDate ?: new Date()
        
        // User and branch information
        UserMaster user = UserMaster.get(userId)
        txnFile.user = user
        txnFile.branch = Branch.get(user.branchId)
        
        // Customer and template
        txnFile.beneficiary = Customer.get(params.customer.id)
        txnFile.txnTemplate = org.icbs.admin.TxnTemplate.get(params.txnTemplate.id)
        
        // Status and currency
        txnFile.status = ConfigItemStatus.get(1) // Pending
        txnFile.currency = Currency.get(params.currency?.id ?: 1)
        
        // Generate unique transaction reference
        txnFile.txnRef = generateTransactionReference()
        
        return txnFile
    }
    
    /**
     * Apply transaction policies and business rules
     */
    private Map applyTransactionPolicies(TxnFile txnFile) {
        Map result = [allowed: true, reason: null, code: null]
        
        try {
            // Check transaction amount limits
            Long amount = txnFile.txnAmt.longValue()
            boolean isAllowed = policyService.isTxnAllowed(txnFile.txnTemplate.code, amount)
            
            if (!isAllowed) {
                result.allowed = false
                result.reason = "Transaction amount exceeds policy limits"
                result.code = "TLR00200"
                return result
            }
            
            // Check customer transaction limits
            Map customerLimits = checkCustomerTransactionLimits(txnFile)
            if (!customerLimits.allowed) {
                result.allowed = false
                result.reason = customerLimits.reason
                result.code = "TLR00201"
                return result
            }
            
            // Check suspicious activity patterns
            Map suspiciousCheck = checkSuspiciousActivity(txnFile)
            if (suspiciousCheck.suspicious) {
                result.allowed = false
                result.reason = "Transaction flagged for suspicious activity"
                result.code = "TLR00202"
                return result
            }
            
            // AML (Anti-Money Laundering) checks
            Map amlCheck = performAMLCheck(txnFile)
            if (!amlCheck.passed) {
                result.allowed = false
                result.reason = "Transaction requires AML review"
                result.code = "TLR00203"
                return result
            }
            
        } catch (Exception e) {
            log.error("Error applying transaction policies", e)
            result.allowed = false
            result.reason = "Policy validation error"
            result.code = "TLR00299"
        }
        
        return result
    }
    
    /**
     * Update teller balance with transaction
     */
    private Map updateTellerBalance(TxnFile txnFile, Long userId) {
        Map result = [success: true, errors: []]
        
        try {
            TellerBalance tellerBalance = TellerBalance.findByUserAndBranch(
                UserMaster.get(userId),
                txnFile.branch
            ) ?: new TellerBalance(
                user: UserMaster.get(userId),
                branch: txnFile.branch,
                currency: txnFile.currency
            )
            
            // Update balance based on transaction type
            String txnType = txnFile.txnTemplate.code
            
            switch (txnType) {
                case 'CASH_DEPOSIT':
                    tellerBalance.cashInAmt = (tellerBalance.cashInAmt ?: 0) + txnFile.txnAmt
                    break
                case 'CASH_WITHDRAWAL':
                    if ((tellerBalance.cashOutAmt ?: 0) + txnFile.txnAmt > tellerBalance.cashLimit) {
                        result.success = false
                        result.errors << "Insufficient teller cash limit"
                        return result
                    }
                    tellerBalance.cashOutAmt = (tellerBalance.cashOutAmt ?: 0) + txnFile.txnAmt
                    break
                case 'CHECK_DEPOSIT':
                    tellerBalance.checkInAmt = (tellerBalance.checkInAmt ?: 0) + txnFile.txnAmt
                    break
                default:
                    log.warn("Unknown transaction type: ${txnType}")
            }
            
            tellerBalance.txnFile = txnFile
            tellerBalance.txnParticulars = txnFile.txnParticulars
            tellerBalance.save(flush: true, failOnError: true)
            
            // Update teller balance status
            userMasterService.updateTellerBalanceStatus(false)
            
        } catch (Exception e) {
            log.error("Error updating teller balance", e)
            result.success = false
            result.errors << "Teller balance update failed: ${e.message}"
        }
        
        return result
    }
    
    /**
     * Generate GL transactions for the banking transaction
     */
    private Map generateGLTransactions(TxnFile txnFile) {
        Map result = [success: true, errors: []]
        
        try {
            // Generate GL transaction breakdown
            glTransactionService.saveTxnBreakdown(txnFile.id)
            
        } catch (Exception e) {
            log.error("Error generating GL transactions", e)
            result.success = false
            result.errors << "GL transaction generation failed: ${e.message}"
        }
        
        return result
    }
    
    // =====================================================
    // HELPER METHODS
    // =====================================================
    
    private String generateTransactionReference() {
        return "TXN${System.currentTimeMillis()}${(Math.random() * 1000).toInteger()}"
    }
    
    private BigDecimal calculateDailyTransactionTotal(Long customerId) {
        Date today = new Date().clearTime()
        Date tomorrow = today + 1
        
        return TxnFile.createCriteria().get {
            eq('beneficiary.id', customerId)
            between('txnDate', today, tomorrow)
            projections {
                sum('txnAmt')
            }
        } ?: 0.0
    }
    
    private BigDecimal getDailyTransactionLimit(Long customerId) {
        // Default daily limit - should be configurable per customer type
        return 50000.0
    }
    
    private Map checkCustomerTransactionLimits(TxnFile txnFile) {
        // Implementation for customer-specific transaction limits
        return [allowed: true]
    }
    
    private Map checkSuspiciousActivity(TxnFile txnFile) {
        // Implementation for suspicious activity detection
        return [suspicious: false]
    }
    
    private Map performAMLCheck(TxnFile txnFile) {
        // Implementation for Anti-Money Laundering checks
        return [passed: true]
    }
    
    private void createPolicyException(TxnFile txnFile, String code) {
        try {
            policyService.createException(
                code,
                'TxnFile',
                txnFile.id,
                'tellering/viewTellerTxnInquiry2/' + txnFile.id
            )
        } catch (Exception e) {
            log.error("Error creating policy exception", e)
        }
    }
    
    private List<String> extractValidationErrors(def domainInstance) {
        List<String> errors = []
        domainInstance.errors.allErrors.each { error ->
            errors << error.defaultMessage
        }
        return errors
    }
}
