package org.icbs.deposit

import grails.gorm.transactions.Transactional
import grails.gorm.DetachedCriteria
import org.hibernate.FetchMode
import org.springframework.cache.annotation.Cacheable
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.CachePut
import org.icbs.cif.Customer

@Transactional
class OptimizedDepositService {

    /**
     * PERFORMANCE OPTIMIZED: Find deposits with eager loading to prevent N+1 queries
     */
    @Cacheable(value = "deposits", key = "#branchId + '_' + #customerId + '_' + #offset + '_' + #max")
    List<Deposit> findDepositsOptimized(Long branchId = null, Long customerId = null, int offset = 0, int max = 20) {
        return Deposit.createCriteria().list(max: max, offset: offset) {
            // PERFORMANCE FIX: Use fetch joins to prevent N+1 queries
            fetchMode 'customer', FetchMode.JOIN
            fetchMode 'branch', FetchMode.JOIN
            fetchMode 'type', FetchMode.JOIN
            fetchMode 'status', FetchMode.JOIN
            fetchMode 'product', FetchMode.JOIN
            fetchMode 'signatories', FetchMode.SELECT
            
            if (branchId) {
                eq('branch.id', branchId)
            }
            if (customerId) {
                eq('customer.id', customerId)
            }
            
            order('dateOpened', 'desc')
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Find deposit by account number with all relationships
     */
    @Cacheable(value = "depositByAcctNo", key = "#acctNo")
    Deposit findByAccountNumberOptimized(String acctNo) {
        return Deposit.createCriteria().get {
            eq('acctNo', acctNo)
            
            // PERFORMANCE FIX: Eagerly load all relationships
            fetchMode 'customer', FetchMode.JOIN
            fetchMode 'branch', FetchMode.JOIN
            fetchMode 'type', FetchMode.JOIN
            fetchMode 'status', FetchMode.JOIN
            fetchMode 'product', FetchMode.JOIN
            fetchMode 'signatories', FetchMode.SELECT
            fetchMode 'passbooks', FetchMode.SELECT
            fetchMode 'chequebooks', FetchMode.SELECT
            fetchMode 'holds', FetchMode.SELECT
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Get deposits by customer with caching
     */
    @Cacheable(value = "customerDeposits", key = "#customerId")
    List<Deposit> getDepositsByCustomerOptimized(Long customerId) {
        return Deposit.createCriteria().list {
            eq('customer.id', customerId)
            
            fetchMode 'type', FetchMode.JOIN
            fetchMode 'status', FetchMode.JOIN
            fetchMode 'product', FetchMode.JOIN
            
            order('dateOpened', 'desc')
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Get deposit summary with aggregated data
     */
    @Cacheable(value = "depositSummary", key = "#depositId")
    Map getDepositSummaryOptimized(Long depositId) {
        def deposit = Deposit.get(depositId)
        if (!deposit) {
            return null
        }
        
        // Calculate aggregated data efficiently
        def totalHolds = deposit.holds?.sum { it.amount ?: 0 } ?: 0
        def signatoryCount = deposit.signatories?.size() ?: 0
        def passbookCount = deposit.passbooks?.size() ?: 0
        def chequebookCount = deposit.chequebooks?.size() ?: 0
        
        return [
            deposit: deposit,
            totalHolds: totalHolds,
            signatoryCount: signatoryCount,
            passbookCount: passbookCount,
            chequebookCount: chequebookCount,
            effectiveBalance: (deposit.ledgerBalAmt ?: 0) - totalHolds
        ]
    }

    /**
     * PERFORMANCE OPTIMIZED: Search deposits with pagination
     */
    @Cacheable(value = "depositSearch", key = "#searchTerm + '_' + #offset + '_' + #max")
    Map searchDepositsOptimized(String searchTerm, int offset = 0, int max = 20) {
        def criteria = Deposit.createCriteria()
        
        def results = criteria.list(max: max, offset: offset) {
            or {
                ilike('acctNo', "%${searchTerm}%")
                ilike('acctName', "%${searchTerm}%")
                customer {
                    or {
                        ilike('name1', "%${searchTerm}%")
                        ilike('displayName', "%${searchTerm}%")
                        ilike('customerId', "%${searchTerm}%")
                    }
                }
            }
            
            // PERFORMANCE FIX: Load essential relationships only
            fetchMode 'customer', FetchMode.JOIN
            fetchMode 'type', FetchMode.JOIN
            fetchMode 'status', FetchMode.JOIN
            
            order('dateOpened', 'desc')
        }
        
        def totalCount = criteria.count {
            or {
                ilike('acctNo', "%${searchTerm}%")
                ilike('acctName', "%${searchTerm}%")
                customer {
                    or {
                        ilike('name1', "%${searchTerm}%")
                        ilike('displayName', "%${searchTerm}%")
                        ilike('customerId', "%${searchTerm}%")
                    }
                }
            }
        }
        
        return [
            deposits: results,
            totalCount: totalCount,
            hasMore: (offset + max) < totalCount
        ]
    }

    /**
     * PERFORMANCE OPTIMIZED: Get branch deposit statistics
     */
    @Cacheable(value = "branchDepositStats", key = "#branchId")
    Map getBranchDepositStatsOptimized(Long branchId) {
        def criteria = Deposit.createCriteria()
        
        def stats = criteria.list {
            eq('branch.id', branchId)
            
            projections {
                groupProperty('type.id')
                groupProperty('status.id')
                sum('ledgerBalAmt')
                count('id')
            }
        }
        
        def result = [
            totalAccounts: 0,
            totalBalance: 0.0,
            byType: [:],
            byStatus: [:]
        ]
        
        stats.each { row ->
            def typeId = row[0]
            def statusId = row[1]
            def balance = row[2] ?: 0.0
            def count = row[3] ?: 0
            
            result.totalAccounts += count
            result.totalBalance += balance
            
            if (!result.byType[typeId]) {
                result.byType[typeId] = [count: 0, balance: 0.0]
            }
            result.byType[typeId].count += count
            result.byType[typeId].balance += balance
            
            if (!result.byStatus[statusId]) {
                result.byStatus[statusId] = [count: 0, balance: 0.0]
            }
            result.byStatus[statusId].count += count
            result.byStatus[statusId].balance += balance
        }
        
        return result
    }

    /**
     * PERFORMANCE OPTIMIZED: Create deposit with proper caching
     */
    @CacheEvict(value = ["deposits", "depositSearch", "customerDeposits", "branchDepositStats"], allEntries = true)
    Deposit createDepositOptimized(Deposit deposit) {
        deposit.save(flush: true)
        
        // Cache the new deposit
        cacheDeposit(deposit)
        
        return deposit
    }

    /**
     * PERFORMANCE OPTIMIZED: Update deposit with cache management
     */
    @CacheEvict(value = ["deposits", "depositSearch", "depositByAcctNo", "depositSummary", "customerDeposits", "branchDepositStats"], 
                key = "#deposit.id")
    Deposit updateDepositOptimized(Deposit deposit) {
        deposit.save(flush: true)
        
        // Update cache
        cacheDeposit(deposit)
        
        return deposit
    }

    /**
     * PERFORMANCE OPTIMIZED: Get deposits maturing today
     */
    @Cacheable(value = "depositsMaturing", key = "#branchId + '_' + #date.format('yyyy-MM-dd')")
    List<Deposit> getDepositsMaturing(Long branchId, Date date) {
        return Deposit.createCriteria().list {
            eq('branch.id', branchId)
            eq('maturityDate', date)
            eq('type.id', 3L) // Fixed deposits
            
            fetchMode 'customer', FetchMode.JOIN
            fetchMode 'product', FetchMode.JOIN
            
            order('maturityDate', 'asc')
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Bulk balance update for better performance
     */
    @CacheEvict(value = ["deposits", "depositSummary", "branchDepositStats"], allEntries = true)
    void bulkUpdateBalances(Map<Long, Double> balanceUpdates) {
        balanceUpdates.each { depositId, newBalance ->
            Deposit.executeUpdate(
                "UPDATE Deposit d SET d.ledgerBalAmt = :balance, d.availableBalAmt = :balance WHERE d.id = :id",
                [balance: newBalance, id: depositId]
            )
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Get dormant accounts
     */
    @Cacheable(value = "dormantAccounts", key = "#branchId + '_' + #monthsInactive")
    List<Deposit> getDormantAccountsOptimized(Long branchId, int monthsInactive = 12) {
        def cutoffDate = new Date() - (monthsInactive * 30)
        
        return Deposit.createCriteria().list {
            eq('branch.id', branchId)
            or {
                lt('lastTxnDate', cutoffDate)
                isNull('lastTxnDate')
            }
            eq('status.id', 1L) // Active status
            
            fetchMode 'customer', FetchMode.JOIN
            fetchMode 'type', FetchMode.JOIN
            
            order('lastTxnDate', 'asc')
        }
    }

    /**
     * Helper method to cache deposit data
     */
    @CachePut(value = "depositByAcctNo", key = "#deposit.acctNo")
    private Deposit cacheDeposit(Deposit deposit) {
        return deposit
    }

    /**
     * PERFORMANCE OPTIMIZED: Get low balance accounts
     */
    @Cacheable(value = "lowBalanceAccounts", key = "#branchId + '_' + #threshold")
    List<Deposit> getLowBalanceAccountsOptimized(Long branchId, Double threshold = 100.0) {
        return Deposit.createCriteria().list {
            eq('branch.id', branchId)
            lt('ledgerBalAmt', threshold)
            eq('status.id', 1L) // Active status
            
            fetchMode 'customer', FetchMode.JOIN
            fetchMode 'type', FetchMode.JOIN
            
            order('ledgerBalAmt', 'asc')
        }
    }
}
