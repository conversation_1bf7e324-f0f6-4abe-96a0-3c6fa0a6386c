package org.icbs.cache

import grails.gorm.transactions.Transactional
import org.springframework.cache.CacheManager
import org.springframework.cache.Cache
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import com.github.benmanes.caffeine.cache.stats.CacheStats

/**
 * PERFORMANCE OPTIMIZATION: Cache management service for monitoring and controlling caches
 */
@Transactional
class CacheManagementService {

    @Autowired
    @Qualifier('primaryCacheManager')
    CacheManager primaryCacheManager

    @Autowired
    @Qualifier('lookupCacheManager')
    CacheManager lookupCacheManager

    @Autowired
    @Qualifier('sessionCacheManager')
    CacheManager sessionCacheManager

    @Autowired
    @Qualifier('largeCacheManager')
    CacheManager largeCacheManager

    /**
     * PERFORMANCE OPTIMIZATION: Clear all caches
     */
    void clearAllCaches() {
        try {
            [primaryCacheManager, lookupCacheManager, sessionCacheManager, largeCacheManager].each { manager ->
                manager.cacheNames.each { cacheName ->
                    manager.getCache(cacheName)?.clear()
                }
            }
            log.info("All caches cleared successfully")
        } catch (Exception e) {
            log.error("Error clearing all caches: ${e.message}", e)
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Clear specific cache
     */
    void clearCache(String cacheName) {
        try {
            def cache = findCache(cacheName)
            if (cache) {
                cache.clear()
                log.info("Cache '${cacheName}' cleared successfully")
            } else {
                log.warn("Cache '${cacheName}' not found")
            }
        } catch (Exception e) {
            log.error("Error clearing cache '${cacheName}': ${e.message}", e)
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Clear cache by pattern
     */
    void clearCachesByPattern(String pattern) {
        try {
            def clearedCaches = []
            [primaryCacheManager, lookupCacheManager, sessionCacheManager, largeCacheManager].each { manager ->
                manager.cacheNames.each { cacheName ->
                    if (cacheName.contains(pattern)) {
                        manager.getCache(cacheName)?.clear()
                        clearedCaches << cacheName
                    }
                }
            }
            log.info("Cleared caches matching pattern '${pattern}': ${clearedCaches}")
        } catch (Exception e) {
            log.error("Error clearing caches by pattern '${pattern}': ${e.message}", e)
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get cache statistics
     */
    Map getCacheStatistics() {
        try {
            def stats = [:]
            
            [
                primary: primaryCacheManager,
                lookup: lookupCacheManager,
                session: sessionCacheManager,
                large: largeCacheManager
            ].each { managerName, manager ->
                stats[managerName] = [:]
                
                manager.cacheNames.each { cacheName ->
                    def cache = manager.getCache(cacheName)
                    if (cache) {
                        def nativeCache = cache.nativeCache
                        if (nativeCache.respondsTo('stats')) {
                            CacheStats cacheStats = nativeCache.stats()
                            stats[managerName][cacheName] = [
                                hitCount: cacheStats.hitCount(),
                                missCount: cacheStats.missCount(),
                                hitRate: cacheStats.hitRate(),
                                evictionCount: cacheStats.evictionCount(),
                                requestCount: cacheStats.requestCount(),
                                estimatedSize: nativeCache.estimatedSize()
                            ]
                        } else {
                            stats[managerName][cacheName] = [
                                message: 'Statistics not available'
                            ]
                        }
                    }
                }
            }
            
            return stats
        } catch (Exception e) {
            log.error("Error getting cache statistics: ${e.message}", e)
            return [error: e.message]
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get cache summary
     */
    Map getCacheSummary() {
        try {
            def summary = [
                totalCaches: 0,
                totalHits: 0,
                totalMisses: 0,
                totalRequests: 0,
                overallHitRate: 0.0,
                managers: [:]
            ]
            
            [
                primary: primaryCacheManager,
                lookup: lookupCacheManager,
                session: sessionCacheManager,
                large: largeCacheManager
            ].each { managerName, manager ->
                def managerStats = [
                    cacheCount: manager.cacheNames.size(),
                    totalHits: 0,
                    totalMisses: 0,
                    totalRequests: 0,
                    hitRate: 0.0
                ]
                
                manager.cacheNames.each { cacheName ->
                    def cache = manager.getCache(cacheName)
                    if (cache) {
                        def nativeCache = cache.nativeCache
                        if (nativeCache.respondsTo('stats')) {
                            CacheStats cacheStats = nativeCache.stats()
                            managerStats.totalHits += cacheStats.hitCount()
                            managerStats.totalMisses += cacheStats.missCount()
                            managerStats.totalRequests += cacheStats.requestCount()
                        }
                    }
                }
                
                if (managerStats.totalRequests > 0) {
                    managerStats.hitRate = managerStats.totalHits / managerStats.totalRequests
                }
                
                summary.managers[managerName] = managerStats
                summary.totalCaches += managerStats.cacheCount
                summary.totalHits += managerStats.totalHits
                summary.totalMisses += managerStats.totalMisses
                summary.totalRequests += managerStats.totalRequests
            }
            
            if (summary.totalRequests > 0) {
                summary.overallHitRate = summary.totalHits / summary.totalRequests
            }
            
            return summary
        } catch (Exception e) {
            log.error("Error getting cache summary: ${e.message}", e)
            return [error: e.message]
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Warm up essential caches
     */
    void warmUpCaches() {
        try {
            log.info("Starting cache warm-up process...")
            
            // Warm up lookup data caches
            warmUpLookupCaches()
            
            // Warm up frequently accessed data
            warmUpFrequentData()
            
            log.info("Cache warm-up process completed")
        } catch (Exception e) {
            log.error("Error during cache warm-up: ${e.message}", e)
        }
    }

    /**
     * PERFORMANCE OPTIMIZATION: Evict expired entries from all caches
     */
    void evictExpiredEntries() {
        try {
            [primaryCacheManager, lookupCacheManager, sessionCacheManager, largeCacheManager].each { manager ->
                manager.cacheNames.each { cacheName ->
                    def cache = manager.getCache(cacheName)
                    if (cache) {
                        def nativeCache = cache.nativeCache
                        if (nativeCache.respondsTo('cleanUp')) {
                            nativeCache.cleanUp()
                        }
                    }
                }
            }
            log.info("Expired cache entries evicted successfully")
        } catch (Exception e) {
            log.error("Error evicting expired entries: ${e.message}", e)
        }
    }

    /**
     * Helper method to find cache across all managers
     */
    private Cache findCache(String cacheName) {
        def managers = [primaryCacheManager, lookupCacheManager, sessionCacheManager, largeCacheManager]
        for (manager in managers) {
            def cache = manager.getCache(cacheName)
            if (cache) {
                return cache
            }
        }
        return null
    }

    /**
     * Warm up lookup caches with essential data
     */
    private void warmUpLookupCaches() {
        // This would be implemented to pre-load essential lookup data
        // Example: Load all branches, products, LOV items, etc.
        log.info("Warming up lookup caches...")
    }

    /**
     * Warm up frequently accessed data
     */
    private void warmUpFrequentData() {
        // This would be implemented to pre-load frequently accessed data
        // Example: Load active customers, recent transactions, etc.
        log.info("Warming up frequent data caches...")
    }

    /**
     * PERFORMANCE OPTIMIZATION: Get cache health status
     */
    Map getCacheHealth() {
        try {
            def health = [
                status: 'HEALTHY',
                issues: [],
                recommendations: []
            ]
            
            def summary = getCacheSummary()
            
            // Check overall hit rate
            if (summary.overallHitRate < 0.7) {
                health.status = 'WARNING'
                health.issues << "Low overall hit rate: ${(summary.overallHitRate * 100).round(2)}%"
                health.recommendations << "Consider cache warm-up or review cache keys"
            }
            
            // Check individual manager performance
            summary.managers.each { managerName, stats ->
                if (stats.hitRate < 0.6) {
                    health.issues << "Low hit rate for ${managerName} cache manager: ${(stats.hitRate * 100).round(2)}%"
                }
            }
            
            if (health.issues.size() > 0 && health.status == 'HEALTHY') {
                health.status = 'WARNING'
            }
            
            return health
        } catch (Exception e) {
            return [
                status: 'ERROR',
                error: e.message
            ]
        }
    }
}
