package org.icbs.periodicops

import grails.gorm.transactions.Transactional
import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.admin.Institution
import org.icbs.admin.TxnTemplate
import org.icbs.loans.Loan
import org.icbs.loans.LoanSweep
import org.icbs.loans.LoanRecovery
import org.icbs.deposit.Deposit
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnLoanPaymentDetails
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnBreakdown
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositStatus
import org.icbs.periodicops.DailyLoanRecoveries
import groovy.sql.Sql

/**
 * LoanRecoveryProcessingService - Handles loan recovery processing
 * 
 * This service manages loan recovery processing operations including:
 * - Automatic loan recoveries from deposit accounts
 * - Loan sweep processing
 * - Recovery transaction posting
 * - Recovery reporting and tracking
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class LoanRecoveryProcessingService {
    
    def sessionFactory
    def dataSource
    def auditLogService
    def glTransactionService
    
    /**
     * Process loan recoveries for all eligible loans
     */
    def processLoanRecoveries(Date currentDate, Branch branch, UserMaster user) {
        println("====================== START LOAN RECOVERY PROCESSING ==============================")
        
        Boolean depOk
        Double recoveryAmt
        Double lnPayAmt
        Double scPayAmt
        Double penPayAmt
        Double intPayAmt
        Double prinPayAmt
        Integer i = 0
        def loan
        def drDepTxn = Institution.findByParamCode('LNS.50072').paramValue.toInteger()
        def crLoanTxn = Institution.findByParamCode('LNS.50073').paramValue.toInteger()
        def lnSweeps
        def db = new Sql(dataSource)
        
        try {
            def sqlstmt = "select id from loan where status_id > 1 and status_id < 6 and branch_id = " + branch.id
            def loans = db.rows(sqlstmt)
            
            for (ln in loans) {
                loan = Loan.get(ln.id)
                if(!loan.isAttached()) {
                    loan.attach()
                }
                
                def totalDue = 0.00
                if (loan.overduePrincipalBalance > 0) {
                    totalDue += loan.overduePrincipalBalance
                }
                if (loan.interestBalanceAmount > 0) {
                    totalDue += loan.interestBalanceAmount
                }
                if (loan.serviceChargeBalanceAmount > 0) {
                    totalDue += loan.serviceChargeBalanceAmount
                }
                if (loan.penaltyBalanceAmount > 0) {
                    totalDue += loan.penaltyBalanceAmount
                }
                
                if (totalDue > 0) {
                    lnSweeps = LoanSweep.findAllByLoan(loan)
                    
                    for (sweep in lnSweeps) {
                        if (sweep.deposit.status.id == 4) { // Active deposit
                            depOk = false
                            recoveryAmt = 0.00
                            
                            if (sweep.deposit.availableBalAmt >= totalDue) {
                                recoveryAmt = totalDue
                                depOk = true
                            } else if (sweep.deposit.availableBalAmt >= sweep.minimumBalance && 
                                      (sweep.deposit.availableBalAmt - sweep.minimumBalance) > 0) {
                                recoveryAmt = sweep.deposit.availableBalAmt - sweep.minimumBalance
                                depOk = true
                            }
                            
                            if (depOk && recoveryAmt > 0) {
                                processRecoveryTransaction(loan, sweep.deposit, recoveryAmt, currentDate, branch, user, drDepTxn, crLoanTxn)
                                
                                // Update totals
                                totalDue -= recoveryAmt
                                if (totalDue <= 0) {
                                    break // Loan fully recovered
                                }
                            }
                        }
                    }
                }
                
                i++
                if (i > 50) {
                    i = 1
                    cleanUpGorm()
                }
            }
            
            def description = "Loan recoveries processed for ${loans.size()} loans on ${currentDate.format('MM/dd/yyyy')}"
            auditLogService.insert('130', 'LRP00100', description, 'LoanRecoveryProcessing', null, null, null, null)
            
        } catch (Exception e) {
            log.error("Error processing loan recoveries", e)
            throw new RuntimeException("Failed to process loan recoveries: ${e.message}")
        } finally {
            db.close()
        }
        
        println("===============  DONE PROCESSING LOAN RECOVERIES ====================")
    }

    /**
     * Process a single recovery transaction
     */
    private def processRecoveryTransaction(Loan loan, Deposit deposit, Double recoveryAmt, Date currentDate, 
                                         Branch branch, UserMaster user, Integer drDepTxn, Integer crLoanTxn) {
        try {
            // Calculate payment allocation
            def lnPayAmt = 0.00
            def scPayAmt = 0.00
            def penPayAmt = 0.00
            def intPayAmt = 0.00
            def prinPayAmt = 0.00
            
            // Allocate payment according to priority: Penalty -> Service Charge -> Interest -> Principal
            def remainingAmt = recoveryAmt
            
            if (loan.penaltyBalanceAmount > 0 && remainingAmt > 0) {
                penPayAmt = Math.min(loan.penaltyBalanceAmount, remainingAmt)
                remainingAmt -= penPayAmt
            }
            
            if (loan.serviceChargeBalanceAmount > 0 && remainingAmt > 0) {
                scPayAmt = Math.min(loan.serviceChargeBalanceAmount, remainingAmt)
                remainingAmt -= scPayAmt
            }
            
            if (loan.interestBalanceAmount > 0 && remainingAmt > 0) {
                intPayAmt = Math.min(loan.interestBalanceAmount, remainingAmt)
                remainingAmt -= intPayAmt
            }
            
            if (loan.overduePrincipalBalance > 0 && remainingAmt > 0) {
                prinPayAmt = Math.min(loan.overduePrincipalBalance, remainingAmt)
                remainingAmt -= prinPayAmt
            }
            
            lnPayAmt = penPayAmt + scPayAmt + intPayAmt + prinPayAmt
            
            if (lnPayAmt > 0) {
                // Create deposit debit transaction
                def drDepTemplate = TxnTemplate.get(drDepTxn)
                def drTxnFile = new TxnFile(
                    acctNo: deposit.accountNo,
                    branch: branch,
                    currency: deposit.currency,
                    status: ConfigItemStatus.get(2),
                    txnAmt: lnPayAmt,
                    txnCode: drDepTemplate.code,
                    txnDate: currentDate,
                    txnDescription: 'Loan Recovery Debit',
                    txnParticulars: "Recovery from deposit ${deposit.accountNo} for loan ${loan.accountNo}",
                    txnRef: 'Automatic Loan Recovery',
                    txnTemplate: drDepTemplate,
                    txnType: drDepTemplate.txnType,
                    txnTimestamp: new Date().toTimestamp(),
                    user: user,
                    beneficiary: deposit.customer
                )
                drTxnFile.save(flush: true, failOnError: true)
                
                // Create deposit ledger entry
                def depLedger = new TxnDepositAcctLedger(
                    txnFile: drTxnFile,
                    acct: deposit,
                    txnDate: currentDate,
                    debitAmt: lnPayAmt,
                    creditAmt: 0,
                    bal: deposit.ledgerBalAmt - lnPayAmt,
                    txnRef: drTxnFile.txnRef
                )
                depLedger.save(flush: true, failOnError: true)
                
                // Update deposit balance
                deposit.ledgerBalAmt -= lnPayAmt
                deposit.availableBalAmt -= lnPayAmt
                deposit.save(flush: true)
                
                // Create loan credit transaction
                def crLoanTemplate = TxnTemplate.get(crLoanTxn)
                def crTxnFile = new TxnFile(
                    acctNo: loan.accountNo,
                    branch: branch,
                    currency: loan.currency,
                    loanAcct: loan,
                    status: ConfigItemStatus.get(2),
                    txnAmt: lnPayAmt,
                    txnCode: crLoanTemplate.code,
                    txnDate: currentDate,
                    txnDescription: 'Loan Recovery Credit',
                    txnParticulars: "Recovery payment for loan ${loan.accountNo} from deposit ${deposit.accountNo}",
                    txnRef: 'Automatic Loan Recovery',
                    txnTemplate: crLoanTemplate,
                    txnType: crLoanTemplate.txnType,
                    txnTimestamp: new Date().toTimestamp(),
                    user: user,
                    beneficiary: loan.customer
                )
                crTxnFile.save(flush: true, failOnError: true)
                
                // Create loan payment details
                def loanPayment = new TxnLoanPaymentDetails(
                    txnFile: crTxnFile,
                    acctId: loan.id,
                    txnDate: currentDate,
                    principalAmtPaid: prinPayAmt,
                    interestAmtPaid: intPayAmt,
                    serviceChargeAmtPaid: scPayAmt,
                    penaltyAmtPaid: penPayAmt,
                    totalAmtPaid: lnPayAmt
                )
                loanPayment.save(flush: true, failOnError: true)
                
                // Update loan balances
                loan.overduePrincipalBalance -= prinPayAmt
                loan.interestBalanceAmount -= intPayAmt
                loan.serviceChargeBalanceAmount -= scPayAmt
                loan.penaltyBalanceAmount -= penPayAmt
                loan.balanceAmount -= prinPayAmt
                
                // Check if loan is fully paid
                if (loan.balanceAmount <= 0) {
                    loan.status = LoanAcctStatus.get(6) // Closed
                    loan.dateClosed = currentDate
                }
                
                loan.save(flush: true)
                
                // Create recovery record
                def recovery = new LoanRecovery(
                    loan: loan,
                    deposit: deposit,
                    recoveryDate: currentDate,
                    recoveryAmount: lnPayAmt,
                    principalAmount: prinPayAmt,
                    interestAmount: intPayAmt,
                    serviceChargeAmount: scPayAmt,
                    penaltyAmount: penPayAmt,
                    txnFile: crTxnFile,
                    user: user
                )
                recovery.save(flush: true, failOnError: true)
                
                // Create daily recovery record
                def dailyRecovery = new DailyLoanRecoveries(
                    processDate: currentDate,
                    loanRecovery: recovery.id,
                    loan: loan.id
                )
                dailyRecovery.save(flush: true)
                
                // Generate GL transactions
                glTransactionService.saveTxnBreakdown(drTxnFile.id)
                glTransactionService.saveTxnBreakdown(crTxnFile.id)
            }
            
        } catch (Exception e) {
            log.error("Error processing recovery transaction for loan ${loan.accountNo}", e)
            throw new RuntimeException("Failed to process recovery transaction: ${e.message}")
        }
    }

    /**
     * Clean up GORM session
     */
    private def cleanUpGorm() {
        def session = sessionFactory.currentSession
        session.flush()
        session.clear()
        def propertyInstanceMap = org.codehaus.groovy.grails.plugins.DomainClassGrailsPlugin.PROPERTY_INSTANCE_MAP
        propertyInstanceMap.get().clear()
    }
}
