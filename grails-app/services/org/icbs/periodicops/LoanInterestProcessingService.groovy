package org.icbs.periodicops

import grails.gorm.transactions.Transactional
import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.loans.Loan
import org.icbs.loans.LoanLedger
import org.icbs.lov.LoanAcctStatus
import org.icbs.admin.TxnTemplate
import org.icbs.admin.Institution
import org.icbs.tellering.TxnFile
import org.icbs.tellering.TxnBreakdown
import org.icbs.lov.ConfigItemStatus
import groovy.sql.Sql

/**
 * LoanInterestProcessingService - Handles loan interest processing
 * 
 * This service manages loan interest processing operations including:
 * - Interest accrual calculations
 * - Interest posting to loan accounts
 * - Interest income recognition
 * - Penalty interest calculations
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class LoanInterestProcessingService {
    
    def sessionFactory
    def dataSource
    def auditLogService
    def glTransactionService
    
    /**
     * Process interest accrual for all active loans
     */
    def processInterestAccrual(Date currentDate, Branch branch, UserMaster user) {
        println("====================== START INTEREST ACCRUAL PROCESSING ==============================")
        
        def sql = new Sql(dataSource)
        def processedCount = 0
        
        try {
            // Get all active loans for interest accrual
            def sqlstmt = """
                SELECT l.id, l.account_no, l.balance_amount, l.interest_rate, 
                       l.accrued_interest_date, l.interest_income_scheme_id
                FROM loan l
                WHERE l.branch_id = ?
                AND l.status_id IN (4, 5)
                AND l.balance_amount > 0
                AND (l.accrued_interest_date < ? OR l.accrued_interest_date IS NULL)
                ORDER BY l.id
            """
            
            def loans = sql.rows(sqlstmt, [branch.id, currentDate])
            
            for (loanRow in loans) {
                def loan = Loan.get(loanRow.id)
                if (loan) {
                    processLoanInterestAccrual(loan, currentDate, branch, user)
                    processedCount++
                    
                    if (processedCount % 50 == 0) {
                        cleanUpGorm()
                    }
                }
            }
            
            def description = "Interest accrual processed for ${processedCount} loans on ${currentDate.format('MM/dd/yyyy')}"
            auditLogService.insert('130', 'LIP00400', description, 'LoanInterestProcessing', null, null, null, null)
            
        } catch (Exception e) {
            log.error("Error processing interest accrual", e)
            throw new RuntimeException("Failed to process interest accrual: ${e.message}")
        } finally {
            sql.close()
        }
        
        println("===============  DONE PROCESSING INTEREST ACCRUAL ====================")
        return processedCount
    }

    /**
     * Process interest accrual for a single loan
     */
    private def processLoanInterestAccrual(Loan loan, Date currentDate, Branch branch, UserMaster user) {
        try {
            // Calculate days since last accrual
            def lastAccrualDate = loan.accruedInterestDate ?: loan.dateReleased
            def daysDiff = currentDate - lastAccrualDate
            
            if (daysDiff <= 0) {
                return // No accrual needed
            }
            
            // Calculate daily interest rate
            def annualRate = loan.interestRate / 100
            def dailyRate = annualRate / 365
            
            // Calculate accrued interest
            def accruedInterest = loan.balanceAmount * dailyRate * daysDiff
            
            if (accruedInterest > 0) {
                // Create interest accrual transaction
                def txnTemplate = TxnTemplate.get(Institution.findByParamCode("LNS.60100").paramValue.toInteger())
                
                def txnFile = new TxnFile(
                    currency: loan.currency,
                    status: ConfigItemStatus.get(2),
                    txnAmt: accruedInterest,
                    txnCode: txnTemplate.code,
                    txnDate: currentDate,
                    txnDescription: 'Interest Accrual',
                    txnParticulars: "Interest accrual for loan ${loan.accountNo}",
                    txnRef: loan.accountNo,
                    txnTemplate: txnTemplate,
                    txnTimestamp: new Date().toTimestamp(),
                    txnType: txnTemplate.txnType,
                    user: user,
                    branch: branch,
                    loanAcct: loan
                )
                
                txnFile.save(flush: true, failOnError: true)
                
                // Create loan ledger entry
                def loanLedger = new LoanLedger(
                    loan: loan,
                    txnFile: txnFile,
                    txnDate: currentDate,
                    txnTemplate: txnTemplate,
                    interestCredit: accruedInterest,
                    txnRef: "Interest Accrual",
                    interestBalance: loan.interestBalanceAmount + accruedInterest
                )
                loanLedger.save(flush: true, failOnError: true)
                
                // Create GL breakdown
                def debitBreakdown = new TxnBreakdown(
                    branch: branch,
                    debitAcct: loan.product.interestReceivableAcct,
                    debitAmt: accruedInterest,
                    txnDate: currentDate,
                    txnFile: txnFile,
                    user: user
                )
                debitBreakdown.save(flush: true)
                
                def creditBreakdown = new TxnBreakdown(
                    branch: branch,
                    creditAcct: loan.product.interestIncomeAcct,
                    creditAmt: accruedInterest,
                    txnDate: currentDate,
                    txnFile: txnFile,
                    user: user
                )
                creditBreakdown.save(flush: true)
                
                // Update loan
                loan.interestBalanceAmount += accruedInterest
                loan.accruedInterestDate = currentDate
                loan.save(flush: true)
                
                // Generate GL transactions
                glTransactionService.saveTxnBreakdown(txnFile.id)
            }
            
        } catch (Exception e) {
            log.error("Error processing interest accrual for loan ${loan.accountNo}", e)
            throw new RuntimeException("Failed to process interest accrual for loan ${loan.accountNo}: ${e.message}")
        }
    }

    /**
     * Process penalty interest for overdue loans
     */
    def processPenaltyInterest(Date currentDate, Branch branch, UserMaster user) {
        println("====================== START PENALTY INTEREST PROCESSING ==============================")
        
        def sql = new Sql(dataSource)
        def processedCount = 0
        
        try {
            // Get overdue loans
            def sqlstmt = """
                SELECT DISTINCT l.id
                FROM loan l
                INNER JOIN loan_installment li ON l.id = li.loan_id
                WHERE l.branch_id = ?
                AND l.status_id IN (4, 5)
                AND li.installment_date < ?
                AND li.status_id = 3
                AND l.penalty_rate > 0
                ORDER BY l.id
            """
            
            def overdueLoans = sql.rows(sqlstmt, [branch.id, currentDate])
            
            for (loanRow in overdueLoans) {
                def loan = Loan.get(loanRow.id)
                if (loan) {
                    processLoanPenaltyInterest(loan, currentDate, branch, user)
                    processedCount++
                    
                    if (processedCount % 50 == 0) {
                        cleanUpGorm()
                    }
                }
            }
            
            def description = "Penalty interest processed for ${processedCount} overdue loans on ${currentDate.format('MM/dd/yyyy')}"
            auditLogService.insert('130', 'LIP00500', description, 'LoanInterestProcessing', null, null, null, null)
            
        } catch (Exception e) {
            log.error("Error processing penalty interest", e)
            throw new RuntimeException("Failed to process penalty interest: ${e.message}")
        } finally {
            sql.close()
        }
        
        println("===============  DONE PROCESSING PENALTY INTEREST ====================")
        return processedCount
    }

    /**
     * Process penalty interest for a single overdue loan
     */
    private def processLoanPenaltyInterest(Loan loan, Date currentDate, Branch branch, UserMaster user) {
        try {
            // Calculate overdue amount
            def overdueAmount = loan.overduePrincipalBalance ?: 0
            
            if (overdueAmount > 0 && loan.penaltyRate > 0) {
                // Calculate penalty interest
                def penaltyRate = loan.penaltyRate / 100 / 365 // Daily penalty rate
                def penaltyInterest = overdueAmount * penaltyRate
                
                if (penaltyInterest > 0) {
                    // Create penalty interest transaction
                    def txnTemplate = TxnTemplate.get(Institution.findByParamCode("LNS.60200").paramValue.toInteger())
                    
                    def txnFile = new TxnFile(
                        currency: loan.currency,
                        status: ConfigItemStatus.get(2),
                        txnAmt: penaltyInterest,
                        txnCode: txnTemplate.code,
                        txnDate: currentDate,
                        txnDescription: 'Penalty Interest',
                        txnParticulars: "Penalty interest for overdue loan ${loan.accountNo}",
                        txnRef: loan.accountNo,
                        txnTemplate: txnTemplate,
                        txnTimestamp: new Date().toTimestamp(),
                        txnType: txnTemplate.txnType,
                        user: user,
                        branch: branch,
                        loanAcct: loan
                    )
                    
                    txnFile.save(flush: true, failOnError: true)
                    
                    // Create loan ledger entry
                    def loanLedger = new LoanLedger(
                        loan: loan,
                        txnFile: txnFile,
                        txnDate: currentDate,
                        txnTemplate: txnTemplate,
                        penaltyCredit: penaltyInterest,
                        txnRef: "Penalty Interest",
                        penaltyBalance: loan.penaltyBalanceAmount + penaltyInterest
                    )
                    loanLedger.save(flush: true, failOnError: true)
                    
                    // Create GL breakdown
                    def debitBreakdown = new TxnBreakdown(
                        branch: branch,
                        debitAcct: loan.product.penaltyReceivableAcct,
                        debitAmt: penaltyInterest,
                        txnDate: currentDate,
                        txnFile: txnFile,
                        user: user
                    )
                    debitBreakdown.save(flush: true)
                    
                    def creditBreakdown = new TxnBreakdown(
                        branch: branch,
                        creditAcct: loan.product.penaltyIncomeAcct,
                        creditAmt: penaltyInterest,
                        txnDate: currentDate,
                        txnFile: txnFile,
                        user: user
                    )
                    creditBreakdown.save(flush: true)
                    
                    // Update loan
                    loan.penaltyBalanceAmount += penaltyInterest
                    loan.save(flush: true)
                    
                    // Generate GL transactions
                    glTransactionService.saveTxnBreakdown(txnFile.id)
                }
            }
            
        } catch (Exception e) {
            log.error("Error processing penalty interest for loan ${loan.accountNo}", e)
            throw new RuntimeException("Failed to process penalty interest for loan ${loan.accountNo}: ${e.message}")
        }
    }

    /**
     * Clean up GORM session
     */
    private def cleanUpGorm() {
        def session = sessionFactory.currentSession
        session.flush()
        session.clear()
        def propertyInstanceMap = org.codehaus.groovy.grails.plugins.DomainClassGrailsPlugin.PROPERTY_INSTANCE_MAP
        propertyInstanceMap.get().clear()
    }
}
