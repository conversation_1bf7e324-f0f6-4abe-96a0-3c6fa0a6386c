package org.icbs.periodicops

import grails.gorm.transactions.Transactional
import org.icbs.admin.Branch
import org.icbs.admin.UserMaster

/**
 * LoanPeriodicOpsCoreService - Core coordination service for loan periodic operations
 * 
 * This service coordinates all loan periodic operations including:
 * - Start of day loan processing
 * - End of day loan processing
 * - End of month loan processing
 * - Service coordination and orchestration
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class LoanPeriodicOpsCoreService {
    
    // Service Dependencies
    def loanInstallmentProcessingService
    def loanInterestProcessingService
    def loanRecoveryProcessingService
    def loanClassificationService
    def auditLogService
    def sessionFactory
    
    /**
     * Start of day loan processing
     */
    def startOfDay(Date currentDate, Branch branch, UserMaster user) {
        println("====================== START OF DAY LOAN PROCESSING ==============================")
        println("Date: ${currentDate}")
        println("Branch: ${branch.name}")
        
        def startTime = System.currentTimeMillis()
        def results = [:]
        
        try {
            // 1. Reclassify installments
            println("1. Processing installment reclassification...")
            loanInstallmentProcessingService.reclassifyInstallments(currentDate, branch)
            results.installmentReclassification = "Completed"
            
            // 2. Update interest accrual
            println("2. Processing interest accrual...")
            def interestCount = loanInterestProcessingService.processInterestAccrual(currentDate, branch, user)
            results.interestAccrual = "Completed - ${interestCount} loans processed"
            
            // 3. Update penalties
            println("3. Processing penalty interest...")
            def penaltyCount = loanInterestProcessingService.processPenaltyInterest(currentDate, branch, user)
            results.penaltyProcessing = "Completed - ${penaltyCount} loans processed"
            
            // 4. Process loan recoveries
            println("4. Processing loan recoveries...")
            loanRecoveryProcessingService.processLoanRecoveries(currentDate, branch, user)
            results.loanRecoveries = "Completed"
            
            def endTime = System.currentTimeMillis()
            def duration = endTime - startTime
            
            def description = "Start of day loan processing completed in ${duration}ms - ${results}"
            auditLogService.insert('130', 'LPC00100', description, 'LoanPeriodicOpsCore', null, null, null, null)
            
            println("===============  START OF DAY LOAN PROCESSING COMPLETED ====================")
            return results
            
        } catch (Exception e) {
            log.error("Error in start of day loan processing", e)
            def description = "Start of day loan processing failed: ${e.message}"
            auditLogService.insert('130', 'LPC00101', description, 'LoanPeriodicOpsCore', null, null, null, null)
            throw new RuntimeException("Start of day loan processing failed: ${e.message}")
        }
    }

    /**
     * End of day loan processing
     */
    def endOfDay(Date currentDate, Branch branch, UserMaster user) {
        println("====================== END OF DAY LOAN PROCESSING ==============================")
        println("Date: ${currentDate}")
        println("Branch: ${branch.name}")
        
        def startTime = System.currentTimeMillis()
        def results = [:]
        
        try {
            // 1. Post paid installments
            println("1. Posting paid installments...")
            loanInstallmentProcessingService.postPaidInstallments(currentDate, branch)
            results.paidInstallments = "Completed"
            
            // 2. Update cancelled payments
            println("2. Processing cancelled payments...")
            loanInstallmentProcessingService.updateCancelledPayments(currentDate, branch)
            results.cancelledPayments = "Completed"
            
            // 3. Update loan age
            println("3. Updating loan age...")
            def ageCount = loanClassificationService.updateLoanAge(currentDate, branch, user)
            results.loanAge = "Completed - ${ageCount} loans updated"
            
            // 4. Update overdue principal
            println("4. Updating overdue principal...")
            def overdueCount = loanClassificationService.updateOverduePrincipal(currentDate, branch)
            results.overduePrincipal = "Completed - ${overdueCount} loans updated"
            
            // 5. Loan performance reclassification
            println("5. Processing loan performance reclassification...")
            def reclassCount = loanClassificationService.processLoanPerformanceReclassification(currentDate, "daily", branch, user)
            results.performanceReclassification = "Completed - ${reclassCount} loans reclassified"
            
            // 6. Create loan due history
            println("6. Creating loan due history...")
            def dueHistCount = loanClassificationService.createLoanDueHistory(currentDate, branch, user)
            results.dueHistory = "Completed - ${dueHistCount} records created"
            
            def endTime = System.currentTimeMillis()
            def duration = endTime - startTime
            
            def description = "End of day loan processing completed in ${duration}ms - ${results}"
            auditLogService.insert('130', 'LPC00200', description, 'LoanPeriodicOpsCore', null, null, null, null)
            
            println("===============  END OF DAY LOAN PROCESSING COMPLETED ====================")
            return results
            
        } catch (Exception e) {
            log.error("Error in end of day loan processing", e)
            def description = "End of day loan processing failed: ${e.message}"
            auditLogService.insert('130', 'LPC00201', description, 'LoanPeriodicOpsCore', null, null, null, null)
            throw new RuntimeException("End of day loan processing failed: ${e.message}")
        }
    }

    /**
     * End of month loan processing
     */
    def endOfMonth(Date currentDate, Branch branch, UserMaster user) {
        println("====================== END OF MONTH LOAN PROCESSING ==============================")
        println("Date: ${currentDate}")
        println("Branch: ${branch.name}")
        
        def startTime = System.currentTimeMillis()
        def results = [:]
        
        try {
            // 1. Monthly loan performance reclassification
            println("1. Processing monthly loan performance reclassification...")
            def reclassCount = loanClassificationService.processLoanPerformanceReclassification(currentDate, "monthly", branch, user)
            results.monthlyReclassification = "Completed - ${reclassCount} loans reclassified"
            
            // 2. Monthly interest processing (if applicable)
            println("2. Processing monthly interest operations...")
            def interestCount = loanInterestProcessingService.processInterestAccrual(currentDate, branch, user)
            results.monthlyInterest = "Completed - ${interestCount} loans processed"
            
            def endTime = System.currentTimeMillis()
            def duration = endTime - startTime
            
            def description = "End of month loan processing completed in ${duration}ms - ${results}"
            auditLogService.insert('130', 'LPC00300', description, 'LoanPeriodicOpsCore', null, null, null, null)
            
            println("===============  END OF MONTH LOAN PROCESSING COMPLETED ====================")
            return results
            
        } catch (Exception e) {
            log.error("Error in end of month loan processing", e)
            def description = "End of month loan processing failed: ${e.message}"
            auditLogService.insert('130', 'LPC00301', description, 'LoanPeriodicOpsCore', null, null, null, null)
            throw new RuntimeException("End of month loan processing failed: ${e.message}")
        }
    }

    /**
     * Clean up GORM session
     */
    def cleanUpLnGorm() {
        def session = sessionFactory.currentSession
        session.flush()
        session.clear()
        def propertyInstanceMap = org.codehaus.groovy.grails.plugins.DomainClassGrailsPlugin.PROPERTY_INSTANCE_MAP
        propertyInstanceMap.get().clear()
    }

    /**
     * Get loan processing statistics
     */
    def getLoanProcessingStatistics(Date currentDate, Branch branch) {
        def stats = [:]
        
        try {
            def sql = new groovy.sql.Sql(dataSource)
            
            // Active loans count
            stats.activeLoans = sql.firstRow("""
                SELECT COUNT(*) as count 
                FROM loan 
                WHERE branch_id = ? AND status_id IN (4, 5)
            """, [branch.id])?.count ?: 0
            
            // Overdue loans count
            stats.overdueLoans = sql.firstRow("""
                SELECT COUNT(*) as count 
                FROM loan 
                WHERE branch_id = ? AND status_id IN (4, 5) 
                AND overdue_principal_balance > 0
            """, [branch.id])?.count ?: 0
            
            // Total overdue amount
            stats.totalOverdueAmount = sql.firstRow("""
                SELECT COALESCE(SUM(overdue_principal_balance), 0) as amount
                FROM loan 
                WHERE branch_id = ? AND status_id IN (4, 5)
            """, [branch.id])?.amount ?: 0
            
            // Loans processed today
            stats.loansProcessedToday = sql.firstRow("""
                SELECT COUNT(DISTINCT loan_id) as count
                FROM daily_loan_installment_processing
                WHERE process_date = ?
            """, [currentDate])?.count ?: 0
            
            sql.close()
            
        } catch (Exception e) {
            log.error("Error getting loan processing statistics", e)
            stats.error = "Error getting statistics: ${e.message}"
        }
        
        return stats
    }

    /**
     * Validate loan processing readiness
     */
    def validateProcessingReadiness(Date currentDate, Branch branch) {
        def validation = [
            ready: true,
            warnings: [],
            errors: []
        ]
        
        try {
            // Check if previous day processing is complete
            def yesterday = currentDate - 1
            def sql = new groovy.sql.Sql(dataSource)
            
            def pendingCount = sql.firstRow("""
                SELECT COUNT(*) as count
                FROM loan l
                WHERE l.branch_id = ?
                AND l.status_id IN (4, 5)
                AND l.accrued_interest_date < ?
            """, [branch.id, yesterday])?.count ?: 0
            
            if (pendingCount > 0) {
                validation.warnings.add("${pendingCount} loans have pending interest accrual from previous days")
            }
            
            // Check for system locks
            def systemLocked = sql.firstRow("""
                SELECT param_value 
                FROM institution 
                WHERE param_code = 'GEN.10250'
            """)?.param_value
            
            if (systemLocked == 'TRUE') {
                validation.errors.add("System is currently locked")
                validation.ready = false
            }
            
            sql.close()
            
        } catch (Exception e) {
            log.error("Error validating processing readiness", e)
            validation.errors.add("Error validating readiness: ${e.message}")
            validation.ready = false
        }
        
        return validation
    }
}
