package org.icbs.periodicops

import grails.gorm.transactions.Transactional
import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.loans.Loan
import org.icbs.loans.LoanReClassHist
import org.icbs.loans.LoanDueHist
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanPerformanceClassification
import org.icbs.lov.LoanPerformanceId
import groovy.sql.Sql

/**
 * LoanClassificationService - Handles loan classification processing
 * 
 * This service manages loan classification operations including:
 * - Loan performance reclassification
 * - Loan aging calculations
 * - Classification history tracking
 * - Due history management
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class LoanClassificationService {
    
    def sessionFactory
    def dataSource
    def auditLogService
    
    /**
     * Process loan performance reclassification
     */
    def processLoanPerformanceReclassification(Date currentDate, String frequency, Branch branch, UserMaster user) {
        println("====================== START LOAN PERFORMANCE RECLASSIFICATION ==============================")
        println("Frequency: ${frequency}")
        
        def sql = new Sql(dataSource)
        def reclassifiedCount = 0
        
        try {
            // Get all active loans for reclassification
            def sqlstmt = """
                SELECT l.id, l.account_no, l.overdue_principal_balance, 
                       l.loan_performance_id_id, l.date_released
                FROM loan l
                WHERE l.branch_id = ?
                AND l.status_id IN (4, 5)
                AND l.balance_amount > 0
                ORDER BY l.id
            """
            
            def loans = sql.rows(sqlstmt, [branch.id])
            
            for (loanRow in loans) {
                def loan = Loan.get(loanRow.id)
                if (loan) {
                    def oldClassification = loan.loanPerformanceId
                    def newClassification = calculateLoanClassification(loan, currentDate)
                    
                    if (oldClassification.id != newClassification.id) {
                        // Reclassify the loan
                        reclassifyLoan(loan, oldClassification, newClassification, currentDate, user, frequency)
                        reclassifiedCount++
                    }
                    
                    if (reclassifiedCount % 50 == 0) {
                        cleanUpGorm()
                    }
                }
            }
            
            def description = "Loan performance reclassification (${frequency}) processed for ${reclassifiedCount} loans on ${currentDate.format('MM/dd/yyyy')}"
            auditLogService.insert('130', 'LCS00100', description, 'LoanClassification', null, null, null, null)
            
        } catch (Exception e) {
            log.error("Error processing loan performance reclassification", e)
            throw new RuntimeException("Failed to process loan performance reclassification: ${e.message}")
        } finally {
            sql.close()
        }
        
        println("===============  DONE PROCESSING LOAN PERFORMANCE RECLASSIFICATION ====================")
        return reclassifiedCount
    }

    /**
     * Calculate loan classification based on overdue days
     */
    private def calculateLoanClassification(Loan loan, Date currentDate) {
        def overdueDays = calculateOverdueDays(loan, currentDate)
        
        // Standard BSP classification rules
        if (overdueDays <= 30) {
            return LoanPerformanceId.get(1) // Current
        } else if (overdueDays <= 90) {
            return LoanPerformanceId.get(2) // OLEM (Other Loans Especially Mentioned)
        } else if (overdueDays <= 180) {
            return LoanPerformanceId.get(3) // Substandard
        } else if (overdueDays <= 365) {
            return LoanPerformanceId.get(4) // Doubtful
        } else {
            return LoanPerformanceId.get(5) // Loss
        }
    }

    /**
     * Calculate overdue days for a loan
     */
    private def calculateOverdueDays(Loan loan, Date currentDate) {
        def sql = new Sql(dataSource)
        
        try {
            // Find the earliest unpaid installment
            def sqlstmt = """
                SELECT MIN(installment_date) as earliest_due
                FROM loan_installment
                WHERE loan_id = ?
                AND status_id = 3
                AND installment_date < ?
            """
            
            def result = sql.firstRow(sqlstmt, [loan.id, currentDate])
            
            if (result?.earliest_due) {
                return currentDate - result.earliest_due
            } else {
                return 0
            }
            
        } catch (Exception e) {
            log.error("Error calculating overdue days for loan ${loan.accountNo}", e)
            return 0
        } finally {
            sql.close()
        }
    }

    /**
     * Reclassify a loan and create history record
     */
    private def reclassifyLoan(Loan loan, LoanPerformanceId oldClassification, LoanPerformanceId newClassification, 
                              Date currentDate, UserMaster user, String frequency) {
        try {
            // Update loan classification
            loan.loanPerformanceId = newClassification
            loan.save(flush: true)
            
            // Create reclassification history
            def reClassHist = new LoanReClassHist(
                loan: loan,
                reclassDate: currentDate,
                oldClassification: oldClassification,
                newClassification: newClassification,
                reason: "Automatic ${frequency} reclassification",
                user: user,
                branch: loan.branch
            )
            reClassHist.save(flush: true, failOnError: true)
            
            println("Loan ${loan.accountNo} reclassified from ${oldClassification.description} to ${newClassification.description}")
            
        } catch (Exception e) {
            log.error("Error reclassifying loan ${loan.accountNo}", e)
            throw new RuntimeException("Failed to reclassify loan ${loan.accountNo}: ${e.message}")
        }
    }

    /**
     * Update loan age for all active loans
     */
    def updateLoanAge(Date currentDate, Branch branch, UserMaster user) {
        println("====================== START LOAN AGE UPDATE ==============================")
        
        def sql = new Sql(dataSource)
        def updatedCount = 0
        
        try {
            // Get all active loans
            def sqlstmt = """
                SELECT l.id, l.account_no, l.date_released
                FROM loan l
                WHERE l.branch_id = ?
                AND l.status_id IN (4, 5)
                ORDER BY l.id
            """
            
            def loans = sql.rows(sqlstmt, [branch.id])
            
            for (loanRow in loans) {
                def loan = Loan.get(loanRow.id)
                if (loan) {
                    // Calculate loan age in days
                    def loanAge = currentDate - loan.dateReleased
                    
                    // Update loan age
                    loan.loanAge = loanAge
                    loan.save(flush: true)
                    
                    updatedCount++
                    
                    if (updatedCount % 100 == 0) {
                        cleanUpGorm()
                    }
                }
            }
            
            def description = "Loan age updated for ${updatedCount} loans on ${currentDate.format('MM/dd/yyyy')}"
            auditLogService.insert('130', 'LCS00200', description, 'LoanClassification', null, null, null, null)
            
        } catch (Exception e) {
            log.error("Error updating loan age", e)
            throw new RuntimeException("Failed to update loan age: ${e.message}")
        } finally {
            sql.close()
        }
        
        println("===============  DONE UPDATING LOAN AGE ====================")
        return updatedCount
    }

    /**
     * Update overdue principal for all loans
     */
    def updateOverduePrincipal(Date currentDate, Branch branch) {
        println("====================== START OVERDUE PRINCIPAL UPDATE ==============================")
        
        def sql = new Sql(dataSource)
        def updatedCount = 0
        
        try {
            // Update overdue principal for all loans with due installments
            def sqlstmt = """
                UPDATE loan 
                SET overdue_principal_balance = (
                    SELECT COALESCE(SUM(principal_installment_amount - COALESCE(principal_installment_paid, 0)), 0)
                    FROM loan_installment 
                    WHERE loan_id = loan.id 
                    AND installment_date <= ?
                    AND status_id IN (3, 4)
                )
                WHERE branch_id = ?
                AND status_id IN (4, 5)
            """
            
            def updateCount = sql.executeUpdate(sqlstmt, [currentDate, branch.id])
            
            def description = "Overdue principal updated for ${updateCount} loans on ${currentDate.format('MM/dd/yyyy')}"
            auditLogService.insert('130', 'LCS00300', description, 'LoanClassification', null, null, null, null)
            
            updatedCount = updateCount
            
        } catch (Exception e) {
            log.error("Error updating overdue principal", e)
            throw new RuntimeException("Failed to update overdue principal: ${e.message}")
        } finally {
            sql.close()
        }
        
        println("===============  DONE UPDATING OVERDUE PRINCIPAL ====================")
        return updatedCount
    }

    /**
     * Create loan due history records
     */
    def createLoanDueHistory(Date currentDate, Branch branch, UserMaster user) {
        def sql = new Sql(dataSource)
        def createdCount = 0
        
        try {
            // Get loans with overdue amounts
            def sqlstmt = """
                SELECT l.id, l.account_no, l.overdue_principal_balance,
                       l.interest_balance_amount, l.service_charge_balance_amount,
                       l.penalty_balance_amount
                FROM loan l
                WHERE l.branch_id = ?
                AND l.status_id IN (4, 5)
                AND (l.overdue_principal_balance > 0 OR l.interest_balance_amount > 0 
                     OR l.service_charge_balance_amount > 0 OR l.penalty_balance_amount > 0)
                ORDER BY l.id
            """
            
            def loans = sql.rows(sqlstmt, [branch.id])
            
            for (loanRow in loans) {
                def loan = Loan.get(loanRow.id)
                if (loan) {
                    def dueHist = new LoanDueHist(
                        loan: loan,
                        dueDate: currentDate,
                        principalDue: loan.overduePrincipalBalance ?: 0,
                        interestDue: loan.interestBalanceAmount ?: 0,
                        serviceChargeDue: loan.serviceChargeBalanceAmount ?: 0,
                        penaltyDue: loan.penaltyBalanceAmount ?: 0,
                        totalDue: (loan.overduePrincipalBalance ?: 0) + 
                                 (loan.interestBalanceAmount ?: 0) + 
                                 (loan.serviceChargeBalanceAmount ?: 0) + 
                                 (loan.penaltyBalanceAmount ?: 0),
                        user: user,
                        branch: branch
                    )
                    dueHist.save(flush: true, failOnError: true)
                    
                    createdCount++
                    
                    if (createdCount % 50 == 0) {
                        cleanUpGorm()
                    }
                }
            }
            
            def description = "Loan due history created for ${createdCount} loans on ${currentDate.format('MM/dd/yyyy')}"
            auditLogService.insert('130', 'LCS00400', description, 'LoanClassification', null, null, null, null)
            
        } catch (Exception e) {
            log.error("Error creating loan due history", e)
            throw new RuntimeException("Failed to create loan due history: ${e.message}")
        } finally {
            sql.close()
        }
        
        return createdCount
    }

    /**
     * Clean up GORM session
     */
    private def cleanUpGorm() {
        def session = sessionFactory.currentSession
        session.flush()
        session.clear()
        def propertyInstanceMap = org.codehaus.groovy.grails.plugins.DomainClassGrailsPlugin.PROPERTY_INSTANCE_MAP
        propertyInstanceMap.get().clear()
    }
}
