package org.icbs.periodicops

import grails.gorm.transactions.Transactional
import org.icbs.admin.Branch
import org.icbs.admin.UserMaster
import org.icbs.loans.Loan
import org.icbs.loans.LoanInstallment
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanInstallmentStatus
import org.icbs.periodicops.DailyLoanInstallmentProcessing
import groovy.sql.Sql

/**
 * LoanInstallmentProcessingService - Handles loan installment processing
 * 
 * This service manages loan installment processing operations including:
 * - Installment reclassification
 * - Due installment processing
 * - Installment status updates
 * - Daily installment processing
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
class LoanInstallmentProcessingService {
    
    def sessionFactory
    def dataSource
    def auditLogService
    
    /**
     * Reclassify installments for start of day processing
     */
    def reclassifyInstallments(Date currentDate, Branch branch) {
        def loans = Loan.createCriteria().list() {
            and{
                eq("branch", branch)
                gt("status", LoanAcctStatus.get(1)) 
                lt("status", LoanAcctStatus.get(6))                
                loanInstallments {      
                    and {
                        le("installmentDate", currentDate)
                        eq("status.id", 2L)  // not fully paid
                    }
                }
            }
        }       
        
        println '+++++++++++++++++++++++++++++++++++++'
        println loans
        println '+++++++++++++++++++++++++++++++++++++'
        
        def lnOdue = 0.00D
        def intPd = 0.00D
        def penPd = 0.00D
        Integer i = 0
        def aacounter = 0				 
        
        for (loan in loans) {  
            aacounter = aacounter + 1
            println("Loan Counter Here ================================================= " + aacounter)
            
            if(!loan.isAttached()) {
                loan.attach()
            }										 
            
            def dueInstallments = loan?.loanInstallments.findAll{
                it.installmentDate <= currentDate && it.status.id == 2L
            }
            
            for(installment in dueInstallments.sort{it.sequenceNo}) {
                if (!loan.overduePrincipalBalance) {
                    loan.overduePrincipalBalance = 0d
                }
                if (!loan.normalInterestAmount) {
                    loan.normalInterestAmount = 0d
                }
                if (!loan.interestBalanceAmount) {
                    loan.interestBalanceAmount = 0d 
                }
                if (!loan.serviceChargeBalanceAmount) {
                    loan.serviceChargeBalanceAmount = 0d
                }
                
                println("loan overduePrincipalBalance value: " + loan.overduePrincipalBalance)
                lnOdue = loan.overduePrincipalBalance * -1
                println("lnOdue: " + lnOdue)						  
                intPd = loan.interestBalanceAmount * -1
                
                loan.overduePrincipalBalance += installment.principalInstallmentAmount
                
                if (loan.interestIncomeScheme.installmentCalcType.id == 1 && 
                    loan.interestIncomeScheme.advInterestType.id == 1) {
                    // do not change interest for single payment
                    loan.interestBalanceAmount += 0.00
                } else if (loan.interestIncomeScheme.installmentCalcType.id == 8) {
                    // SBGFC & Kosrae - interest computed dynamically
                    loan.interestBalanceAmount += 0.00
                } else {
                    loan.interestBalanceAmount += installment.interestInstallmentAmount         
                }
                
                loan.normalInterestAmount += installment.normalInterestInstallmentAmount
                loan.serviceChargeBalanceAmount += installment.serviceChargeInstallmentAmount
                installment.status = LoanInstallmentStatus.get(3)
                installment.save(failOnError: true)
                
                // mark installment as paid based on advanced payments
                if (lnOdue > 0) {
                    if (lnOdue >= installment.principalInstallmentAmount) {
                        installment.principalInstallmentPaid = installment.principalInstallmentAmount
                    } else {
                        installment.principalInstallmentPaid = lnOdue
                    }
                }
                
                if (intPd > 0) {
                    if (intPd > installment.interestInstallmentAmount) {
                       installment.interestInstallmentPaid = installment.interestInstallmentAmount
                    } else {
                        installment.interestInstallmentPaid = intPd
                    }
                }

                // check if fully paid
                if ((installment.principalInstallmentPaid == installment.principalInstallmentAmount) && 
                    (installment.interestInstallmentPaid == installment.interestInstallmentAmount)) {
                    installment.datePaid = loan.branch.runDate
                    installment.status = LoanInstallmentStatus.get(5)   
                    println '???? FULLY PAID ????'
                    println loan.accountNo
                    println installment
                } else if ((installment.principalInstallmentPaid > 0.00) && 
                          (installment.interestInstallmentPaid >= 0.00)) {
                    // partially paid
                    installment.status = LoanInstallmentStatus.get(4)                
                }
                
                installment.save(failOnError: true)
                loan.accruedInterestDate = currentDate
                
                if (loan.balanceAmount > 0 && installment.installmentDate == loan.maturityDate) {
                    // change loan status to matured
                    loan.status = LoanAcctStatus.get(5)
                }

                loan.save(failOnError: true)   
                
                def daily = new DailyLoanInstallmentProcessing(
                    processDate: currentDate, 
                    loanInstallment: installment.id, 
                    loan: loan.id
                )
                daily.save(flush: true)
                
                i++
                if (i == 50) {
                    i = 1
                    cleanUpGorm()
                }
            }
        }
        
        def description = "Loan installments reclassified for ${loans.size()} loans on ${currentDate.format('MM/dd/yyyy')}"
        auditLogService.insert('130', 'LIP00100', description, 'LoanInstallmentProcessing', null, null, null, null)
    }

    /**
     * Post paid installments for end of day processing
     */
    def postPaidInstallments(Date currentDate, Branch branch) {
        def sql = new Sql(dataSource)
        
        try {
            // Get all loans with paid installments for the day
            def sqlstmt = """
                SELECT DISTINCT l.id 
                FROM loan l
                INNER JOIN loan_installment li ON l.id = li.loan_id
                WHERE l.branch_id = ? 
                AND li.date_paid = ?
                AND li.status_id = 5
            """
            
            def loans = sql.rows(sqlstmt, [branch.id, currentDate])
            
            for (loanRow in loans) {
                def loan = Loan.get(loanRow.id)
                if (loan) {
                    // Process paid installments for this loan
                    def paidInstallments = loan.loanInstallments.findAll { 
                        it.datePaid == currentDate && it.status.id == 5 
                    }
                    
                    for (installment in paidInstallments) {
                        // Update loan balances based on paid installment
                        loan.balanceAmount -= installment.principalInstallmentPaid
                        loan.interestBalanceAmount -= installment.interestInstallmentPaid
                        loan.serviceChargeBalanceAmount -= installment.serviceChargeInstallmentPaid
                        
                        // Check if loan is fully paid
                        if (loan.balanceAmount <= 0) {
                            loan.status = LoanAcctStatus.get(6) // Closed
                            loan.dateClosed = currentDate
                        }
                    }
                    
                    loan.save(flush: true)
                }
            }
            
            def description = "Paid installments posted for ${loans.size()} loans on ${currentDate.format('MM/dd/yyyy')}"
            auditLogService.insert('130', 'LIP00200', description, 'LoanInstallmentProcessing', null, null, null, null)
            
        } catch (Exception e) {
            log.error("Error posting paid installments", e)
            throw new RuntimeException("Failed to post paid installments: ${e.message}")
        } finally {
            sql.close()
        }
    }

    /**
     * Update cancelled payments
     */
    def updateCancelledPayments(Date currentDate, Branch branch) {
        def sql = new Sql(dataSource)
        
        try {
            // Find cancelled payments and reverse installment status
            def sqlstmt = """
                SELECT li.id, li.loan_id
                FROM loan_installment li
                INNER JOIN loan l ON li.loan_id = l.id
                INNER JOIN txn_file tf ON tf.loan_acct_id = l.id
                WHERE l.branch_id = ?
                AND tf.txn_date = ?
                AND tf.status_id = 3
                AND li.status_id = 5
            """
            
            def cancelledPayments = sql.rows(sqlstmt, [branch.id, currentDate])
            
            for (payment in cancelledPayments) {
                def installment = LoanInstallment.get(payment.id)
                if (installment) {
                    // Reverse the payment
                    installment.principalInstallmentPaid = 0
                    installment.interestInstallmentPaid = 0
                    installment.serviceChargeInstallmentPaid = 0
                    installment.datePaid = null
                    installment.status = LoanInstallmentStatus.get(3) // Due
                    installment.save(flush: true)
                    
                    // Update loan balances
                    def loan = installment.loan
                    loan.balanceAmount += installment.principalInstallmentAmount
                    loan.interestBalanceAmount += installment.interestInstallmentAmount
                    loan.serviceChargeBalanceAmount += installment.serviceChargeInstallmentAmount
                    loan.save(flush: true)
                }
            }
            
            def description = "Cancelled payments updated for ${cancelledPayments.size()} installments on ${currentDate.format('MM/dd/yyyy')}"
            auditLogService.insert('130', 'LIP00300', description, 'LoanInstallmentProcessing', null, null, null, null)
            
        } catch (Exception e) {
            log.error("Error updating cancelled payments", e)
            throw new RuntimeException("Failed to update cancelled payments: ${e.message}")
        } finally {
            sql.close()
        }
    }

    /**
     * Clean up GORM session
     */
    private def cleanUpGorm() {
        def session = sessionFactory.currentSession
        session.flush()
        session.clear()
        def propertyInstanceMap = org.codehaus.groovy.grails.plugins.DomainClassGrailsPlugin.PROPERTY_INSTANCE_MAP
        propertyInstanceMap.get().clear()
    }
}
