package org.icbs.security

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.crypto.password.PasswordEncoder
import grails.gorm.transactions.Transactional

@Transactional
class SecurePasswordService {
    
    private final PasswordEncoder passwordEncoder = new BCryptPasswordEncoder(12)
    
    String hashPassword(String plainPassword) {
        if (!plainPassword) {
            throw new IllegalArgumentException("Password cannot be null or empty")
        }
        return passwordEncoder.encode(plainPassword)
    }
    
    boolean verifyPassword(String plainPassword, String hashedPassword) {
        if (!plainPassword || !hashedPassword) {
            return false
        }
        return passwordEncoder.matches(plainPassword, hashedPassword)
    }
    
    boolean isValidPassword(String password) {
        if (!password) return false
        
        // Password must be at least 8 characters
        if (password.length() < 8) return false
        
        // Must contain uppercase letter
        if (!password.matches(".*[A-Z].*")) return false
        
        // Must contain lowercase letter
        if (!password.matches(".*[a-z].*")) return false
        
        // Must contain digit
        if (!password.matches(".*[0-9].*")) return false
        
        // Must contain special character
        if (!password.matches(".*[!@#\$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) return false
        
        return true
    }
    
    String generateSecurePassword() {
        def chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#\$%^&*"
        def random = new Random()
        def password = new StringBuilder()
        
        // Ensure at least one of each required character type
        password.append((char) ('A' + random.nextInt(26))) // Uppercase
        password.append((char) ('a' + random.nextInt(26))) // Lowercase
        password.append((char) ('0' + random.nextInt(10))) // Digit
        password.append("!@#\$%^&*".charAt(random.nextInt(8))) // Special
        
        // Fill remaining positions
        for (int i = 4; i < 12; i++) {
            password.append(chars.charAt(random.nextInt(chars.length())))
        }
        
        // Shuffle the password
        def passwordArray = password.toString().toCharArray()
        for (int i = passwordArray.length - 1; i > 0; i--) {
            int j = random.nextInt(i + 1)
            char temp = passwordArray[i]
            passwordArray[i] = passwordArray[j]
            passwordArray[j] = temp
        }
        
        return new String(passwordArray)
    }
}
