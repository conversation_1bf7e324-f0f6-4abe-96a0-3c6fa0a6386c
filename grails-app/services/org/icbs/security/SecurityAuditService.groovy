package org.icbs.security

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.springframework.scheduling.annotation.Async
import javax.servlet.http.HttpServletRequest
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes

/**
 * Security Audit Service
 * Comprehensive security event logging and audit trail management
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 7.0
 */
// @Service annotation removed for Grails 6.2.3 compatibility
@Transactional
@Slf4j
class SecurityAuditService {
    
    /**
     * Log security event with comprehensive details
     */
    // @Async annotation removed for Grails 6.2.3 compatibility
    void logSecurityEvent(Map eventParams) {
        try {
            // Get current request context if available
            HttpServletRequest request = getCurrentRequest()
            
            // Build comprehensive audit log entry
            Map<String, Object> auditParams = buildAuditParams(eventParams, request)
            
            // Create audit log entry
            SecurityAuditLog auditLog = SecurityAuditLog.createAuditLog(auditParams)
            
            // Check if event requires immediate attention
            if (auditLog.requiresImmediateAttention()) {
                handleHighPriorityEvent(auditLog)
            }
            
            log.debug("Security event logged: ${auditLog.eventType} for user: ${auditLog.username}")
            
        } catch (Exception e) {
            log.error("Failed to log security event", e)
            // Don't throw exception to avoid breaking the main flow
        }
    }
    
    /**
     * Log authentication event
     */
    void logAuthenticationEvent(String eventType, String username, boolean success, Map additionalData = [:]) {
        Map eventParams = [
            eventType: eventType,
            eventCategory: 'AUTHENTICATION',
            eventDescription: getAuthenticationEventDescription(eventType, success),
            username: username,
            result: success ? 'SUCCESS' : 'FAILURE',
            eventData: additionalData
        ]
        
        // Add security level based on event type
        if (!success) {
            eventParams.securityLevel = 'HIGH'
            eventParams.requiresReview = true
        }
        
        logSecurityEvent(eventParams)
    }
    
    /**
     * Log authorization event
     */
    void logAuthorizationEvent(String username, String resource, String action, boolean granted, String reason = null) {
        Map eventParams = [
            eventType: granted ? 'AUTHORIZATION_GRANTED' : 'AUTHORIZATION_DENIED',
            eventCategory: 'AUTHORIZATION',
            eventDescription: "Access ${granted ? 'granted' : 'denied'} to ${resource} for action ${action}",
            username: username,
            result: granted ? 'SUCCESS' : 'FAILURE',
            eventData: [
                resource: resource,
                action: action,
                reason: reason
            ]
        ]
        
        if (!granted) {
            eventParams.securityLevel = 'HIGH'
            eventParams.requiresReview = true
        }
        
        logSecurityEvent(eventParams)
    }
    
    /**
     * Log data access event
     */
    void logDataAccessEvent(String username, String dataType, String operation, Map dataDetails = [:]) {
        Map eventParams = [
            eventType: 'DATA_ACCESS',
            eventCategory: 'DATA_ACCESS',
            eventDescription: "Data access: ${operation} on ${dataType}",
            username: username,
            result: 'SUCCESS',
            eventData: [
                dataType: dataType,
                operation: operation
            ] + dataDetails
        ]
        
        // Set compliance flags for sensitive data
        if (isSensitiveData(dataType)) {
            eventParams.complianceFlags = 'PCI_DSS,GDPR'
            eventParams.regulatoryCategory = 'GDPR'
        }
        
        logSecurityEvent(eventParams)
    }
    
    /**
     * Log configuration change event
     */
    void logConfigurationChange(String username, String configType, String beforeState, String afterState) {
        Map eventParams = [
            eventType: 'CONFIGURATION_CHANGE',
            eventCategory: 'CONFIGURATION',
            eventDescription: "Configuration changed: ${configType}",
            username: username,
            result: 'SUCCESS',
            beforeState: beforeState,
            afterState: afterState,
            securityLevel: 'MEDIUM',
            requiresReview: true,
            eventData: [
                configurationType: configType
            ]
        ]
        
        logSecurityEvent(eventParams)
    }
    
    /**
     * Log security violation
     */
    void logSecurityViolation(String violationType, String description, Map violationDetails = [:]) {
        Map eventParams = [
            eventType: 'SECURITY_VIOLATION',
            eventCategory: 'SECURITY',
            eventDescription: "Security violation: ${description}",
            result: 'BLOCKED',
            securityLevel: 'CRITICAL',
            requiresReview: true,
            eventData: [
                violationType: violationType
            ] + violationDetails
        ]
        
        logSecurityEvent(eventParams)
    }
    
    /**
     * Log compliance event
     */
    void logComplianceEvent(String complianceType, String description, String regulatoryCategory, Map complianceData = [:]) {
        Map eventParams = [
            eventType: 'COMPLIANCE_EVENT',
            eventCategory: 'COMPLIANCE',
            eventDescription: description,
            result: 'SUCCESS',
            regulatoryCategory: regulatoryCategory,
            complianceFlags: complianceType,
            requiresReview: true,
            eventData: complianceData
        ]
        
        logSecurityEvent(eventParams)
    }
    
    /**
     * Build comprehensive audit parameters
     */
    private Map<String, Object> buildAuditParams(Map eventParams, HttpServletRequest request) {
        Map<String, Object> auditParams = [:]
        
        // Copy provided parameters
        auditParams.putAll(eventParams)
        
        // Add request information if available
        if (request) {
            auditParams.ipAddress = auditParams.ipAddress ?: getClientIpAddress(request)
            auditParams.userAgent = auditParams.userAgent ?: request.getHeader('User-Agent')
            auditParams.requestUri = auditParams.requestUri ?: request.requestURI
            auditParams.requestMethod = auditParams.requestMethod ?: request.method
            auditParams.referer = request.getHeader('Referer')
            auditParams.forwardedFor = request.getHeader('X-Forwarded-For')
            auditParams.remoteHost = request.remoteHost
            
            // Add session information
            if (request.session) {
                auditParams.sessionId = auditParams.sessionId ?: request.session.id
            }
            
            // Add request parameters (sanitized)
            auditParams.requestParameters = sanitizeRequestParameters(request.parameterMap)
        }
        
        // Set default values
        auditParams.timestamp = auditParams.timestamp ?: new Date()
        auditParams.eventId = auditParams.eventId ?: UUID.randomUUID().toString()
        
        // Calculate risk score if not provided
        if (!auditParams.riskScore) {
            auditParams.riskScore = calculateRiskScore(auditParams)
        }
        
        return auditParams
    }
    
    /**
     * Get current HTTP request
     */
    private HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = RequestContextHolder.currentRequestAttributes() as ServletRequestAttributes
            return attributes?.request
        } catch (Exception e) {
            // No request context available (e.g., background job)
            return null
        }
    }
    
    /**
     * Get client IP address considering proxies
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader('X-Forwarded-For')
        if (xForwardedFor && !xForwardedFor.isEmpty() && !'unknown'.equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(',')[0].trim()
        }
        
        String xRealIp = request.getHeader('X-Real-IP')
        if (xRealIp && !xRealIp.isEmpty() && !'unknown'.equalsIgnoreCase(xRealIp)) {
            return xRealIp
        }
        
        return request.remoteAddr
    }
    
    /**
     * Sanitize request parameters for logging
     */
    private String sanitizeRequestParameters(Map<String, String[]> parameterMap) {
        if (!parameterMap) {
            return null
        }
        
        Map<String, Object> sanitized = [:]
        Set<String> sensitiveParams = ['password', 'token', 'secret', 'key', 'ssn', 'pin']
        
        parameterMap.each { key, values ->
            String lowerKey = key.toLowerCase()
            if (sensitiveParams.any { lowerKey.contains(it) }) {
                sanitized[key] = '[REDACTED]'
            } else {
                sanitized[key] = values?.length == 1 ? values[0] : values
            }
        }
        
        return sanitized.toString()
    }
    
    /**
     * Calculate risk score based on event parameters
     */
    private String calculateRiskScore(Map auditParams) {
        int score = 0
        
        // Base score by event type
        switch (auditParams.eventType) {
            case 'SECURITY_VIOLATION':
            case 'UNAUTHORIZED_ACCESS':
                score += 90
                break
            case 'LOGIN_FAILURE':
            case 'ACCOUNT_LOCKED':
                score += 70
                break
            case 'CONFIGURATION_CHANGE':
            case 'PRIVILEGE_ESCALATION':
                score += 60
                break
            case 'DATA_ACCESS':
                score += 30
                break
            default:
                score += 10
        }
        
        // Adjust based on result
        if (auditParams.result == 'FAILURE' || auditParams.result == 'BLOCKED') {
            score += 20
        }
        
        // Adjust based on security level
        switch (auditParams.securityLevel) {
            case 'CRITICAL':
                score += 30
                break
            case 'HIGH':
                score += 20
                break
            case 'MEDIUM':
                score += 10
                break
        }
        
        // Cap at 100
        score = Math.min(score, 100)
        
        return score.toString()
    }
    
    /**
     * Check if data type is considered sensitive
     */
    private boolean isSensitiveData(String dataType) {
        Set<String> sensitiveTypes = [
            'CUSTOMER_PII', 'ACCOUNT_NUMBER', 'SSN', 'CREDIT_CARD',
            'FINANCIAL_DATA', 'TRANSACTION_DATA', 'LOAN_DATA'
        ]
        return sensitiveTypes.contains(dataType?.toUpperCase())
    }
    
    /**
     * Get authentication event description
     */
    private String getAuthenticationEventDescription(String eventType, boolean success) {
        switch (eventType) {
            case 'LOGIN_SUCCESS':
                return 'User successfully logged in'
            case 'LOGIN_FAILURE':
                return 'User login attempt failed'
            case 'LOGOUT':
                return 'User logged out'
            case 'PASSWORD_CHANGE':
                return 'User changed password'
            case 'PASSWORD_RESET':
                return 'User password was reset'
            case 'MFA_SUCCESS':
                return 'Multi-factor authentication successful'
            case 'MFA_FAILURE':
                return 'Multi-factor authentication failed'
            default:
                return "Authentication event: ${eventType}"
        }
    }
    
    /**
     * Handle high priority security events
     */
    private void handleHighPriorityEvent(SecurityAuditLog auditLog) {
        try {
            // Log to system log immediately
            log.warn("HIGH PRIORITY SECURITY EVENT: ${auditLog.eventType} - ${auditLog.eventDescription}")
            
            // TODO: Implement real-time alerting
            // - Send email to security team
            // - Send SMS alerts for critical events
            // - Integrate with SIEM systems
            // - Trigger automated response procedures
            
            // For now, just mark for review
            auditLog.requiresReview = true
            
        } catch (Exception e) {
            log.error("Error handling high priority security event", e)
        }
    }
    
    /**
     * Get security events requiring review
     */
    List<SecurityAuditLog> getEventsRequiringReview(int maxResults = 100) {
        return SecurityAuditLog.getEventsRequiringReview(maxResults)
    }
    
    /**
     * Get recent security events by type
     */
    List<SecurityAuditLog> getRecentEventsByType(String eventType, int hours = 24, int maxResults = 100) {
        Date since = new Date() - hours/24
        return SecurityAuditLog.createCriteria().list(max: maxResults) {
            eq('eventType', eventType)
            ge('timestamp', since)
            order('timestamp', 'desc')
        }
    }
    
    /**
     * Get security statistics
     */
    Map<String, Object> getSecurityStatistics(Date startDate = null, Date endDate = null) {
        if (!startDate) {
            startDate = new Date() - 30 // Last 30 days
        }
        if (!endDate) {
            endDate = new Date()
        }
        
        return SecurityAuditLog.getAuditStatistics(startDate, endDate)
    }
    
    /**
     * Generate security report
     */
    Map<String, Object> generateSecurityReport(Date startDate, Date endDate) {
        Map<String, Object> report = [:]
        
        // Basic statistics
        report.statistics = getSecurityStatistics(startDate, endDate)
        
        // Events requiring review
        report.eventsRequiringReview = getEventsRequiringReview(50)
        
        // Recent security violations
        report.securityViolations = getRecentEventsByType('SECURITY_VIOLATION', 24 * 7, 20) // Last week
        
        // Failed login attempts
        report.failedLogins = getRecentEventsByType('LOGIN_FAILURE', 24, 50) // Last 24 hours
        
        // Configuration changes
        report.configurationChanges = getRecentEventsByType('CONFIGURATION_CHANGE', 24 * 7, 20) // Last week
        
        return report
    }
}
