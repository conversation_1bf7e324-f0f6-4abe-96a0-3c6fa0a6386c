package org.icbs.workflow

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.common.CommonUtilityService
import org.icbs.security.SecurityAuditService

/**
 * Workflow Management Service
 * Handles business process workflows, state transitions,
 * and approval processes across the banking system
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class WorkflowManagementService {
    
    CommonUtilityService commonUtilityService
    SecurityAuditService securityAuditService
    
    // =====================================================
    // WORKFLOW STATE MANAGEMENT
    // =====================================================
    
    /**
     * Process workflow state transition
     */
    Map processStateTransition(String workflowType, Long entityId, String fromState, String toState, Map context = [:]) {
        Map result = [success: false, errors: [], newState: fromState]
        
        try {
            // 1. Validate transition
            Map validation = validateStateTransition(workflowType, fromState, toState, context)
            if (!validation.valid) {
                result.errors.addAll(validation.errors)
                return result
            }
            
            // 2. Execute pre-transition actions
            Map preActions = executePreTransitionActions(workflowType, entityId, fromState, toState, context)
            if (!preActions.success) {
                result.errors.addAll(preActions.errors)
                return result
            }
            
            // 3. Perform state transition
            Map transition = performStateTransition(workflowType, entityId, fromState, toState, context)
            if (!transition.success) {
                result.errors.addAll(transition.errors)
                return result
            }
            
            // 4. Execute post-transition actions
            Map postActions = executePostTransitionActions(workflowType, entityId, toState, context)
            if (!postActions.success) {
                log.warn("Post-transition actions failed but transition completed")
            }
            
            // 5. Audit the transition
            auditStateTransition(workflowType, entityId, fromState, toState, context)
            
            result.success = true
            result.newState = toState
            result.message = "State transition completed successfully"
            
        } catch (Exception e) {
            log.error("Error processing state transition", e)
            result.errors << "State transition failed: ${e.message}"
        }
        
        return result
    }
    
    /**
     * Get available transitions for current state
     */
    List<String> getAvailableTransitions(String workflowType, String currentState, Map context = [:]) {
        try {
            Map workflowDefinition = getWorkflowDefinition(workflowType)
            Map stateDefinition = workflowDefinition.states[currentState]
            
            if (!stateDefinition) {
                return []
            }
            
            List<String> availableTransitions = []
            
            stateDefinition.transitions?.each { transition ->
                if (evaluateTransitionConditions(transition, context)) {
                    availableTransitions << transition.toState
                }
            }
            
            return availableTransitions
            
        } catch (Exception e) {
            log.error("Error getting available transitions", e)
            return []
        }
    }
    
    /**
     * Check if transition is allowed
     */
    boolean isTransitionAllowed(String workflowType, String fromState, String toState, Map context = [:]) {
        try {
            List<String> availableTransitions = getAvailableTransitions(workflowType, fromState, context)
            return toState in availableTransitions
        } catch (Exception e) {
            log.error("Error checking transition allowance", e)
            return false
        }
    }
    
    // =====================================================
    // APPROVAL WORKFLOWS
    // =====================================================
    
    /**
     * Process approval workflow
     */
    Map processApproval(String approvalType, Long entityId, String action, Map approvalData) {
        Map result = [success: false, errors: [], approvalStatus: null]
        
        try {
            // 1. Validate approval request
            Map validation = validateApprovalRequest(approvalType, entityId, action, approvalData)
            if (!validation.valid) {
                result.errors.addAll(validation.errors)
                return result
            }
            
            // 2. Check approval authority
            Map authorityCheck = checkApprovalAuthority(approvalType, approvalData.approverId, approvalData.amount)
            if (!authorityCheck.authorized) {
                result.errors << authorityCheck.reason
                return result
            }
            
            // 3. Process the approval
            Map approval = executeApproval(approvalType, entityId, action, approvalData)
            if (!approval.success) {
                result.errors.addAll(approval.errors)
                return result
            }
            
            // 4. Update entity status
            Map statusUpdate = updateEntityApprovalStatus(approvalType, entityId, approval.status)
            if (!statusUpdate.success) {
                result.errors.addAll(statusUpdate.errors)
                return result
            }
            
            // 5. Trigger next workflow step if needed
            triggerNextWorkflowStep(approvalType, entityId, approval.status)
            
            result.success = true
            result.approvalStatus = approval.status
            result.message = "Approval processed successfully"
            
        } catch (Exception e) {
            log.error("Error processing approval", e)
            result.errors << "Approval processing failed: ${e.message}"
        }
        
        return result
    }
    
    /**
     * Get approval requirements for entity
     */
    Map getApprovalRequirements(String approvalType, Long entityId, BigDecimal amount = null) {
        Map result = [required: false, levels: [], reasons: []]
        
        try {
            Map approvalRules = getApprovalRules(approvalType)
            
            // Check if approval is required
            if (amount && approvalRules.amountThresholds) {
                approvalRules.amountThresholds.each { threshold ->
                    if (amount >= threshold.minAmount) {
                        result.required = true
                        result.levels << threshold.approvalLevel
                        result.reasons << "Amount exceeds ${threshold.minAmount} threshold"
                    }
                }
            }
            
            // Check entity-specific requirements
            Map entityRequirements = getEntityApprovalRequirements(approvalType, entityId)
            if (entityRequirements.required) {
                result.required = true
                result.levels.addAll(entityRequirements.levels)
                result.reasons.addAll(entityRequirements.reasons)
            }
            
        } catch (Exception e) {
            log.error("Error getting approval requirements", e)
            result.required = true
            result.reasons << "Error determining requirements - approval required"
        }
        
        return result
    }
    
    // =====================================================
    // WORKFLOW DEFINITIONS
    // =====================================================
    
    /**
     * Get workflow definition for type
     */
    private Map getWorkflowDefinition(String workflowType) {
        Map workflows = [
            'LOAN_APPLICATION': [
                states: [
                    'DRAFT': [
                        transitions: [
                            [toState: 'SUBMITTED', condition: 'hasRequiredDocuments']
                        ]
                    ],
                    'SUBMITTED': [
                        transitions: [
                            [toState: 'UNDER_REVIEW', condition: 'always'],
                            [toState: 'REJECTED', condition: 'hasRejectionReason']
                        ]
                    ],
                    'UNDER_REVIEW': [
                        transitions: [
                            [toState: 'APPROVED', condition: 'meetsApprovalCriteria'],
                            [toState: 'REJECTED', condition: 'hasRejectionReason'],
                            [toState: 'PENDING_DOCUMENTS', condition: 'needsAdditionalDocs']
                        ]
                    ],
                    'PENDING_DOCUMENTS': [
                        transitions: [
                            [toState: 'UNDER_REVIEW', condition: 'documentsReceived']
                        ]
                    ],
                    'APPROVED': [
                        transitions: [
                            [toState: 'DISBURSED', condition: 'disbursementCompleted']
                        ]
                    ],
                    'REJECTED': [
                        transitions: [] // Terminal state
                    ],
                    'DISBURSED': [
                        transitions: [] // Terminal state
                    ]
                ]
            ],
            'CUSTOMER_ONBOARDING': [
                states: [
                    'INITIATED': [
                        transitions: [
                            [toState: 'KYC_PENDING', condition: 'basicInfoComplete']
                        ]
                    ],
                    'KYC_PENDING': [
                        transitions: [
                            [toState: 'KYC_APPROVED', condition: 'kycDocumentsVerified'],
                            [toState: 'KYC_REJECTED', condition: 'kycDocumentsRejected']
                        ]
                    ],
                    'KYC_APPROVED': [
                        transitions: [
                            [toState: 'ACTIVE', condition: 'accountCreated']
                        ]
                    ],
                    'KYC_REJECTED': [
                        transitions: [
                            [toState: 'KYC_PENDING', condition: 'documentsResubmitted']
                        ]
                    ],
                    'ACTIVE': [
                        transitions: [] // Terminal state
                    ]
                ]
            ],
            'TRANSACTION_APPROVAL': [
                states: [
                    'PENDING': [
                        transitions: [
                            [toState: 'APPROVED', condition: 'hasApprovalAuthority'],
                            [toState: 'REJECTED', condition: 'hasRejectionReason'],
                            [toState: 'ESCALATED', condition: 'requiresHigherApproval']
                        ]
                    ],
                    'ESCALATED': [
                        transitions: [
                            [toState: 'APPROVED', condition: 'seniorApprovalReceived'],
                            [toState: 'REJECTED', condition: 'seniorRejectionReceived']
                        ]
                    ],
                    'APPROVED': [
                        transitions: [
                            [toState: 'COMPLETED', condition: 'transactionExecuted']
                        ]
                    ],
                    'REJECTED': [
                        transitions: [] // Terminal state
                    ],
                    'COMPLETED': [
                        transitions: [] // Terminal state
                    ]
                ]
            ]
        ]
        
        return workflows[workflowType] ?: [states: [:]]
    }
    
    /**
     * Get approval rules for type
     */
    private Map getApprovalRules(String approvalType) {
        Map rules = [
            'LOAN_APPLICATION': [
                amountThresholds: [
                    [minAmount: 50000, approvalLevel: 'MANAGER'],
                    [minAmount: 100000, approvalLevel: 'SENIOR_MANAGER'],
                    [minAmount: 500000, approvalLevel: 'DIRECTOR']
                ]
            ],
            'TRANSACTION': [
                amountThresholds: [
                    [minAmount: 10000, approvalLevel: 'SUPERVISOR'],
                    [minAmount: 50000, approvalLevel: 'MANAGER'],
                    [minAmount: 100000, approvalLevel: 'SENIOR_MANAGER']
                ]
            ],
            'CUSTOMER_LIMIT_CHANGE': [
                amountThresholds: [
                    [minAmount: 25000, approvalLevel: 'MANAGER'],
                    [minAmount: 100000, approvalLevel: 'SENIOR_MANAGER']
                ]
            ]
        ]
        
        return rules[approvalType] ?: [amountThresholds: []]
    }
    
    // =====================================================
    // HELPER METHODS
    // =====================================================
    
    private Map validateStateTransition(String workflowType, String fromState, String toState, Map context) {
        Map result = [valid: true, errors: []]
        
        if (!isTransitionAllowed(workflowType, fromState, toState, context)) {
            result.valid = false
            result.errors << "Transition from ${fromState} to ${toState} is not allowed"
        }
        
        return result
    }
    
    private boolean evaluateTransitionConditions(Map transition, Map context) {
        String condition = transition.condition
        
        switch (condition) {
            case 'always':
                return true
            case 'hasRequiredDocuments':
                return context.documentsComplete == true
            case 'meetsApprovalCriteria':
                return context.creditScore >= 650
            case 'hasRejectionReason':
                return context.rejectionReason != null
            case 'needsAdditionalDocs':
                return context.additionalDocsRequired == true
            case 'documentsReceived':
                return context.documentsReceived == true
            case 'disbursementCompleted':
                return context.disbursementCompleted == true
            case 'basicInfoComplete':
                return context.basicInfoComplete == true
            case 'kycDocumentsVerified':
                return context.kycVerified == true
            case 'kycDocumentsRejected':
                return context.kycRejected == true
            case 'documentsResubmitted':
                return context.documentsResubmitted == true
            case 'accountCreated':
                return context.accountCreated == true
            case 'hasApprovalAuthority':
                return context.hasApprovalAuthority == true
            case 'requiresHigherApproval':
                return context.requiresHigherApproval == true
            case 'seniorApprovalReceived':
                return context.seniorApprovalReceived == true
            case 'seniorRejectionReceived':
                return context.seniorRejectionReceived == true
            case 'transactionExecuted':
                return context.transactionExecuted == true
            default:
                return false
        }
    }
    
    private Map executePreTransitionActions(String workflowType, Long entityId, String fromState, String toState, Map context) {
        // Implementation for pre-transition actions
        return [success: true]
    }
    
    private Map performStateTransition(String workflowType, Long entityId, String fromState, String toState, Map context) {
        // Implementation for actual state transition
        return [success: true]
    }
    
    private Map executePostTransitionActions(String workflowType, Long entityId, String toState, Map context) {
        // Implementation for post-transition actions
        return [success: true]
    }
    
    private void auditStateTransition(String workflowType, Long entityId, String fromState, String toState, Map context) {
        securityAuditService.logSecurityEvent([
            eventType: 'WORKFLOW_STATE_TRANSITION',
            eventDescription: "Workflow state transition: ${fromState} -> ${toState}",
            workflowType: workflowType,
            entityId: entityId,
            fromState: fromState,
            toState: toState,
            result: 'SUCCESS'
        ])
    }
    
    private Map validateApprovalRequest(String approvalType, Long entityId, String action, Map approvalData) {
        // Implementation for approval validation
        return [valid: true, errors: []]
    }
    
    private Map checkApprovalAuthority(String approvalType, Long approverId, BigDecimal amount) {
        // Implementation for approval authority check
        return [authorized: true, reason: null]
    }
    
    private Map executeApproval(String approvalType, Long entityId, String action, Map approvalData) {
        // Implementation for approval execution
        return [success: true, status: 'APPROVED']
    }
    
    private Map updateEntityApprovalStatus(String approvalType, Long entityId, String status) {
        // Implementation for entity status update
        return [success: true]
    }
    
    private void triggerNextWorkflowStep(String approvalType, Long entityId, String status) {
        // Implementation for triggering next workflow step
        log.info("Next workflow step triggered for ${approvalType} entity ${entityId} with status ${status}")
    }
    
    private Map getEntityApprovalRequirements(String approvalType, Long entityId) {
        // Implementation for entity-specific approval requirements
        return [required: false, levels: [], reasons: []]
    }
}
