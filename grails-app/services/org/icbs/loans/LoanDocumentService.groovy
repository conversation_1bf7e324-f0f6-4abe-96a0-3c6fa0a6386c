package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.loans.Loan
import org.icbs.loans.LoanApplication
import org.icbs.loans.LoanDocument
import org.icbs.loans.LoanAgreement
import org.icbs.admin.DocumentType
import org.icbs.admin.UserMaster
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import org.springframework.web.multipart.MultipartFile

/**
 * REFACTORED: Loan Document Service
 * Extracted from LoanService.groovy (1,800+ lines)
 * Handles all loan document management operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanDocumentService {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def fileStorageService
    def documentGenerationService
    def auditLogService
    
    // =====================================================
    // DOCUMENT MANAGEMENT OPERATIONS
    // =====================================================
    
    /**
     * Upload loan document
     */
    Map uploadLoanDocument(Map documentData) {
        Map result = [success: false, errors: [], document: null, message: '']
        
        try {
            log.info("Uploading loan document for loan: ${documentData.loanId}")
            
            // 1. Validate document data
            Map validationResult = unifiedValidationService.validateLoanDocument(documentData)
            if (!validationResult.isValid) {
                result.errors = validationResult.errors
                return result
            }
            
            // 2. Get loan or application
            def loanEntity = getLoanEntity(documentData)
            if (!loanEntity) {
                result.errors << "Loan or application not found"
                return result
            }
            
            // 3. Process file upload
            Map uploadResult = processFileUpload(documentData.file, documentData)
            if (!uploadResult.success) {
                result.errors = uploadResult.errors
                return result
            }
            
            // 4. Create document record
            LoanDocument document = createDocumentRecord(loanEntity, documentData, uploadResult)
            
            // 5. Update document checklist
            updateDocumentChecklist(loanEntity, document)
            
            // 6. Audit logging
            auditDocumentUpload(loanEntity, document, 'SUCCESS')
            
            result.success = true
            result.document = document
            result.message = "Document uploaded successfully"
            
        } catch (Exception e) {
            log.error("Error uploading loan document", e)
            result.errors << "Error uploading document: ${e.message}"
        }
        
        return result
    }
    
    /**
     * Generate loan agreement
     */
    Map generateLoanAgreement(Long loanId, Map agreementData = [:]) {
        Map result = [success: false, errors: [], agreement: null, message: '']
        
        try {
            log.info("Generating loan agreement for loan: ${loanId}")
            
            Loan loan = Loan.get(loanId)
            if (!loan) {
                result.errors << "Loan not found"
                return result
            }
            
            // 1. Prepare agreement data
            Map templateData = prepareAgreementData(loan, agreementData)
            
            // 2. Generate agreement document
            Map generationResult = documentGenerationService.generateLoanAgreement(templateData)
            if (!generationResult.success) {
                result.errors = generationResult.errors
                return result
            }
            
            // 3. Create agreement record
            LoanAgreement agreement = createAgreementRecord(loan, generationResult, agreementData)
            
            // 4. Store generated document
            Map storageResult = storeGeneratedDocument(agreement, generationResult.documentContent)
            if (!storageResult.success) {
                result.errors = storageResult.errors
                return result
            }
            
            // 5. Update agreement status
            agreement.documentPath = storageResult.filePath
            agreement.save(flush: true, failOnError: true)
            
            // 6. Audit logging
            auditAgreementGeneration(loan, agreement, 'SUCCESS')
            
            result.success = true
            result.agreement = agreement
            result.message = "Loan agreement generated successfully"
            
        } catch (Exception e) {
            log.error("Error generating loan agreement", e)
            result.errors << "Error generating loan agreement: ${e.message}"
        }
        
        return result
    }
    
    /**
     * Get document checklist for loan
     */
    Map getDocumentChecklist(Long loanId, String entityType = 'LOAN') {
        Map checklist = [required: [], optional: [], submitted: []]
        
        try {
            def loanEntity = entityType == 'LOAN' ? Loan.get(loanId) : LoanApplication.get(loanId)
            if (!loanEntity) {
                checklist.error = "Loan entity not found"
                return checklist
            }
            
            // Get required documents for loan type/product
            checklist.required = getRequiredDocuments(loanEntity)
            
            // Get optional documents
            checklist.optional = getOptionalDocuments(loanEntity)
            
            // Get submitted documents
            checklist.submitted = getSubmittedDocuments(loanEntity)
            
            // Calculate completion status
            checklist.completionStatus = calculateCompletionStatus(checklist)
            
        } catch (Exception e) {
            log.error("Error getting document checklist", e)
            checklist.error = "Error getting document checklist"
        }
        
        return checklist
    }
    
    /**
     * Download loan document
     */
    Map downloadLoanDocument(Long documentId) {
        Map result = [success: false, errors: [], fileContent: null, fileName: '', contentType: '']
        
        try {
            LoanDocument document = LoanDocument.get(documentId)
            if (!document) {
                result.errors << "Document not found"
                return result
            }
            
            // Check access permissions
            Map accessResult = checkDocumentAccess(document)
            if (!accessResult.hasAccess) {
                result.errors << "Access denied to document"
                return result
            }
            
            // Retrieve file content
            Map fileResult = fileStorageService.retrieveFile(document.filePath)
            if (!fileResult.success) {
                result.errors = fileResult.errors
                return result
            }
            
            result.success = true
            result.fileContent = fileResult.content
            result.fileName = document.fileName
            result.contentType = document.contentType
            
            // Audit logging
            auditDocumentDownload(document, 'SUCCESS')
            
        } catch (Exception e) {
            log.error("Error downloading loan document", e)
            result.errors << "Error downloading document"
        }
        
        return result
    }
    
    /**
     * Delete loan document
     */
    Map deleteLoanDocument(Long documentId, String reason = '') {
        Map result = [success: false, errors: [], message: '']
        
        try {
            LoanDocument document = LoanDocument.get(documentId)
            if (!document) {
                result.errors << "Document not found"
                return result
            }
            
            // Check deletion permissions
            Map permissionResult = checkDeletionPermission(document)
            if (!permissionResult.canDelete) {
                result.errors << permissionResult.reason
                return result
            }
            
            // Delete physical file
            Map deleteResult = fileStorageService.deleteFile(document.filePath)
            if (!deleteResult.success) {
                log.warn("Failed to delete physical file: ${document.filePath}")
            }
            
            // Mark document as deleted (soft delete)
            document.isDeleted = true
            document.deletedDate = new Date()
            document.deletedBy = UserMaster.get(1) // Current user
            document.deletionReason = reason
            document.save(flush: true, failOnError: true)
            
            // Audit logging
            auditDocumentDeletion(document, reason, 'SUCCESS')
            
            result.success = true
            result.message = "Document deleted successfully"
            
        } catch (Exception e) {
            log.error("Error deleting loan document", e)
            result.errors << "Error deleting document"
        }
        
        return result
    }
    
    /**
     * Generate document reports
     */
    Map generateDocumentReport(Date fromDate, Date toDate, Map filters = [:]) {
        Map report = [:]
        
        try {
            log.info("Generating document report for period: ${fromDate} to ${toDate}")
            
            // Document upload statistics
            report.uploadStats = getDocumentUploadStats(fromDate, toDate, filters)
            
            // Document type breakdown
            report.typeBreakdown = getDocumentTypeBreakdown(fromDate, toDate, filters)
            
            // Compliance status
            report.complianceStatus = getDocumentComplianceStatus(filters)
            
            // Missing documents
            report.missingDocuments = getMissingDocuments(filters)
            
            // Document storage metrics
            report.storageMetrics = getDocumentStorageMetrics()
            
        } catch (Exception e) {
            log.error("Error generating document report", e)
            report.error = "Error generating document report"
        }
        
        return report
    }
    
    /**
     * Validate document completeness
     */
    Map validateDocumentCompleteness(Long loanId, String entityType = 'LOAN') {
        Map validation = [isComplete: true, missingDocuments: [], warnings: []]
        
        try {
            def loanEntity = entityType == 'LOAN' ? Loan.get(loanId) : LoanApplication.get(loanId)
            if (!loanEntity) {
                validation.isComplete = false
                validation.missingDocuments << "Loan entity not found"
                return validation
            }
            
            // Get document checklist
            Map checklist = getDocumentChecklist(loanId, entityType)
            
            // Check required documents
            checklist.required.each { requiredDoc ->
                boolean isSubmitted = checklist.submitted.any { 
                    it.documentType.id == requiredDoc.id 
                }
                
                if (!isSubmitted) {
                    validation.isComplete = false
                    validation.missingDocuments << requiredDoc.name
                }
            }
            
            // Check document validity
            checklist.submitted.each { submittedDoc ->
                if (isDocumentExpired(submittedDoc)) {
                    validation.warnings << "Document expired: ${submittedDoc.documentType.name}"
                }
            }
            
        } catch (Exception e) {
            log.error("Error validating document completeness", e)
            validation.isComplete = false
            validation.missingDocuments << "Error validating documents"
        }
        
        return validation
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Get loan entity (Loan or LoanApplication)
     */
    private def getLoanEntity(Map documentData) {
        if (documentData.loanId) {
            return Loan.get(documentData.loanId)
        } else if (documentData.applicationId) {
            return LoanApplication.get(documentData.applicationId)
        }
        return null
    }
    
    /**
     * Process file upload
     */
    private Map processFileUpload(MultipartFile file, Map documentData) {
        Map result = [success: false, errors: []]
        
        try {
            // Validate file
            Map fileValidation = validateUploadedFile(file)
            if (!fileValidation.isValid) {
                result.errors = fileValidation.errors
                return result
            }
            
            // Generate unique file name
            String fileName = generateUniqueFileName(file.originalFilename)
            
            // Store file
            Map storageResult = fileStorageService.storeFile(file, fileName, 'loan-documents')
            if (!storageResult.success) {
                result.errors = storageResult.errors
                return result
            }
            
            result.success = true
            result.fileName = fileName
            result.filePath = storageResult.filePath
            result.fileSize = file.size
            result.contentType = file.contentType
            
        } catch (Exception e) {
            log.error("Error processing file upload", e)
            result.errors << "Error processing file upload"
        }
        
        return result
    }
    
    /**
     * Create document record
     */
    private LoanDocument createDocumentRecord(def loanEntity, Map documentData, Map uploadResult) {
        LoanDocument document = new LoanDocument()
        
        if (loanEntity instanceof Loan) {
            document.loan = loanEntity
        } else if (loanEntity instanceof LoanApplication) {
            document.loanApplication = loanEntity
        }
        
        document.documentType = DocumentType.get(documentData.documentTypeId)
        document.fileName = uploadResult.fileName
        document.originalFileName = documentData.file.originalFilename
        document.filePath = uploadResult.filePath
        document.fileSize = uploadResult.fileSize
        document.contentType = uploadResult.contentType
        document.uploadDate = new Date()
        document.uploadedBy = UserMaster.get(documentData.uploadedBy ?: 1)
        document.description = documentData.description
        document.isRequired = documentData.isRequired ?: false
        document.expiryDate = documentData.expiryDate
        
        document.save(flush: true, failOnError: true)
        return document
    }
    
    /**
     * Update document checklist
     */
    private void updateDocumentChecklist(def loanEntity, LoanDocument document) {
        try {
            // Update completion status or flags as needed
            // This could trigger workflow events or status updates
            
        } catch (Exception e) {
            log.error("Error updating document checklist", e)
        }
    }
    
    /**
     * Prepare agreement data
     */
    private Map prepareAgreementData(Loan loan, Map agreementData) {
        Map templateData = [:]
        
        try {
            templateData.loan = loan
            templateData.customer = loan.customer
            templateData.product = loan.product
            templateData.branch = loan.branch
            templateData.agreementDate = new Date()
            templateData.loanAmount = loan.grantedAmount
            templateData.interestRate = loan.interestRate
            templateData.term = loan.term
            templateData.installmentAmount = loan.installmentAmount
            templateData.maturityDate = loan.maturityDate
            
            // Add custom agreement data
            templateData.putAll(agreementData)
            
        } catch (Exception e) {
            log.error("Error preparing agreement data", e)
        }
        
        return templateData
    }
    
    /**
     * Create agreement record
     */
    private LoanAgreement createAgreementRecord(Loan loan, Map generationResult, Map agreementData) {
        LoanAgreement agreement = new LoanAgreement(
            loan: loan,
            agreementNumber: generateAgreementNumber(loan),
            agreementDate: new Date(),
            templateVersion: generationResult.templateVersion,
            generatedBy: UserMaster.get(agreementData.generatedBy ?: 1),
            status: 'GENERATED'
        )
        
        agreement.save(flush: true, failOnError: true)
        return agreement
    }
    
    /**
     * Store generated document
     */
    private Map storeGeneratedDocument(LoanAgreement agreement, byte[] documentContent) {
        Map result = [success: false, errors: []]
        
        try {
            String fileName = "loan_agreement_${agreement.agreementNumber}.pdf"
            Map storageResult = fileStorageService.storeFileContent(
                documentContent, fileName, 'loan-agreements'
            )
            
            if (storageResult.success) {
                result.success = true
                result.filePath = storageResult.filePath
            } else {
                result.errors = storageResult.errors
            }
            
        } catch (Exception e) {
            log.error("Error storing generated document", e)
            result.errors << "Error storing generated document"
        }
        
        return result
    }
    
    /**
     * Get required documents for loan
     */
    private List getRequiredDocuments(def loanEntity) {
        try {
            // This would be based on loan product, amount, type, etc.
            return DocumentType.createCriteria().list {
                eq("isRequired", true)
                eq("entityType", loanEntity instanceof Loan ? "LOAN" : "APPLICATION")
                eq("isActive", true)
            }
        } catch (Exception e) {
            log.error("Error getting required documents", e)
            return []
        }
    }
    
    /**
     * Get optional documents for loan
     */
    private List getOptionalDocuments(def loanEntity) {
        try {
            return DocumentType.createCriteria().list {
                eq("isRequired", false)
                eq("entityType", loanEntity instanceof Loan ? "LOAN" : "APPLICATION")
                eq("isActive", true)
            }
        } catch (Exception e) {
            log.error("Error getting optional documents", e)
            return []
        }
    }
    
    /**
     * Get submitted documents for loan
     */
    private List getSubmittedDocuments(def loanEntity) {
        try {
            return LoanDocument.createCriteria().list {
                if (loanEntity instanceof Loan) {
                    eq("loan", loanEntity)
                } else {
                    eq("loanApplication", loanEntity)
                }
                eq("isDeleted", false)
                order("uploadDate", "desc")
            }
        } catch (Exception e) {
            log.error("Error getting submitted documents", e)
            return []
        }
    }
    
    /**
     * Calculate completion status
     */
    private Map calculateCompletionStatus(Map checklist) {
        Map status = [:]
        
        try {
            Integer requiredCount = checklist.required.size()
            Integer submittedRequiredCount = 0
            
            checklist.required.each { requiredDoc ->
                boolean isSubmitted = checklist.submitted.any { 
                    it.documentType.id == requiredDoc.id 
                }
                if (isSubmitted) {
                    submittedRequiredCount++
                }
            }
            
            status.totalRequired = requiredCount
            status.submittedRequired = submittedRequiredCount
            status.completionPercentage = requiredCount > 0 ? 
                (submittedRequiredCount / requiredCount * 100).setScale(2) : 100
            status.isComplete = submittedRequiredCount == requiredCount
            
        } catch (Exception e) {
            log.error("Error calculating completion status", e)
            status.error = "Error calculating completion status"
        }
        
        return status
    }
    
    /**
     * Validate uploaded file
     */
    private Map validateUploadedFile(MultipartFile file) {
        Map validation = [isValid: true, errors: []]
        
        try {
            if (!file || file.empty) {
                validation.isValid = false
                validation.errors << "File is required"
                return validation
            }
            
            // Check file size (10MB limit)
            if (file.size > 10 * 1024 * 1024) {
                validation.isValid = false
                validation.errors << "File size exceeds 10MB limit"
            }
            
            // Check file type
            List allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/gif']
            if (!allowedTypes.contains(file.contentType)) {
                validation.isValid = false
                validation.errors << "File type not allowed"
            }
            
        } catch (Exception e) {
            log.error("Error validating uploaded file", e)
            validation.isValid = false
            validation.errors << "Error validating file"
        }
        
        return validation
    }
    
    /**
     * Generate unique file name
     */
    private String generateUniqueFileName(String originalFileName) {
        try {
            String extension = originalFileName.substring(originalFileName.lastIndexOf('.'))
            String timestamp = new Date().format('yyyyMMdd_HHmmss')
            String randomString = UUID.randomUUID().toString().substring(0, 8)
            return "loan_doc_${timestamp}_${randomString}${extension}"
        } catch (Exception e) {
            log.error("Error generating unique file name", e)
            return "loan_doc_${System.currentTimeMillis()}.pdf"
        }
    }
    
    /**
     * Generate agreement number
     */
    private String generateAgreementNumber(Loan loan) {
        try {
            String branchCode = loan.branch.code
            String year = new Date().format('yyyy')
            String sequence = String.format('%06d', loan.id)
            return "AGR-${branchCode}-${year}-${sequence}"
        } catch (Exception e) {
            log.error("Error generating agreement number", e)
            return "AGR-${System.currentTimeMillis()}"
        }
    }
    
    /**
     * Check document access permissions
     */
    private Map checkDocumentAccess(LoanDocument document) {
        Map access = [hasAccess: true, reason: '']
        
        try {
            // Implement access control logic
            // For now, allowing all access
            
        } catch (Exception e) {
            log.error("Error checking document access", e)
            access.hasAccess = false
            access.reason = "Error checking access permissions"
        }
        
        return access
    }
    
    /**
     * Check deletion permission
     */
    private Map checkDeletionPermission(LoanDocument document) {
        Map permission = [canDelete: true, reason: '']
        
        try {
            // Check if document is required and loan is active
            if (document.isRequired && document.loan?.status?.id in [2, 3]) {
                permission.canDelete = false
                permission.reason = "Cannot delete required document for active loan"
            }
            
        } catch (Exception e) {
            log.error("Error checking deletion permission", e)
            permission.canDelete = false
            permission.reason = "Error checking deletion permission"
        }
        
        return permission
    }
    
    /**
     * Check if document is expired
     */
    private boolean isDocumentExpired(LoanDocument document) {
        try {
            return document.expiryDate && document.expiryDate < new Date()
        } catch (Exception e) {
            log.error("Error checking document expiry", e)
            return false
        }
    }
    
    // Report helper methods
    private Map getDocumentUploadStats(Date fromDate, Date toDate, Map filters) { return [:] }
    private Map getDocumentTypeBreakdown(Date fromDate, Date toDate, Map filters) { return [:] }
    private Map getDocumentComplianceStatus(Map filters) { return [:] }
    private List getMissingDocuments(Map filters) { return [] }
    private Map getDocumentStorageMetrics() { return [:] }
    
    // Audit methods
    private void auditDocumentUpload(def loanEntity, LoanDocument document, String result) {
        try {
            auditLogService.insert('130', 'LDS07000', 
                "Document uploaded - Type: ${document.documentType?.name}, Result: ${result}", 
                'LoanDocumentService', null, null, null, loanEntity.id)
        } catch (Exception e) {
            log.error("Error auditing document upload", e)
        }
    }
    
    private void auditAgreementGeneration(Loan loan, LoanAgreement agreement, String result) {
        try {
            auditLogService.insert('130', 'LDS07100', 
                "Loan agreement generated - Number: ${agreement.agreementNumber}, Result: ${result}", 
                'LoanDocumentService', null, null, null, loan.id)
        } catch (Exception e) {
            log.error("Error auditing agreement generation", e)
        }
    }
    
    private void auditDocumentDownload(LoanDocument document, String result) {
        try {
            auditLogService.insert('130', 'LDS07200', 
                "Document downloaded - File: ${document.fileName}, Result: ${result}", 
                'LoanDocumentService', null, null, null, document.id)
        } catch (Exception e) {
            log.error("Error auditing document download", e)
        }
    }
    
    private void auditDocumentDeletion(LoanDocument document, String reason, String result) {
        try {
            auditLogService.insert('130', 'LDS07300', 
                "Document deleted - File: ${document.fileName}, Reason: ${reason}, Result: ${result}", 
                'LoanDocumentService', null, null, null, document.id)
        } catch (Exception e) {
            log.error("Error auditing document deletion", e)
        }
    }
}
