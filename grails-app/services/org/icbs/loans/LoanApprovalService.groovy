package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.loans.Loan
import org.icbs.loans.LoanApplication
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.ConfigItemStatus
import org.icbs.admin.UserMaster
import org.icbs.cif.Customer
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import org.icbs.workflow.WorkflowManagementService

/**
 * REFACTORED: Loan Approval Service
 * Extracted from LoanService.groovy (1,800+ lines)
 * Handles loan approval processing with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanApprovalService {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    WorkflowManagementService workflowManagementService
    LoanCalculationService loanCalculationService
    LoanValidationService loanValidationService
    def auditLogService
    
    // =====================================================
    // LOAN APPROVAL PROCESSING
    // =====================================================
    
    /**
     * Process loan approval decision
     */
    Map processLoanApproval(Map approvalData) {
        Map result = [success: false, errors: [], loan: null, message: '']
        
        try {
            log.info("Processing loan approval for application: ${approvalData.applicationId}")
            
            // 1. Validate approval data
            Map validationResult = unifiedValidationService.validateLoanApproval(approvalData)
            if (!validationResult.isValid) {
                result.errors = validationResult.errors
                return result
            }
            
            // 2. Get loan application
            LoanApplication application = LoanApplication.get(approvalData.applicationId)
            if (!application) {
                result.errors << "Loan application not found"
                return result
            }
            
            // 3. Validate approval authority
            Map authorityResult = validateApprovalAuthority(approvalData.approverId, approvalData.approvedAmount)
            if (!authorityResult.isValid) {
                result.errors = authorityResult.errors
                return result
            }
            
            // 4. Process approval decision
            Map approvalResult = processApprovalDecision(application, approvalData)
            
            if (approvalResult.success) {
                // 5. Create loan account if approved
                if (approvalData.decision == 'APPROVED') {
                    Map loanCreationResult = createApprovedLoanAccount(application, approvalData)
                    if (loanCreationResult.success) {
                        result.loan = loanCreationResult.loan
                    } else {
                        result.errors.addAll(loanCreationResult.errors)
                        return result
                    }
                }
                
                // 6. Update workflow status
                Map workflowResult = workflowManagementService.processStateTransition(
                    'LOAN_APPLICATION',
                    application.id,
                    'UNDER_REVIEW',
                    getNextWorkflowState(approvalData.decision),
                    [approvalDecision: approvalData.decision]
                )
                
                // 7. Generate approval documents
                generateApprovalDocuments(application, approvalData)
                
                // 8. Audit logging
                auditLoanApproval(application, approvalData, 'SUCCESS')
                
                result.success = true
                result.message = "Loan approval processed successfully"
                
            } else {
                result.errors.addAll(approvalResult.errors)
            }
            
        } catch (Exception e) {
            log.error("Error processing loan approval", e)
            result.errors << "Error processing loan approval: ${e.message}"
        }
        
        return result
    }
    
    /**
     * Validate loan for approval readiness
     */
    Map validateLoanForApproval(Long applicationId) {
        Map result = [isValid: true, errors: [], warnings: []]
        
        try {
            LoanApplication application = LoanApplication.get(applicationId)
            if (!application) {
                result.isValid = false
                result.errors << "Loan application not found"
                return result
            }
            
            // Check application status
            if (application.status?.id != 1) { // Not in submitted status
                result.isValid = false
                result.errors << "Application is not in submitted status"
            }
            
            // Check required documents
            Map documentCheck = validateRequiredDocuments(application)
            if (!documentCheck.isComplete) {
                result.warnings.addAll(documentCheck.missingDocuments)
            }
            
            // Check credit assessment
            Map creditCheck = validateCreditAssessment(application)
            if (!creditCheck.isValid) {
                result.errors.addAll(creditCheck.errors)
                result.isValid = false
            }
            
            // Check collateral requirements
            Map collateralCheck = validateCollateralRequirements(application)
            if (!collateralCheck.isValid) {
                result.warnings.addAll(collateralCheck.warnings)
            }
            
        } catch (Exception e) {
            log.error("Error validating loan for approval", e)
            result.isValid = false
            result.errors << "Error validating loan for approval"
        }
        
        return result
    }
    
    /**
     * Get approval recommendations
     */
    Map getApprovalRecommendations(Long applicationId) {
        Map result = [recommendations: [], riskAssessment: [:], suggestedTerms: [:]]
        
        try {
            LoanApplication application = LoanApplication.get(applicationId)
            if (!application) {
                return result
            }
            
            // Risk assessment
            result.riskAssessment = performRiskAssessment(application)
            
            // Suggested terms
            result.suggestedTerms = calculateSuggestedTerms(application)
            
            // Recommendations based on risk profile
            result.recommendations = generateRecommendations(application, result.riskAssessment)
            
        } catch (Exception e) {
            log.error("Error getting approval recommendations", e)
        }
        
        return result
    }
    
    /**
     * Update loan status after approval
     */
    Map updateLoanStatus(Long loanId, LoanAcctStatus newStatus, String comments = '') {
        Map result = [success: false, message: '']
        
        try {
            Loan loan = Loan.get(loanId)
            if (!loan) {
                result.message = 'Loan not found'
                return result
            }
            
            LoanAcctStatus previousStatus = loan.status
            loan.status = newStatus
            
            if (newStatus.id == 6) { // Closed status
                loan.statusChangedDate = new Date()
            }
            
            loan.save(flush: true, failOnError: true)
            
            // Audit logging
            auditLogService.insert('130', 'LAP02100', 
                "Loan status updated from ${previousStatus.description} to ${newStatus.description} - ${comments}", 
                'LoanApprovalService', null, null, null, loan.id)
            
            result.success = true
            result.message = 'Loan status updated successfully'
            
        } catch (Exception e) {
            log.error("Error updating loan status", e)
            result.message = 'Error updating loan status'
        }
        
        return result
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Validate approval authority
     */
    private Map validateApprovalAuthority(Long approverId, BigDecimal approvedAmount) {
        Map result = [isValid: true, errors: []]
        
        try {
            UserMaster approver = UserMaster.get(approverId)
            if (!approver) {
                result.isValid = false
                result.errors << "Approver not found"
                return result
            }
            
            // Check approval limits based on user role/position
            BigDecimal approvalLimit = getApprovalLimit(approver)
            if (approvedAmount > approvalLimit) {
                result.isValid = false
                result.errors << "Approved amount exceeds user's approval limit"
            }
            
        } catch (Exception e) {
            log.error("Error validating approval authority", e)
            result.isValid = false
            result.errors << "Error validating approval authority"
        }
        
        return result
    }
    
    /**
     * Process approval decision
     */
    private Map processApprovalDecision(LoanApplication application, Map approvalData) {
        Map result = [success: false, errors: []]
        
        try {
            // Update application with approval details
            application.approvedAmount = approvalData.approvedAmount
            application.approvedTenure = approvalData.approvedTenure
            application.approvedInterestRate = approvalData.approvedInterestRate
            application.approvalComments = approvalData.comments
            application.approvedBy = UserMaster.get(approvalData.approverId)
            application.approvalDate = new Date()
            
            // Set status based on decision
            switch (approvalData.decision) {
                case 'APPROVED':
                    application.status = ConfigItemStatus.get(2) // Approved
                    break
                case 'REJECTED':
                    application.status = ConfigItemStatus.get(4) // Rejected
                    break
                case 'CONDITIONAL':
                    application.status = ConfigItemStatus.get(5) // Conditional
                    break
                default:
                    result.errors << "Invalid approval decision"
                    return result
            }
            
            application.save(flush: true, failOnError: true)
            result.success = true
            
        } catch (Exception e) {
            log.error("Error processing approval decision", e)
            result.errors << "Error processing approval decision"
        }
        
        return result
    }
    
    /**
     * Create approved loan account
     */
    private Map createApprovedLoanAccount(LoanApplication application, Map approvalData) {
        Map result = [success: false, errors: [], loan: null]
        
        try {
            // Create loan account from approved application
            Loan loan = new Loan()
            loan.loanApplication = application
            loan.customer = application.customer
            loan.product = application.product
            loan.branch = application.branch
            loan.grantedAmount = approvalData.approvedAmount
            loan.interestRate = approvalData.approvedInterestRate
            loan.term = approvalData.approvedTenure
            loan.status = LoanAcctStatus.get(2) // Approved
            loan.openingDate = new Date()
            loan.currency = application.product.currency
            
            // Initialize loan balances
            loan.balanceAmount = 0
            loan.totalDisbursementAmount = 0
            loan.transactionSequenceNo = 0
            loan.normalInterestAmount = 0
            loan.interestBalanceAmount = 0
            loan.penaltyBalanceAmount = 0
            loan.serviceChargeBalanceAmount = 0
            loan.taxBalanceAmount = 0
            loan.accruedInterestAmount = 0
            
            loan.save(flush: true, failOnError: true)
            
            result.success = true
            result.loan = loan
            
        } catch (Exception e) {
            log.error("Error creating approved loan account", e)
            result.errors << "Error creating loan account"
        }
        
        return result
    }
    
    /**
     * Validate required documents
     */
    private Map validateRequiredDocuments(LoanApplication application) {
        Map result = [isComplete: true, missingDocuments: []]
        
        try {
            // Check for required documents based on loan type and amount
            List<String> requiredDocs = getRequiredDocuments(application)
            List<String> submittedDocs = getSubmittedDocuments(application)
            
            requiredDocs.each { doc ->
                if (!submittedDocs.contains(doc)) {
                    result.isComplete = false
                    result.missingDocuments << doc
                }
            }
            
        } catch (Exception e) {
            log.error("Error validating required documents", e)
            result.isComplete = false
        }
        
        return result
    }
    
    /**
     * Validate credit assessment
     */
    private Map validateCreditAssessment(LoanApplication application) {
        Map result = [isValid: true, errors: []]
        
        try {
            // Check if credit assessment is complete
            if (!application.creditScore) {
                result.isValid = false
                result.errors << "Credit assessment not completed"
            }
            
            // Check minimum credit score requirements
            if (application.creditScore < getMinimumCreditScore(application.product)) {
                result.isValid = false
                result.errors << "Credit score below minimum requirement"
            }
            
        } catch (Exception e) {
            log.error("Error validating credit assessment", e)
            result.isValid = false
            result.errors << "Error validating credit assessment"
        }
        
        return result
    }
    
    /**
     * Validate collateral requirements
     */
    private Map validateCollateralRequirements(LoanApplication application) {
        Map result = [isValid: true, warnings: []]
        
        try {
            BigDecimal loanAmount = application.requestedAmount
            BigDecimal collateralValue = calculateCollateralValue(application)
            
            // Check loan-to-value ratio
            if (collateralValue > 0) {
                BigDecimal ltvRatio = loanAmount / collateralValue
                BigDecimal maxLtv = getMaximumLtvRatio(application.product)
                
                if (ltvRatio > maxLtv) {
                    result.warnings << "Loan-to-value ratio exceeds maximum allowed"
                }
            } else if (loanAmount > getUnsecuredLoanLimit(application.product)) {
                result.warnings << "Loan amount exceeds unsecured loan limit"
            }
            
        } catch (Exception e) {
            log.error("Error validating collateral requirements", e)
            result.warnings << "Error validating collateral requirements"
        }
        
        return result
    }
    
    /**
     * Perform risk assessment
     */
    private Map performRiskAssessment(LoanApplication application) {
        Map assessment = [:]
        
        try {
            // Credit risk factors
            assessment.creditScore = application.creditScore ?: 0
            assessment.debtToIncomeRatio = calculateDebtToIncomeRatio(application)
            assessment.employmentStability = assessEmploymentStability(application)
            assessment.collateralCoverage = calculateCollateralCoverage(application)
            
            // Overall risk rating
            assessment.overallRisk = calculateOverallRisk(assessment)
            
        } catch (Exception e) {
            log.error("Error performing risk assessment", e)
            assessment.overallRisk = 'HIGH'
        }
        
        return assessment
    }
    
    /**
     * Calculate suggested terms
     */
    private Map calculateSuggestedTerms(LoanApplication application) {
        Map terms = [:]
        
        try {
            // Suggested interest rate based on risk profile
            terms.suggestedInterestRate = calculateSuggestedInterestRate(application)
            
            // Suggested loan amount
            terms.suggestedAmount = calculateSuggestedAmount(application)
            
            // Suggested tenure
            terms.suggestedTenure = calculateSuggestedTenure(application)
            
        } catch (Exception e) {
            log.error("Error calculating suggested terms", e)
        }
        
        return terms
    }
    
    /**
     * Generate recommendations
     */
    private List<String> generateRecommendations(LoanApplication application, Map riskAssessment) {
        List<String> recommendations = []
        
        try {
            String riskLevel = riskAssessment.overallRisk
            
            switch (riskLevel) {
                case 'LOW':
                    recommendations << "Recommend approval with standard terms"
                    recommendations << "Consider offering preferential interest rate"
                    break
                case 'MEDIUM':
                    recommendations << "Recommend approval with standard terms"
                    recommendations << "Consider additional documentation"
                    break
                case 'HIGH':
                    recommendations << "Recommend additional review"
                    recommendations << "Consider requiring additional collateral"
                    recommendations << "Consider reducing loan amount"
                    break
            }
            
        } catch (Exception e) {
            log.error("Error generating recommendations", e)
        }
        
        return recommendations
    }
    
    /**
     * Get approval limit for user
     */
    private BigDecimal getApprovalLimit(UserMaster user) {
        // This would be based on user role/position
        // For now, returning a default limit
        return new BigDecimal("1000000")
    }
    
    /**
     * Get next workflow state based on decision
     */
    private String getNextWorkflowState(String decision) {
        switch (decision) {
            case 'APPROVED':
                return 'APPROVED'
            case 'REJECTED':
                return 'REJECTED'
            case 'CONDITIONAL':
                return 'CONDITIONAL_APPROVAL'
            default:
                return 'UNDER_REVIEW'
        }
    }
    
    /**
     * Generate approval documents
     */
    private void generateApprovalDocuments(LoanApplication application, Map approvalData) {
        try {
            // Generate approval letter, loan agreement, etc.
            log.info("Generating approval documents for application: ${application.id}")
        } catch (Exception e) {
            log.error("Error generating approval documents", e)
        }
    }
    
    /**
     * Audit loan approval
     */
    private void auditLoanApproval(LoanApplication application, Map approvalData, String result) {
        try {
            auditLogService.insert('130', 'LAP02000', 
                "Loan approval processed - Decision: ${approvalData.decision}, Amount: ${approvalData.approvedAmount}", 
                'LoanApprovalService', null, null, null, application.id)
        } catch (Exception e) {
            log.error("Error auditing loan approval", e)
        }
    }
    
    // Additional helper methods would be implemented here...
    private List<String> getRequiredDocuments(LoanApplication application) { return [] }
    private List<String> getSubmittedDocuments(LoanApplication application) { return [] }
    private Integer getMinimumCreditScore(def product) { return 600 }
    private BigDecimal calculateCollateralValue(LoanApplication application) { return 0 }
    private BigDecimal getMaximumLtvRatio(def product) { return 0.8 }
    private BigDecimal getUnsecuredLoanLimit(def product) { return 100000 }
    private BigDecimal calculateDebtToIncomeRatio(LoanApplication application) { return 0.3 }
    private String assessEmploymentStability(LoanApplication application) { return 'STABLE' }
    private BigDecimal calculateCollateralCoverage(LoanApplication application) { return 1.2 }
    private String calculateOverallRisk(Map assessment) { return 'MEDIUM' }
    private BigDecimal calculateSuggestedInterestRate(LoanApplication application) { return 12.0 }
    private BigDecimal calculateSuggestedAmount(LoanApplication application) { return application.requestedAmount }
    private Integer calculateSuggestedTenure(LoanApplication application) { return application.requestedTenure }
}
