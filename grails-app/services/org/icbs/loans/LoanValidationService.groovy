package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.loans.Loan
import org.icbs.loans.LoanApplication
import org.icbs.cif.Customer
import org.icbs.admin.Product
import org.icbs.lov.LoanAcctStatus
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import java.math.BigDecimal

/**
 * REFACTORED: Loan Validation Service
 * Extracted from LoanService.groovy (1,800+ lines)
 * Handles all loan validation operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanValidationService {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def auditLogService
    
    // =====================================================
    // LOAN VALIDATION OPERATIONS
    // =====================================================
    
    /**
     * Validate loan application
     */
    Map validateLoanApplication(LoanApplication application) {
        Map result = [isValid: true, errors: [], warnings: []]
        
        try {
            log.debug("Validating loan application: ${application.id}")
            
            // Basic field validations
            result = validateBasicFields(application, result)
            
            // Customer validations
            result = validateCustomer(application.customer, result)
            
            // Product validations
            result = validateProduct(application.product, result)
            
            // Amount validations
            result = validateLoanAmount(application, result)
            
            // Term validations
            result = validateLoanTerm(application, result)
            
            // Interest rate validations
            result = validateInterestRate(application, result)
            
            // Collateral validations
            result = validateCollateral(application, result)
            
            // Business rule validations
            result = validateBusinessRules(application, result)
            
        } catch (Exception e) {
            log.error("Error validating loan application", e)
            result.isValid = false
            result.errors << "Error during validation process"
        }
        
        return result
    }
    
    /**
     * Validate loan account
     */
    Map validateLoanAccount(Loan loan) {
        Map result = [isValid: true, errors: [], warnings: []]
        
        try {
            log.debug("Validating loan account: ${loan.accountNo}")
            
            // Account number validation
            if (!loan.accountNo) {
                result.isValid = false
                result.errors << "Account number is required"
            }
            
            // Status validation
            if (!loan.status) {
                result.isValid = false
                result.errors << "Loan status is required"
            }
            
            // Balance validations
            result = validateLoanBalances(loan, result)
            
            // Date validations
            result = validateLoanDates(loan, result)
            
            // Interest scheme validation
            if (!loan.interestIncomeScheme) {
                result.isValid = false
                result.errors << "Interest income scheme is required"
            }
            
        } catch (Exception e) {
            log.error("Error validating loan account", e)
            result.isValid = false
            result.errors << "Error during loan account validation"
        }
        
        return result
    }
    
    /**
     * Validate loan payment
     */
    Map validateLoanPayment(Loan loan, BigDecimal paymentAmount, String paymentType) {
        Map result = [isValid: true, errors: [], warnings: []]
        
        try {
            log.debug("Validating loan payment: ${loan.accountNo}, Amount: ${paymentAmount}")
            
            // Basic validations
            if (!loan) {
                result.isValid = false
                result.errors << "Loan account is required"
                return result
            }
            
            if (!paymentAmount || paymentAmount <= 0) {
                result.isValid = false
                result.errors << "Valid payment amount is required"
                return result
            }
            
            // Loan status validation
            if (loan.status?.id in [6, 7, 8]) { // Closed, Cancelled, Written-off
                result.isValid = false
                result.errors << "Cannot make payment to ${loan.status.description} loan"
                return result
            }
            
            // Payment amount validation
            BigDecimal totalOutstanding = calculateTotalOutstanding(loan)
            if (paymentAmount > totalOutstanding) {
                result.warnings << "Payment amount exceeds total outstanding balance"
            }
            
            // Minimum payment validation
            BigDecimal minimumPayment = calculateMinimumPayment(loan)
            if (paymentAmount < minimumPayment) {
                result.warnings << "Payment amount is below minimum required payment"
            }
            
            // Payment type validation
            if (!paymentType || !['CASH', 'CHECK', 'TRANSFER'].contains(paymentType)) {
                result.isValid = false
                result.errors << "Valid payment type is required"
            }
            
        } catch (Exception e) {
            log.error("Error validating loan payment", e)
            result.isValid = false
            result.errors << "Error during payment validation"
        }
        
        return result
    }
    
    /**
     * Validate loan disbursement
     */
    Map validateLoanDisbursement(Loan loan, BigDecimal disbursementAmount) {
        Map result = [isValid: true, errors: [], warnings: []]
        
        try {
            log.debug("Validating loan disbursement: ${loan.accountNo}, Amount: ${disbursementAmount}")
            
            // Basic validations
            if (!loan) {
                result.isValid = false
                result.errors << "Loan account is required"
                return result
            }
            
            if (!disbursementAmount || disbursementAmount <= 0) {
                result.isValid = false
                result.errors << "Valid disbursement amount is required"
                return result
            }
            
            // Loan status validation
            if (loan.status?.id != 2) { // Not approved
                result.isValid = false
                result.errors << "Loan must be in approved status for disbursement"
                return result
            }
            
            // Disbursement amount validation
            BigDecimal remainingAmount = loan.grantedAmount - loan.totalDisbursementAmount
            if (disbursementAmount > remainingAmount) {
                result.isValid = false
                result.errors << "Disbursement amount exceeds remaining loan balance"
            }
            
            // Minimum disbursement validation
            BigDecimal minimumDisbursement = getMinimumDisbursementAmount(loan)
            if (disbursementAmount < minimumDisbursement) {
                result.warnings << "Disbursement amount is below recommended minimum"
            }
            
        } catch (Exception e) {
            log.error("Error validating loan disbursement", e)
            result.isValid = false
            result.errors << "Error during disbursement validation"
        }
        
        return result
    }
    
    /**
     * Validate loan closure
     */
    Map validateLoanClosure(Loan loan) {
        Map result = [isValid: true, errors: [], warnings: []]
        
        try {
            log.debug("Validating loan closure: ${loan.accountNo}")
            
            if (!loan) {
                result.isValid = false
                result.errors << "Loan account is required"
                return result
            }
            
            // Check outstanding balances
            BigDecimal totalOutstanding = calculateTotalOutstanding(loan)
            if (totalOutstanding > 0) {
                result.isValid = false
                result.errors << "Cannot close loan with outstanding balance"
            }
            
            // Check loan status
            if (loan.status?.id in [6, 7, 8]) { // Already closed, cancelled, or written-off
                result.isValid = false
                result.errors << "Loan is already ${loan.status.description}"
            }
            
            // Check for pending transactions
            if (hasPendingTransactions(loan)) {
                result.warnings << "Loan has pending transactions"
            }
            
        } catch (Exception e) {
            log.error("Error validating loan closure", e)
            result.isValid = false
            result.errors << "Error during closure validation"
        }
        
        return result
    }
    
    /**
     * Validate credit limit
     */
    Map validateCreditLimit(Customer customer, BigDecimal requestedAmount) {
        Map result = [isValid: true, errors: [], warnings: []]
        
        try {
            log.debug("Validating credit limit for customer: ${customer.customerId}")
            
            // Get customer's existing loans
            List<Loan> existingLoans = getActiveLoans(customer)
            BigDecimal totalExistingExposure = existingLoans.sum { it.balanceAmount } ?: 0
            
            // Calculate total exposure
            BigDecimal totalExposure = totalExistingExposure + requestedAmount
            
            // Get customer credit limit
            BigDecimal creditLimit = getCreditLimit(customer)
            
            if (totalExposure > creditLimit) {
                result.isValid = false
                result.errors << "Total exposure exceeds customer credit limit"
            }
            
            // Check concentration limits
            if (requestedAmount > (creditLimit * 0.8)) {
                result.warnings << "Single loan amount exceeds 80% of credit limit"
            }
            
        } catch (Exception e) {
            log.error("Error validating credit limit", e)
            result.isValid = false
            result.errors << "Error during credit limit validation"
        }
        
        return result
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Validate basic fields
     */
    private Map validateBasicFields(LoanApplication application, Map result) {
        if (!application.requestedAmount || application.requestedAmount <= 0) {
            result.isValid = false
            result.errors << "Valid loan amount is required"
        }
        
        if (!application.requestedTenure || application.requestedTenure <= 0) {
            result.isValid = false
            result.errors << "Valid loan tenure is required"
        }
        
        if (!application.purpose) {
            result.isValid = false
            result.errors << "Loan purpose is required"
        }
        
        return result
    }
    
    /**
     * Validate customer
     */
    private Map validateCustomer(Customer customer, Map result) {
        if (!customer) {
            result.isValid = false
            result.errors << "Customer is required"
            return result
        }
        
        // Check customer status
        if (customer.status?.id != 2) { // Not active
            result.isValid = false
            result.errors << "Customer must be in active status"
        }
        
        // Check customer type
        if (!customer.customerType) {
            result.warnings << "Customer type not specified"
        }
        
        // Check KYC compliance
        if (!isKycCompliant(customer)) {
            result.isValid = false
            result.errors << "Customer KYC is not compliant"
        }
        
        return result
    }
    
    /**
     * Validate product
     */
    private Map validateProduct(Product product, Map result) {
        if (!product) {
            result.isValid = false
            result.errors << "Loan product is required"
            return result
        }
        
        // Check product status
        if (product.status?.id != 2) { // Not active
            result.isValid = false
            result.errors << "Loan product is not active"
        }
        
        return result
    }
    
    /**
     * Validate loan amount
     */
    private Map validateLoanAmount(LoanApplication application, Map result) {
        if (application.product) {
            // Check minimum amount
            if (application.requestedAmount < application.product.minAmount) {
                result.isValid = false
                result.errors << "Loan amount is below product minimum"
            }
            
            // Check maximum amount
            if (application.requestedAmount > application.product.maxAmount) {
                result.isValid = false
                result.errors << "Loan amount exceeds product maximum"
            }
        }
        
        return result
    }
    
    /**
     * Validate loan term
     */
    private Map validateLoanTerm(LoanApplication application, Map result) {
        if (application.product) {
            // Check minimum term
            if (application.requestedTenure < application.product.minTerm) {
                result.isValid = false
                result.errors << "Loan term is below product minimum"
            }
            
            // Check maximum term
            if (application.requestedTenure > application.product.maxTerm) {
                result.isValid = false
                result.errors << "Loan term exceeds product maximum"
            }
        }
        
        return result
    }
    
    /**
     * Validate interest rate
     */
    private Map validateInterestRate(LoanApplication application, Map result) {
        if (application.requestedInterestRate) {
            if (application.product) {
                // Check minimum rate
                if (application.requestedInterestRate < application.product.minInterestRate) {
                    result.warnings << "Interest rate is below product minimum"
                }
                
                // Check maximum rate
                if (application.requestedInterestRate > application.product.maxInterestRate) {
                    result.warnings << "Interest rate exceeds product maximum"
                }
            }
        }
        
        return result
    }
    
    /**
     * Validate collateral
     */
    private Map validateCollateral(LoanApplication application, Map result) {
        // Check if collateral is required
        if (isCollateralRequired(application)) {
            if (!application.collaterals || application.collaterals.isEmpty()) {
                result.isValid = false
                result.errors << "Collateral is required for this loan"
            } else {
                // Validate collateral value
                BigDecimal collateralValue = calculateCollateralValue(application.collaterals)
                BigDecimal requiredValue = application.requestedAmount * getRequiredCollateralRatio(application.product)
                
                if (collateralValue < requiredValue) {
                    result.isValid = false
                    result.errors << "Insufficient collateral value"
                }
            }
        }
        
        return result
    }
    
    /**
     * Validate business rules
     */
    private Map validateBusinessRules(LoanApplication application, Map result) {
        // Age validation
        if (application.customer.age < 18) {
            result.isValid = false
            result.errors << "Customer must be at least 18 years old"
        }
        
        if (application.customer.age > 65) {
            result.warnings << "Customer age exceeds standard lending age"
        }
        
        // Income validation
        if (application.monthlyIncome) {
            BigDecimal debtToIncomeRatio = calculateDebtToIncomeRatio(application)
            if (debtToIncomeRatio > 0.4) { // 40% DTI ratio
                result.warnings << "Debt-to-income ratio exceeds recommended threshold"
            }
        }
        
        return result
    }
    
    /**
     * Validate loan balances
     */
    private Map validateLoanBalances(Loan loan, Map result) {
        // Balance amount should not be negative
        if (loan.balanceAmount < 0) {
            result.errors << "Loan balance cannot be negative"
            result.isValid = false
        }
        
        // Interest balance should not be negative
        if (loan.interestBalanceAmount < 0) {
            result.errors << "Interest balance cannot be negative"
            result.isValid = false
        }
        
        // Penalty balance should not be negative
        if (loan.penaltyBalanceAmount < 0) {
            result.errors << "Penalty balance cannot be negative"
            result.isValid = false
        }
        
        return result
    }
    
    /**
     * Validate loan dates
     */
    private Map validateLoanDates(Loan loan, Map result) {
        if (loan.openingDate && loan.maturityDate) {
            if (loan.maturityDate <= loan.openingDate) {
                result.errors << "Maturity date must be after opening date"
                result.isValid = false
            }
        }
        
        if (loan.interestStartDate && loan.openingDate) {
            if (loan.interestStartDate < loan.openingDate) {
                result.warnings << "Interest start date is before opening date"
            }
        }
        
        return result
    }
    
    // Additional helper methods
    private BigDecimal calculateTotalOutstanding(Loan loan) {
        return (loan.balanceAmount ?: 0) + (loan.interestBalanceAmount ?: 0) + 
               (loan.penaltyBalanceAmount ?: 0) + (loan.serviceChargeBalanceAmount ?: 0)
    }
    
    private BigDecimal calculateMinimumPayment(Loan loan) {
        // This would be based on loan terms and overdue amounts
        return loan.balanceAmount * 0.05 // 5% minimum payment as example
    }
    
    private BigDecimal getMinimumDisbursementAmount(Loan loan) {
        return loan.grantedAmount * 0.1 // 10% minimum as example
    }
    
    private boolean hasPendingTransactions(Loan loan) {
        // Check for pending transactions
        return false // Placeholder implementation
    }
    
    private List<Loan> getActiveLoans(Customer customer) {
        return Loan.findAllByCustomerAndStatusNotInList(customer, [
            LoanAcctStatus.get(6), // Closed
            LoanAcctStatus.get(7), // Cancelled
            LoanAcctStatus.get(8)  // Written-off
        ])
    }
    
    private BigDecimal getCreditLimit(Customer customer) {
        // This would be based on customer profile and credit assessment
        return new BigDecimal("1000000") // Default limit
    }
    
    private boolean isKycCompliant(Customer customer) {
        // Check KYC compliance
        return true // Placeholder implementation
    }
    
    private boolean isCollateralRequired(LoanApplication application) {
        // Check if collateral is required based on amount and product
        return application.requestedAmount > 100000 // Example threshold
    }
    
    private BigDecimal calculateCollateralValue(def collaterals) {
        // Calculate total collateral value
        return new BigDecimal("0") // Placeholder implementation
    }
    
    private BigDecimal getRequiredCollateralRatio(Product product) {
        // Get required collateral ratio for product
        return new BigDecimal("1.2") // 120% coverage as example
    }
    
    private BigDecimal calculateDebtToIncomeRatio(LoanApplication application) {
        // Calculate debt-to-income ratio
        return new BigDecimal("0.3") // Placeholder implementation
    }
}
