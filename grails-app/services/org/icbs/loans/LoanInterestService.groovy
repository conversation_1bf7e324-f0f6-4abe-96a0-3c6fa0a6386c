package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.loans.Loan
import org.icbs.loans.LoanInstallment
import org.icbs.loans.LoanInterestAccrual
import org.icbs.loans.LoanInterestPosting
import org.icbs.admin.Branch
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * REFACTORED: Loan Interest Service
 * Extracted from LoanService.groovy (1,800+ lines)
 * Handles all loan interest calculation and accrual operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanInterestService {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    LoanCalculationService loanCalculationService
    def auditLogService
    
    // =====================================================
    // INTEREST CALCULATION OPERATIONS
    // =====================================================
    
    /**
     * Calculate daily interest accrual for loan
     */
    Map calculateDailyInterestAccrual(Loan loan, Date accrualDate = new Date()) {
        Map result = [success: false, accrualAmount: BigDecimal.ZERO, message: '']
        
        try {
            log.debug("Calculating daily interest accrual for loan: ${loan.accountNo}")
            
            if (!loan || loan.status?.id in [6, 7, 8]) { // Closed, Cancelled, Written-off
                result.message = 'Loan is not eligible for interest accrual'
                return result
            }
            
            // Calculate daily interest rate
            BigDecimal annualRate = loan.interestRate / 100
            BigDecimal dailyRate = calculateDailyInterestRate(annualRate, loan.interestIncomeScheme?.divisor ?: 365)
            
            // Calculate accrual amount based on outstanding principal
            BigDecimal principalBalance = loan.balanceAmount ?: BigDecimal.ZERO
            BigDecimal accrualAmount = principalBalance * dailyRate
            
            // Round to 2 decimal places
            accrualAmount = accrualAmount.setScale(2, RoundingMode.HALF_UP)
            
            // Create accrual record
            if (accrualAmount > 0) {
                LoanInterestAccrual accrual = createInterestAccrualRecord(loan, accrualDate, accrualAmount, dailyRate)
                
                // Update loan accrued interest balance
                loan.accruedInterestAmount = (loan.accruedInterestAmount ?: BigDecimal.ZERO) + accrualAmount
                loan.save(flush: true, failOnError: true)
                
                result.success = true
                result.accrualAmount = accrualAmount
                result.accrual = accrual
                result.message = 'Interest accrual calculated successfully'
            } else {
                result.success = true
                result.message = 'No interest accrual required'
            }
            
        } catch (Exception e) {
            log.error("Error calculating daily interest accrual", e)
            result.message = 'Error calculating interest accrual'
        }
        
        return result
    }
    
    /**
     * Process interest posting for loan
     */
    Map processInterestPosting(Loan loan, Date postingDate = new Date()) {
        Map result = [success: false, postedAmount: BigDecimal.ZERO, message: '']
        
        try {
            log.debug("Processing interest posting for loan: ${loan.accountNo}")
            
            // Get unposted accrued interest
            BigDecimal accruedAmount = loan.accruedInterestAmount ?: BigDecimal.ZERO
            
            if (accruedAmount <= 0) {
                result.success = true
                result.message = 'No accrued interest to post'
                return result
            }
            
            // Create interest posting record
            LoanInterestPosting posting = createInterestPostingRecord(loan, postingDate, accruedAmount)
            
            // Update loan interest balance
            loan.interestBalanceAmount = (loan.interestBalanceAmount ?: BigDecimal.ZERO) + accruedAmount
            loan.accruedInterestAmount = BigDecimal.ZERO // Reset accrued amount
            loan.save(flush: true, failOnError: true)
            
            // Update next installment if applicable
            updateInstallmentWithPostedInterest(loan, accruedAmount)
            
            result.success = true
            result.postedAmount = accruedAmount
            result.posting = posting
            result.message = 'Interest posted successfully'
            
        } catch (Exception e) {
            log.error("Error processing interest posting", e)
            result.message = 'Error processing interest posting'
        }
        
        return result
    }
    
    /**
     * Calculate compound interest
     */
    BigDecimal calculateCompoundInterest(BigDecimal principal, BigDecimal annualRate, Integer periods, Integer compoundingFrequency) {
        try {
            if (principal <= 0 || annualRate <= 0 || periods <= 0) {
                return BigDecimal.ZERO
            }
            
            BigDecimal rate = annualRate / 100 / compoundingFrequency
            BigDecimal amount = principal * Math.pow(1 + rate, periods * compoundingFrequency)
            BigDecimal interest = amount - principal
            
            return interest.setScale(2, RoundingMode.HALF_UP)
            
        } catch (Exception e) {
            log.error("Error calculating compound interest", e)
            return BigDecimal.ZERO
        }
    }
    
    /**
     * Calculate simple interest
     */
    BigDecimal calculateSimpleInterest(BigDecimal principal, BigDecimal annualRate, Integer days, Integer divisor = 365) {
        try {
            if (principal <= 0 || annualRate <= 0 || days <= 0) {
                return BigDecimal.ZERO
            }
            
            BigDecimal interest = principal * (annualRate / 100) * (days / divisor)
            return interest.setScale(2, RoundingMode.HALF_UP)
            
        } catch (Exception e) {
            log.error("Error calculating simple interest", e)
            return BigDecimal.ZERO
        }
    }
    
    /**
     * Calculate effective interest rate
     */
    BigDecimal calculateEffectiveInterestRate(Loan loan) {
        try {
            BigDecimal nominalRate = loan.interestRate
            Integer compoundingFrequency = getCompoundingFrequency(loan.frequency?.id)
            
            // EIR = (1 + r/n)^n - 1
            BigDecimal rate = nominalRate / 100 / compoundingFrequency
            BigDecimal effectiveRate = (Math.pow(1 + rate, compoundingFrequency) - 1) * 100
            
            return effectiveRate.setScale(4, RoundingMode.HALF_UP)
            
        } catch (Exception e) {
            log.error("Error calculating effective interest rate", e)
            return loan.interestRate
        }
    }
    
    /**
     * Process end-of-day interest accrual for all active loans
     */
    Map processEODInterestAccrual(Date accrualDate = new Date()) {
        Map result = [success: false, processedCount: 0, totalAccrued: BigDecimal.ZERO, errors: []]
        
        try {
            log.info("Processing EOD interest accrual for date: ${accrualDate}")
            
            // Get all active loans
            List<Loan> activeLoans = getActiveLoansForAccrual()
            
            BigDecimal totalAccrued = BigDecimal.ZERO
            Integer processedCount = 0
            List<String> errors = []
            
            for (Loan loan : activeLoans) {
                try {
                    Map accrualResult = calculateDailyInterestAccrual(loan, accrualDate)
                    if (accrualResult.success) {
                        totalAccrued += accrualResult.accrualAmount
                        processedCount++
                    } else {
                        errors << "Loan ${loan.accountNo}: ${accrualResult.message}"
                    }
                } catch (Exception e) {
                    log.error("Error processing accrual for loan ${loan.accountNo}", e)
                    errors << "Loan ${loan.accountNo}: Error processing accrual"
                }
            }
            
            result.success = true
            result.processedCount = processedCount
            result.totalAccrued = totalAccrued
            result.errors = errors
            result.message = "EOD interest accrual completed: ${processedCount} loans processed"
            
        } catch (Exception e) {
            log.error("Error processing EOD interest accrual", e)
            result.message = 'Error processing EOD interest accrual'
        }
        
        return result
    }
    
    /**
     * Calculate interest for specific period
     */
    BigDecimal calculateInterestForPeriod(Loan loan, Date fromDate, Date toDate) {
        try {
            if (!fromDate || !toDate || fromDate >= toDate) {
                return BigDecimal.ZERO
            }
            
            Integer days = calculateDaysBetween(fromDate, toDate)
            BigDecimal principal = loan.balanceAmount ?: BigDecimal.ZERO
            BigDecimal annualRate = loan.interestRate
            Integer divisor = loan.interestIncomeScheme?.divisor ?: 365
            
            return calculateSimpleInterest(principal, annualRate, days, divisor)
            
        } catch (Exception e) {
            log.error("Error calculating interest for period", e)
            return BigDecimal.ZERO
        }
    }
    
    /**
     * Get interest accrual history for loan
     */
    List<LoanInterestAccrual> getInterestAccrualHistory(Long loanId, Date fromDate = null, Date toDate = null) {
        try {
            Loan loan = Loan.get(loanId)
            if (!loan) {
                return []
            }
            
            def criteria = LoanInterestAccrual.createCriteria()
            return criteria.list {
                eq("loan", loan)
                if (fromDate) {
                    ge("accrualDate", fromDate)
                }
                if (toDate) {
                    le("accrualDate", toDate)
                }
                order("accrualDate", "desc")
            }
            
        } catch (Exception e) {
            log.error("Error getting interest accrual history", e)
            return []
        }
    }
    
    /**
     * Calculate past due interest
     */
    BigDecimal calculatePastDueInterest(Loan loan, Date asOfDate = new Date()) {
        try {
            BigDecimal pastDueInterest = BigDecimal.ZERO
            
            // Get overdue installments
            List<LoanInstallment> overdueInstallments = getOverdueInstallments(loan, asOfDate)
            
            for (LoanInstallment installment : overdueInstallments) {
                // Calculate additional interest on overdue amount
                BigDecimal overdueAmount = installment.principalInstallmentAmount - installment.principalInstallmentPaid
                Integer daysOverdue = calculateDaysOverdue(installment.installmentDate, asOfDate)
                
                if (overdueAmount > 0 && daysOverdue > 0) {
                    BigDecimal pastDueRate = loan.pastDueInterestRate ?: loan.interestRate
                    BigDecimal additionalInterest = calculateSimpleInterest(overdueAmount, pastDueRate, daysOverdue)
                    pastDueInterest += additionalInterest
                }
            }
            
            return pastDueInterest.setScale(2, RoundingMode.HALF_UP)
            
        } catch (Exception e) {
            log.error("Error calculating past due interest", e)
            return BigDecimal.ZERO
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Calculate daily interest rate
     */
    private BigDecimal calculateDailyInterestRate(BigDecimal annualRate, Integer divisor) {
        return annualRate / divisor
    }
    
    /**
     * Create interest accrual record
     */
    private LoanInterestAccrual createInterestAccrualRecord(Loan loan, Date accrualDate, BigDecimal accrualAmount, BigDecimal dailyRate) {
        LoanInterestAccrual accrual = new LoanInterestAccrual(
            loan: loan,
            accrualDate: accrualDate,
            principalBalance: loan.balanceAmount,
            interestRate: loan.interestRate,
            dailyRate: dailyRate,
            accrualAmount: accrualAmount,
            branch: loan.branch
        )
        
        accrual.save(flush: true, failOnError: true)
        return accrual
    }
    
    /**
     * Create interest posting record
     */
    private LoanInterestPosting createInterestPostingRecord(Loan loan, Date postingDate, BigDecimal postedAmount) {
        LoanInterestPosting posting = new LoanInterestPosting(
            loan: loan,
            postingDate: postingDate,
            postedAmount: postedAmount,
            principalBalance: loan.balanceAmount,
            interestRate: loan.interestRate,
            branch: loan.branch
        )
        
        posting.save(flush: true, failOnError: true)
        return posting
    }
    
    /**
     * Update installment with posted interest
     */
    private void updateInstallmentWithPostedInterest(Loan loan, BigDecimal postedAmount) {
        try {
            // Get next unpaid installment
            LoanInstallment nextInstallment = LoanInstallment.createCriteria().get {
                eq("loan", loan)
                ne("status", org.icbs.lov.LoanInstallmentStatus.get(3)) // Not paid
                order("sequenceNo", "asc")
                maxResults(1)
            }
            
            if (nextInstallment) {
                // Add posted interest to installment
                nextInstallment.interestInstallmentAmount += postedAmount
                nextInstallment.totalInstallmentAmount += postedAmount
                nextInstallment.save(flush: true, failOnError: true)
            }
            
        } catch (Exception e) {
            log.error("Error updating installment with posted interest", e)
        }
    }
    
    /**
     * Get active loans for accrual
     */
    private List<Loan> getActiveLoansForAccrual() {
        return Loan.createCriteria().list {
            not {
                'in'("status", [
                    org.icbs.lov.LoanAcctStatus.get(6), // Closed
                    org.icbs.lov.LoanAcctStatus.get(7), // Cancelled
                    org.icbs.lov.LoanAcctStatus.get(8)  // Written-off
                ])
            }
            gt("balanceAmount", BigDecimal.ZERO)
            order("accountNo", "asc")
        }
    }
    
    /**
     * Get compounding frequency based on loan frequency
     */
    private Integer getCompoundingFrequency(Integer frequency) {
        switch (frequency) {
            case 1: // Daily
                return 365
            case 2: // Weekly
                return 52
            case 3: // Monthly
                return 12
            case 4: // Quarterly
                return 4
            case 5: // Semi-annually
                return 2
            case 6: // Annually
                return 1
            default:
                return 12 // Default to monthly
        }
    }
    
    /**
     * Calculate days between two dates
     */
    private Integer calculateDaysBetween(Date fromDate, Date toDate) {
        if (!fromDate || !toDate) {
            return 0
        }
        
        long diffInMillis = toDate.time - fromDate.time
        return (int) (diffInMillis / (24 * 60 * 60 * 1000))
    }
    
    /**
     * Get overdue installments
     */
    private List<LoanInstallment> getOverdueInstallments(Loan loan, Date asOfDate) {
        return LoanInstallment.createCriteria().list {
            eq("loan", loan)
            lt("installmentDate", asOfDate)
            ne("status", org.icbs.lov.LoanInstallmentStatus.get(3)) // Not paid
            order("installmentDate", "asc")
        }
    }
    
    /**
     * Calculate days overdue
     */
    private Integer calculateDaysOverdue(Date dueDate, Date asOfDate) {
        if (!dueDate || !asOfDate || asOfDate <= dueDate) {
            return 0
        }
        
        long diffInMillis = asOfDate.time - dueDate.time
        return (int) (diffInMillis / (24 * 60 * 60 * 1000))
    }
}
