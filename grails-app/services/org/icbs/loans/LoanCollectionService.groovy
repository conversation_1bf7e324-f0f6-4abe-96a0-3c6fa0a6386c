package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.loans.Loan
import org.icbs.loans.LoanInstallment
import org.icbs.loans.LoanCollectionAction
import org.icbs.loans.LoanCollectionNote
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanCollectionStatus
import org.icbs.admin.UserMaster
import org.icbs.cif.Customer
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import java.math.BigDecimal

/**
 * REFACTORED: Loan Collection Service
 * Extracted from LoanService.groovy (1,800+ lines)
 * Handles all loan collection and recovery operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanCollectionService {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    LoanCalculationService loanCalculationService
    def notificationService
    def auditLogService
    
    // =====================================================
    // LOAN COLLECTION OPERATIONS
    // =====================================================
    
    /**
     * Process loan collection workflow
     */
    Map processLoanCollection(Map collectionData) {
        Map result = [success: false, errors: [], actions: [], message: '']
        
        try {
            log.info("Processing loan collection for loan: ${collectionData.loanId}")
            
            // 1. Validate collection data
            Map validationResult = unifiedValidationService.validateLoanCollection(collectionData)
            if (!validationResult.isValid) {
                result.errors = validationResult.errors
                return result
            }
            
            // 2. Get loan account
            Loan loan = Loan.get(collectionData.loanId)
            if (!loan) {
                result.errors << "Loan account not found"
                return result
            }
            
            // 3. Assess collection status
            Map assessmentResult = assessCollectionStatus(loan)
            
            // 4. Determine collection actions
            List<Map> recommendedActions = determineCollectionActions(loan, assessmentResult)
            
            // 5. Execute collection actions if specified
            if (collectionData.executeActions) {
                Map executionResult = executeCollectionActions(loan, recommendedActions, collectionData)
                result.actions = executionResult.actions
                result.errors.addAll(executionResult.errors)
            } else {
                result.actions = recommendedActions
            }
            
            // 6. Update collection status
            updateCollectionStatus(loan, assessmentResult)
            
            // 7. Audit logging
            auditLoanCollection(loan, result.actions, 'PROCESSED')
            
            result.success = true
            result.assessment = assessmentResult
            result.message = "Loan collection processed successfully"
            
        } catch (Exception e) {
            log.error("Error processing loan collection", e)
            result.errors << "Error processing loan collection: ${e.message}"
        }
        
        return result
    }
    
    /**
     * Assess collection status for loan
     */
    Map assessCollectionStatus(Loan loan) {
        Map assessment = [:]
        
        try {
            log.debug("Assessing collection status for loan: ${loan.accountNo}")
            
            // Calculate overdue amounts
            assessment.overdueAmount = calculateOverdueAmount(loan)
            assessment.totalOutstanding = calculateTotalOutstanding(loan)
            assessment.daysOverdue = calculateDaysOverdue(loan)
            
            // Determine collection stage
            assessment.collectionStage = determineCollectionStage(assessment.daysOverdue, assessment.overdueAmount)
            assessment.riskLevel = determineRiskLevel(loan, assessment)
            
            // Get payment history analysis
            assessment.paymentHistory = analyzePaymentHistory(loan)
            assessment.contactHistory = getContactHistory(loan)
            
            // Calculate collection metrics
            assessment.collectionEffectiveness = calculateCollectionEffectiveness(loan)
            assessment.recoveryProbability = calculateRecoveryProbability(loan, assessment)
            
        } catch (Exception e) {
            log.error("Error assessing collection status", e)
            assessment.error = "Error assessing collection status"
        }
        
        return assessment
    }
    
    /**
     * Generate collection report for portfolio
     */
    Map generateCollectionReport(Date fromDate, Date toDate, Map filters = [:]) {
        Map report = [:]
        
        try {
            log.info("Generating collection report for period: ${fromDate} to ${toDate}")
            
            // Get loans in collection
            List<Loan> collectionLoans = getLoansInCollection(fromDate, toDate, filters)
            
            // Calculate portfolio metrics
            report.totalLoans = collectionLoans.size()
            report.totalOutstanding = collectionLoans.sum { calculateTotalOutstanding(it) } ?: 0
            report.totalOverdue = collectionLoans.sum { calculateOverdueAmount(it) } ?: 0
            
            // Group by collection stage
            report.byStage = collectionLoans.groupBy { 
                determineCollectionStage(calculateDaysOverdue(it), calculateOverdueAmount(it))
            }.collectEntries { stage, loans ->
                [stage, [
                    count: loans.size(),
                    outstanding: loans.sum { calculateTotalOutstanding(it) } ?: 0,
                    overdue: loans.sum { calculateOverdueAmount(it) } ?: 0
                ]]
            }
            
            // Group by risk level
            report.byRisk = collectionLoans.groupBy {
                determineRiskLevel(it, assessCollectionStatus(it))
            }.collectEntries { risk, loans ->
                [risk, [
                    count: loans.size(),
                    outstanding: loans.sum { calculateTotalOutstanding(it) } ?: 0
                ]]
            }
            
            // Collection actions summary
            report.actionsSummary = getCollectionActionsSummary(fromDate, toDate)
            
        } catch (Exception e) {
            log.error("Error generating collection report", e)
            report.error = "Error generating collection report"
        }
        
        return report
    }
    
    /**
     * Schedule collection action
     */
    Map scheduleCollectionAction(Long loanId, String actionType, Date scheduledDate, Map actionData = [:]) {
        Map result = [success: false, action: null, message: '']
        
        try {
            Loan loan = Loan.get(loanId)
            if (!loan) {
                result.message = 'Loan not found'
                return result
            }
            
            // Create collection action record
            LoanCollectionAction action = new LoanCollectionAction(
                loan: loan,
                actionType: actionType,
                scheduledDate: scheduledDate,
                status: LoanCollectionStatus.get(1), // Scheduled
                assignedTo: UserMaster.get(actionData.assignedToUserId ?: 1),
                priority: actionData.priority ?: 'MEDIUM',
                description: actionData.description,
                expectedOutcome: actionData.expectedOutcome
            )
            
            action.save(flush: true, failOnError: true)
            
            // Send notifications if required
            if (actionData.sendNotification) {
                sendCollectionNotification(action)
            }
            
            result.success = true
            result.action = action
            result.message = 'Collection action scheduled successfully'
            
        } catch (Exception e) {
            log.error("Error scheduling collection action", e)
            result.message = 'Error scheduling collection action'
        }
        
        return result
    }
    
    /**
     * Record collection contact
     */
    Map recordCollectionContact(Long loanId, Map contactData) {
        Map result = [success: false, contact: null, message: '']
        
        try {
            Loan loan = Loan.get(loanId)
            if (!loan) {
                result.message = 'Loan not found'
                return result
            }
            
            // Create collection note
            LoanCollectionNote note = new LoanCollectionNote(
                loan: loan,
                contactDate: contactData.contactDate ?: new Date(),
                contactType: contactData.contactType,
                contactMethod: contactData.contactMethod,
                contactedPerson: contactData.contactedPerson,
                outcome: contactData.outcome,
                notes: contactData.notes,
                followUpRequired: contactData.followUpRequired ?: false,
                followUpDate: contactData.followUpDate,
                createdBy: UserMaster.get(contactData.userId ?: 1)
            )
            
            note.save(flush: true, failOnError: true)
            
            // Update loan collection status if needed
            if (contactData.updateStatus) {
                updateLoanCollectionStatus(loan, contactData.newStatus)
            }
            
            result.success = true
            result.contact = note
            result.message = 'Collection contact recorded successfully'
            
        } catch (Exception e) {
            log.error("Error recording collection contact", e)
            result.message = 'Error recording collection contact'
        }
        
        return result
    }
    
    /**
     * Get collection dashboard data
     */
    Map getCollectionDashboard(Long userId = null) {
        Map dashboard = [:]
        
        try {
            // Get user's assigned collection items
            if (userId) {
                dashboard.assignedActions = getAssignedCollectionActions(userId)
                dashboard.assignedLoans = getAssignedCollectionLoans(userId)
            }
            
            // Get overall collection metrics
            dashboard.totalInCollection = getTotalLoansInCollection()
            dashboard.totalOverdue = getTotalOverdueAmount()
            dashboard.collectionRate = calculateCollectionRate()
            
            // Get urgent items
            dashboard.urgentActions = getUrgentCollectionActions()
            dashboard.criticalLoans = getCriticalCollectionLoans()
            
            // Get recent activities
            dashboard.recentContacts = getRecentCollectionContacts()
            dashboard.recentPayments = getRecentCollectionPayments()
            
        } catch (Exception e) {
            log.error("Error getting collection dashboard", e)
            dashboard.error = "Error getting collection dashboard"
        }
        
        return dashboard
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Calculate overdue amount for loan
     */
    private BigDecimal calculateOverdueAmount(Loan loan) {
        try {
            BigDecimal overdueAmount = BigDecimal.ZERO
            
            List<LoanInstallment> overdueInstallments = LoanInstallment.createCriteria().list {
                eq("loan", loan)
                lt("installmentDate", new Date())
                ne("status", org.icbs.lov.LoanInstallmentStatus.get(3)) // Not paid
            }
            
            for (LoanInstallment installment : overdueInstallments) {
                BigDecimal installmentOverdue = installment.totalInstallmentAmount - 
                    (installment.principalInstallmentPaid + installment.interestInstallmentPaid + 
                     installment.penaltyInstallmentPaid + installment.serviceChargeInstallmentPaid)
                overdueAmount += installmentOverdue
            }
            
            return overdueAmount
            
        } catch (Exception e) {
            log.error("Error calculating overdue amount", e)
            return BigDecimal.ZERO
        }
    }
    
    /**
     * Calculate total outstanding for loan
     */
    private BigDecimal calculateTotalOutstanding(Loan loan) {
        return (loan.balanceAmount ?: 0) + (loan.interestBalanceAmount ?: 0) + 
               (loan.penaltyBalanceAmount ?: 0) + (loan.serviceChargeBalanceAmount ?: 0)
    }
    
    /**
     * Calculate days overdue for loan
     */
    private Integer calculateDaysOverdue(Loan loan) {
        try {
            LoanInstallment oldestOverdue = LoanInstallment.createCriteria().get {
                eq("loan", loan)
                lt("installmentDate", new Date())
                ne("status", org.icbs.lov.LoanInstallmentStatus.get(3)) // Not paid
                order("installmentDate", "asc")
                maxResults(1)
            }
            
            if (oldestOverdue) {
                long diffInMillis = new Date().time - oldestOverdue.installmentDate.time
                return (int) (diffInMillis / (24 * 60 * 60 * 1000))
            }
            
            return 0
            
        } catch (Exception e) {
            log.error("Error calculating days overdue", e)
            return 0
        }
    }
    
    /**
     * Determine collection stage based on days overdue
     */
    private String determineCollectionStage(Integer daysOverdue, BigDecimal overdueAmount) {
        if (daysOverdue == 0 || overdueAmount <= 0) {
            return 'CURRENT'
        } else if (daysOverdue <= 30) {
            return 'EARLY_COLLECTION'
        } else if (daysOverdue <= 60) {
            return 'ACTIVE_COLLECTION'
        } else if (daysOverdue <= 90) {
            return 'INTENSIVE_COLLECTION'
        } else if (daysOverdue <= 180) {
            return 'LEGAL_ACTION'
        } else {
            return 'WRITE_OFF_CONSIDERATION'
        }
    }
    
    /**
     * Determine risk level for loan
     */
    private String determineRiskLevel(Loan loan, Map assessment) {
        try {
            Integer daysOverdue = assessment.daysOverdue ?: 0
            BigDecimal overdueAmount = assessment.overdueAmount ?: BigDecimal.ZERO
            BigDecimal totalOutstanding = assessment.totalOutstanding ?: BigDecimal.ZERO
            
            BigDecimal overdueRatio = totalOutstanding > 0 ? overdueAmount / totalOutstanding : BigDecimal.ZERO
            
            if (daysOverdue > 90 || overdueRatio > 0.5) {
                return 'HIGH'
            } else if (daysOverdue > 30 || overdueRatio > 0.25) {
                return 'MEDIUM'
            } else {
                return 'LOW'
            }
            
        } catch (Exception e) {
            log.error("Error determining risk level", e)
            return 'MEDIUM'
        }
    }
    
    /**
     * Determine collection actions based on assessment
     */
    private List<Map> determineCollectionActions(Loan loan, Map assessment) {
        List<Map> actions = []
        
        try {
            String stage = assessment.collectionStage
            Integer daysOverdue = assessment.daysOverdue ?: 0
            
            switch (stage) {
                case 'EARLY_COLLECTION':
                    actions << [type: 'REMINDER_CALL', priority: 'LOW', description: 'Courtesy reminder call']
                    actions << [type: 'SMS_REMINDER', priority: 'LOW', description: 'SMS payment reminder']
                    break
                    
                case 'ACTIVE_COLLECTION':
                    actions << [type: 'COLLECTION_CALL', priority: 'MEDIUM', description: 'Collection follow-up call']
                    actions << [type: 'DEMAND_LETTER', priority: 'MEDIUM', description: 'Send demand letter']
                    break
                    
                case 'INTENSIVE_COLLECTION':
                    actions << [type: 'FIELD_VISIT', priority: 'HIGH', description: 'Field collection visit']
                    actions << [type: 'GUARANTOR_CONTACT', priority: 'HIGH', description: 'Contact guarantors']
                    break
                    
                case 'LEGAL_ACTION':
                    actions << [type: 'LEGAL_NOTICE', priority: 'URGENT', description: 'Send legal notice']
                    actions << [type: 'ASSET_VERIFICATION', priority: 'HIGH', description: 'Verify collateral assets']
                    break
                    
                case 'WRITE_OFF_CONSIDERATION':
                    actions << [type: 'WRITE_OFF_REVIEW', priority: 'URGENT', description: 'Review for write-off']
                    actions << [type: 'FINAL_SETTLEMENT', priority: 'HIGH', description: 'Negotiate final settlement']
                    break
            }
            
        } catch (Exception e) {
            log.error("Error determining collection actions", e)
        }
        
        return actions
    }
    
    /**
     * Execute collection actions
     */
    private Map executeCollectionActions(Loan loan, List<Map> actions, Map collectionData) {
        Map result = [actions: [], errors: []]
        
        try {
            for (Map actionData : actions) {
                try {
                    Map actionResult = scheduleCollectionAction(
                        loan.id, 
                        actionData.type, 
                        new Date() + 1, // Schedule for tomorrow
                        actionData
                    )
                    
                    if (actionResult.success) {
                        result.actions << actionResult.action
                    } else {
                        result.errors << "Failed to schedule ${actionData.type}: ${actionResult.message}"
                    }
                    
                } catch (Exception e) {
                    log.error("Error executing collection action", e)
                    result.errors << "Error executing ${actionData.type}"
                }
            }
            
        } catch (Exception e) {
            log.error("Error executing collection actions", e)
            result.errors << "Error executing collection actions"
        }
        
        return result
    }
    
    /**
     * Update collection status for loan
     */
    private void updateCollectionStatus(Loan loan, Map assessment) {
        try {
            // Update loan collection status based on assessment
            String stage = assessment.collectionStage
            
            // This would update a collection status field on the loan
            // For now, we'll add a comment to indicate the collection stage
            
        } catch (Exception e) {
            log.error("Error updating collection status", e)
        }
    }
    
    /**
     * Analyze payment history for loan
     */
    private Map analyzePaymentHistory(Loan loan) {
        Map analysis = [:]
        
        try {
            // Get recent payment history
            // This would analyze payment patterns, frequency, amounts, etc.
            analysis.recentPayments = 0
            analysis.averagePaymentAmount = BigDecimal.ZERO
            analysis.paymentFrequency = 'IRREGULAR'
            analysis.lastPaymentDate = null
            
        } catch (Exception e) {
            log.error("Error analyzing payment history", e)
            analysis.error = "Error analyzing payment history"
        }
        
        return analysis
    }
    
    /**
     * Get contact history for loan
     */
    private List<LoanCollectionNote> getContactHistory(Loan loan) {
        try {
            return LoanCollectionNote.createCriteria().list {
                eq("loan", loan)
                order("contactDate", "desc")
                maxResults(10)
            }
        } catch (Exception e) {
            log.error("Error getting contact history", e)
            return []
        }
    }
    
    /**
     * Calculate collection effectiveness
     */
    private BigDecimal calculateCollectionEffectiveness(Loan loan) {
        try {
            // This would calculate the effectiveness of collection efforts
            // Based on recovery amounts vs. collection costs
            return new BigDecimal("0.75") // 75% effectiveness as placeholder
        } catch (Exception e) {
            log.error("Error calculating collection effectiveness", e)
            return BigDecimal.ZERO
        }
    }
    
    /**
     * Calculate recovery probability
     */
    private BigDecimal calculateRecoveryProbability(Loan loan, Map assessment) {
        try {
            // This would use machine learning or statistical models
            // to predict recovery probability
            String riskLevel = assessment.riskLevel
            
            switch (riskLevel) {
                case 'LOW':
                    return new BigDecimal("0.90")
                case 'MEDIUM':
                    return new BigDecimal("0.65")
                case 'HIGH':
                    return new BigDecimal("0.30")
                default:
                    return new BigDecimal("0.50")
            }
            
        } catch (Exception e) {
            log.error("Error calculating recovery probability", e)
            return BigDecimal.ZERO
        }
    }
    
    /**
     * Get loans in collection
     */
    private List<Loan> getLoansInCollection(Date fromDate, Date toDate, Map filters) {
        return Loan.createCriteria().list {
            // Add criteria for loans in collection
            // This would include loans with overdue amounts
            ne("status", LoanAcctStatus.get(6)) // Not closed
            // Add date and filter criteria as needed
        }
    }
    
    /**
     * Send collection notification
     */
    private void sendCollectionNotification(LoanCollectionAction action) {
        try {
            // This would integrate with notification service
            // to send emails, SMS, etc.
            log.info("Sending collection notification for action: ${action.id}")
        } catch (Exception e) {
            log.error("Error sending collection notification", e)
        }
    }
    
    /**
     * Update loan collection status
     */
    private void updateLoanCollectionStatus(Loan loan, String newStatus) {
        try {
            // Update loan collection status
            // This would update a collection status field
            loan.save(flush: true, failOnError: true)
        } catch (Exception e) {
            log.error("Error updating loan collection status", e)
        }
    }
    
    /**
     * Get collection actions summary
     */
    private Map getCollectionActionsSummary(Date fromDate, Date toDate) {
        Map summary = [:]
        
        try {
            // Get collection actions summary for the period
            summary.totalActions = 0
            summary.completedActions = 0
            summary.pendingActions = 0
            summary.successRate = BigDecimal.ZERO
            
        } catch (Exception e) {
            log.error("Error getting collection actions summary", e)
            summary.error = "Error getting collection actions summary"
        }
        
        return summary
    }
    
    // Additional helper methods for dashboard
    private List getAssignedCollectionActions(Long userId) { return [] }
    private List getAssignedCollectionLoans(Long userId) { return [] }
    private Integer getTotalLoansInCollection() { return 0 }
    private BigDecimal getTotalOverdueAmount() { return BigDecimal.ZERO }
    private BigDecimal calculateCollectionRate() { return BigDecimal.ZERO }
    private List getUrgentCollectionActions() { return [] }
    private List getCriticalCollectionLoans() { return [] }
    private List getRecentCollectionContacts() { return [] }
    private List getRecentCollectionPayments() { return [] }
    
    /**
     * Audit loan collection
     */
    private void auditLoanCollection(Loan loan, List actions, String result) {
        try {
            auditLogService.insert('130', 'LCS05000', 
                "Loan collection processed - Actions: ${actions.size()}, Result: ${result}", 
                'LoanCollectionService', null, null, null, loan.id)
        } catch (Exception e) {
            log.error("Error auditing loan collection", e)
        }
    }
}
