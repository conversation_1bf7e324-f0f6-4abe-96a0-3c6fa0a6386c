package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.loans.Loan
import org.icbs.loans.LoanInstallment
import org.icbs.loans.LoanLedger
import org.icbs.loans.LoanPaymentDetails
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanInstallmentStatus
import org.icbs.admin.UserMaster
import org.icbs.admin.Branch
import org.icbs.admin.TxnTemplate
import org.icbs.tellering.TxnFile
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * REFACTORED: Loan Payment Service
 * Extracted from LoanService.groovy (1,800+ lines)
 * Handles all loan payment processing operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanPaymentService {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    LoanCalculationService loanCalculationService
    LoanValidationService loanValidationService
    def glTransactionService
    def auditLogService
    
    // =====================================================
    // LOAN PAYMENT PROCESSING
    // =====================================================
    
    /**
     * Process loan payment
     */
    Map processLoanPayment(Map paymentData) {
        Map result = [success: false, errors: [], payment: null, message: '']
        
        try {
            log.info("Processing loan payment for loan: ${paymentData.loanId}")
            
            // 1. Validate payment data
            Map validationResult = unifiedValidationService.validateLoanPayment(paymentData)
            if (!validationResult.isValid) {
                result.errors = validationResult.errors
                return result
            }
            
            // 2. Get loan account
            Loan loan = Loan.get(paymentData.loanId)
            if (!loan) {
                result.errors << "Loan account not found"
                return result
            }
            
            // 3. Validate payment eligibility
            Map eligibilityResult = loanValidationService.validateLoanPayment(
                loan, paymentData.paymentAmount, paymentData.paymentType
            )
            if (!eligibilityResult.isValid) {
                result.errors = eligibilityResult.errors
                return result
            }
            
            // 4. Calculate payment allocation
            Map allocationResult = calculatePaymentAllocation(loan, paymentData.paymentAmount)
            
            // 5. Process payment
            Map paymentResult = processPayment(loan, paymentData, allocationResult)
            
            if (paymentResult.success) {
                // 6. Update loan balances
                updateLoanBalances(loan, allocationResult)
                
                // 7. Update installments
                updateInstallments(loan, allocationResult)
                
                // 8. Create payment record
                LoanPaymentDetails payment = createPaymentRecord(loan, paymentData, allocationResult)
                
                // 9. Create loan ledger entry
                createLoanLedgerEntry(loan, payment, allocationResult)
                
                // 10. Update loan status if fully paid
                updateLoanStatusIfFullyPaid(loan)
                
                // 11. Audit logging
                auditLoanPayment(loan, payment, 'SUCCESS')
                
                result.success = true
                result.payment = payment
                result.message = "Loan payment processed successfully"
                
            } else {
                result.errors.addAll(paymentResult.errors)
            }
            
        } catch (Exception e) {
            log.error("Error processing loan payment", e)
            result.errors << "Error processing loan payment: ${e.message}"
        }
        
        return result
    }
    
    /**
     * Process regular installment payment
     */
    Map processInstallmentPayment(Long loanId, BigDecimal paymentAmount, String paymentType) {
        Map paymentData = [
            loanId: loanId,
            paymentAmount: paymentAmount,
            paymentType: paymentType,
            isInstallmentPayment: true
        ]
        
        return processLoanPayment(paymentData)
    }
    
    /**
     * Process partial payment
     */
    Map processPartialPayment(Long loanId, BigDecimal paymentAmount, String paymentType, Map allocationOverride = [:]) {
        Map paymentData = [
            loanId: loanId,
            paymentAmount: paymentAmount,
            paymentType: paymentType,
            isPartialPayment: true,
            allocationOverride: allocationOverride
        ]
        
        return processLoanPayment(paymentData)
    }
    
    /**
     * Process full loan payoff
     */
    Map processLoanPayoff(Long loanId, String paymentType) {
        try {
            Loan loan = Loan.get(loanId)
            if (!loan) {
                return [success: false, errors: ["Loan not found"]]
            }
            
            BigDecimal payoffAmount = calculatePayoffAmount(loan)
            
            Map paymentData = [
                loanId: loanId,
                paymentAmount: payoffAmount,
                paymentType: paymentType,
                isPayoffPayment: true
            ]
            
            return processLoanPayment(paymentData)
            
        } catch (Exception e) {
            log.error("Error processing loan payoff", e)
            return [success: false, errors: ["Error processing loan payoff"]]
        }
    }
    
    /**
     * Calculate payment allocation
     */
    Map calculatePaymentAllocation(Loan loan, BigDecimal paymentAmount) {
        Map allocation = [
            principalPaid: BigDecimal.ZERO,
            interestPaid: BigDecimal.ZERO,
            penaltyPaid: BigDecimal.ZERO,
            serviceChargePaid: BigDecimal.ZERO,
            remainingPayment: paymentAmount
        ]
        
        try {
            BigDecimal remainingPayment = paymentAmount
            
            // Payment hierarchy: Service Charges -> Penalties -> Interest -> Principal
            
            // 1. Pay service charges first
            if (remainingPayment > 0 && loan.serviceChargeBalanceAmount > 0) {
                BigDecimal serviceChargePayment = remainingPayment.min(loan.serviceChargeBalanceAmount)
                allocation.serviceChargePaid = serviceChargePayment
                remainingPayment -= serviceChargePayment
            }
            
            // 2. Pay penalties
            if (remainingPayment > 0 && loan.penaltyBalanceAmount > 0) {
                BigDecimal penaltyPayment = remainingPayment.min(loan.penaltyBalanceAmount)
                allocation.penaltyPaid = penaltyPayment
                remainingPayment -= penaltyPayment
            }
            
            // 3. Pay interest
            if (remainingPayment > 0 && loan.interestBalanceAmount > 0) {
                BigDecimal interestPayment = remainingPayment.min(loan.interestBalanceAmount)
                allocation.interestPaid = interestPayment
                remainingPayment -= interestPayment
            }
            
            // 4. Pay principal
            if (remainingPayment > 0 && loan.balanceAmount > 0) {
                BigDecimal principalPayment = remainingPayment.min(loan.balanceAmount)
                allocation.principalPaid = principalPayment
                remainingPayment -= principalPayment
            }
            
            allocation.remainingPayment = remainingPayment
            allocation.totalPaid = paymentAmount - remainingPayment
            
        } catch (Exception e) {
            log.error("Error calculating payment allocation", e)
        }
        
        return allocation
    }
    
    /**
     * Get payment history for loan
     */
    List<LoanPaymentDetails> getPaymentHistory(Long loanId) {
        try {
            Loan loan = Loan.get(loanId)
            if (!loan) {
                return []
            }
            
            return LoanPaymentDetails.createCriteria().list {
                eq("loan", loan)
                order("paymentDate", "desc")
            }
            
        } catch (Exception e) {
            log.error("Error getting payment history", e)
            return []
        }
    }
    
    /**
     * Calculate next payment due
     */
    Map calculateNextPaymentDue(Long loanId) {
        Map paymentDue = [:]
        
        try {
            Loan loan = Loan.get(loanId)
            if (!loan) {
                return paymentDue
            }
            
            // Get next unpaid installment
            LoanInstallment nextInstallment = getNextUnpaidInstallment(loan)
            
            if (nextInstallment) {
                paymentDue.dueDate = nextInstallment.installmentDate
                paymentDue.principalDue = nextInstallment.principalInstallmentAmount - nextInstallment.principalInstallmentPaid
                paymentDue.interestDue = nextInstallment.interestInstallmentAmount - nextInstallment.interestInstallmentPaid
                paymentDue.penaltyDue = nextInstallment.penaltyInstallmentAmount - nextInstallment.penaltyInstallmentPaid
                paymentDue.serviceChargeDue = nextInstallment.serviceChargeInstallmentAmount - nextInstallment.serviceChargeInstallmentPaid
                paymentDue.totalDue = paymentDue.principalDue + paymentDue.interestDue + paymentDue.penaltyDue + paymentDue.serviceChargeDue
                paymentDue.isOverdue = nextInstallment.installmentDate < new Date()
                
                if (paymentDue.isOverdue) {
                    paymentDue.daysOverdue = calculateDaysOverdue(nextInstallment.installmentDate)
                }
            }
            
        } catch (Exception e) {
            log.error("Error calculating next payment due", e)
            paymentDue.error = "Error calculating payment due"
        }
        
        return paymentDue
    }
    
    /**
     * Calculate payoff amount
     */
    BigDecimal calculatePayoffAmount(Loan loan, Date asOfDate = new Date()) {
        try {
            BigDecimal payoffAmount = BigDecimal.ZERO
            
            // Principal balance
            payoffAmount += loan.balanceAmount ?: BigDecimal.ZERO
            
            // Interest balance
            payoffAmount += loan.interestBalanceAmount ?: BigDecimal.ZERO
            
            // Penalty balance
            payoffAmount += loan.penaltyBalanceAmount ?: BigDecimal.ZERO
            
            // Service charge balance
            payoffAmount += loan.serviceChargeBalanceAmount ?: BigDecimal.ZERO
            
            // Calculate any additional accrued interest up to payoff date
            BigDecimal accruedInterest = calculateAccruedInterest(loan, asOfDate)
            payoffAmount += accruedInterest
            
            return payoffAmount.setScale(2, RoundingMode.HALF_UP)
            
        } catch (Exception e) {
            log.error("Error calculating payoff amount", e)
            return BigDecimal.ZERO
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Process payment based on type
     */
    private Map processPayment(Loan loan, Map paymentData, Map allocationResult) {
        Map result = [success: false, errors: []]
        
        try {
            String paymentType = paymentData.paymentType
            
            switch (paymentType) {
                case 'CASH':
                    result = processCashPayment(loan, paymentData, allocationResult)
                    break
                case 'CHECK':
                    result = processCheckPayment(loan, paymentData, allocationResult)
                    break
                case 'TRANSFER':
                    result = processTransferPayment(loan, paymentData, allocationResult)
                    break
                default:
                    result.errors << "Invalid payment type"
            }
            
        } catch (Exception e) {
            log.error("Error processing payment", e)
            result.errors << "Error processing payment"
        }
        
        return result
    }
    
    /**
     * Process cash payment
     */
    private Map processCashPayment(Loan loan, Map paymentData, Map allocationResult) {
        Map result = [success: false, errors: []]
        
        try {
            // Create transaction file for cash payment
            TxnFile txnFile = createPaymentTxnFile(loan, paymentData, allocationResult, 'CASH')
            
            // Update teller cash balance
            // This would integrate with teller balance management
            
            result.success = true
            result.txnFile = txnFile
            
        } catch (Exception e) {
            log.error("Error processing cash payment", e)
            result.errors << "Error processing cash payment"
        }
        
        return result
    }
    
    /**
     * Process check payment
     */
    private Map processCheckPayment(Loan loan, Map paymentData, Map allocationResult) {
        Map result = [success: false, errors: []]
        
        try {
            // Create transaction file for check payment
            TxnFile txnFile = createPaymentTxnFile(loan, paymentData, allocationResult, 'CHECK')
            
            // Process check details
            // This would integrate with check management system
            
            result.success = true
            result.txnFile = txnFile
            
        } catch (Exception e) {
            log.error("Error processing check payment", e)
            result.errors << "Error processing check payment"
        }
        
        return result
    }
    
    /**
     * Process transfer payment
     */
    private Map processTransferPayment(Loan loan, Map paymentData, Map allocationResult) {
        Map result = [success: false, errors: []]
        
        try {
            // Create transaction file for transfer payment
            TxnFile txnFile = createPaymentTxnFile(loan, paymentData, allocationResult, 'TRANSFER')
            
            // Process account transfer
            // This would integrate with account transfer system
            
            result.success = true
            result.txnFile = txnFile
            
        } catch (Exception e) {
            log.error("Error processing transfer payment", e)
            result.errors << "Error processing transfer payment"
        }
        
        return result
    }
    
    /**
     * Update loan balances after payment
     */
    private void updateLoanBalances(Loan loan, Map allocationResult) {
        loan.balanceAmount -= allocationResult.principalPaid
        loan.interestBalanceAmount -= allocationResult.interestPaid
        loan.penaltyBalanceAmount -= allocationResult.penaltyPaid
        loan.serviceChargeBalanceAmount -= allocationResult.serviceChargePaid
        
        // Update transaction sequence and dates
        loan.transactionSequenceNo = (loan.transactionSequenceNo ?: 0) + 1
        loan.lastTransactionDate = new Date()
        loan.lastCustomerTransactionDate = new Date()
        
        loan.save(flush: true, failOnError: true)
    }
    
    /**
     * Update installments after payment
     */
    private void updateInstallments(Loan loan, Map allocationResult) {
        try {
            // Get unpaid installments in order
            List<LoanInstallment> unpaidInstallments = getUnpaidInstallments(loan)
            
            BigDecimal remainingPrincipal = allocationResult.principalPaid
            BigDecimal remainingInterest = allocationResult.interestPaid
            BigDecimal remainingPenalty = allocationResult.penaltyPaid
            BigDecimal remainingServiceCharge = allocationResult.serviceChargePaid
            
            for (LoanInstallment installment : unpaidInstallments) {
                if (remainingPrincipal <= 0 && remainingInterest <= 0 && 
                    remainingPenalty <= 0 && remainingServiceCharge <= 0) {
                    break
                }
                
                // Apply payments to installment
                applyPaymentToInstallment(installment, remainingPrincipal, remainingInterest, 
                                        remainingPenalty, remainingServiceCharge)
                
                // Update remaining amounts
                remainingPrincipal = [remainingPrincipal - installment.principalInstallmentAmount, BigDecimal.ZERO].max()
                remainingInterest = [remainingInterest - installment.interestInstallmentAmount, BigDecimal.ZERO].max()
                remainingPenalty = [remainingPenalty - installment.penaltyInstallmentAmount, BigDecimal.ZERO].max()
                remainingServiceCharge = [remainingServiceCharge - installment.serviceChargeInstallmentAmount, BigDecimal.ZERO].max()
                
                installment.save(flush: true, failOnError: true)
            }
            
        } catch (Exception e) {
            log.error("Error updating installments", e)
        }
    }
    
    /**
     * Apply payment to specific installment
     */
    private void applyPaymentToInstallment(LoanInstallment installment, BigDecimal principalPayment, 
                                         BigDecimal interestPayment, BigDecimal penaltyPayment, 
                                         BigDecimal serviceChargePayment) {
        
        // Apply service charge payment
        BigDecimal serviceChargeOwed = installment.serviceChargeInstallmentAmount - installment.serviceChargeInstallmentPaid
        BigDecimal serviceChargeApplied = [serviceChargePayment, serviceChargeOwed].min()
        installment.serviceChargeInstallmentPaid += serviceChargeApplied
        
        // Apply penalty payment
        BigDecimal penaltyOwed = installment.penaltyInstallmentAmount - installment.penaltyInstallmentPaid
        BigDecimal penaltyApplied = [penaltyPayment, penaltyOwed].min()
        installment.penaltyInstallmentPaid += penaltyApplied
        
        // Apply interest payment
        BigDecimal interestOwed = installment.interestInstallmentAmount - installment.interestInstallmentPaid
        BigDecimal interestApplied = [interestPayment, interestOwed].min()
        installment.interestInstallmentPaid += interestApplied
        
        // Apply principal payment
        BigDecimal principalOwed = installment.principalInstallmentAmount - installment.principalInstallmentPaid
        BigDecimal principalApplied = [principalPayment, principalOwed].min()
        installment.principalInstallmentPaid += principalApplied
        
        // Update installment status if fully paid
        BigDecimal totalOwed = principalOwed + interestOwed + penaltyOwed + serviceChargeOwed
        BigDecimal totalApplied = principalApplied + interestApplied + penaltyApplied + serviceChargeApplied
        
        if (totalApplied >= totalOwed) {
            installment.status = LoanInstallmentStatus.get(3) // Paid
            installment.paidDate = new Date()
        }
    }
    
    /**
     * Create payment record
     */
    private LoanPaymentDetails createPaymentRecord(Loan loan, Map paymentData, Map allocationResult) {
        LoanPaymentDetails payment = new LoanPaymentDetails(
            loan: loan,
            paymentAmount: paymentData.paymentAmount,
            principalPaid: allocationResult.principalPaid,
            interestPaid: allocationResult.interestPaid,
            penaltyPaid: allocationResult.penaltyPaid,
            serviceChargePaid: allocationResult.serviceChargePaid,
            paymentDate: new Date(),
            paymentType: paymentData.paymentType,
            branch: loan.branch,
            user: UserMaster.get(paymentData.userId ?: 1)
        )
        
        payment.save(flush: true, failOnError: true)
        return payment
    }
    
    /**
     * Create loan ledger entry
     */
    private void createLoanLedgerEntry(Loan loan, LoanPaymentDetails payment, Map allocationResult) {
        LoanLedger ledger = new LoanLedger(
            loan: loan,
            txnFile: payment.txnFile,
            txnDate: new Date(),
            txnTemplate: TxnTemplate.findByCode('LOAN_PAYMENT'),
            principalDebit: allocationResult.principalPaid,
            principalCredit: 0,
            principalBalance: loan.balanceAmount,
            interestDebit: allocationResult.interestPaid,
            interestCredit: 0,
            interestBalance: loan.interestBalanceAmount,
            txnRef: payment.txnFile?.txnRef
        )
        
        ledger.save(flush: true, failOnError: true)
    }
    
    /**
     * Create payment transaction file
     */
    private TxnFile createPaymentTxnFile(Loan loan, Map paymentData, Map allocationResult, String method) {
        TxnFile txnFile = new TxnFile(
            txnAmt: paymentData.paymentAmount,
            txnDate: new Date(),
            txnParticulars: "Loan Payment - ${method}",
            status: org.icbs.lov.ConfigItemStatus.get(2),
            branch: loan.branch,
            user: UserMaster.get(paymentData.userId ?: 1),
            currency: loan.currency,
            txnTemplate: TxnTemplate.findByCode('LOAN_PAYMENT'),
            loanAcct: loan
        )
        
        txnFile.save(flush: true, failOnError: true)
        return txnFile
    }
    
    /**
     * Update loan status if fully paid
     */
    private void updateLoanStatusIfFullyPaid(Loan loan) {
        BigDecimal totalOutstanding = (loan.balanceAmount ?: 0) + (loan.interestBalanceAmount ?: 0) + 
                                     (loan.penaltyBalanceAmount ?: 0) + (loan.serviceChargeBalanceAmount ?: 0)
        
        if (totalOutstanding <= 0.01) { // Allow for rounding differences
            loan.status = LoanAcctStatus.get(6) // Closed
            loan.statusChangedDate = new Date()
            loan.save(flush: true, failOnError: true)
        }
    }
    
    /**
     * Get next unpaid installment
     */
    private LoanInstallment getNextUnpaidInstallment(Loan loan) {
        return LoanInstallment.createCriteria().get {
            eq("loan", loan)
            ne("status", LoanInstallmentStatus.get(3)) // Not paid
            order("sequenceNo", "asc")
            maxResults(1)
        }
    }
    
    /**
     * Get unpaid installments
     */
    private List<LoanInstallment> getUnpaidInstallments(Loan loan) {
        return LoanInstallment.createCriteria().list {
            eq("loan", loan)
            ne("status", LoanInstallmentStatus.get(3)) // Not paid
            order("sequenceNo", "asc")
        }
    }
    
    /**
     * Calculate days overdue
     */
    private Integer calculateDaysOverdue(Date dueDate) {
        if (!dueDate || dueDate >= new Date()) {
            return 0
        }
        
        long diffInMillis = new Date().time - dueDate.time
        return (int) (diffInMillis / (24 * 60 * 60 * 1000))
    }
    
    /**
     * Calculate accrued interest
     */
    private BigDecimal calculateAccruedInterest(Loan loan, Date asOfDate) {
        try {
            // This would contain the actual accrued interest calculation logic
            // For now, returning zero as placeholder
            return BigDecimal.ZERO
        } catch (Exception e) {
            log.error("Error calculating accrued interest", e)
            return BigDecimal.ZERO
        }
    }
    
    /**
     * Audit loan payment
     */
    private void auditLoanPayment(Loan loan, LoanPaymentDetails payment, String result) {
        try {
            auditLogService.insert('130', 'LPS04000', 
                "Loan payment processed - Amount: ${payment.paymentAmount}, Type: ${payment.paymentType}", 
                'LoanPaymentService', null, null, null, loan.id)
        } catch (Exception e) {
            log.error("Error auditing loan payment", e)
        }
    }
}
