package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.loans.Loan
import org.icbs.loans.LoanInstallment
import org.icbs.loans.LoanApplication
import org.icbs.lov.LoanInstallmentFreq
import org.icbs.lov.LoanInstallmentType
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * REFACTORED: Loan Calculation Service
 * Extracted from LoanService.groovy (1,800+ lines)
 * Handles all loan calculation operations with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanCalculationService {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def auditLogService
    
    // =====================================================
    // LOAN CALCULATION OPERATIONS
    // =====================================================
    
    /**
     * Calculate loan installment amount
     */
    BigDecimal calculateInstallmentAmount(Loan loan) {
        try {
            log.debug("Calculating installment amount for loan: ${loan.accountNo}")
            
            if (!loan.grantedAmount || !loan.interestRate || !loan.numInstallments) {
                throw new IllegalArgumentException("Missing required loan parameters for calculation")
            }
            
            BigDecimal principal = loan.grantedAmount
            BigDecimal annualRate = loan.interestRate / 100
            Integer numPayments = loan.numInstallments
            
            // Calculate based on installment type
            BigDecimal installmentAmount = 0
            
            switch (loan.interestIncomeScheme?.installmentType?.id) {
                case 1: // Equal Principal
                    installmentAmount = calculateEqualPrincipalInstallment(principal, annualRate, numPayments)
                    break
                case 2: // Equal Installment (PMT)
                    installmentAmount = calculateEqualInstallment(principal, annualRate, numPayments, loan.frequency?.id)
                    break
                case 3: // Interest Only
                    installmentAmount = calculateInterestOnlyInstallment(principal, annualRate, loan.frequency?.id)
                    break
                case 4: // Balloon Payment
                    installmentAmount = calculateBalloonInstallment(principal, annualRate, numPayments, loan.frequency?.id)
                    break
                case 5: // Single Payment
                    installmentAmount = calculateSinglePaymentAmount(principal, annualRate, loan.term)
                    break
                default:
                    installmentAmount = calculateEqualInstallment(principal, annualRate, numPayments, loan.frequency?.id)
            }
            
            return installmentAmount.setScale(2, RoundingMode.HALF_UP)
            
        } catch (Exception e) {
            log.error("Error calculating installment amount", e)
            return BigDecimal.ZERO
        }
    }
    
    /**
     * Calculate total interest for loan
     */
    BigDecimal calculateTotalInterest(Loan loan) {
        try {
            BigDecimal totalInterest = 0
            
            if (loan.interestIncomeScheme?.installmentType?.id == 5) { // Single payment
                totalInterest = calculateSinglePaymentInterest(loan.grantedAmount, loan.interestRate, loan.term)
            } else {
                // Calculate from installment schedule
                BigDecimal installmentAmount = calculateInstallmentAmount(loan)
                BigDecimal totalPayments = installmentAmount * loan.numInstallments
                totalInterest = totalPayments - loan.grantedAmount
            }
            
            return totalInterest.setScale(2, RoundingMode.HALF_UP)
            
        } catch (Exception e) {
            log.error("Error calculating total interest", e)
            return BigDecimal.ZERO
        }
    }
    
    /**
     * Calculate effective interest rate
     */
    BigDecimal calculateEffectiveInterestRate(Loan loan) {
        try {
            BigDecimal nominalRate = loan.interestRate
            Integer compoundingFrequency = getCompoundingFrequency(loan.frequency?.id)
            
            // EIR = (1 + r/n)^n - 1
            BigDecimal rate = nominalRate / 100 / compoundingFrequency
            BigDecimal effectiveRate = (1 + rate).pow(compoundingFrequency) - 1
            
            return (effectiveRate * 100).setScale(4, RoundingMode.HALF_UP)
            
        } catch (Exception e) {
            log.error("Error calculating effective interest rate", e)
            return loan.interestRate
        }
    }
    
    /**
     * Calculate loan maturity date
     */
    Date calculateMaturityDate(Loan loan) {
        try {
            if (!loan.openingDate || !loan.term) {
                return null
            }
            
            Calendar calendar = Calendar.getInstance()
            calendar.setTime(loan.openingDate)
            
            Integer frequency = loan.frequency?.id ?: 1
            
            switch (frequency) {
                case 1: // Daily
                    calendar.add(Calendar.DAY_OF_MONTH, loan.term)
                    break
                case 2: // Weekly
                    calendar.add(Calendar.WEEK_OF_YEAR, loan.term)
                    break
                case 3: // Monthly
                    calendar.add(Calendar.MONTH, loan.term)
                    break
                case 4: // Quarterly
                    calendar.add(Calendar.MONTH, loan.term * 3)
                    break
                case 5: // Semi-annually
                    calendar.add(Calendar.MONTH, loan.term * 6)
                    break
                case 6: // Annually
                    calendar.add(Calendar.YEAR, loan.term)
                    break
                default:
                    calendar.add(Calendar.MONTH, loan.term)
            }
            
            return calendar.getTime()
            
        } catch (Exception e) {
            log.error("Error calculating maturity date", e)
            return null
        }
    }
    
    /**
     * Calculate payment allocation for loan payment
     */
    Map calculatePaymentAllocation(Loan loan, BigDecimal paymentAmount) {
        Map allocation = [
            principalPaid: BigDecimal.ZERO,
            interestPaid: BigDecimal.ZERO,
            penaltyPaid: BigDecimal.ZERO,
            serviceChargePaid: BigDecimal.ZERO,
            remainingPayment: paymentAmount
        ]
        
        try {
            BigDecimal remainingPayment = paymentAmount
            
            // Payment hierarchy: Service Charges -> Penalties -> Interest -> Principal
            
            // 1. Pay service charges first
            if (remainingPayment > 0 && loan.serviceChargeBalanceAmount > 0) {
                BigDecimal serviceChargePayment = remainingPayment.min(loan.serviceChargeBalanceAmount)
                allocation.serviceChargePaid = serviceChargePayment
                remainingPayment -= serviceChargePayment
            }
            
            // 2. Pay penalties
            if (remainingPayment > 0 && loan.penaltyBalanceAmount > 0) {
                BigDecimal penaltyPayment = remainingPayment.min(loan.penaltyBalanceAmount)
                allocation.penaltyPaid = penaltyPayment
                remainingPayment -= penaltyPayment
            }
            
            // 3. Pay interest
            if (remainingPayment > 0 && loan.interestBalanceAmount > 0) {
                BigDecimal interestPayment = remainingPayment.min(loan.interestBalanceAmount)
                allocation.interestPaid = interestPayment
                remainingPayment -= interestPayment
            }
            
            // 4. Pay principal
            if (remainingPayment > 0 && loan.balanceAmount > 0) {
                BigDecimal principalPayment = remainingPayment.min(loan.balanceAmount)
                allocation.principalPaid = principalPayment
                remainingPayment -= principalPayment
            }
            
            allocation.remainingPayment = remainingPayment
            
        } catch (Exception e) {
            log.error("Error calculating payment allocation", e)
        }
        
        return allocation
    }
    
    /**
     * Calculate loan amortization schedule
     */
    List<Map> calculateAmortizationSchedule(Loan loan) {
        List<Map> schedule = []
        
        try {
            BigDecimal principal = loan.grantedAmount
            BigDecimal monthlyRate = loan.interestRate / 100 / 12
            Integer numPayments = loan.numInstallments
            BigDecimal installmentAmount = calculateInstallmentAmount(loan)
            
            BigDecimal remainingBalance = principal
            Date paymentDate = loan.openingDate
            
            for (int i = 1; i <= numPayments; i++) {
                Map payment = [:]
                
                // Calculate interest for this period
                BigDecimal interestPayment = remainingBalance * monthlyRate
                BigDecimal principalPayment = installmentAmount - interestPayment
                
                // Adjust last payment if necessary
                if (i == numPayments) {
                    principalPayment = remainingBalance
                    installmentAmount = principalPayment + interestPayment
                }
                
                remainingBalance -= principalPayment
                
                payment.paymentNumber = i
                payment.paymentDate = calculateNextPaymentDate(paymentDate, loan.frequency?.id)
                payment.installmentAmount = installmentAmount.setScale(2, RoundingMode.HALF_UP)
                payment.principalPayment = principalPayment.setScale(2, RoundingMode.HALF_UP)
                payment.interestPayment = interestPayment.setScale(2, RoundingMode.HALF_UP)
                payment.remainingBalance = remainingBalance.setScale(2, RoundingMode.HALF_UP)
                
                schedule.add(payment)
                paymentDate = payment.paymentDate
            }
            
        } catch (Exception e) {
            log.error("Error calculating amortization schedule", e)
        }
        
        return schedule
    }
    
    /**
     * Calculate penalty amount
     */
    BigDecimal calculatePenaltyAmount(Loan loan, Date asOfDate = new Date()) {
        try {
            if (!loan.currentPenaltyScheme) {
                return BigDecimal.ZERO
            }
            
            // Get overdue installments
            List<LoanInstallment> overdueInstallments = getOverdueInstallments(loan, asOfDate)
            
            if (!overdueInstallments) {
                return BigDecimal.ZERO
            }
            
            BigDecimal totalPenalty = BigDecimal.ZERO
            
            for (LoanInstallment installment : overdueInstallments) {
                BigDecimal penaltyAmount = calculateInstallmentPenalty(loan, installment, asOfDate)
                totalPenalty += penaltyAmount
            }
            
            return totalPenalty.setScale(2, RoundingMode.HALF_UP)
            
        } catch (Exception e) {
            log.error("Error calculating penalty amount", e)
            return BigDecimal.ZERO
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Calculate equal principal installment
     */
    private BigDecimal calculateEqualPrincipalInstallment(BigDecimal principal, BigDecimal annualRate, Integer numPayments) {
        BigDecimal principalPayment = principal / numPayments
        BigDecimal interestPayment = principal * (annualRate / 12) // Assuming monthly payments
        return principalPayment + interestPayment
    }
    
    /**
     * Calculate equal installment (PMT formula)
     */
    private BigDecimal calculateEqualInstallment(BigDecimal principal, BigDecimal annualRate, Integer numPayments, Integer frequency) {
        BigDecimal periodicRate = getPeriodicRate(annualRate, frequency)
        
        if (periodicRate == 0) {
            return principal / numPayments
        }
        
        BigDecimal numerator = periodicRate * Math.pow(1 + periodicRate, numPayments)
        BigDecimal denominator = Math.pow(1 + periodicRate, numPayments) - 1
        
        return principal * (numerator / denominator)
    }
    
    /**
     * Calculate interest only installment
     */
    private BigDecimal calculateInterestOnlyInstallment(BigDecimal principal, BigDecimal annualRate, Integer frequency) {
        BigDecimal periodicRate = getPeriodicRate(annualRate, frequency)
        return principal * periodicRate
    }
    
    /**
     * Calculate balloon installment
     */
    private BigDecimal calculateBalloonInstallment(BigDecimal principal, BigDecimal annualRate, Integer numPayments, Integer frequency) {
        // For balloon payments, typically only interest is paid during the term
        return calculateInterestOnlyInstallment(principal, annualRate, frequency)
    }
    
    /**
     * Calculate single payment amount
     */
    private BigDecimal calculateSinglePaymentAmount(BigDecimal principal, BigDecimal annualRate, Integer termInMonths) {
        BigDecimal rate = annualRate / 100 / 12
        BigDecimal amount = principal * Math.pow(1 + rate, termInMonths)
        return amount
    }
    
    /**
     * Calculate single payment interest
     */
    private BigDecimal calculateSinglePaymentInterest(BigDecimal principal, BigDecimal annualRate, Integer termInMonths) {
        BigDecimal totalAmount = calculateSinglePaymentAmount(principal, annualRate, termInMonths)
        return totalAmount - principal
    }
    
    /**
     * Get periodic interest rate based on frequency
     */
    private BigDecimal getPeriodicRate(BigDecimal annualRate, Integer frequency) {
        switch (frequency) {
            case 1: // Daily
                return annualRate / 365
            case 2: // Weekly
                return annualRate / 52
            case 3: // Monthly
                return annualRate / 12
            case 4: // Quarterly
                return annualRate / 4
            case 5: // Semi-annually
                return annualRate / 2
            case 6: // Annually
                return annualRate
            default:
                return annualRate / 12 // Default to monthly
        }
    }
    
    /**
     * Get compounding frequency
     */
    private Integer getCompoundingFrequency(Integer frequency) {
        switch (frequency) {
            case 1: // Daily
                return 365
            case 2: // Weekly
                return 52
            case 3: // Monthly
                return 12
            case 4: // Quarterly
                return 4
            case 5: // Semi-annually
                return 2
            case 6: // Annually
                return 1
            default:
                return 12 // Default to monthly
        }
    }
    
    /**
     * Calculate next payment date
     */
    private Date calculateNextPaymentDate(Date currentDate, Integer frequency) {
        Calendar calendar = Calendar.getInstance()
        calendar.setTime(currentDate)
        
        switch (frequency) {
            case 1: // Daily
                calendar.add(Calendar.DAY_OF_MONTH, 1)
                break
            case 2: // Weekly
                calendar.add(Calendar.WEEK_OF_YEAR, 1)
                break
            case 3: // Monthly
                calendar.add(Calendar.MONTH, 1)
                break
            case 4: // Quarterly
                calendar.add(Calendar.MONTH, 3)
                break
            case 5: // Semi-annually
                calendar.add(Calendar.MONTH, 6)
                break
            case 6: // Annually
                calendar.add(Calendar.YEAR, 1)
                break
            default:
                calendar.add(Calendar.MONTH, 1)
        }
        
        return calendar.getTime()
    }
    
    /**
     * Get overdue installments
     */
    private List<LoanInstallment> getOverdueInstallments(Loan loan, Date asOfDate) {
        return LoanInstallment.createCriteria().list {
            eq("loan", loan)
            lt("dueDate", asOfDate)
            gt("balanceAmount", 0)
            order("dueDate", "asc")
        }
    }
    
    /**
     * Calculate penalty for specific installment
     */
    private BigDecimal calculateInstallmentPenalty(Loan loan, LoanInstallment installment, Date asOfDate) {
        try {
            Integer daysOverdue = calculateDaysOverdue(installment.dueDate, asOfDate)
            
            if (daysOverdue <= 0) {
                return BigDecimal.ZERO
            }
            
            BigDecimal penaltyAmount = BigDecimal.ZERO
            
            if (loan.currentPenaltyScheme.type.id == 1) { // Fixed amount
                penaltyAmount = loan.penaltyAmount ?: BigDecimal.ZERO
            } else if (loan.currentPenaltyScheme.type.id == 2) { // Rate based
                BigDecimal penaltyRate = loan.penaltyRate / 100
                penaltyAmount = installment.balanceAmount * penaltyRate * daysOverdue / 365
            }
            
            return penaltyAmount.setScale(2, RoundingMode.HALF_UP)
            
        } catch (Exception e) {
            log.error("Error calculating installment penalty", e)
            return BigDecimal.ZERO
        }
    }
    
    /**
     * Calculate days overdue
     */
    private Integer calculateDaysOverdue(Date dueDate, Date asOfDate) {
        if (!dueDate || !asOfDate || asOfDate <= dueDate) {
            return 0
        }
        
        long diffInMillis = asOfDate.time - dueDate.time
        return (int) (diffInMillis / (24 * 60 * 60 * 1000))
    }
}
