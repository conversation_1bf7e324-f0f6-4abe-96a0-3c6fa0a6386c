package org.icbs.loans

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.icbs.loans.Loan
import org.icbs.loans.LoanApplication
import org.icbs.admin.Product
import org.icbs.admin.Branch
import org.icbs.admin.Currency
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecurityAuditService
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * REFACTORED: Loan Utility Service
 * Extracted from LoanService.groovy (1,800+ lines)
 * Handles utility operations and helper functions with modern patterns
 * 
 * <AUTHOR> Development Team
 * @version 2.0 - Refactored for Phase II
 * @since Grails 6.2.3
 */
@Transactional
@Slf4j
class LoanUtilityService {
    
    UnifiedValidationService unifiedValidationService
    SecurityAuditService securityAuditService
    def auditLogService
    
    // =====================================================
    // UTILITY OPERATIONS
    // =====================================================
    
    /**
     * Generate loan account number
     */
    String generateLoanAccountNumber(Loan loan) {
        try {
            log.debug("Generating loan account number for loan: ${loan.id}")
            
            String branchCode = loan.branch?.code ?: '001'
            String productCode = loan.product?.code ?: 'LN'
            String year = new Date().format('yy')
            
            // Get next sequence number
            Integer sequence = getNextLoanSequence(loan.branch, loan.product)
            String sequenceStr = String.format('%06d', sequence)
            
            return "${branchCode}${productCode}${year}${sequenceStr}"
            
        } catch (Exception e) {
            log.error("Error generating loan account number", e)
            return "LN${System.currentTimeMillis()}"
        }
    }
    
    /**
     * Generate application reference number
     */
    String generateApplicationReference(LoanApplication application) {
        try {
            log.debug("Generating application reference for application: ${application.id}")
            
            String branchCode = application.branch?.code ?: '001'
            String year = new Date().format('yyyy')
            String month = new Date().format('MM')
            
            // Get next sequence number for applications
            Integer sequence = getNextApplicationSequence(application.branch)
            String sequenceStr = String.format('%05d', sequence)
            
            return "APP${branchCode}${year}${month}${sequenceStr}"
            
        } catch (Exception e) {
            log.error("Error generating application reference", e)
            return "APP${System.currentTimeMillis()}"
        }
    }
    
    /**
     * Format currency amount
     */
    String formatCurrencyAmount(BigDecimal amount, Currency currency = null) {
        try {
            if (!amount) {
                return "0.00"
            }
            
            String currencySymbol = currency?.symbol ?: ''
            String formattedAmount = String.format("%,.2f", amount)
            
            return "${currencySymbol}${formattedAmount}".trim()
            
        } catch (Exception e) {
            log.error("Error formatting currency amount", e)
            return amount?.toString() ?: "0.00"
        }
    }
    
    /**
     * Parse currency amount from string
     */
    BigDecimal parseCurrencyAmount(String amountStr) {
        try {
            if (!amountStr) {
                return BigDecimal.ZERO
            }
            
            // Remove currency symbols and formatting
            String cleanAmount = amountStr.replaceAll(/[^\d.-]/, '')
            return new BigDecimal(cleanAmount).setScale(2, RoundingMode.HALF_UP)
            
        } catch (Exception e) {
            log.error("Error parsing currency amount: ${amountStr}", e)
            return BigDecimal.ZERO
        }
    }
    
    /**
     * Calculate age from date of birth
     */
    Integer calculateAge(Date dateOfBirth) {
        try {
            if (!dateOfBirth) {
                return 0
            }
            
            Calendar birthCalendar = Calendar.getInstance()
            birthCalendar.setTime(dateOfBirth)
            
            Calendar todayCalendar = Calendar.getInstance()
            
            int age = todayCalendar.get(Calendar.YEAR) - birthCalendar.get(Calendar.YEAR)
            
            if (todayCalendar.get(Calendar.DAY_OF_YEAR) < birthCalendar.get(Calendar.DAY_OF_YEAR)) {
                age--
            }
            
            return age
            
        } catch (Exception e) {
            log.error("Error calculating age", e)
            return 0
        }
    }
    
    /**
     * Validate CNIC/ID number
     */
    Map validateIdNumber(String idNumber, String idType = 'CNIC') {
        Map result = [isValid: true, errors: [], formattedId: '']
        
        try {
            if (!idNumber) {
                result.isValid = false
                result.errors << "ID number is required"
                return result
            }
            
            switch (idType) {
                case 'CNIC':
                    result = validateCNIC(idNumber)
                    break
                case 'PASSPORT':
                    result = validatePassport(idNumber)
                    break
                case 'DRIVING_LICENSE':
                    result = validateDrivingLicense(idNumber)
                    break
                default:
                    result = validateGenericId(idNumber)
            }
            
        } catch (Exception e) {
            log.error("Error validating ID number", e)
            result.isValid = false
            result.errors << "Error validating ID number"
        }
        
        return result
    }
    
    /**
     * Generate loan summary
     */
    Map generateLoanSummary(Loan loan) {
        Map summary = [:]
        
        try {
            summary.basicInfo = [
                accountNumber: loan.accountNo,
                customerName: loan.customer?.fullName,
                productName: loan.product?.name,
                branchName: loan.branch?.name,
                status: loan.status?.description
            ]
            
            summary.amounts = [
                grantedAmount: loan.grantedAmount,
                balanceAmount: loan.balanceAmount,
                interestBalance: loan.interestBalanceAmount,
                penaltyBalance: loan.penaltyBalanceAmount,
                totalOutstanding: calculateTotalOutstanding(loan)
            ]
            
            summary.terms = [
                interestRate: loan.interestRate,
                term: loan.term,
                frequency: loan.frequency?.description,
                installmentAmount: loan.installmentAmount,
                openingDate: loan.openingDate,
                maturityDate: loan.maturityDate
            ]
            
            summary.performance = [
                daysOverdue: calculateDaysOverdue(loan),
                paymentHistory: getPaymentHistorySummary(loan),
                lastPaymentDate: getLastPaymentDate(loan)
            ]
            
        } catch (Exception e) {
            log.error("Error generating loan summary", e)
            summary.error = "Error generating loan summary"
        }
        
        return summary
    }
    
    /**
     * Get system configuration
     */
    Map getSystemConfiguration() {
        Map config = [:]
        
        try {
            config.dateFormats = [
                short: 'dd/MM/yyyy',
                long: 'dd MMMM yyyy',
                timestamp: 'dd/MM/yyyy HH:mm:ss'
            ]
            
            config.currencyFormats = [
                decimalPlaces: 2,
                thousandSeparator: ',',
                decimalSeparator: '.'
            ]
            
            config.businessRules = [
                maxLoanAmount: new BigDecimal("10000000"),
                minLoanAmount: new BigDecimal("1000"),
                maxLoanTerm: 360, // months
                minLoanTerm: 1,
                maxInterestRate: new BigDecimal("30.00"),
                minInterestRate: new BigDecimal("1.00")
            ]
            
            config.workingDays = [
                monday: true,
                tuesday: true,
                wednesday: true,
                thursday: true,
                friday: true,
                saturday: false,
                sunday: false
            ]
            
        } catch (Exception e) {
            log.error("Error getting system configuration", e)
            config.error = "Error getting system configuration"
        }
        
        return config
    }
    
    /**
     * Calculate business days between dates
     */
    Integer calculateBusinessDays(Date startDate, Date endDate) {
        try {
            if (!startDate || !endDate || startDate >= endDate) {
                return 0
            }
            
            Calendar start = Calendar.getInstance()
            start.setTime(startDate)
            
            Calendar end = Calendar.getInstance()
            end.setTime(endDate)
            
            Integer businessDays = 0
            
            while (start.before(end)) {
                int dayOfWeek = start.get(Calendar.DAY_OF_WEEK)
                
                // Skip weekends (Saturday = 7, Sunday = 1)
                if (dayOfWeek != Calendar.SATURDAY && dayOfWeek != Calendar.SUNDAY) {
                    businessDays++
                }
                
                start.add(Calendar.DAY_OF_MONTH, 1)
            }
            
            return businessDays
            
        } catch (Exception e) {
            log.error("Error calculating business days", e)
            return 0
        }
    }
    
    /**
     * Get next business date
     */
    Date getNextBusinessDate(Date fromDate = new Date(), Integer daysToAdd = 1) {
        try {
            Calendar calendar = Calendar.getInstance()
            calendar.setTime(fromDate)
            
            Integer addedDays = 0
            
            while (addedDays < daysToAdd) {
                calendar.add(Calendar.DAY_OF_MONTH, 1)
                
                int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
                
                // Skip weekends
                if (dayOfWeek != Calendar.SATURDAY && dayOfWeek != Calendar.SUNDAY) {
                    addedDays++
                }
            }
            
            return calendar.getTime()
            
        } catch (Exception e) {
            log.error("Error getting next business date", e)
            return fromDate
        }
    }
    
    /**
     * Validate email address
     */
    boolean isValidEmail(String email) {
        try {
            if (!email) {
                return false
            }
            
            String emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
            return email.matches(emailPattern)
            
        } catch (Exception e) {
            log.error("Error validating email", e)
            return false
        }
    }
    
    /**
     * Validate phone number
     */
    boolean isValidPhoneNumber(String phoneNumber) {
        try {
            if (!phoneNumber) {
                return false
            }
            
            // Remove all non-digit characters
            String cleanNumber = phoneNumber.replaceAll(/\D/, '')
            
            // Check length (assuming 10-15 digits)
            return cleanNumber.length() >= 10 && cleanNumber.length() <= 15
            
        } catch (Exception e) {
            log.error("Error validating phone number", e)
            return false
        }
    }
    
    /**
     * Generate random password
     */
    String generateRandomPassword(Integer length = 8) {
        try {
            String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#\$%^&*"
            Random random = new Random()
            StringBuilder password = new StringBuilder()
            
            for (int i = 0; i < length; i++) {
                password.append(chars.charAt(random.nextInt(chars.length())))
            }
            
            return password.toString()
            
        } catch (Exception e) {
            log.error("Error generating random password", e)
            return "Password123!"
        }
    }
    
    // =====================================================
    // PRIVATE HELPER METHODS
    // =====================================================
    
    /**
     * Get next loan sequence number
     */
    private Integer getNextLoanSequence(Branch branch, Product product) {
        try {
            // This would query the database for the next sequence
            // For now, returning a simple increment
            Integer lastSequence = Loan.createCriteria().get {
                eq("branch", branch)
                eq("product", product)
                projections {
                    max("id")
                }
            } ?: 0
            
            return lastSequence + 1
            
        } catch (Exception e) {
            log.error("Error getting next loan sequence", e)
            return 1
        }
    }
    
    /**
     * Get next application sequence number
     */
    private Integer getNextApplicationSequence(Branch branch) {
        try {
            Integer lastSequence = LoanApplication.createCriteria().get {
                eq("branch", branch)
                projections {
                    max("id")
                }
            } ?: 0
            
            return lastSequence + 1
            
        } catch (Exception e) {
            log.error("Error getting next application sequence", e)
            return 1
        }
    }
    
    /**
     * Validate CNIC
     */
    private Map validateCNIC(String cnic) {
        Map result = [isValid: true, errors: [], formattedId: '']
        
        try {
            // Remove all non-digit characters
            String cleanCnic = cnic.replaceAll(/\D/, '')
            
            if (cleanCnic.length() != 13) {
                result.isValid = false
                result.errors << "CNIC must be 13 digits"
                return result
            }
            
            // Format as XXXXX-XXXXXXX-X
            result.formattedId = "${cleanCnic.substring(0, 5)}-${cleanCnic.substring(5, 12)}-${cleanCnic.substring(12)}"
            
        } catch (Exception e) {
            log.error("Error validating CNIC", e)
            result.isValid = false
            result.errors << "Invalid CNIC format"
        }
        
        return result
    }
    
    /**
     * Validate passport
     */
    private Map validatePassport(String passport) {
        Map result = [isValid: true, errors: [], formattedId: passport?.toUpperCase()]
        
        try {
            if (!passport || passport.length() < 6 || passport.length() > 12) {
                result.isValid = false
                result.errors << "Passport number must be 6-12 characters"
            }
            
        } catch (Exception e) {
            log.error("Error validating passport", e)
            result.isValid = false
            result.errors << "Invalid passport format"
        }
        
        return result
    }
    
    /**
     * Validate driving license
     */
    private Map validateDrivingLicense(String license) {
        Map result = [isValid: true, errors: [], formattedId: license?.toUpperCase()]
        
        try {
            if (!license || license.length() < 8 || license.length() > 15) {
                result.isValid = false
                result.errors << "Driving license must be 8-15 characters"
            }
            
        } catch (Exception e) {
            log.error("Error validating driving license", e)
            result.isValid = false
            result.errors << "Invalid driving license format"
        }
        
        return result
    }
    
    /**
     * Validate generic ID
     */
    private Map validateGenericId(String id) {
        Map result = [isValid: true, errors: [], formattedId: id]
        
        try {
            if (!id || id.trim().length() < 3) {
                result.isValid = false
                result.errors << "ID must be at least 3 characters"
            }
            
        } catch (Exception e) {
            log.error("Error validating generic ID", e)
            result.isValid = false
            result.errors << "Invalid ID format"
        }
        
        return result
    }
    
    /**
     * Calculate total outstanding for loan
     */
    private BigDecimal calculateTotalOutstanding(Loan loan) {
        return (loan.balanceAmount ?: 0) + (loan.interestBalanceAmount ?: 0) + 
               (loan.penaltyBalanceAmount ?: 0) + (loan.serviceChargeBalanceAmount ?: 0)
    }
    
    /**
     * Calculate days overdue for loan
     */
    private Integer calculateDaysOverdue(Loan loan) {
        try {
            // This would calculate based on overdue installments
            return 0 // Placeholder
        } catch (Exception e) {
            log.error("Error calculating days overdue", e)
            return 0
        }
    }
    
    /**
     * Get payment history summary
     */
    private Map getPaymentHistorySummary(Loan loan) {
        try {
            return [
                totalPayments: 0,
                onTimePayments: 0,
                latePayments: 0,
                averagePaymentAmount: BigDecimal.ZERO
            ]
        } catch (Exception e) {
            log.error("Error getting payment history summary", e)
            return [:]
        }
    }
    
    /**
     * Get last payment date
     */
    private Date getLastPaymentDate(Loan loan) {
        try {
            // This would query the last payment date
            return null // Placeholder
        } catch (Exception e) {
            log.error("Error getting last payment date", e)
            return null
        }
    }
}
