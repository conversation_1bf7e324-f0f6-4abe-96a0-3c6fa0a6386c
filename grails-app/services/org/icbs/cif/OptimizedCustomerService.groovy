package org.icbs.cif

import grails.gorm.transactions.Transactional
import grails.gorm.DetachedCriteria
import org.hibernate.FetchMode
import org.springframework.cache.annotation.Cacheable
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.CachePut

@Transactional
class OptimizedCustomerService {

    /**
     * PERFORMANCE OPTIMIZED: Find customers with eager loading to prevent N+1 queries
     */
    @Cacheable(value = "customers", key = "#branchId + '_' + #status + '_' + #offset + '_' + #max")
    List<Customer> findCustomersOptimized(Long branchId = null, String status = null, int offset = 0, int max = 20) {
        return Customer.createCriteria().list(max: max, offset: offset) {
            // PERFORMANCE FIX: Use fetch joins to prevent N+1 queries
            fetchMode 'deposits', FetchMode.JOIN
            fetchMode 'loans', FetchMode.JOIN
            fetchMode 'addresses', FetchMode.JOIN
            fetchMode 'contacts', FetchMode.JOIN
            fetchMode 'branch', FetchMode.JOIN
            fetchMode 'type', FetchMode.JOIN
            fetchMode 'status', FetchMode.JOIN
            
            if (branchId) {
                eq('branch.id', branchId)
            }
            if (status) {
                eq('status.itemValue', status)
            }
            
            order('lastUpdatedAt', 'desc')
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Find customer by ID with all relationships loaded
     */
    @Cacheable(value = "customer", key = "#customerId")
    Customer findCustomerByIdOptimized(Long customerId) {
        return Customer.createCriteria().get {
            eq('id', customerId)
            
            // PERFORMANCE FIX: Eagerly load all relationships
            fetchMode 'deposits', FetchMode.JOIN
            fetchMode 'loans', FetchMode.JOIN
            fetchMode 'addresses', FetchMode.JOIN
            fetchMode 'contacts', FetchMode.JOIN
            fetchMode 'employments', FetchMode.JOIN
            fetchMode 'businesses', FetchMode.JOIN
            fetchMode 'educations', FetchMode.JOIN
            fetchMode 'relations', FetchMode.JOIN
            fetchMode 'branch', FetchMode.JOIN
            fetchMode 'type', FetchMode.JOIN
            fetchMode 'status', FetchMode.JOIN
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Find customer by customer ID with minimal data
     */
    @Cacheable(value = "customerBasic", key = "#customerIdStr")
    Customer findByCustomerIdOptimized(String customerIdStr) {
        return Customer.createCriteria().get {
            eq('customerId', customerIdStr)
            
            // Load only essential relationships
            fetchMode 'branch', FetchMode.JOIN
            fetchMode 'type', FetchMode.JOIN
            fetchMode 'status', FetchMode.JOIN
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Search customers with pagination and caching
     */
    @Cacheable(value = "customerSearch", key = "#searchTerm + '_' + #offset + '_' + #max")
    Map searchCustomersOptimized(String searchTerm, int offset = 0, int max = 20) {
        def criteria = Customer.createCriteria()
        
        def results = criteria.list(max: max, offset: offset) {
            or {
                ilike('name1', "%${searchTerm}%")
                ilike('name2', "%${searchTerm}%")
                ilike('name3', "%${searchTerm}%")
                ilike('customerId', "%${searchTerm}%")
                ilike('displayName', "%${searchTerm}%")
            }
            
            // PERFORMANCE FIX: Load essential relationships only
            fetchMode 'branch', FetchMode.JOIN
            fetchMode 'type', FetchMode.JOIN
            fetchMode 'status', FetchMode.JOIN
            
            order('displayName', 'asc')
        }
        
        def totalCount = criteria.count {
            or {
                ilike('name1', "%${searchTerm}%")
                ilike('name2', "%${searchTerm}%")
                ilike('name3', "%${searchTerm}%")
                ilike('customerId', "%${searchTerm}%")
                ilike('displayName', "%${searchTerm}%")
            }
        }
        
        return [
            customers: results,
            totalCount: totalCount,
            hasMore: (offset + max) < totalCount
        ]
    }

    /**
     * PERFORMANCE OPTIMIZED: Get customer summary with aggregated data
     */
    @Cacheable(value = "customerSummary", key = "#customerId")
    Map getCustomerSummaryOptimized(Long customerId) {
        def customer = Customer.get(customerId)
        if (!customer) {
            return null
        }
        
        // Use efficient queries for aggregated data
        def depositCount = customer.deposits?.size() ?: 0
        def loanCount = customer.loans?.size() ?: 0
        
        def totalDepositBalance = customer.deposits?.sum { it.ledgerBalAmt ?: 0 } ?: 0
        def totalLoanBalance = customer.loans?.sum { it.outstandingPrincipalBalance ?: 0 } ?: 0
        
        return [
            customer: customer,
            depositCount: depositCount,
            loanCount: loanCount,
            totalDepositBalance: totalDepositBalance,
            totalLoanBalance: totalLoanBalance,
            netWorth: totalDepositBalance - totalLoanBalance
        ]
    }

    /**
     * PERFORMANCE OPTIMIZED: Create customer with proper caching
     */
    @CacheEvict(value = ["customers", "customerSearch"], allEntries = true)
    Customer createCustomerOptimized(Customer customer) {
        customer.save(flush: true)
        
        // Cache the new customer
        cacheCustomer(customer)
        
        return customer
    }

    /**
     * PERFORMANCE OPTIMIZED: Update customer with cache management
     */
    @CacheEvict(value = ["customers", "customerSearch", "customer", "customerBasic", "customerSummary"], 
                key = "#customer.id")
    Customer updateCustomerOptimized(Customer customer) {
        customer.save(flush: true)
        
        // Update cache
        cacheCustomer(customer)
        
        return customer
    }

    /**
     * PERFORMANCE OPTIMIZED: Delete customer with cache cleanup
     */
    @CacheEvict(value = ["customers", "customerSearch", "customer", "customerBasic", "customerSummary"], 
                key = "#customerId")
    boolean deleteCustomerOptimized(Long customerId) {
        def customer = Customer.get(customerId)
        if (customer) {
            customer.delete(flush: true)
            return true
        }
        return false
    }

    /**
     * PERFORMANCE OPTIMIZED: Get customers by branch with caching
     */
    @Cacheable(value = "customersByBranch", key = "#branchId + '_' + #max")
    List<Customer> getCustomersByBranchOptimized(Long branchId, int max = 50) {
        return Customer.createCriteria().list(max: max) {
            eq('branch.id', branchId)
            
            fetchMode 'type', FetchMode.JOIN
            fetchMode 'status', FetchMode.JOIN
            
            order('displayName', 'asc')
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Get active customers count by branch
     */
    @Cacheable(value = "activeCustomerCount", key = "#branchId")
    Long getActiveCustomerCountOptimized(Long branchId) {
        return Customer.createCriteria().count {
            eq('branch.id', branchId)
            eq('status.id', 2L) // Assuming 2 is active status
        }
    }

    /**
     * Helper method to cache customer data
     */
    @CachePut(value = "customer", key = "#customer.id")
    private Customer cacheCustomer(Customer customer) {
        return customer
    }

    /**
     * PERFORMANCE OPTIMIZED: Bulk operations for better performance
     */
    @CacheEvict(value = ["customers", "customerSearch"], allEntries = true)
    void bulkUpdateCustomerStatus(List<Long> customerIds, Long statusId) {
        Customer.executeUpdate(
            "UPDATE Customer c SET c.status.id = :statusId, c.lastUpdatedAt = :now WHERE c.id IN (:ids)",
            [statusId: statusId, now: new Date(), ids: customerIds]
        )
    }

    /**
     * PERFORMANCE OPTIMIZED: Get customer statistics
     */
    @Cacheable(value = "customerStats", key = "#branchId")
    Map getCustomerStatsOptimized(Long branchId = null) {
        def criteria = Customer.createCriteria()
        
        def stats = criteria.list {
            if (branchId) {
                eq('branch.id', branchId)
            }
            
            projections {
                groupProperty('status.id')
                count('id')
            }
        }
        
        def result = [:]
        stats.each { row ->
            result[row[0]] = row[1]
        }
        
        return result
    }
}
