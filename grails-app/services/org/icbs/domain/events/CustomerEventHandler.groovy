package org.icbs.domain.events

import grails.gorm.transactions.Transactional
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component

/**
 * ARCHITECTURE MODERNIZATION: Event Handler for Customer Domain Events
 * Handles side effects and integrations when customer events occur
 */
@Component
@Transactional
class CustomerEventHandler {

    def notificationService
    def auditService
    def integrationService
    def cacheManagementService
    def reportingService

    /**
     * Handle customer creation event
     */
    @EventListener
    @Async
    void handleCustomerCreated(CustomerCreatedEvent event) {
        try {
            log.info("Processing customer created event: ${event}")
            
            // Send welcome notification
            sendWelcomeNotification(event.customer)
            
            // Update reporting data
            updateCustomerReports(event.customer)
            
            // Clear relevant caches
            clearCustomerCaches(event.customer.branch.id)
            
            // Integrate with external systems
            notifyExternalSystems(event)
            
            // Record event in audit log
            auditService.recordDomainEvent(event.eventData)
            
            log.info("Customer created event processed successfully: ${event.customer.customerId}")
            
        } catch (Exception e) {
            log.error("Error processing customer created event: ${e.message}", e)
            // Could implement retry logic or dead letter queue here
        }
    }

    /**
     * Handle customer updated event
     */
    @EventListener
    @Async
    void handleCustomerUpdated(CustomerUpdatedEvent event) {
        try {
            log.info("Processing customer updated event: ${event}")
            
            // Send update notification if significant changes
            if (hasSignificantChanges(event.changes)) {
                sendUpdateNotification(event.customer, event.changes)
            }
            
            // Update search indexes
            updateSearchIndexes(event.customer)
            
            // Clear customer-specific caches
            clearCustomerSpecificCaches(event.customer.id)
            
            // Sync with external systems
            syncCustomerData(event.customer, event.changes)
            
            // Record event in audit log
            auditService.recordDomainEvent(event.eventData)
            
            log.info("Customer updated event processed successfully: ${event.customer.customerId}")
            
        } catch (Exception e) {
            log.error("Error processing customer updated event: ${e.message}", e)
        }
    }

    /**
     * Handle customer activation event
     */
    @EventListener
    @Async
    void handleCustomerActivated(CustomerActivatedEvent event) {
        try {
            log.info("Processing customer activated event: ${event}")
            
            // Send activation confirmation
            sendActivationNotification(event.customer)
            
            // Enable customer services
            enableCustomerServices(event.customer)
            
            // Update customer statistics
            updateCustomerStatistics(event.customer.branch.id)
            
            // Generate welcome package
            generateWelcomePackage(event.customer)
            
            // Clear caches
            clearCustomerCaches(event.customer.branch.id)
            
            // Record event in audit log
            auditService.recordDomainEvent(event.eventData)
            
            log.info("Customer activated event processed successfully: ${event.customer.customerId}")
            
        } catch (Exception e) {
            log.error("Error processing customer activated event: ${e.message}", e)
        }
    }

    /**
     * Handle customer deactivation event
     */
    @EventListener
    @Async
    void handleCustomerDeactivated(CustomerDeactivatedEvent event) {
        try {
            log.info("Processing customer deactivated event: ${event}")
            
            // Send deactivation notification
            sendDeactivationNotification(event.customer, event.reason)
            
            // Disable customer services
            disableCustomerServices(event.customer)
            
            // Archive customer data
            archiveCustomerData(event.customer)
            
            // Update customer statistics
            updateCustomerStatistics(event.customer.branch.id)
            
            // Clear caches
            clearCustomerCaches(event.customer.branch.id)
            
            // Record event in audit log
            auditService.recordDomainEvent(event.eventData)
            
            log.info("Customer deactivated event processed successfully: ${event.customer.customerId}")
            
        } catch (Exception e) {
            log.error("Error processing customer deactivated event: ${e.message}", e)
        }
    }

    /**
     * Send welcome notification to new customer
     */
    private void sendWelcomeNotification(Customer customer) {
        try {
            def message = [
                to: customer.email,
                subject: "Welcome to QwikBanka",
                template: "customer_welcome",
                data: [
                    customerName: customer.displayName,
                    customerNumber: customer.customerId,
                    branchName: customer.branch.branchName
                ]
            ]
            
            notificationService.sendEmail(message)
            
            // Also send SMS if phone number available
            if (customer.phoneNumber) {
                def smsMessage = "Welcome to QwikBanka! Your customer number is ${customer.customerId}. Thank you for choosing us."
                notificationService.sendSMS(customer.phoneNumber, smsMessage)
            }
            
        } catch (Exception e) {
            log.error("Error sending welcome notification: ${e.message}", e)
        }
    }

    /**
     * Send update notification for significant changes
     */
    private void sendUpdateNotification(Customer customer, Map changes) {
        try {
            if (customer.email) {
                def message = [
                    to: customer.email,
                    subject: "Account Information Updated",
                    template: "customer_update",
                    data: [
                        customerName: customer.displayName,
                        customerNumber: customer.customerId,
                        changes: changes
                    ]
                ]
                
                notificationService.sendEmail(message)
            }
        } catch (Exception e) {
            log.error("Error sending update notification: ${e.message}", e)
        }
    }

    /**
     * Send activation notification
     */
    private void sendActivationNotification(Customer customer) {
        try {
            if (customer.email) {
                def message = [
                    to: customer.email,
                    subject: "Account Activated - QwikBanka",
                    template: "customer_activation",
                    data: [
                        customerName: customer.displayName,
                        customerNumber: customer.customerId,
                        activationDate: customer.dateActivated
                    ]
                ]
                
                notificationService.sendEmail(message)
            }
        } catch (Exception e) {
            log.error("Error sending activation notification: ${e.message}", e)
        }
    }

    /**
     * Send deactivation notification
     */
    private void sendDeactivationNotification(Customer customer, String reason) {
        try {
            if (customer.email) {
                def message = [
                    to: customer.email,
                    subject: "Account Deactivated - QwikBanka",
                    template: "customer_deactivation",
                    data: [
                        customerName: customer.displayName,
                        customerNumber: customer.customerId,
                        reason: reason,
                        deactivationDate: customer.dateDeactivated
                    ]
                ]
                
                notificationService.sendEmail(message)
            }
        } catch (Exception e) {
            log.error("Error sending deactivation notification: ${e.message}", e)
        }
    }

    /**
     * Check if changes are significant enough to notify
     */
    private boolean hasSignificantChanges(Map changes) {
        def significantFields = ['email', 'status', 'firstName', 'lastName']
        return changes.keySet().any { it in significantFields }
    }

    /**
     * Update customer reports and statistics
     */
    private void updateCustomerReports(Customer customer) {
        try {
            reportingService.updateCustomerStatistics(customer.branch.id)
            reportingService.refreshCustomerReports()
        } catch (Exception e) {
            log.error("Error updating customer reports: ${e.message}", e)
        }
    }

    /**
     * Update search indexes for customer
     */
    private void updateSearchIndexes(Customer customer) {
        try {
            // Implementation would depend on search technology used
            // Could be Elasticsearch, Solr, or database full-text search
            log.info("Updating search indexes for customer: ${customer.customerId}")
        } catch (Exception e) {
            log.error("Error updating search indexes: ${e.message}", e)
        }
    }

    /**
     * Clear customer-related caches
     */
    private void clearCustomerCaches(Long branchId) {
        try {
            cacheManagementService.clearCachesByPattern("customer")
            cacheManagementService.clearCache("customersByBranch")
            cacheManagementService.clearCache("activeCustomerCount")
        } catch (Exception e) {
            log.error("Error clearing customer caches: ${e.message}", e)
        }
    }

    /**
     * Clear customer-specific caches
     */
    private void clearCustomerSpecificCaches(Long customerId) {
        try {
            cacheManagementService.clearCache("customer_${customerId}")
            cacheManagementService.clearCache("customerSummary_${customerId}")
        } catch (Exception e) {
            log.error("Error clearing customer-specific caches: ${e.message}", e)
        }
    }

    /**
     * Enable customer services after activation
     */
    private void enableCustomerServices(Customer customer) {
        try {
            // Enable online banking, mobile app access, etc.
            log.info("Enabling customer services for: ${customer.customerId}")
        } catch (Exception e) {
            log.error("Error enabling customer services: ${e.message}", e)
        }
    }

    /**
     * Disable customer services after deactivation
     */
    private void disableCustomerServices(Customer customer) {
        try {
            // Disable online banking, mobile app access, etc.
            log.info("Disabling customer services for: ${customer.customerId}")
        } catch (Exception e) {
            log.error("Error disabling customer services: ${e.message}", e)
        }
    }

    /**
     * Archive customer data
     */
    private void archiveCustomerData(Customer customer) {
        try {
            // Move customer data to archive tables or systems
            log.info("Archiving customer data for: ${customer.customerId}")
        } catch (Exception e) {
            log.error("Error archiving customer data: ${e.message}", e)
        }
    }

    /**
     * Update customer statistics
     */
    private void updateCustomerStatistics(Long branchId) {
        try {
            reportingService.updateCustomerStatistics(branchId)
        } catch (Exception e) {
            log.error("Error updating customer statistics: ${e.message}", e)
        }
    }

    /**
     * Generate welcome package for new customer
     */
    private void generateWelcomePackage(Customer customer) {
        try {
            // Generate welcome documents, cards, etc.
            log.info("Generating welcome package for: ${customer.customerId}")
        } catch (Exception e) {
            log.error("Error generating welcome package: ${e.message}", e)
        }
    }

    /**
     * Notify external systems of customer events
     */
    private void notifyExternalSystems(CustomerCreatedEvent event) {
        try {
            integrationService.notifyCustomerCreated(event.eventData)
        } catch (Exception e) {
            log.error("Error notifying external systems: ${e.message}", e)
        }
    }

    /**
     * Sync customer data with external systems
     */
    private void syncCustomerData(Customer customer, Map changes) {
        try {
            integrationService.syncCustomerData(customer.id, changes)
        } catch (Exception e) {
            log.error("Error syncing customer data: ${e.message}", e)
        }
    }
}
