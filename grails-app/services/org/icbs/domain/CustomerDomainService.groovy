package org.icbs.domain

import grails.gorm.transactions.Transactional
import org.icbs.cif.Customer
import org.icbs.domain.valueobjects.CustomerNumber
import org.icbs.domain.valueobjects.CustomerStatus
import org.icbs.domain.events.CustomerCreatedEvent
import org.icbs.domain.events.CustomerUpdatedEvent
import org.springframework.context.ApplicationEventPublisher
import org.springframework.beans.factory.annotation.Autowired

/**
 * ARCHITECTURE MODERNIZATION: Domain-Driven Design implementation for Customer aggregate
 * This service encapsulates business logic and domain rules for customer management
 */
@Transactional
class CustomerDomainService {

    @Autowired
    ApplicationEventPublisher eventPublisher

    def validationService
    def auditService

    /**
     * DDD: Create new customer with business rules validation
     */
    Customer createCustomer(CreateCustomerCommand command) {
        // Domain validation
        validateCustomerCreation(command)
        
        // Generate customer number using domain logic
        def customerNumber = generateCustomerNumber(command.branchId)
        
        // Create customer aggregate
        def customer = new Customer(
            customerId: customerNumber.value,
            name1: command.firstName,
            name2: command.middleName,
            name3: command.lastName,
            branch: command.branch,
            type: command.customerType,
            status: CustomerStatus.PENDING_APPROVAL
        )
        
        // Apply business rules
        applyCustomerCreationRules(customer, command)
        
        // Save customer
        customer.save(flush: true)
        
        // Publish domain event
        eventPublisher.publishEvent(new CustomerCreatedEvent(customer))
        
        // Audit trail
        auditService.recordCustomerCreation(customer, command.createdBy)
        
        return customer
    }

    /**
     * DDD: Update customer with business rules validation
     */
    Customer updateCustomer(UpdateCustomerCommand command) {
        def customer = Customer.get(command.customerId)
        if (!customer) {
            throw new CustomerNotFoundException("Customer not found: ${command.customerId}")
        }
        
        // Domain validation
        validateCustomerUpdate(customer, command)
        
        // Apply changes with business rules
        def originalState = customer.clone()
        applyCustomerUpdates(customer, command)
        
        // Save changes
        customer.save(flush: true)
        
        // Publish domain event
        eventPublisher.publishEvent(new CustomerUpdatedEvent(customer, originalState))
        
        // Audit trail
        auditService.recordCustomerUpdate(customer, command.updatedBy, originalState)
        
        return customer
    }

    /**
     * DDD: Activate customer with business rules
     */
    Customer activateCustomer(Long customerId, String activatedBy) {
        def customer = Customer.get(customerId)
        if (!customer) {
            throw new CustomerNotFoundException("Customer not found: ${customerId}")
        }
        
        // Business rule: Can only activate pending customers
        if (customer.status.id != CustomerStatus.PENDING_APPROVAL.id) {
            throw new InvalidCustomerStateException("Customer must be in pending approval status to activate")
        }
        
        // Business rule: Customer must have required documents
        validateRequiredDocuments(customer)
        
        // Business rule: Customer must have valid address
        validateCustomerAddress(customer)
        
        // Activate customer
        customer.status = CustomerStatus.ACTIVE
        customer.dateActivated = new Date()
        customer.activatedBy = activatedBy
        customer.save(flush: true)
        
        // Publish domain event
        eventPublisher.publishEvent(new CustomerActivatedEvent(customer))
        
        // Audit trail
        auditService.recordCustomerActivation(customer, activatedBy)
        
        return customer
    }

    /**
     * DDD: Deactivate customer with business rules
     */
    Customer deactivateCustomer(Long customerId, String reason, String deactivatedBy) {
        def customer = Customer.get(customerId)
        if (!customer) {
            throw new CustomerNotFoundException("Customer not found: ${customerId}")
        }
        
        // Business rule: Cannot deactivate if has active accounts
        validateNoActiveAccounts(customer)
        
        // Business rule: Cannot deactivate if has outstanding loans
        validateNoOutstandingLoans(customer)
        
        // Deactivate customer
        customer.status = CustomerStatus.INACTIVE
        customer.dateDeactivated = new Date()
        customer.deactivatedBy = deactivatedBy
        customer.deactivationReason = reason
        customer.save(flush: true)
        
        // Publish domain event
        eventPublisher.publishEvent(new CustomerDeactivatedEvent(customer, reason))
        
        // Audit trail
        auditService.recordCustomerDeactivation(customer, deactivatedBy, reason)
        
        return customer
    }

    /**
     * DDD: Generate customer number using domain logic
     */
    private CustomerNumber generateCustomerNumber(Long branchId) {
        def branch = Branch.get(branchId)
        def sequence = getNextCustomerSequence(branchId)
        
        // Format: BBBBYYYYNNNNNN (Branch + Year + Sequence)
        def year = new Date().format('yyyy')
        def branchCode = branch.branchCode.padLeft(4, '0')
        def sequenceStr = sequence.toString().padLeft(6, '0')
        
        def customerNumber = "${branchCode}${year}${sequenceStr}"
        
        return new CustomerNumber(customerNumber)
    }

    /**
     * DDD: Validate customer creation business rules
     */
    private void validateCustomerCreation(CreateCustomerCommand command) {
        // Business rule: Customer must be at least 18 years old
        if (command.birthDate && calculateAge(command.birthDate) < 18) {
            throw new BusinessRuleViolationException("Customer must be at least 18 years old")
        }
        
        // Business rule: Customer name must be unique within branch
        if (isCustomerNameExists(command.firstName, command.lastName, command.branchId)) {
            throw new BusinessRuleViolationException("Customer with same name already exists in this branch")
        }
        
        // Business rule: Valid identification required
        if (!command.identificationNumber || command.identificationNumber.trim().isEmpty()) {
            throw new BusinessRuleViolationException("Valid identification number is required")
        }
        
        // Additional domain validations
        validationService.validateCustomerData(command)
    }

    /**
     * DDD: Validate customer update business rules
     */
    private void validateCustomerUpdate(Customer customer, UpdateCustomerCommand command) {
        // Business rule: Cannot update inactive customers
        if (customer.status.id == CustomerStatus.INACTIVE.id) {
            throw new BusinessRuleViolationException("Cannot update inactive customer")
        }
        
        // Business rule: Certain fields cannot be changed after activation
        if (customer.status.id == CustomerStatus.ACTIVE.id) {
            if (command.identificationNumber && command.identificationNumber != customer.identificationNumber) {
                throw new BusinessRuleViolationException("Cannot change identification number for active customer")
            }
        }
        
        // Additional domain validations
        validationService.validateCustomerUpdateData(command)
    }

    /**
     * DDD: Apply customer creation business rules
     */
    private void applyCustomerCreationRules(Customer customer, CreateCustomerCommand command) {
        // Set default values based on business rules
        customer.dateCreated = new Date()
        customer.createdBy = command.createdBy
        customer.lastUpdatedAt = new Date()
        
        // Apply customer type specific rules
        if (command.customerType.isIndividual) {
            customer.taxExempt = false
        } else {
            // Corporate customer rules
            customer.taxExempt = command.taxExempt ?: false
        }
        
        // Set initial credit rating
        customer.creditRating = calculateInitialCreditRating(command)
        
        // Generate display name
        customer.displayName = generateDisplayName(command.firstName, command.middleName, command.lastName)
    }

    /**
     * DDD: Apply customer updates with business rules
     */
    private void applyCustomerUpdates(Customer customer, UpdateCustomerCommand command) {
        // Update allowed fields
        if (command.firstName) customer.name1 = command.firstName
        if (command.middleName) customer.name2 = command.middleName
        if (command.lastName) customer.name3 = command.lastName
        if (command.email) customer.email = command.email
        if (command.phoneNumber) customer.phoneNumber = command.phoneNumber
        
        // Update display name if name changed
        if (command.firstName || command.middleName || command.lastName) {
            customer.displayName = generateDisplayName(
                command.firstName ?: customer.name1,
                command.middleName ?: customer.name2,
                command.lastName ?: customer.name3
            )
        }
        
        // Update timestamp
        customer.lastUpdatedAt = new Date()
        customer.lastUpdatedBy = command.updatedBy
    }

    /**
     * DDD: Business rule validations
     */
    private void validateRequiredDocuments(Customer customer) {
        if (!customer.presentedids || customer.presentedids.isEmpty()) {
            throw new BusinessRuleViolationException("Customer must have at least one valid identification document")
        }
    }

    private void validateCustomerAddress(Customer customer) {
        if (!customer.addresses || customer.addresses.isEmpty()) {
            throw new BusinessRuleViolationException("Customer must have at least one valid address")
        }
    }

    private void validateNoActiveAccounts(Customer customer) {
        def activeDeposits = customer.deposits?.findAll { it.status.id == 1 } // Active status
        if (activeDeposits && !activeDeposits.isEmpty()) {
            throw new BusinessRuleViolationException("Cannot deactivate customer with active deposit accounts")
        }
    }

    private void validateNoOutstandingLoans(Customer customer) {
        def activeLoans = customer.loans?.findAll { it.status.id in [3, 4, 5] } // Active loan statuses
        if (activeLoans && !activeLoans.isEmpty()) {
            throw new BusinessRuleViolationException("Cannot deactivate customer with outstanding loans")
        }
    }

    /**
     * Helper methods
     */
    private int calculateAge(Date birthDate) {
        def now = new Date()
        def age = (now.time - birthDate.time) / (365.25 * 24 * 60 * 60 * 1000)
        return age.intValue()
    }

    private boolean isCustomerNameExists(String firstName, String lastName, Long branchId) {
        return Customer.countByName1AndName3AndBranchId(firstName, lastName, branchId) > 0
    }

    private Long getNextCustomerSequence(Long branchId) {
        // Implementation to get next sequence number for customer
        def lastCustomer = Customer.createCriteria().get {
            eq('branch.id', branchId)
            order('id', 'desc')
            maxResults(1)
        }
        
        return (lastCustomer?.id ?: 0) + 1
    }

    private String calculateInitialCreditRating(CreateCustomerCommand command) {
        // Business logic for initial credit rating
        return "STANDARD"
    }

    private String generateDisplayName(String firstName, String middleName, String lastName) {
        def parts = [firstName, middleName, lastName].findAll { it && !it.trim().isEmpty() }
        return parts.join(' ')
    }
}

/**
 * DDD: Command objects for customer operations
 */
class CreateCustomerCommand {
    String firstName
    String middleName
    String lastName
    Date birthDate
    String identificationNumber
    String email
    String phoneNumber
    Long branchId
    def branch
    def customerType
    Boolean taxExempt
    String createdBy
}

class UpdateCustomerCommand {
    Long customerId
    String firstName
    String middleName
    String lastName
    String email
    String phoneNumber
    String updatedBy
}

/**
 * DDD: Domain exceptions
 */
class CustomerNotFoundException extends RuntimeException {
    CustomerNotFoundException(String message) { super(message) }
}

class InvalidCustomerStateException extends RuntimeException {
    InvalidCustomerStateException(String message) { super(message) }
}

class BusinessRuleViolationException extends RuntimeException {
    BusinessRuleViolationException(String message) { super(message) }
}
