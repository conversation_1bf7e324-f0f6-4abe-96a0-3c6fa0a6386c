package org.icbs.tellering

import grails.gorm.transactions.Transactional
import javax.servlet.http.HttpSession
import org.springframework.web.context.request.RequestContextHolder
import org.icbs.lov.Designation
import org.icbs.admin.Branch
import org.icbs.admin.UserMaster

@Transactional
class IsTelleringActiveService {

    def disableTellering() {
        boolean active = false
    	HttpSession session = RequestContextHolder.currentRequestAttributes().getSession();
        
        def branch = UserMaster.get(session.user_id).branch

        branch.isTelleringActive = active
        branch.save(failOnError:true, validate:false)
    }
    
    def enableTellering() {
        boolean active = true
    	HttpSession session = RequestContextHolder.currentRequestAttributes().getSession();
        
        def branch = UserMaster.get(session.user_id).branch
        branch.isTelleringActive = active
        branch.save(failOnError:true, validate:false)
    }

}
