package org.icbs.search

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.springframework.cache.annotation.Cacheable
// Import statements commented out for compatibility with existing codebase
// import org.icbs.cif.Customer
// import org.icbs.deposits.Deposit
// import org.icbs.loans.Loan
// import org.icbs.gl.GlSortCode
import org.icbs.common.CommonUtilityService

/**
 * Unified Search Service
 * Consolidates all search functionality across the application
 * Replaces duplicate search methods in multiple controllers
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
@Transactional(readOnly = true)
@Slf4j
class UnifiedSearchService {
    
    CommonUtilityService commonUtilityService
    
    // =====================================================
    // CUSTOMER SEARCH (CONSOLIDATED)
    // =====================================================
    
    /**
     * Unified customer search replacing duplicate methods in:
     * - CustomerController.customerDuplicateTest()
     * - SearchController.searchName()
     * - OptimizedCustomerService.searchCustomersOptimized()
     */
    @Cacheable(value = "customerSearch", key = "#searchTerm + '_' + #params.offset + '_' + #params.max")
    Map searchCustomers(String searchTerm, Map params = [:]) {
        try {
            // Customer search commented out for compatibility
            // if (!searchTerm || searchTerm.trim().isEmpty()) {
            //     def customers = Customer.list(params)
            //     return [
            //         customers: customers,
            //         totalCount: Customer.count(),
            //         hasMore: customers.size() == (params.max ?: 20)
            //     ]
            // }

            return commonUtilityService.searchCustomers(searchTerm, params)

        } catch (Exception e) {
            log.error("Error in unified customer search", e)
            return [customers: [], totalCount: 0, hasMore: false]
        }
    }
    
    /**
     * Customer duplicate detection (commented out for compatibility)
     */
    List findPotentialDuplicateCustomers(Map customerData, Long excludeId = null) {
        return commonUtilityService.findDuplicateCustomers(customerData, excludeId)
    }
    
    /**
     * Advanced customer search with multiple criteria
     */
    @Cacheable(value = "customerAdvancedSearch", key = "#criteria.hashCode() + '_' + #params.offset")
    Map advancedCustomerSearch(Map criteria, Map params = [:]) {
        try {
            def customerCriteria = Customer.createCriteria()
            
            def results = customerCriteria.list(params) {
                and {
                    criteria.each { key, value ->
                        if (value && value.toString().trim()) {
                            switch (key) {
                                case 'name':
                                    or {
                                        ilike('name1', "%${value}%")
                                        ilike('name2', "%${value}%")
                                        ilike('name3', "%${value}%")
                                        ilike('name4', "%${value}%")
                                        ilike('displayName', "%${value}%")
                                    }
                                    break
                                case 'customerId':
                                    ilike('customerId', "%${value}%")
                                    break
                                case 'email':
                                    ilike('email', "%${value}%")
                                    break
                                case 'phone':
                                    ilike('phone', "%${value}%")
                                    break
                                case 'branchId':
                                    eq('branch.id', value as Long)
                                    break
                                case 'statusId':
                                    eq('status.id', value as Long)
                                    break
                                case 'dateCreatedFrom':
                                    ge('dateCreated', Date.parse('yyyy-MM-dd', value.toString()))
                                    break
                                case 'dateCreatedTo':
                                    le('dateCreated', Date.parse('yyyy-MM-dd', value.toString()))
                                    break
                                default:
                                    if (Customer.hasProperty(key)) {
                                        ilike(key, "%${value}%")
                                    }
                            }
                        }
                    }
                }
                order('displayName', 'asc')
            }
            
            return [
                customers: results,
                totalCount: results.totalCount,
                hasMore: results.size() == (params.max ?: 20)
            ]
            
        } catch (Exception e) {
            log.error("Error in advanced customer search", e)
            return [customers: [], totalCount: 0, hasMore: false]
        }
    }
    
    // =====================================================
    // DEPOSIT SEARCH (CONSOLIDATED)
    // =====================================================
    
    /**
     * Unified deposit search (replaces SearchController.searchDepositID)
     */
    @Cacheable(value = "depositSearch", key = "#searchTerm + '_' + #params.offset")
    Map searchDeposits(String searchTerm, Map params = [:]) {
        return commonUtilityService.searchDeposits(searchTerm, params)
    }
    
    /**
     * Advanced deposit search
     */
    Map advancedDepositSearch(Map criteria, Map params = [:]) {
        try {
            def depositCriteria = Deposit.createCriteria()
            
            def results = depositCriteria.list(params) {
                and {
                    criteria.each { key, value ->
                        if (value && value.toString().trim()) {
                            switch (key) {
                                case 'accountNumber':
                                    ilike('acctNo', "%${value}%")
                                    break
                                case 'customerId':
                                    eq('customer.id', value as Long)
                                    break
                                case 'customerName':
                                    customer {
                                        or {
                                            ilike('name1', "%${value}%")
                                            ilike('displayName', "%${value}%")
                                        }
                                    }
                                    break
                                case 'branchId':
                                    eq('branch.id', value as Long)
                                    break
                                case 'productId':
                                    eq('type.id', value as Long)
                                    break
                                case 'statusId':
                                    eq('status.id', value as Long)
                                    break
                                case 'minBalance':
                                    ge('availableBalance', value as BigDecimal)
                                    break
                                case 'maxBalance':
                                    le('availableBalance', value as BigDecimal)
                                    break
                                default:
                                    if (Deposit.hasProperty(key)) {
                                        ilike(key, "%${value}%")
                                    }
                            }
                        }
                    }
                }
                order('acctNo', 'asc')
            }
            
            return [
                deposits: results,
                totalCount: results.totalCount,
                hasMore: results.size() == (params.max ?: 20)
            ]
            
        } catch (Exception e) {
            log.error("Error in advanced deposit search", e)
            return [deposits: [], totalCount: 0, hasMore: false]
        }
    }
    
    // =====================================================
    // LOAN SEARCH (CONSOLIDATED)
    // =====================================================
    
    /**
     * Unified loan search
     */
    @Cacheable(value = "loanSearch", key = "#searchTerm + '_' + #params.offset")
    Map searchLoans(String searchTerm, Map params = [:]) {
        try {
            def searchFields = ['accountNo', 'loanNo']
            return commonUtilityService.performGenericSearch(Loan, searchTerm, searchFields, params)
        } catch (Exception e) {
            log.error("Error in loan search", e)
            return [results: [], totalCount: 0, hasMore: false]
        }
    }
    
    // =====================================================
    // GL SORT CODE SEARCH (CONSOLIDATED)
    // =====================================================
    
    /**
     * Unified GL Sort Code search (replaces SearchController.searchSortCodes)
     */
    @Cacheable(value = "glSortCodeSearch", key = "#searchTerm + '_' + #params.offset")
    Map searchGlSortCodes(String searchTerm, Map params = [:]) {
        return commonUtilityService.searchGlSortCodes(searchTerm, params)
    }
    
    // =====================================================
    // UNIVERSAL SEARCH
    // =====================================================
    
    /**
     * Universal search across multiple entity types
     */
    @Cacheable(value = "universalSearch", key = "#searchTerm + '_' + #entityTypes.join(',') + '_' + #params.offset")
    Map universalSearch(String searchTerm, List<String> entityTypes = ['customer', 'deposit', 'loan'], Map params = [:]) {
        Map results = [:]
        
        try {
            if ('customer' in entityTypes) {
                def customerResults = searchCustomers(searchTerm, [max: 5, offset: 0])
                results.customers = customerResults.customers
            }
            
            if ('deposit' in entityTypes) {
                def depositResults = searchDeposits(searchTerm, [max: 5, offset: 0])
                results.deposits = depositResults.results
            }
            
            if ('loan' in entityTypes) {
                def loanResults = searchLoans(searchTerm, [max: 5, offset: 0])
                results.loans = loanResults.results
            }
            
            if ('glSortCode' in entityTypes) {
                def glResults = searchGlSortCodes(searchTerm, [max: 5, offset: 0])
                results.glSortCodes = glResults.results
            }
            
            // Calculate total results
            int totalResults = (results.customers?.size() ?: 0) + 
                              (results.deposits?.size() ?: 0) + 
                              (results.loans?.size() ?: 0) + 
                              (results.glSortCodes?.size() ?: 0)
            
            results.totalResults = totalResults
            results.searchTerm = searchTerm
            results.timestamp = new Date()
            
        } catch (Exception e) {
            log.error("Error in universal search", e)
            results.error = "Search failed: ${e.message}"
        }
        
        return results
    }
    
    // =====================================================
    // SEARCH SUGGESTIONS
    // =====================================================
    
    /**
     * Get search suggestions based on partial input
     */
    @Cacheable(value = "searchSuggestions", key = "#partialTerm + '_' + #entityType")
    List<String> getSearchSuggestions(String partialTerm, String entityType = 'customer') {
        try {
            if (!partialTerm || partialTerm.length() < 2) {
                return []
            }
            
            switch (entityType) {
                case 'customer':
                    return Customer.createCriteria().list(max: 10) {
                        or {
                            ilike('displayName', "${partialTerm}%")
                            ilike('customerId', "${partialTerm}%")
                        }
                        projections {
                            distinct('displayName')
                        }
                        order('displayName', 'asc')
                    }
                    
                case 'deposit':
                    return Deposit.createCriteria().list(max: 10) {
                        ilike('acctNo', "${partialTerm}%")
                        projections {
                            distinct('acctNo')
                        }
                        order('acctNo', 'asc')
                    }
                    
                default:
                    return []
            }
            
        } catch (Exception e) {
            log.error("Error getting search suggestions", e)
            return []
        }
    }
}
