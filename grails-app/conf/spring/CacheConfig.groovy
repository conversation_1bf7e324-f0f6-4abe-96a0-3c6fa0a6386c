import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.cache.CacheManager
import org.springframework.cache.annotation.EnableCaching
import org.springframework.cache.caffeine.CaffeineCacheManager
import com.github.benmanes.caffeine.cache.Caffeine
import java.time.Duration

@Configuration
@EnableCaching
class CacheConfig {

    /**
     * PERFORMANCE OPTIMIZATION: Primary cache manager using Caffeine for high performance
     */
    @Bean
    @Primary
    CacheManager primaryCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager()
        
        // Configure Caffeine cache with optimal settings
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(10000)                    // Maximum 10,000 entries
            .expireAfterWrite(Duration.ofMinutes(30))  // Expire after 30 minutes
            .expireAfterAccess(Duration.ofMinutes(15)) // Expire after 15 minutes of no access
            .recordStats()                         // Enable statistics
        )
        
        // Pre-configure cache names for better performance
        cacheManager.setCacheNames([
            // Customer caches
            'customers',
            'customer',
            'customerBasic',
            'customerSearch',
            'customerSummary',
            'customersByBranch',
            'activeCustomerCount',
            'customerStats',
            
            // Deposit caches
            'deposits',
            'depositByAcctNo',
            'depositSummary',
            'depositSearch',
            'customerDeposits',
            'branchDepositStats',
            'depositsMaturing',
            'dormantAccounts',
            'lowBalanceAccounts',
            
            // Loan caches
            'loans',
            'loanByAcctNo',
            'loanSummary',
            'loanSearch',
            'customerLoans',
            'branchLoanStats',
            'loansMaturing',
            'overdueLoans',
            
            // System caches
            'branches',
            'products',
            'lovItems',
            'configurations',
            'userSessions',
            'glAccounts',

            // Security caches
            'customerCache',
            'depositCache',
            'loanCache',
            'branchCache',
            'lookupCache',
            'summaryCache',
            'securityCache',
            'auditCache',

            // Report caches
            'reportData',
            'reportTemplates'
        ])
        
        return cacheManager
    }

    // REMOVED DUPLICATE CACHE MANAGERS - CONSOLIDATED INTO PRIMARY CACHE MANAGER
}
