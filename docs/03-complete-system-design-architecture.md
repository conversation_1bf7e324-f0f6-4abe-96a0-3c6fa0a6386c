# QwikBanka - Complete System Design & Architecture

## Executive Summary

This document presents the complete system design and architecture for the modernized QwikBanka Core Banking System. The design transforms the current monolithic architecture into a scalable, secure, and maintainable world-class banking platform while preserving Grails conventions and ensuring seamless migration.

## Target Architecture Overview

### High-Level Architecture Principles
1. **Domain-Driven Design (DDD)**: Clear domain boundaries and ubiquitous language
2. **Event-Driven Architecture**: Asynchronous communication through domain events
3. **CQRS Pattern**: Separation of read and write operations for optimal performance
4. **Microservices Ready**: Modular design enabling future microservices migration
5. **API-First Design**: RESTful APIs with comprehensive OpenAPI documentation
6. **Security by Design**: Zero-trust security model with comprehensive protection
7. **Cloud-Native Patterns**: Containerization and cloud deployment readiness

### Technology Stack Evolution

#### Current Stack
```
Frontend: GSP + Bootstrap + jQuery
Backend: Grails 6.2.3 + Groovy
Database: PostgreSQL
Security: Custom Authentication
Caching: Basic Grails Cache
```

#### Target Stack
```
Frontend: GSP + Modern CSS Framework + Vue.js/React (Progressive Enhancement)
Backend: Grails 6.2.3+ + Spring Boot 3.x + Groovy/Java
Database: PostgreSQL 15+ with Read Replicas
Security: OAuth2/JWT + Spring Security 6.x
Caching: Redis + Caffeine (Multi-Level)
Messaging: Apache Kafka for Event Streaming
Monitoring: Prometheus + Grafana + ELK Stack
API Gateway: Spring Cloud Gateway
Service Discovery: Consul/Eureka
```

## Domain Architecture Design

### Core Domain Boundaries

#### 1. Customer Information File (CIF) Domain
```groovy
// Customer Aggregate Root
@Entity
class Customer {
    private CustomerId customerId
    private CustomerProfile profile
    private List<Address> addresses
    private List<Contact> contacts
    private CustomerStatus status
    private List<DomainEvent> domainEvents
    
    // Business Methods
    void activate(ActivationCommand command) {
        validateActivationRules(command)
        this.status = CustomerStatus.ACTIVE
        addDomainEvent(new CustomerActivatedEvent(this))
    }
    
    void updateProfile(UpdateProfileCommand command) {
        this.profile.update(command)
        addDomainEvent(new CustomerProfileUpdatedEvent(this))
    }
}

// Value Objects
@Embeddable
class CustomerProfile {
    private PersonalName name
    private DateOfBirth birthDate
    private Gender gender
    private Nationality nationality
}

@Embeddable
class PersonalName {
    private String firstName
    private String middleName
    private String lastName
    private String suffix
    
    String getDisplayName() {
        return "${firstName} ${middleName} ${lastName} ${suffix}".trim()
    }
}
```

#### 2. Account Management Domain
```groovy
// Account Aggregate Root
@Entity
abstract class Account {
    protected AccountId accountId
    protected CustomerId customerId
    protected Money balance
    protected AccountStatus status
    protected List<DomainEvent> domainEvents
    
    abstract void processTransaction(TransactionCommand command)
    abstract boolean canProcessTransaction(TransactionCommand command)
}

@Entity
class DepositAccount extends Account {
    private DepositType type
    private InterestRate interestRate
    private Money minimumBalance
    
    @Override
    void processTransaction(TransactionCommand command) {
        validateTransaction(command)
        applyTransaction(command)
        addDomainEvent(new TransactionProcessedEvent(this, command))
    }
}

@Entity
class LoanAccount extends Account {
    private LoanTerms terms
    private Money principalBalance
    private Money interestBalance
    private PaymentSchedule schedule
    
    void makePayment(PaymentCommand command) {
        validatePayment(command)
        applyPayment(command)
        addDomainEvent(new LoanPaymentMadeEvent(this, command))
    }
}
```

#### 3. Transaction Processing Domain
```groovy
// Transaction Aggregate Root
@Entity
class Transaction {
    private TransactionId transactionId
    private TransactionType type
    private Money amount
    private AccountId fromAccount
    private AccountId toAccount
    private TransactionStatus status
    private List<TransactionEntry> entries
    
    void process() {
        validateTransaction()
        createDoubleEntries()
        updateAccountBalances()
        this.status = TransactionStatus.COMPLETED
        addDomainEvent(new TransactionCompletedEvent(this))
    }
    
    void reverse(ReversalReason reason) {
        validateReversal()
        createReversalEntries()
        this.status = TransactionStatus.REVERSED
        addDomainEvent(new TransactionReversedEvent(this, reason))
    }
}

// Transaction Entry for Double-Entry Bookkeeping
@Entity
class TransactionEntry {
    private GLAccount account
    private Money debitAmount
    private Money creditAmount
    private String description
}
```

### Domain Services

#### 1. Customer Domain Service
```groovy
@Service
@Transactional
class CustomerDomainService {
    
    private CustomerRepository customerRepository
    private DomainEventPublisher eventPublisher
    
    Customer createCustomer(CreateCustomerCommand command) {
        validateCustomerCreation(command)
        
        def customer = new Customer(
            customerId: generateCustomerId(),
            profile: createProfile(command),
            status: CustomerStatus.PENDING
        )
        
        customer = customerRepository.save(customer)
        eventPublisher.publishEvents(customer.getDomainEvents())
        
        return customer
    }
    
    void activateCustomer(CustomerId customerId, ActivationCommand command) {
        def customer = customerRepository.findById(customerId)
        customer.activate(command)
        
        customerRepository.save(customer)
        eventPublisher.publishEvents(customer.getDomainEvents())
    }
}
```

#### 2. Account Domain Service
```groovy
@Service
@Transactional
class AccountDomainService {
    
    private AccountRepository accountRepository
    private CustomerRepository customerRepository
    private DomainEventPublisher eventPublisher
    
    DepositAccount openDepositAccount(OpenDepositAccountCommand command) {
        def customer = customerRepository.findById(command.customerId)
        validateAccountOpening(customer, command)
        
        def account = new DepositAccount(
            accountId: generateAccountId(),
            customerId: command.customerId,
            type: command.depositType,
            balance: Money.zero()
        )
        
        account = accountRepository.save(account)
        eventPublisher.publishEvents(account.getDomainEvents())
        
        return account
    }
}
```

## Application Architecture

### Application Services (Use Cases)

#### 1. Customer Application Service
```groovy
@Service
@Transactional
class CustomerApplicationService {
    
    private CustomerDomainService customerDomainService
    private CustomerQueryService customerQueryService
    private NotificationService notificationService
    
    CustomerDto createCustomer(CreateCustomerCommand command) {
        def customer = customerDomainService.createCustomer(command)
        
        // Send welcome notification
        notificationService.sendWelcomeMessage(customer.getCustomerId())
        
        return customerMapper.toDto(customer)
    }
    
    CustomerSummaryDto getCustomerSummary(CustomerId customerId) {
        return customerQueryService.getCustomerSummary(customerId)
    }
}
```

#### 2. Transaction Application Service
```groovy
@Service
@Transactional
class TransactionApplicationService {
    
    private TransactionDomainService transactionDomainService
    private AccountRepository accountRepository
    private FraudDetectionService fraudDetectionService
    
    TransactionDto processTransaction(ProcessTransactionCommand command) {
        // Fraud detection
        fraudDetectionService.validateTransaction(command)
        
        // Process transaction
        def transaction = transactionDomainService.processTransaction(command)
        
        return transactionMapper.toDto(transaction)
    }
}
```

### Query Services (CQRS Read Side)

#### 1. Customer Query Service
```groovy
@Service
@Transactional(readOnly = true)
class CustomerQueryService {
    
    private JdbcTemplate jdbcTemplate
    private RedisTemplate redisTemplate
    
    @Cacheable(value = "customerSummary", key = "#customerId")
    CustomerSummaryDto getCustomerSummary(CustomerId customerId) {
        def sql = """
            SELECT c.customer_id, c.display_name, c.status,
                   COUNT(d.id) as deposit_count,
                   COALESCE(SUM(d.balance), 0) as total_deposits,
                   COUNT(l.id) as loan_count,
                   COALESCE(SUM(l.outstanding_balance), 0) as total_loans
            FROM customer c
            LEFT JOIN deposit_account d ON c.id = d.customer_id
            LEFT JOIN loan_account l ON c.id = l.customer_id
            WHERE c.customer_id = ?
            GROUP BY c.customer_id, c.display_name, c.status
        """
        
        return jdbcTemplate.queryForObject(sql, customerSummaryRowMapper, customerId.value)
    }
    
    PagedResult<CustomerSearchDto> searchCustomers(CustomerSearchCriteria criteria) {
        // Optimized search implementation with pagination
    }
}
```

### Event Handlers

#### 1. Customer Event Handlers
```groovy
@Component
class CustomerEventHandler {
    
    private NotificationService notificationService
    private AuditService auditService
    private CacheManager cacheManager
    
    @EventListener
    @Async
    void handleCustomerActivated(CustomerActivatedEvent event) {
        // Send activation notification
        notificationService.sendActivationNotification(event.getCustomerId())
        
        // Log audit event
        auditService.logCustomerActivation(event)
        
        // Clear related caches
        cacheManager.evict("customerSummary", event.getCustomerId())
    }
    
    @EventListener
    @Async
    void handleCustomerProfileUpdated(CustomerProfileUpdatedEvent event) {
        // Update search indexes
        searchIndexService.updateCustomerIndex(event.getCustomer())
        
        // Clear caches
        cacheManager.evictAll("customerSearch")
    }
}
```

## Infrastructure Architecture

### Database Design

#### 1. Optimized Schema Design
```sql
-- Customer table with optimizations
CREATE TABLE customer (
    id BIGSERIAL PRIMARY KEY,
    customer_id VARCHAR(20) UNIQUE NOT NULL,
    display_name VARCHAR(200) NOT NULL,
    status_id INTEGER NOT NULL,
    branch_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version INTEGER DEFAULT 0
);

-- Optimized indexes
CREATE INDEX idx_customer_display_name_gin ON customer USING gin(to_tsvector('english', display_name));
CREATE INDEX idx_customer_branch_status ON customer(branch_id, status_id);
CREATE INDEX idx_customer_created_at ON customer(created_at);

-- Partitioned transaction table
CREATE TABLE transaction_log (
    id BIGSERIAL,
    transaction_date DATE NOT NULL,
    transaction_id VARCHAR(50) NOT NULL,
    account_id BIGINT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    transaction_type VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) PARTITION BY RANGE (transaction_date);

-- Monthly partitions
CREATE TABLE transaction_log_2025_01 PARTITION OF transaction_log
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

#### 2. Read Replicas Configuration
```yaml
# Database configuration for read replicas
spring:
  datasource:
    primary:
      url: *******************************************
      username: ${DB_USERNAME}
      password: ${DB_PASSWORD}
    replica:
      url: *******************************************
      username: ${DB_USERNAME}
      password: ${DB_PASSWORD}
```

### Caching Architecture

#### 1. Multi-Level Caching Strategy
```groovy
@Configuration
class CacheArchitecture {
    
    // L1 Cache: Application-level (Caffeine)
    @Bean
    @Primary
    CacheManager l1CacheManager() {
        CaffeineCacheManager manager = new CaffeineCacheManager()
        manager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(Duration.ofMinutes(15))
            .recordStats())
        return manager
    }
    
    // L2 Cache: Distributed (Redis)
    @Bean
    CacheManager l2CacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration())
        return builder.build()
    }
}
```

#### 2. Cache Warming Strategy
```groovy
@Component
class CacheWarmupService {
    
    @EventListener(ApplicationReadyEvent.class)
    void warmupCaches() {
        // Warm up frequently accessed data
        warmupCustomerCache()
        warmupProductCache()
        warmupBranchCache()
    }
    
    @Scheduled(fixedRate = 3600000) // Every hour
    void refreshCaches() {
        // Refresh stale cache entries
    }
}
```

### Security Architecture

#### 1. OAuth2/JWT Implementation
```groovy
@Configuration
@EnableAuthorizationServer
class OAuth2SecurityConfig {
    
    @Bean
    JwtAccessTokenConverter accessTokenConverter() {
        JwtAccessTokenConverter converter = new JwtAccessTokenConverter()
        converter.setSigningKey("qwikbanka-secret-key")
        return converter
    }
    
    @Bean
    TokenStore tokenStore() {
        return new JwtTokenStore(accessTokenConverter())
    }
}

@RestController
@RequestMapping("/oauth")
class AuthenticationController {
    
    @PostMapping("/token")
    ResponseEntity<TokenResponse> authenticate(@RequestBody LoginRequest request) {
        // Authenticate user and generate JWT token
    }
    
    @PostMapping("/refresh")
    ResponseEntity<TokenResponse> refreshToken(@RequestBody RefreshTokenRequest request) {
        // Refresh JWT token
    }
}
```

#### 2. Security Filters and Interceptors
```groovy
@Component
class JwtAuthenticationFilter extends OncePerRequestFilter {
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) {
        String token = extractToken(request)
        if (token != null && jwtTokenProvider.validateToken(token)) {
            Authentication auth = jwtTokenProvider.getAuthentication(token)
            SecurityContextHolder.getContext().setAuthentication(auth)
        }
        filterChain.doFilter(request, response)
    }
}
```

### API Architecture

#### 1. RESTful API Design
```groovy
@RestController
@RequestMapping("/api/v1/customers")
@Validated
class CustomerApiController {
    
    private CustomerApplicationService customerService
    
    @GetMapping("/{customerId}")
    @PreAuthorize("hasPermission(#customerId, 'CUSTOMER', 'READ')")
    ResponseEntity<CustomerDto> getCustomer(@PathVariable @Valid CustomerId customerId) {
        def customer = customerService.getCustomer(customerId)
        return ResponseEntity.ok(customer)
    }
    
    @PostMapping
    @PreAuthorize("hasAuthority('CUSTOMER_CREATE')")
    ResponseEntity<CustomerDto> createCustomer(@Valid @RequestBody CreateCustomerCommand command) {
        def customer = customerService.createCustomer(command)
        return ResponseEntity.status(HttpStatus.CREATED).body(customer)
    }
    
    @PutMapping("/{customerId}")
    @PreAuthorize("hasPermission(#customerId, 'CUSTOMER', 'WRITE')")
    ResponseEntity<CustomerDto> updateCustomer(@PathVariable CustomerId customerId,
                                             @Valid @RequestBody UpdateCustomerCommand command) {
        command.setCustomerId(customerId)
        def customer = customerService.updateCustomer(command)
        return ResponseEntity.ok(customer)
    }
}
```

#### 2. API Documentation with OpenAPI
```groovy
@Configuration
@OpenAPIDefinition(
    info = @Info(
        title = "QwikBanka Core Banking API",
        version = "v1",
        description = "Modern REST API for QwikBanka Core Banking System"
    ),
    security = @SecurityRequirement(name = "bearerAuth")
)
class OpenApiConfig {
    
    @Bean
    OpenAPI customOpenAPI() {
        return new OpenAPI()
            .components(new Components()
                .addSecuritySchemes("bearerAuth",
                    new SecurityScheme()
                        .type(SecurityScheme.Type.HTTP)
                        .scheme("bearer")
                        .bearerFormat("JWT")))
    }
}
```

## Event-Driven Architecture

### Event Streaming with Kafka

#### 1. Event Publisher
```groovy
@Component
class DomainEventPublisher {
    
    private KafkaTemplate<String, Object> kafkaTemplate
    
    @EventListener
    void publishDomainEvent(DomainEvent event) {
        String topic = getTopicForEvent(event)
        String key = event.getAggregateId()
        
        kafkaTemplate.send(topic, key, event)
            .addCallback(
                result -> log.info("Event published: {}", event),
                failure -> log.error("Failed to publish event: {}", event, failure)
            )
    }
}
```

#### 2. Event Consumers
```groovy
@Component
class TransactionEventConsumer {
    
    @KafkaListener(topics = "transaction-events")
    void handleTransactionEvent(TransactionCompletedEvent event) {
        // Update real-time balances
        balanceUpdateService.updateBalance(event.getAccountId(), event.getAmount())
        
        // Trigger fraud detection
        fraudDetectionService.analyzeTransaction(event)
        
        // Update analytics
        analyticsService.recordTransaction(event)
    }
}
```

## Monitoring and Observability

### Application Performance Monitoring
```groovy
@Configuration
class MonitoringConfig {
    
    @Bean
    MeterRegistry meterRegistry() {
        return new PrometheusMeterRegistry(PrometheusConfig.DEFAULT)
    }
    
    @Bean
    @ConditionalOnMissingBean
    TimedAspect timedAspect(MeterRegistry registry) {
        return new TimedAspect(registry)
    }
}

@Service
class CustomerService {
    
    @Timed(name = "customer.operations", description = "Customer service operations")
    @Counted(name = "customer.requests", description = "Customer service requests")
    Customer findCustomerById(CustomerId customerId) {
        return customerRepository.findById(customerId)
    }
}
```

### Health Checks and Circuit Breakers
```groovy
@Component
class DatabaseHealthIndicator implements HealthIndicator {
    
    private DataSource dataSource
    
    @Override
    Health health() {
        try {
            Connection connection = dataSource.getConnection()
            connection.close()
            return Health.up().withDetail("database", "Available").build()
        } catch (Exception e) {
            return Health.down(e).withDetail("database", "Unavailable").build()
        }
    }
}

@Service
class ExternalServiceClient {
    
    @CircuitBreaker(name = "external-service", fallbackMethod = "fallbackMethod")
    @Retry(name = "external-service")
    @TimeLimiter(name = "external-service")
    String callExternalService(String request) {
        // External service call
    }
    
    String fallbackMethod(String request, Exception ex) {
        return "Fallback response"
    }
}
```

## Deployment Architecture

### Containerization Strategy
```dockerfile
# Dockerfile for QwikBanka Application
FROM openjdk:17-jre-slim

WORKDIR /app
COPY build/libs/qwikbanka-*.jar app.jar

EXPOSE 8080
ENTRYPOINT ["java", "-jar", "app.jar"]
```

### Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: qwikbanka-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: qwikbanka
  template:
    metadata:
      labels:
        app: qwikbanka
    spec:
      containers:
      - name: qwikbanka
        image: qwikbanka:latest
        ports:
        - containerPort: 8080
        env:
        - name: DB_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
```

### CI/CD Pipeline
```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - deploy

test:
  stage: test
  script:
    - ./gradlew test
    - ./gradlew integrationTest

build:
  stage: build
  script:
    - ./gradlew build
    - docker build -t qwikbanka:$CI_COMMIT_SHA .

deploy:
  stage: deploy
  script:
    - kubectl apply -f k8s/
    - kubectl set image deployment/qwikbanka-app qwikbanka=qwikbanka:$CI_COMMIT_SHA
```

## Performance Benchmarks

### Target Performance Metrics
- **Response Time**: < 200ms for 95% of API requests
- **Throughput**: > 10,000 transactions per second
- **Concurrent Users**: > 5,000 simultaneous users
- **Database Queries**: < 50ms for 90% of queries
- **Memory Usage**: < 4GB per application instance
- **CPU Usage**: < 70% under normal load

### Load Testing Strategy
```groovy
// JMeter test plan configuration
@Test
class PerformanceTest {

    @Test
    void testCustomerCreationLoad() {
        // Simulate 1000 concurrent customer creations
        ThreadGroup threadGroup = new ThreadGroup()
        threadGroup.setNumThreads(1000)
        threadGroup.setRampUp(60) // 60 seconds ramp-up

        HTTPSampler sampler = new HTTPSampler()
        sampler.setDomain("localhost")
        sampler.setPort(8080)
        sampler.setPath("/api/v1/customers")
        sampler.setMethod("POST")
    }
}
```

## Security Implementation Details

### Data Encryption Strategy
```groovy
@Configuration
class EncryptionConfig {

    @Bean
    AESUtil aesUtil() {
        return new AESUtil("qwikbanka-encryption-key-256bit")
    }

    @Bean
    PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12)
    }
}

@Entity
class SensitiveData {

    @Convert(converter = EncryptedStringConverter.class)
    private String accountNumber

    @Convert(converter = EncryptedStringConverter.class)
    private String socialSecurityNumber
}
```

### Audit Trail Implementation
```groovy
@Entity
@EntityListeners(AuditingEntityListener.class)
class AuditLog {

    @Id
    @GeneratedValue
    private Long id

    private String entityType
    private String entityId
    private String operation
    private String oldValues
    private String newValues
    private String userId
    private String ipAddress
    private LocalDateTime timestamp
}

@Component
class AuditEventListener {

    @EventListener
    void handleAuditEvent(AuditEvent event) {
        AuditLog auditLog = new AuditLog()
        auditLog.setEntityType(event.getEntityType())
        auditLog.setOperation(event.getOperation())
        auditLog.setUserId(event.getUserId())
        auditLog.setTimestamp(LocalDateTime.now())

        auditLogRepository.save(auditLog)
    }
}
```

## Migration Strategy

### Database Migration Plan
```sql
-- Phase 1: Add new columns without breaking existing functionality
ALTER TABLE customer ADD COLUMN customer_uuid UUID;
ALTER TABLE customer ADD COLUMN encrypted_ssn TEXT;

-- Phase 2: Migrate data
UPDATE customer SET customer_uuid = gen_random_uuid() WHERE customer_uuid IS NULL;
UPDATE customer SET encrypted_ssn = encrypt_ssn(ssn) WHERE encrypted_ssn IS NULL;

-- Phase 3: Add constraints and indexes
ALTER TABLE customer ALTER COLUMN customer_uuid SET NOT NULL;
CREATE UNIQUE INDEX idx_customer_uuid ON customer(customer_uuid);
```

### Application Migration Strategy
1. **Parallel Deployment**: Run old and new versions simultaneously
2. **Feature Flags**: Gradually enable new features
3. **Data Synchronization**: Keep data in sync during migration
4. **Rollback Plan**: Quick rollback capability if issues arise

## Disaster Recovery Plan

### Backup Strategy
```yaml
# Automated backup configuration
apiVersion: batch/v1
kind: CronJob
metadata:
  name: database-backup
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: backup
            image: postgres:15
            command:
            - /bin/bash
            - -c
            - pg_dump $DATABASE_URL | gzip > /backup/qwikbanka-$(date +%Y%m%d).sql.gz
```

### Recovery Procedures
1. **Database Recovery**: Point-in-time recovery from backups
2. **Application Recovery**: Container orchestration with health checks
3. **Data Center Failover**: Multi-region deployment with automatic failover
4. **Business Continuity**: Manual processes for critical operations

This comprehensive system design provides a robust foundation for transforming QwikBanka into a world-class core banking system while maintaining Grails conventions and ensuring scalability, security, and maintainability.
