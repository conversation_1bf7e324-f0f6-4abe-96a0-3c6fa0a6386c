package org.icbs.integration

import grails.testing.mixin.integration.Integration
import grails.gorm.transactions.Rollback
import spock.lang.Specification
import spock.lang.Shared
import org.icbs.cif.Customer
import org.icbs.admin.Branch
import org.icbs.lov.LovItem
import org.icbs.domain.CustomerDomainService
import org.icbs.domain.CreateCustomerCommand
import org.icbs.domain.UpdateCustomerCommand
import org.icbs.cif.OptimizedCustomerService
import org.springframework.beans.factory.annotation.Autowired

/**
 * COMPREHENSIVE TESTING: Integration tests for Customer functionality
 * Tests the complete customer lifecycle with real database interactions
 */
@Integration
@Rollback
class CustomerIntegrationSpec extends Specification {

    @Autowired
    CustomerDomainService customerDomainService
    
    @Autowired
    OptimizedCustomerService optimizedCustomerService
    
    @Shared
    Branch testBranch
    
    @Shared
    LovItem customerType
    
    @Shared
    LovItem customerStatus

    def setupSpec() {
        // Create test data that persists across tests
        testBranch = new Branch(
            branchName: 'Test Branch',
            branchCode: 'TEST001',
            address: 'Test Address'
        ).save(flush: true)
        
        customerType = new LovItem(
            itemType: 'CUSTOMER_TYPE',
            itemValue: 'INDIVIDUAL',
            itemDesc: 'Individual Customer'
        ).save(flush: true)
        
        customerStatus = new LovItem(
            itemType: 'CUSTOMER_STATUS',
            itemValue: 'PENDING_APPROVAL',
            itemDesc: 'Pending Approval'
        ).save(flush: true)
    }

    def "test complete customer creation workflow"() {
        given: "A valid customer creation command"
        def command = new CreateCustomerCommand(
            firstName: 'John',
            middleName: 'Michael',
            lastName: 'Doe',
            birthDate: Date.parse('yyyy-MM-dd', '1990-01-15'),
            identificationNumber: 'ID123456789',
            email: '<EMAIL>',
            phoneNumber: '+1234567890',
            branchId: testBranch.id,
            branch: testBranch,
            customerType: customerType,
            taxExempt: false,
            createdBy: 'test-user'
        )

        when: "Creating a customer using domain service"
        def customer = customerDomainService.createCustomer(command)

        then: "Customer is created successfully"
        customer != null
        customer.id != null
        customer.customerId != null
        customer.name1 == 'John'
        customer.name2 == 'Michael'
        customer.name3 == 'Doe'
        customer.email == '<EMAIL>'
        customer.phoneNumber == '+1234567890'
        customer.branch.id == testBranch.id
        customer.displayName == 'John Michael Doe'
        customer.createdBy == 'test-user'
        customer.dateCreated != null

        and: "Customer can be retrieved from database"
        def retrievedCustomer = Customer.get(customer.id)
        retrievedCustomer != null
        retrievedCustomer.customerId == customer.customerId
    }

    def "test customer update workflow"() {
        given: "An existing customer"
        def customer = createTestCustomer('Jane', 'Smith')
        
        and: "An update command"
        def updateCommand = new UpdateCustomerCommand(
            customerId: customer.id,
            firstName: 'Jane',
            middleName: 'Elizabeth',
            lastName: 'Smith-Johnson',
            email: '<EMAIL>',
            phoneNumber: '+1987654321',
            updatedBy: 'test-updater'
        )

        when: "Updating the customer"
        def updatedCustomer = customerDomainService.updateCustomer(updateCommand)

        then: "Customer is updated successfully"
        updatedCustomer.id == customer.id
        updatedCustomer.name2 == 'Elizabeth'
        updatedCustomer.name3 == 'Smith-Johnson'
        updatedCustomer.email == '<EMAIL>'
        updatedCustomer.phoneNumber == '+1987654321'
        updatedCustomer.displayName == 'Jane Elizabeth Smith-Johnson'
        updatedCustomer.lastUpdatedBy == 'test-updater'
        updatedCustomer.lastUpdatedAt != null
    }

    def "test customer activation workflow"() {
        given: "A pending customer"
        def customer = createTestCustomer('Bob', 'Wilson')
        
        when: "Activating the customer"
        def activatedCustomer = customerDomainService.activateCustomer(customer.id, 'test-activator')

        then: "Customer is activated successfully"
        activatedCustomer.status.itemValue == 'ACTIVE'
        activatedCustomer.dateActivated != null
        activatedCustomer.activatedBy == 'test-activator'
    }

    def "test optimized customer service performance"() {
        given: "Multiple test customers"
        def customers = []
        (1..10).each { i ->
            customers << createTestCustomer("Customer${i}", "Test${i}")
        }

        when: "Finding customers using optimized service"
        def startTime = System.currentTimeMillis()
        def foundCustomers = optimizedCustomerService.findCustomersOptimized(testBranch.id, null, 0, 20)
        def endTime = System.currentTimeMillis()
        def executionTime = endTime - startTime

        then: "Results are returned efficiently"
        foundCustomers.size() >= 10
        executionTime < 1000 // Should complete in less than 1 second
        
        and: "All customers have required relationships loaded"
        foundCustomers.each { customer ->
            assert customer.branch != null
            assert customer.type != null
            assert customer.status != null
        }
    }

    def "test customer search functionality"() {
        given: "Customers with searchable data"
        createTestCustomer('Alice', 'Johnson')
        createTestCustomer('Bob', 'Alice')
        createTestCustomer('Charlie', 'Brown')

        when: "Searching for customers"
        def searchResult = optimizedCustomerService.searchCustomersOptimized('Alice', 0, 10)

        then: "Search returns relevant results"
        searchResult.customers.size() >= 2
        searchResult.totalCount >= 2
        searchResult.customers.every { customer ->
            customer.name1.contains('Alice') || customer.name2?.contains('Alice') || customer.name3?.contains('Alice')
        }
    }

    def "test customer summary generation"() {
        given: "A customer with related data"
        def customer = createTestCustomer('Summary', 'Test')

        when: "Getting customer summary"
        def summary = optimizedCustomerService.getCustomerSummaryOptimized(customer.id)

        then: "Summary contains expected data"
        summary != null
        summary.customer.id == customer.id
        summary.depositCount != null
        summary.loanCount != null
        summary.totalDepositBalance != null
        summary.totalLoanBalance != null
        summary.netWorth != null
    }

    def "test business rule validation"() {
        given: "An invalid customer creation command"
        def invalidCommand = new CreateCustomerCommand(
            firstName: 'Under',
            lastName: 'Age',
            birthDate: new Date(), // Today's date - underage
            identificationNumber: 'ID999',
            branchId: testBranch.id,
            branch: testBranch,
            customerType: customerType,
            createdBy: 'test-user'
        )

        when: "Attempting to create underage customer"
        customerDomainService.createCustomer(invalidCommand)

        then: "Business rule violation is thrown"
        thrown(BusinessRuleViolationException)
    }

    def "test concurrent customer operations"() {
        given: "A customer for concurrent testing"
        def customer = createTestCustomer('Concurrent', 'Test')

        when: "Multiple threads update the customer simultaneously"
        def results = []
        def threads = []
        
        (1..5).each { i ->
            def thread = Thread.start {
                try {
                    def updateCommand = new UpdateCustomerCommand(
                        customerId: customer.id,
                        email: "concurrent${i}@example.com",
                        updatedBy: "thread-${i}"
                    )
                    def result = customerDomainService.updateCustomer(updateCommand)
                    synchronized(results) {
                        results << result
                    }
                } catch (Exception e) {
                    synchronized(results) {
                        results << e
                    }
                }
            }
            threads << thread
        }
        
        threads.each { it.join() }

        then: "At least one update succeeds"
        results.any { it instanceof Customer }
        
        and: "No data corruption occurs"
        def finalCustomer = Customer.get(customer.id)
        finalCustomer != null
        finalCustomer.email != null
    }

    def "test customer caching behavior"() {
        given: "A customer for cache testing"
        def customer = createTestCustomer('Cache', 'Test')

        when: "Accessing customer multiple times"
        def startTime1 = System.currentTimeMillis()
        def customer1 = optimizedCustomerService.findCustomerByIdOptimized(customer.id)
        def endTime1 = System.currentTimeMillis()
        def firstAccessTime = endTime1 - startTime1

        def startTime2 = System.currentTimeMillis()
        def customer2 = optimizedCustomerService.findCustomerByIdOptimized(customer.id)
        def endTime2 = System.currentTimeMillis()
        def secondAccessTime = endTime2 - startTime2

        then: "Second access is faster (cached)"
        customer1 != null
        customer2 != null
        customer1.id == customer2.id
        secondAccessTime <= firstAccessTime // Should be same or faster due to caching
    }

    def "test data integrity constraints"() {
        given: "A customer creation command with duplicate data"
        def command1 = new CreateCustomerCommand(
            firstName: 'Duplicate',
            lastName: 'Test',
            identificationNumber: 'DUPLICATE123',
            branchId: testBranch.id,
            branch: testBranch,
            customerType: customerType,
            createdBy: 'test-user'
        )

        when: "Creating first customer"
        def customer1 = customerDomainService.createCustomer(command1)

        and: "Attempting to create duplicate customer"
        def command2 = new CreateCustomerCommand(
            firstName: 'Duplicate',
            lastName: 'Test',
            identificationNumber: 'DUPLICATE123',
            branchId: testBranch.id,
            branch: testBranch,
            customerType: customerType,
            createdBy: 'test-user'
        )
        customerDomainService.createCustomer(command2)

        then: "First customer is created successfully"
        customer1 != null

        and: "Duplicate creation fails"
        thrown(BusinessRuleViolationException)
    }

    def "test transaction rollback on failure"() {
        given: "A customer creation that will fail"
        def invalidCommand = new CreateCustomerCommand(
            firstName: null, // This will cause validation failure
            lastName: 'Rollback',
            branchId: testBranch.id,
            branch: testBranch,
            customerType: customerType,
            createdBy: 'test-user'
        )

        when: "Attempting to create invalid customer"
        customerDomainService.createCustomer(invalidCommand)

        then: "Exception is thrown"
        thrown(Exception)

        and: "No partial data is saved"
        def customers = Customer.findAllByName3('Rollback')
        customers.size() == 0
    }

    // Helper method to create test customers
    private Customer createTestCustomer(String firstName, String lastName) {
        def command = new CreateCustomerCommand(
            firstName: firstName,
            lastName: lastName,
            birthDate: Date.parse('yyyy-MM-dd', '1985-06-15'),
            identificationNumber: "ID${firstName}${lastName}${System.currentTimeMillis()}",
            email: "${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com",
            phoneNumber: '+1234567890',
            branchId: testBranch.id,
            branch: testBranch,
            customerType: customerType,
            createdBy: 'test-user'
        )
        
        return customerDomainService.createCustomer(command)
    }
}
