package org.icbs

import grails.testing.gorm.DomainUnitTest
import grails.testing.services.ServiceUnitTest
import spock.lang.Specification
import spock.lang.Unroll
import org.icbs.common.CommonUtilityService
import org.icbs.search.UnifiedSearchService
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecureUser
import org.icbs.security.JwtTokenService
import org.icbs.security.EncryptionService
import org.icbs.transaction.TransactionProcessingService
import org.icbs.cif.CustomerBusinessService
import org.icbs.loans.LoanProcessingService
import org.icbs.workflow.WorkflowManagementService
import org.icbs.compliance.RegulatoryComplianceService
import org.icbs.exception.ExceptionManagementService

/**
 * Consolidated Test Suite
 * Tests all consolidated services and eliminates duplicate test code
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class ConsolidatedTestSuite extends Specification 
    implements ServiceUnitTest<CommonUtilityService> {
    
    UnifiedSearchService unifiedSearchService
    UnifiedValidationService unifiedValidationService
    JwtTokenService jwtTokenService
    EncryptionService encryptionService
    TransactionProcessingService transactionProcessingService
    CustomerBusinessService customerBusinessService
    LoanProcessingService loanProcessingService
    WorkflowManagementService workflowManagementService
    RegulatoryComplianceService regulatoryComplianceService
    ExceptionManagementService exceptionManagementService
    
    def setup() {
        // Mock services
        unifiedSearchService = Mock(UnifiedSearchService)
        unifiedValidationService = Mock(UnifiedValidationService)
        jwtTokenService = Mock(JwtTokenService)
        encryptionService = Mock(EncryptionService)
        transactionProcessingService = Mock(TransactionProcessingService)
        customerBusinessService = Mock(CustomerBusinessService)
        loanProcessingService = Mock(LoanProcessingService)
        workflowManagementService = Mock(WorkflowManagementService)
        regulatoryComplianceService = Mock(RegulatoryComplianceService)
        exceptionManagementService = Mock(ExceptionManagementService)
        
        // Setup test data
        mockDomains(SecureUser)
    }
    
    // =====================================================
    // COMMON UTILITY SERVICE TESTS
    // =====================================================
    
    def "test CommonUtilityService duplicate detection"() {
        given: "test data"
        def testClass = SecureUser
        def criteria = [username: 'testuser', email: '<EMAIL>']
        
        when: "checking for duplicates"
        boolean hasDuplicates = service.checkForDuplicates(testClass, criteria)
        
        then: "should return false for no duplicates"
        !hasDuplicates
    }
    
    def "test CommonUtilityService safe string comparison"() {
        expect: "safe string comparison works correctly"
        service.safeStringEquals(str1, str2) == expected
        
        where:
        str1      | str2      | expected
        "test"    | "test"    | true
        "Test"    | "test"    | true
        " test "  | "test"    | true
        null      | null      | true
        null      | "test"    | false
        "test"    | null      | false
        ""        | ""        | true
    }
    
    def "test CommonUtilityService unique code generation"() {
        when: "generating unique codes"
        String code1 = service.generateUniqueCode("TEST", 6)
        String code2 = service.generateUniqueCode("TEST", 6)
        
        then: "codes should be unique and have correct format"
        code1 != code2
        code1.startsWith("TEST")
        code1.length() == 10 // TEST + 6 digits
        code2.startsWith("TEST")
        code2.length() == 10
    }
    
    def "test CommonUtilityService currency formatting"() {
        expect: "currency formatting works correctly"
        service.formatCurrency(amount, currency) == expected
        
        where:
        amount          | currency | expected
        new BigDecimal("100.50") | "USD"    | "100.50 USD"
        new BigDecimal("0")      | "EUR"    | "0.00 EUR"
        null            | "USD"    | "0.00"
    }
    
    def "test CommonUtilityService input cleaning"() {
        expect: "input cleaning works correctly"
        service.cleanInput(input) == expected
        
        where:
        input           | expected
        "normal text"   | "normal text"
        " spaced text " | "spaced text"
        "<script>alert('xss')</script>" | "scriptalert('xss')/script"
        null            | null
        ""              | null
    }
    
    // =====================================================
    // VALIDATION SERVICE TESTS
    // =====================================================
    
    def "test email validation"() {
        given: "validation service"
        def validationService = new UnifiedValidationService()
        
        expect: "email validation works correctly"
        validationService.isValidEmail(email) == expected
        
        where:
        email                    | expected
        "<EMAIL>"       | true
        "<EMAIL>" | true
        "invalid.email"          | false
        "test@"                  | false
        "@domain.com"            | false
        null                     | false
        ""                       | false
    }
    
    def "test phone validation"() {
        given: "validation service"
        def validationService = new UnifiedValidationService()
        
        expect: "phone validation works correctly"
        validationService.isValidPhone(phone) == expected
        
        where:
        phone              | expected
        "**********"       | true
        "(*************"   | true
        "******-456-7890"  | true
        "123"              | false
        "**********1234567" | false
        null               | false
        ""                 | false
    }
    
    def "test account number validation"() {
        given: "validation service"
        def validationService = new UnifiedValidationService()
        
        expect: "account number validation works correctly"
        validationService.validateAccountNumber(accountNumber, accountType) == expected
        
        where:
        accountNumber    | accountType | expected
        "**********"     | null        | true
        "**********1234" | null        | true
        "**********"     | "SAVINGS"   | true
        "**********"     | "CHECKING"  | true
        "**********"     | "LOAN"      | true
        "*********"      | null        | false  // Too short
        "**********1234567" | null     | false  // Too long
        "abcd567890"     | null        | false  // Non-numeric
        null             | null        | false
    }
    
    def "test field validation with constraints"() {
        given: "validation service"
        def validationService = new UnifiedValidationService()
        
        when: "validating field with constraints"
        def result = validationService.validateField("testField", value, constraints)
        
        then: "validation result should match expected"
        result.isValid == expectedValid
        
        where:
        value    | constraints                              | expectedValid
        "test"   | [required: true, minLength: 3]         | true
        "te"     | [required: true, minLength: 3]         | false
        ""       | [required: true]                        | false
        "test"   | [required: false]                       | true
        "123"    | [numeric: true, min: 100, max: 200]    | false
        "150"    | [numeric: true, min: 100, max: 200]    | true
    }
    
    // =====================================================
    // SECURITY TESTS
    // =====================================================
    
    def "test SecureUser creation"() {
        when: "creating a secure user"
        def user = new SecureUser(
            username: 'testuser',
            password: 'TestPassword123!',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            employeeId: 'EMP001'
        )
        
        then: "user should be valid"
        user.validate()
        user.username == 'testuser'
        user.fullName == 'Test User'
    }
    
    @Unroll
    def "test SecureUser validation for invalid #field"() {
        given: "a user with invalid data"
        def user = new SecureUser(
            username: username,
            password: password,
            email: email,
            firstName: firstName,
            lastName: lastName
        )
        
        when: "validating the user"
        def isValid = user.validate()
        
        then: "validation should fail"
        !isValid
        user.errors.hasFieldErrors(field)
        
        where:
        field        | username    | password        | email              | firstName | lastName
        'username'   | ''          | 'ValidPass123!' | '<EMAIL>' | 'Test'    | 'User'
        'username'   | 'ab'        | 'ValidPass123!' | '<EMAIL>' | 'Test'    | 'User'
        'password'   | 'testuser'  | ''              | '<EMAIL>' | 'Test'    | 'User'
        'email'      | 'testuser'  | 'ValidPass123!' | 'invalid-email'    | 'Test'    | 'User'
        'firstName'  | 'testuser'  | 'ValidPass123!' | '<EMAIL>' | ''        | 'User'
        'lastName'   | 'testuser'  | 'ValidPass123!' | '<EMAIL>' | 'Test'    | ''
    }
    
    def "test password encryption and verification"() {
        given: "encryption service"
        def realEncryptionService = new EncryptionService()
        
        when: "hashing password"
        String plainPassword = "TestPassword123!"
        String hashedPassword = realEncryptionService.hashPassword(plainPassword)
        
        then: "password should be hashed correctly"
        hashedPassword != plainPassword
        hashedPassword.startsWith('$2a$') || hashedPassword.startsWith('$2b$')
        realEncryptionService.verifyPassword(plainPassword, hashedPassword)
        !realEncryptionService.verifyPassword('wrongpassword', hashedPassword)
    }
    
    def "test data masking"() {
        given: "encryption service"
        def realEncryptionService = new EncryptionService()
        
        expect: "data should be masked correctly"
        realEncryptionService.maskSensitiveData(input, type) == expected
        
        where:
        input              | type            | expected
        "**********"       | "ACCOUNT_NUMBER"| "******7890"
        "***********"      | "SSN"           | "***-**-6789"
        "<EMAIL>" | "EMAIL"         | "t***@example.com"
        "************"     | "PHONE"         | "*******4567"
    }
    
    def "test password strength validation"() {
        given: "encryption service"
        def realEncryptionService = new EncryptionService()
        
        expect: "password strength should be validated correctly"
        def result = realEncryptionService.validatePasswordStrength(password)
        result.isValid == expectedValid
        result.strength == expectedStrength
        
        where:
        password           | expectedValid | expectedStrength
        "TestPass123!"     | true          | "STRONG"
        "weakpass"         | false         | "VERY_WEAK"
        "TestPassword"     | false         | "WEAK"
        "TestPass123"      | false         | "MEDIUM"
        "VeryStrongPass1!" | true          | "VERY_STRONG"
    }
    
    // =====================================================
    // INTEGRATION TESTS
    // =====================================================
    
    def "test service integration"() {
        given: "multiple services working together"
        def commonService = new CommonUtilityService()
        def validationService = new UnifiedValidationService()
        
        when: "performing integrated operations"
        String cleanedInput = commonService.cleanInput(" <EMAIL> ")
        boolean isValidEmail = validationService.isValidEmail(cleanedInput)
        String uniqueCode = commonService.generateUniqueCode("USR", 4)
        
        then: "all operations should work correctly"
        cleanedInput == "<EMAIL>"
        isValidEmail
        uniqueCode.startsWith("USR")
        uniqueCode.length() == 7 // USR + 4 digits
    }
    
    def "test error handling in services"() {
        given: "services with potential error conditions"
        def commonService = new CommonUtilityService()
        
        when: "calling methods with invalid parameters"
        boolean result1 = commonService.checkForDuplicates(null, [:])
        String result2 = commonService.cleanInput(null)
        String result3 = commonService.formatCurrency(null)
        
        then: "services should handle errors gracefully"
        !result1  // Should return false for null class
        result2 == null  // Should return null for null input
        result3 == "0.00"  // Should return default for null amount
    }
    
    // =====================================================
    // PERFORMANCE TESTS
    // =====================================================
    
    def "test service performance"() {
        given: "services for performance testing"
        def commonService = new CommonUtilityService()
        
        when: "performing multiple operations"
        long startTime = System.currentTimeMillis()
        
        100.times {
            commonService.generateUniqueCode("TEST", 6)
            commonService.safeStringEquals("test", "TEST")
            commonService.cleanInput("<script>test</script>")
        }
        
        long endTime = System.currentTimeMillis()
        long duration = endTime - startTime
        
        then: "operations should complete within reasonable time"
        duration < 1000 // Should complete within 1 second
    }

    // =====================================================
    // BUSINESS LOGIC TESTS
    // =====================================================

    def "test transaction processing validation"() {
        given: "transaction parameters"
        Map params = [
            txnAmt: 5000.00,
            customer: [id: 1],
            txnTemplate: [id: 1],
            txnParticulars: "Test transaction"
        ]

        when: "processing transaction"
        transactionProcessingService.processCashTransfer(params, 1L) >> [
            success: true,
            txnFile: [id: 123, txnRef: "TXN123456"],
            message: "Transaction processed successfully"
        ]

        then: "transaction should be processed"
        def result = transactionProcessingService.processCashTransfer(params, 1L)
        result.success == true
        result.txnFile.id == 123
    }

    def "test customer business logic validation"() {
        given: "customer data"
        Map customerData = [
            name1: "John",
            lastName: "Doe",
            email: "<EMAIL>",
            phone: "************",
            birthDate: new Date() - (25 * 365), // 25 years old
            address1: "123 Main St",
            monthlyIncome: 5000.00
        ]

        when: "creating customer"
        customerBusinessService.createCustomer(customerData) >> [
            success: true,
            customer: [id: 1, customerId: "CUS123456", displayName: "John Doe"],
            message: "Customer created successfully"
        ]

        then: "customer should be created"
        def result = customerBusinessService.createCustomer(customerData)
        result.success == true
        result.customer.customerId == "CUS123456"
    }

    def "test loan processing business rules"() {
        given: "loan application data"
        Map loanData = [
            customerId: 1,
            requestedAmount: 25000.00,
            loanPurpose: "Home improvement",
            monthlyIncome: 5000.00,
            requestedTenure: 24
        ]

        when: "processing loan application"
        loanProcessingService.processLoanApplication(loanData) >> [
            success: true,
            application: [
                id: 1,
                applicationNumber: "LA123456",
                creditScore: 720,
                creditGrade: "B",
                approvedAmount: 25000.00,
                interestRate: 10.0
            ],
            decision: [decision: "APPROVED", statusId: 2]
        ]

        then: "loan should be processed"
        def result = loanProcessingService.processLoanApplication(loanData)
        result.success == true
        result.application.creditScore == 720
        result.decision.decision == "APPROVED"
    }

    def "test workflow state transitions"() {
        given: "workflow parameters"
        String workflowType = "LOAN_APPLICATION"
        String fromState = "SUBMITTED"
        String toState = "UNDER_REVIEW"
        Map context = [hasRequiredDocuments: true]

        when: "processing state transition"
        workflowManagementService.processStateTransition(workflowType, 1L, fromState, toState, context) >> [
            success: true,
            newState: "UNDER_REVIEW",
            message: "State transition completed successfully"
        ]

        then: "state should transition"
        def result = workflowManagementService.processStateTransition(workflowType, 1L, fromState, toState, context)
        result.success == true
        result.newState == "UNDER_REVIEW"
    }

    def "test AML compliance screening"() {
        given: "transaction data for AML screening"
        Map transactionData = [
            transactionId: "TXN123456",
            customerId: 1,
            amount: 15000.00,
            transactionType: "CASH_DEPOSIT",
            transactionDate: new Date()
        ]

        when: "performing AML screening"
        regulatoryComplianceService.performAMLScreening(transactionData) >> [
            passed: true,
            riskLevel: "MEDIUM",
            flags: ["LARGE_TRANSACTION"],
            recommendations: ["CTR filing may be required"]
        ]

        then: "AML screening should complete"
        def result = regulatoryComplianceService.performAMLScreening(transactionData)
        result.passed == true
        result.riskLevel == "MEDIUM"
        result.flags.contains("LARGE_TRANSACTION")
    }

    def "test exception handling and recovery"() {
        given: "an exception and context"
        Exception testException = new IllegalArgumentException("Invalid input")
        Map context = [operation: "customer_creation", userId: 1]

        when: "handling business exception"
        exceptionManagementService.handleBusinessException(testException, context) >> [
            handled: true,
            recovered: true,
            errorCode: "INVALID_INPUT",
            userMessage: "Please check your input and try again.",
            escalationRequired: false
        ]

        then: "exception should be handled"
        def result = exceptionManagementService.handleBusinessException(testException, context)
        result.handled == true
        result.recovered == true
        result.errorCode == "INVALID_INPUT"
    }

    def "test regulatory compliance CTR generation"() {
        given: "transaction requiring CTR"
        Map transactionData = [
            transactionId: "TXN789012",
            customerId: 1,
            amount: 12000.00,
            transactionType: "CASH_WITHDRAWAL",
            transactionDate: new Date()
        ]

        when: "generating CTR"
        regulatoryComplianceService.generateCTR(transactionData) >> [
            success: true,
            ctrNumber: "CTR*********",
            message: "CTR generated and submitted successfully"
        ]

        then: "CTR should be generated"
        def result = regulatoryComplianceService.generateCTR(transactionData)
        result.success == true
        result.ctrNumber.startsWith("CTR")
    }

    def "test workflow approval requirements"() {
        given: "approval parameters"
        String approvalType = "LOAN_APPLICATION"
        Long entityId = 1L
        BigDecimal amount = 75000.00

        when: "checking approval requirements"
        workflowManagementService.getApprovalRequirements(approvalType, entityId, amount) >> [
            required: true,
            levels: ["MANAGER", "SENIOR_MANAGER"],
            reasons: ["Amount exceeds 50000 threshold", "Amount exceeds 100000 threshold"]
        ]

        then: "approval requirements should be determined"
        def result = workflowManagementService.getApprovalRequirements(approvalType, entityId, amount)
        result.required == true
        result.levels.contains("MANAGER")
        result.levels.contains("SENIOR_MANAGER")
    }

    def "test customer risk profiling"() {
        given: "customer with financial data"
        Map customerData = [
            name1: "Jane",
            lastName: "Smith",
            birthDate: new Date() - (30 * 365), // 30 years old
            annualIncome: 75000.00,
            email: "<EMAIL>"
        ]

        when: "creating customer with risk profiling"
        customerBusinessService.createCustomer(customerData) >> [
            success: true,
            customer: [
                id: 2,
                customerId: "CUS789012",
                riskProfile: "LOW",
                customerTier: "GOLD",
                dailyTransactionLimit: 50000,
                monthlyTransactionLimit: 1000000
            ]
        ]

        then: "customer should be created with proper risk profile"
        def result = customerBusinessService.createCustomer(customerData)
        result.success == true
        result.customer.riskProfile == "LOW"
        result.customer.customerTier == "GOLD"
    }
}
