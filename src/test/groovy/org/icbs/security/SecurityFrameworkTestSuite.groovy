package org.icbs.security

import grails.testing.gorm.DomainUnitTest
import grails.testing.services.ServiceUnitTest
import spock.lang.Specification
import spock.lang.Unroll
import org.icbs.admin.Branch
import org.icbs.admin.Department

/**
 * Comprehensive Security Framework Test Suite
 * Tests all security components for banking system compliance
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 7.0
 */
class SecurityFrameworkTestSuite extends Specification 
    implements DomainUnitTest<SecureUser>, ServiceUnitTest<JwtTokenService> {
    
    EncryptionService encryptionService
    SecurityAuditService securityAuditService
    
    def setup() {
        // Mock services
        encryptionService = Mock(EncryptionService)
        securityAuditService = Mock(SecurityAuditService)
        
        // Setup test data
        mockDomains(SecureUser, SecureRole, SecureUserRole, SecurityAuditLog, 
                   UserSession, PasswordHistory, Branch, Department)
        
        // Create test branch and department
        def testBranch = new Branch(code: 'TEST001', name: 'Test Branch').save()
        def testDept = new Department(code: 'IT', name: 'Information Technology').save()
    }
    
    // =====================================================
    // SECURE USER DOMAIN TESTS
    // =====================================================
    
    def "test SecureUser creation with valid data"() {
        given: "valid user data"
        def branch = Branch.findByCode('TEST001')
        def department = Department.findByCode('IT')
        
        when: "creating a secure user"
        def user = new SecureUser(
            username: 'testuser',
            password: 'TestPassword123!',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            branch: branch,
            department: department,
            employeeId: 'EMP001'
        )
        
        then: "user should be valid"
        user.validate()
        user.save()
        user.id != null
        user.username == 'testuser'
        user.fullName == 'Test User'
    }
    
    @Unroll
    def "test SecureUser validation for invalid #field"() {
        given: "a user with invalid data"
        def branch = Branch.findByCode('TEST001')
        def department = Department.findByCode('IT')
        
        def user = new SecureUser(
            username: username,
            password: password,
            email: email,
            firstName: firstName,
            lastName: lastName,
            branch: branch,
            department: department
        )
        
        when: "validating the user"
        def isValid = user.validate()
        
        then: "validation should fail"
        !isValid
        user.errors.hasFieldErrors(field)
        
        where:
        field        | username    | password        | email              | firstName | lastName
        'username'   | ''          | 'ValidPass123!' | '<EMAIL>' | 'Test'    | 'User'
        'username'   | 'ab'        | 'ValidPass123!' | '<EMAIL>' | 'Test'    | 'User'
        'username'   | 'admin'     | 'ValidPass123!' | '<EMAIL>' | 'Test'    | 'User'
        'password'   | 'testuser'  | ''              | '<EMAIL>' | 'Test'    | 'User'
        'password'   | 'testuser'  | 'weak'          | '<EMAIL>' | 'Test'    | 'User'
        'password'   | 'testuser'  | 'testuser123'   | '<EMAIL>' | 'Test'    | 'User'
        'email'      | 'testuser'  | 'ValidPass123!' | 'invalid-email'    | 'Test'    | 'User'
        'firstName'  | 'testuser'  | 'ValidPass123!' | '<EMAIL>' | ''        | 'User'
        'lastName'   | 'testuser'  | 'ValidPass123!' | '<EMAIL>' | 'Test'    | ''
    }
    
    def "test SecureUser role management"() {
        given: "a user and roles"
        def user = createTestUser()
        def adminRole = new SecureRole(authority: 'ROLE_ADMIN', description: 'Administrator').save()
        def userRole = new SecureRole(authority: 'ROLE_USER', description: 'User').save()
        
        when: "assigning roles to user"
        SecureUserRole.create(user, adminRole, 'SYSTEM', 'Initial assignment')
        SecureUserRole.create(user, userRole, 'SYSTEM', 'Initial assignment')
        
        then: "user should have roles"
        user.authorities.size() == 2
        user.hasRole('ROLE_ADMIN')
        user.hasRole('ROLE_USER')
        user.hasAnyRole('ROLE_ADMIN', 'ROLE_SUPER_ADMIN')
    }
    
    def "test SecureUser password management"() {
        given: "a user"
        def user = createTestUser()
        def oldPassword = user.password
        
        when: "updating password"
        user.updatePassword('NewPassword123!')
        
        then: "password should be updated"
        user.password != oldPassword
        user.passwordVersion == 2
        user.passwordLastChanged != null
        !user.passwordExpired
    }
    
    def "test SecureUser failed login tracking"() {
        given: "a user"
        def user = createTestUser()
        
        when: "recording failed login attempts"
        5.times {
            user.recordFailedLogin('***********')
        }
        
        then: "account should be locked"
        user.failedLoginAttempts == 5
        user.accountLocked
        user.isAccountLockedByFailedAttempts()
    }
    
    // =====================================================
    // JWT TOKEN SERVICE TESTS
    // =====================================================
    
    def "test JWT token generation and validation"() {
        given: "a valid user"
        def user = createTestUser()
        service.jwtSecret = "testSecretKeyForJWTTokenGeneration"
        service.jwtExpirationInSeconds = 3600
        service.jwtIssuer = "QwikBanka"
        service.jwtAudience = "QwikBanka-Users"
        
        when: "generating JWT token"
        String token = service.generateAccessToken(user, 'session123')
        
        then: "token should be valid"
        token != null
        service.validateToken(token)
        service.getUsernameFromToken(token) == user.username
        service.getUserIdFromToken(token) == user.id
        service.getSessionIdFromToken(token) == 'session123'
    }
    
    def "test JWT token expiration"() {
        given: "a user and expired token settings"
        def user = createTestUser()
        service.jwtSecret = "testSecretKeyForJWTTokenGeneration"
        service.jwtExpirationInSeconds = -1 // Expired immediately
        service.jwtIssuer = "QwikBanka"
        service.jwtAudience = "QwikBanka-Users"
        
        when: "generating and validating expired token"
        String token = service.generateAccessToken(user)
        
        then: "token should be invalid due to expiration"
        token != null
        !service.validateToken(token)
        service.isTokenExpired(token)
    }
    
    def "test JWT refresh token functionality"() {
        given: "a user"
        def user = createTestUser()
        service.jwtSecret = "testSecretKeyForJWTTokenGeneration"
        service.jwtExpirationInSeconds = 3600
        service.refreshTokenExpirationInSeconds = 7200
        service.jwtIssuer = "QwikBanka"
        service.jwtAudience = "QwikBanka-Users"
        
        when: "generating token pair"
        Map tokenPair = service.generateTokenPair(user, 'session123')
        
        then: "should have both tokens"
        tokenPair.accessToken != null
        tokenPair.refreshToken != null
        tokenPair.tokenType == 'Bearer'
        tokenPair.expiresIn == 3600
        
        when: "refreshing access token"
        String newAccessToken = service.refreshAccessToken(tokenPair.refreshToken)
        
        then: "should get new access token"
        newAccessToken != null
        newAccessToken != tokenPair.accessToken
        service.validateToken(newAccessToken)
    }
    
    def "test JWT token revocation"() {
        given: "a valid token"
        def user = createTestUser()
        service.jwtSecret = "testSecretKeyForJWTTokenGeneration"
        service.jwtExpirationInSeconds = 3600
        service.jwtIssuer = "QwikBanka"
        service.jwtAudience = "QwikBanka-Users"
        
        String token = service.generateAccessToken(user)
        
        when: "revoking token"
        service.revokeToken(token)
        
        then: "token should be invalid"
        service.isTokenRevoked(token)
        !service.validateToken(token)
    }
    
    // =====================================================
    // ENCRYPTION SERVICE TESTS
    // =====================================================
    
    def "test password hashing and verification"() {
        given: "encryption service"
        def realEncryptionService = new EncryptionService()
        
        when: "hashing password"
        String plainPassword = "TestPassword123!"
        String hashedPassword = realEncryptionService.hashPassword(plainPassword)
        
        then: "password should be hashed correctly"
        hashedPassword != plainPassword
        hashedPassword.startsWith('$2a$') || hashedPassword.startsWith('$2b$')
        realEncryptionService.verifyPassword(plainPassword, hashedPassword)
        !realEncryptionService.verifyPassword('wrongpassword', hashedPassword)
    }
    
    def "test sensitive data encryption"() {
        given: "encryption service"
        def realEncryptionService = new EncryptionService()
        realEncryptionService.masterKey = "TestMasterKeyForEncryption123!"
        
        when: "encrypting sensitive data"
        String plainText = "***********"
        String encrypted = realEncryptionService.encryptSensitiveData(plainText, 'SSN')
        String decrypted = realEncryptionService.decryptSensitiveData(encrypted, 'SSN')
        
        then: "data should be encrypted and decrypted correctly"
        encrypted != plainText
        decrypted == plainText
    }
    
    def "test data masking"() {
        given: "encryption service"
        def realEncryptionService = new EncryptionService()
        
        expect: "data should be masked correctly"
        realEncryptionService.maskSensitiveData(input, type) == expected
        
        where:
        input              | type            | expected
        "**********"       | "ACCOUNT_NUMBER"| "******7890"
        "***********"      | "SSN"           | "***-**-6789"
        "<EMAIL>" | "EMAIL"         | "t***@example.com"
        "************"     | "PHONE"         | "*******4567"
    }
    
    def "test password strength validation"() {
        given: "encryption service"
        def realEncryptionService = new EncryptionService()
        
        expect: "password strength should be validated correctly"
        def result = realEncryptionService.validatePasswordStrength(password)
        result.isValid == expectedValid
        result.strength == expectedStrength
        
        where:
        password           | expectedValid | expectedStrength
        "TestPass123!"     | true          | "STRONG"
        "weakpass"         | false         | "VERY_WEAK"
        "TestPassword"     | false         | "WEAK"
        "TestPass123"      | false         | "MEDIUM"
        "VeryStrongPass1!" | true          | "VERY_STRONG"
    }
    
    // =====================================================
    // SECURITY AUDIT TESTS
    // =====================================================
    
    def "test security audit log creation"() {
        given: "audit parameters"
        Map auditParams = [
            eventType: 'LOGIN_SUCCESS',
            eventCategory: 'AUTHENTICATION',
            eventDescription: 'User successfully logged in',
            username: 'testuser',
            result: 'SUCCESS',
            ipAddress: '***********'
        ]
        
        when: "creating audit log"
        SecurityAuditLog auditLog = SecurityAuditLog.createAuditLog(auditParams)
        
        then: "audit log should be created"
        auditLog != null
        auditLog.eventType == 'LOGIN_SUCCESS'
        auditLog.username == 'testuser'
        auditLog.result == 'SUCCESS'
        auditLog.severityLevel == 'LOW'
    }
    
    def "test high priority security events"() {
        given: "high priority event parameters"
        Map auditParams = [
            eventType: 'SECURITY_VIOLATION',
            eventCategory: 'SECURITY',
            eventDescription: 'Unauthorized access attempt',
            username: 'attacker',
            result: 'BLOCKED',
            ipAddress: '*************'
        ]
        
        when: "creating high priority audit log"
        SecurityAuditLog auditLog = SecurityAuditLog.createAuditLog(auditParams)
        
        then: "should be marked for review"
        auditLog.severityLevel == 'CRITICAL'
        auditLog.requiresImmediateAttention()
        auditLog.isSecurityEvent()
    }
    
    // =====================================================
    // SESSION MANAGEMENT TESTS
    // =====================================================
    
    def "test user session creation and management"() {
        given: "user and session data"
        def user = createTestUser()
        String sessionId = UUID.randomUUID().toString()
        
        when: "creating user session"
        UserSession session = UserSession.createSession(user.userMaster, sessionId, '***********')
        
        then: "session should be created"
        session != null
        session.sessionId == sessionId
        session.username == user.username
        session.isActive
        !session.isExpired()
    }
    
    def "test session expiration"() {
        given: "an expired session"
        def user = createTestUser()
        UserSession session = UserSession.createSession(user.userMaster, UUID.randomUUID().toString(), '***********')
        session.lastActivity = new Date() - 1 // 1 day ago
        session.timeoutMinutes = 30
        session.save()
        
        when: "checking expiration"
        boolean expired = session.isExpired()
        
        then: "session should be expired"
        expired
        session.getRemainingMinutes() == 0
    }
    
    // =====================================================
    // PASSWORD HISTORY TESTS
    // =====================================================
    
    def "test password history tracking"() {
        given: "a user and password history"
        def user = createTestUser()
        
        when: "creating password history entries"
        PasswordHistory.createEntry(user, 'oldPasswordHash1', [changeReason: 'USER_INITIATED'])
        PasswordHistory.createEntry(user, 'oldPasswordHash2', [changeReason: 'POLICY_EXPIRY'])
        
        then: "password history should be tracked"
        def history = PasswordHistory.getHistoryForUser(user)
        history.size() == 2
        PasswordHistory.isPasswordReused(user, 'oldPasswordHash1')
        !PasswordHistory.isPasswordReused(user, 'newPasswordHash')
    }
    
    // =====================================================
    // INTEGRATION TESTS
    // =====================================================
    
    def "test complete authentication flow"() {
        given: "a user and services"
        def user = createTestUser()
        def realJwtService = new JwtTokenService()
        realJwtService.jwtSecret = "testSecretKeyForJWTTokenGeneration"
        realJwtService.jwtExpirationInSeconds = 3600
        realJwtService.jwtIssuer = "QwikBanka"
        realJwtService.jwtAudience = "QwikBanka-Users"
        
        when: "performing complete authentication"
        // 1. Generate tokens
        Map tokenPair = realJwtService.generateTokenPair(user, 'session123')
        
        // 2. Validate access token
        boolean tokenValid = realJwtService.validateToken(tokenPair.accessToken)
        
        // 3. Extract user info
        String username = realJwtService.getUsernameFromToken(tokenPair.accessToken)
        
        then: "authentication flow should work"
        tokenPair.accessToken != null
        tokenPair.refreshToken != null
        tokenValid
        username == user.username
    }
    
    // =====================================================
    // HELPER METHODS
    // =====================================================
    
    private SecureUser createTestUser() {
        def branch = Branch.findByCode('TEST001')
        def department = Department.findByCode('IT')
        
        return new SecureUser(
            username: 'testuser',
            password: '$2a$12$hashedPasswordExample',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            branch: branch,
            department: department,
            employeeId: 'EMP001',
            enabled: true
        ).save(flush: true)
    }
}
