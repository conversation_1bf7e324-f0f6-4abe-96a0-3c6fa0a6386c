package org.icbs.performance

import grails.testing.mixin.integration.Integration
import grails.gorm.transactions.Rollback
import spock.lang.Specification
import spock.lang.Shared
import org.icbs.cif.Customer
import org.icbs.deposit.Deposit
import org.icbs.loans.Loan
import org.icbs.admin.Branch
import org.icbs.lov.LovItem
import org.icbs.cif.OptimizedCustomerService
import org.icbs.deposit.OptimizedDepositService
import org.icbs.monitoring.PerformanceMonitoringService
import org.springframework.beans.factory.annotation.Autowired
import java.util.concurrent.Executors
import java.util.concurrent.Future
import java.util.concurrent.TimeUnit

/**
 * COMPREHENSIVE TESTING: Performance tests for QwikBanka system
 * Tests system performance under various load conditions
 */
@Integration
@Rollback
class PerformanceTestSpec extends Specification {

    @Autowired
    OptimizedCustomerService optimizedCustomerService
    
    @Autowired
    OptimizedDepositService optimizedDepositService
    
    @Autowired
    PerformanceMonitoringService performanceMonitoringService
    
    @Shared
    Branch testBranch
    
    @Shared
    List<Customer> testCustomers = []
    
    @Shared
    List<Deposit> testDeposits = []

    def setupSpec() {
        // Create test branch
        testBranch = new Branch(
            branchName: 'Performance Test Branch',
            branchCode: 'PERF001',
            address: 'Performance Test Address'
        ).save(flush: true)
        
        // Create customer type and status
        def customerType = new LovItem(
            itemType: 'CUSTOMER_TYPE',
            itemValue: 'INDIVIDUAL',
            itemDesc: 'Individual Customer'
        ).save(flush: true)
        
        def customerStatus = new LovItem(
            itemType: 'CUSTOMER_STATUS',
            itemValue: 'ACTIVE',
            itemDesc: 'Active Customer'
        ).save(flush: true)
        
        def depositType = new LovItem(
            itemType: 'DEPOSIT_TYPE',
            itemValue: 'SAVINGS',
            itemDesc: 'Savings Account'
        ).save(flush: true)
        
        def depositStatus = new LovItem(
            itemType: 'DEPOSIT_STATUS',
            itemValue: 'ACTIVE',
            itemDesc: 'Active Deposit'
        ).save(flush: true)
        
        // Create test customers for performance testing
        (1..1000).each { i ->
            def customer = new Customer(
                customerId: "PERF${String.format('%06d', i)}",
                name1: "Customer${i}",
                name2: "Test",
                name3: "Performance",
                displayName: "Customer${i} Test Performance",
                email: "customer${i}@performance.test",
                phoneNumber: "+*********${i}",
                branch: testBranch,
                type: customerType,
                status: customerStatus,
                dateCreated: new Date(),
                createdBy: 'performance-test'
            ).save(flush: true)
            
            testCustomers << customer
            
            // Create deposits for some customers
            if (i % 10 == 0) {
                def deposit = new Deposit(
                    acctNo: "DEP${String.format('%06d', i)}",
                    acctName: "Performance Deposit ${i}",
                    customer: customer,
                    branch: testBranch,
                    type: depositType,
                    status: depositStatus,
                    ledgerBalAmt: 1000.0 + (i * 10),
                    availableBalAmt: 1000.0 + (i * 10),
                    dateOpened: new Date()
                ).save(flush: true)
                
                testDeposits << deposit
            }
        }
        
        // Flush to ensure all data is persisted
        Customer.withSession { session ->
            session.flush()
            session.clear()
        }
    }

    def "test customer list performance with large dataset"() {
        given: "A large number of customers in the system"
        def customerCount = Customer.countByBranch(testBranch)
        assert customerCount >= 1000

        when: "Retrieving customers with pagination"
        def startTime = System.currentTimeMillis()
        def customers = optimizedCustomerService.findCustomersOptimized(testBranch.id, null, 0, 50)
        def endTime = System.currentTimeMillis()
        def executionTime = endTime - startTime

        then: "Query executes within acceptable time"
        customers.size() == 50
        executionTime < 500 // Should complete in less than 500ms
        
        and: "All relationships are properly loaded"
        customers.each { customer ->
            assert customer.branch != null
            assert customer.type != null
            assert customer.status != null
        }
    }

    def "test customer search performance"() {
        given: "A search term that will match multiple customers"
        def searchTerm = "Performance"

        when: "Performing search operation"
        def startTime = System.currentTimeMillis()
        def searchResult = optimizedCustomerService.searchCustomersOptimized(searchTerm, 0, 20)
        def endTime = System.currentTimeMillis()
        def executionTime = endTime - startTime

        then: "Search completes quickly"
        executionTime < 300 // Should complete in less than 300ms
        searchResult.customers.size() <= 20
        searchResult.totalCount >= 20
        
        and: "Results are relevant"
        searchResult.customers.every { customer ->
            customer.displayName.contains(searchTerm) || 
            customer.name1.contains(searchTerm) ||
            customer.name3.contains(searchTerm)
        }
    }

    def "test concurrent customer access performance"() {
        given: "Multiple threads accessing customer data"
        def executor = Executors.newFixedThreadPool(10)
        def futures = []

        when: "Multiple threads access customers simultaneously"
        def startTime = System.currentTimeMillis()
        
        (1..50).each { i ->
            def future = executor.submit({
                def randomCustomerId = testCustomers[i % testCustomers.size()].id
                return optimizedCustomerService.findCustomerByIdOptimized(randomCustomerId)
            })
            futures << future
        }
        
        // Wait for all threads to complete
        def results = futures.collect { it.get(5, TimeUnit.SECONDS) }
        def endTime = System.currentTimeMillis()
        def totalExecutionTime = endTime - startTime

        then: "All operations complete successfully"
        results.size() == 50
        results.every { it != null }
        totalExecutionTime < 2000 // Should complete in less than 2 seconds
        
        cleanup:
        executor.shutdown()
    }

    def "test deposit operations performance"() {
        given: "Multiple deposits in the system"
        assert testDeposits.size() >= 100

        when: "Retrieving deposits with relationships"
        def startTime = System.currentTimeMillis()
        def deposits = optimizedDepositService.findDepositsOptimized(testBranch.id, null, 0, 50)
        def endTime = System.currentTimeMillis()
        def executionTime = endTime - startTime

        then: "Query executes efficiently"
        deposits.size() <= 50
        executionTime < 400 // Should complete in less than 400ms
        
        and: "All relationships are loaded"
        deposits.each { deposit ->
            assert deposit.customer != null
            assert deposit.branch != null
            assert deposit.type != null
            assert deposit.status != null
        }
    }

    def "test memory usage during large operations"() {
        given: "Initial memory state"
        System.gc() // Force garbage collection
        def runtime = Runtime.getRuntime()
        def initialMemory = runtime.totalMemory() - runtime.freeMemory()

        when: "Performing memory-intensive operations"
        def customers = optimizedCustomerService.findCustomersOptimized(testBranch.id, null, 0, 500)
        def deposits = optimizedDepositService.findDepositsOptimized(testBranch.id, null, 0, 100)
        
        // Process the data
        def summaries = customers.collect { customer ->
            optimizedCustomerService.getCustomerSummaryOptimized(customer.id)
        }

        def finalMemory = runtime.totalMemory() - runtime.freeMemory()
        def memoryIncrease = finalMemory - initialMemory

        then: "Memory usage is reasonable"
        customers.size() <= 500
        deposits.size() <= 100
        summaries.size() <= 500
        memoryIncrease < 100 * 1024 * 1024 // Less than 100MB increase
    }

    def "test database connection pool performance"() {
        given: "Multiple concurrent database operations"
        def executor = Executors.newFixedThreadPool(20)
        def futures = []

        when: "Executing many database operations simultaneously"
        def startTime = System.currentTimeMillis()
        
        (1..100).each { i ->
            def future = executor.submit({
                // Simulate various database operations
                def customer = Customer.get(testCustomers[i % testCustomers.size()].id)
                def deposits = Deposit.findAllByCustomer(customer)
                return [customer: customer, deposits: deposits]
            })
            futures << future
        }
        
        def results = futures.collect { it.get(10, TimeUnit.SECONDS) }
        def endTime = System.currentTimeMillis()
        def totalExecutionTime = endTime - startTime

        then: "All operations complete without connection pool exhaustion"
        results.size() == 100
        results.every { it.customer != null }
        totalExecutionTime < 5000 // Should complete in less than 5 seconds
        
        cleanup:
        executor.shutdown()
    }

    def "test caching effectiveness"() {
        given: "A customer for cache testing"
        def testCustomer = testCustomers[0]

        when: "Accessing customer data multiple times"
        def times = []
        
        // First access (cache miss)
        def start1 = System.currentTimeMillis()
        def customer1 = optimizedCustomerService.findCustomerByIdOptimized(testCustomer.id)
        def end1 = System.currentTimeMillis()
        times << (end1 - start1)
        
        // Second access (cache hit)
        def start2 = System.currentTimeMillis()
        def customer2 = optimizedCustomerService.findCustomerByIdOptimized(testCustomer.id)
        def end2 = System.currentTimeMillis()
        times << (end2 - start2)
        
        // Third access (cache hit)
        def start3 = System.currentTimeMillis()
        def customer3 = optimizedCustomerService.findCustomerByIdOptimized(testCustomer.id)
        def end3 = System.currentTimeMillis()
        times << (end3 - start3)

        then: "Subsequent accesses are faster due to caching"
        customer1 != null
        customer2 != null
        customer3 != null
        customer1.id == customer2.id
        customer2.id == customer3.id
        
        // Cache hits should be faster or equal
        times[1] <= times[0]
        times[2] <= times[0]
    }

    def "test performance monitoring accuracy"() {
        given: "Performance monitoring is active"
        def initialMetrics = performanceMonitoringService.getCurrentMetrics()

        when: "Performing monitored operations"
        def startTime = System.currentTimeMillis()
        
        // Simulate various operations
        (1..10).each { i ->
            def customer = optimizedCustomerService.findCustomerByIdOptimized(testCustomers[i].id)
            performanceMonitoringService.recordRequest("customer.findById", 50 + i)
        }
        
        def endTime = System.currentTimeMillis()
        def finalMetrics = performanceMonitoringService.getCurrentMetrics()

        then: "Performance metrics are captured accurately"
        finalMetrics != null
        finalMetrics.requests != null
        finalMetrics.memory != null
        finalMetrics.database != null
        
        and: "Request metrics show recorded operations"
        finalMetrics.requests.containsKey("customer.findById")
        finalMetrics.requests["customer.findById"].requestCount >= 10
    }

    def "test system performance under stress"() {
        given: "High load simulation setup"
        def executor = Executors.newFixedThreadPool(50)
        def futures = []
        def errors = []

        when: "Simulating high concurrent load"
        def startTime = System.currentTimeMillis()
        
        (1..200).each { i ->
            def future = executor.submit({
                try {
                    // Mix of different operations
                    if (i % 4 == 0) {
                        return optimizedCustomerService.findCustomersOptimized(testBranch.id, null, 0, 10)
                    } else if (i % 4 == 1) {
                        return optimizedCustomerService.searchCustomersOptimized("Test", 0, 5)
                    } else if (i % 4 == 2) {
                        def customerId = testCustomers[i % testCustomers.size()].id
                        return optimizedCustomerService.getCustomerSummaryOptimized(customerId)
                    } else {
                        return optimizedDepositService.findDepositsOptimized(testBranch.id, null, 0, 10)
                    }
                } catch (Exception e) {
                    synchronized(errors) {
                        errors << e
                    }
                    return null
                }
            })
            futures << future
        }
        
        def results = futures.collect { 
            try {
                return it.get(30, TimeUnit.SECONDS)
            } catch (Exception e) {
                synchronized(errors) {
                    errors << e
                }
                return null
            }
        }
        
        def endTime = System.currentTimeMillis()
        def totalExecutionTime = endTime - startTime

        then: "System handles stress load gracefully"
        results.size() == 200
        errors.size() < 10 // Less than 5% error rate
        totalExecutionTime < 15000 // Should complete in less than 15 seconds
        
        and: "Most operations succeed"
        def successfulResults = results.findAll { it != null }
        successfulResults.size() >= 190 // At least 95% success rate
        
        cleanup:
        executor.shutdown()
    }

    def "test query optimization effectiveness"() {
        given: "A complex query scenario"
        def branchId = testBranch.id

        when: "Executing optimized vs non-optimized queries"
        // Optimized query with proper joins
        def startOptimized = System.currentTimeMillis()
        def optimizedResults = optimizedCustomerService.findCustomersOptimized(branchId, null, 0, 100)
        def endOptimized = System.currentTimeMillis()
        def optimizedTime = endOptimized - startOptimized
        
        // Simulate non-optimized query (N+1 problem)
        def startNonOptimized = System.currentTimeMillis()
        def customers = Customer.findAllByBranch(testBranch, [max: 100])
        customers.each { customer ->
            // This would cause N+1 queries in non-optimized version
            customer.branch.branchName
            customer.type.itemValue
            customer.status.itemValue
        }
        def endNonOptimized = System.currentTimeMillis()
        def nonOptimizedTime = endNonOptimized - startNonOptimized

        then: "Optimized queries perform significantly better"
        optimizedResults.size() <= 100
        customers.size() <= 100
        optimizedTime < nonOptimizedTime // Optimized should be faster
        optimizedTime < 500 // Should complete in less than 500ms
    }
}
