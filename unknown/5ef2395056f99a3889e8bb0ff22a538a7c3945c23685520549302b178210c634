package org.icbs.periodicops

import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonOutput
import org.grails.web.json.JSONObject
import groovy.time.TimeCategory
import org.icbs.admin.UserMaster
import java.nio.file.*
import org.icbs.admin.Branch
import org.icbs.admin.Holiday
import org.icbs.admin.UserMaster
import org.icbs.admin.CheckDepositClearingType
import org.icbs.admin.Institution
import org.icbs.admin.BranchHoliday
import org.icbs.security.UserSession
import org.icbs.lov.BranchRunStatus
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.gl.GlBatchHdr
import org.icbs.gl.GlBatch
import org.icbs.gl.GlDailyFile
import org.icbs.gl.GlMonthlyBalance
import org.icbs.gl.GlAccount
import org.icbs.deposit.Deposit
import org.icbs.periodicops.PeriodicOpsLog
import org.icbs.periodicops.DailyLoanRecoveries
import org.icbs.lov.ConfigItemStatus
import org.icbs.lov.DepositStatus
import org.icbs.lov.DepositType
import org.icbs.loans.Loan
import org.icbs.lov.LoanAcctStatus
import org.icbs.lov.LoanInstallmentStatus
import org.icbs.lov.GlBatchHdrStatus
import org.icbs.loans.Loan
import org.icbs.loans.LoanLedger
import org.icbs.tellering.TxnBreakdown
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnTellerBalance
import org.icbs.tellering.TxnPassbookLine
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.cif.Customer
import grails.gorm.transactions.Transactional
import org.apache.commons.io.FileUtils
import org.apache.tools.zip.ZipEntry
import java.text.SimpleDateFormat
import java.util.zip.ZipOutputStream
import java.util.Calendar
import org.icbs.admin.UserMessage
import static grails.async.Promises.*
import groovy.sql.Sql
import org.icbs.tellering.TxnCOCI
import org.icbs.tellering.TxnDepositAcctLedger
import org.icbs.tellering.TxnFile
import org.icbs.lov.CheckStatus
import org.icbs.lov.TxnCheckStatus
import org.icbs.periodicops.DailyBalance
import org.icbs.periodicops.MonthlyBalance
import org.icbs.periodicops.MonthlyPointerLoan
import org.icbs.periodicops.MonthlyCustomer
import org.icbs.periodicops.DailyCheckClearing

/**
 * PeriodicUtilityController - Handles utility operations and helper functions
 * 
 * This controller manages utility operations including:
 * - Periodic operations logging and monitoring
 * - GORM session cleanup utilities
 * - System utility functions
 * - Helper methods for periodic operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class PeriodicUtilityController {
    
    // Service Dependencies
    def periodicOpsService
    def loanPeriodicOpsService
    def IsTelleringActiveService
    def depositPeriodicOpsService
    def jasperService
    def AuditLogService
    def GlTransactionService
    def UserMasterService
    def sessionFactory
    def dataSource
    def glPeriodicOpsService

    /**
     * Display the periodic utilities index page
     * @return rendered view with utility options
     */
    def index() {
        try {
            def runDate = Branch.read(1).runDate
            render(view: "index", model: [currentDate: runDate])
        } catch (Exception e) {
            log.error("Error loading periodic utilities index: ${e.message}", e)
            flash.message = "Error loading periodic utilities page |error|alert"
            redirect(controller: "periodicOps", action: "index")
        }
    }

    /**
     * Display periodic operations log with pagination
     * @param max Maximum number of records to display
     */
    def periodicOpsLog(Integer max) {
        try {
            log.info("Displaying periodic ops log with params: ${params}")
            
            params.max = Math.min(max ?: 10, 100)
            
            def periodicOpsLogList = PeriodicOpsLog.createCriteria().list(params) {
                order("runDate", "desc")
                order("startTime", "desc")
            }
            
            respond periodicOpsLogList, model: [
                periodicOpsLogList: periodicOpsLogList,
                periodicOpsLogInstanceCount: periodicOpsLogList.totalCount
            ]
            
        } catch (Exception e) {
            log.error("Error displaying periodic ops log: ${e.message}", e)
            flash.message = "Error loading periodic operations log |error|alert"
            redirect(action: "index")
        }
    }

    /**
     * Get periodic operations log data as JSON
     * @return JSON response with log data
     */
    def getPeriodicOpsLogData() {
        try {
            def max = params.max ? params.max.toInteger() : 10
            def offset = params.offset ? params.offset.toInteger() : 0
            
            def logs = PeriodicOpsLog.createCriteria().list(max: max, offset: offset) {
                order("runDate", "desc")
                order("startTime", "desc")
            }
            
            def logData = logs.collect { log ->
                [
                    id: log.id,
                    runDate: log.runDate?.format('yyyy-MM-dd'),
                    processType: log.processType,
                    startTime: log.startTime,
                    endTime: log.endTime,
                    status: getStatusDescription(log.status),
                    duration: calculateDuration(log.startTime, log.endTime)
                ]
            }
            
            render([
                logs: logData,
                totalCount: PeriodicOpsLog.count(),
                success: true
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error getting periodic ops log data: ${e.message}", e)
            render([
                success: false,
                error: "Unable to get log data: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Show periodic operations success page
     * @param pLog The periodic operations log instance
     */
    def periodicOpsSuccess(PeriodicOpsLog pLog) {
        try {
            log.info("Displaying periodic ops success page with params: ${params}")
            
            def periodicLogInstance = PeriodicOpsLog.get(params.id)
            
            if (!periodicLogInstance) {
                flash.message = "Periodic operations log not found |error|alert"
                redirect(action: "index")
                return
            }
            
            render(view: "periodicOpsSuccess", model: [
                periodicLogInstance: periodicLogInstance
            ])
            
        } catch (Exception e) {
            log.error("Error displaying periodic ops success: ${e.message}", e)
            flash.message = "Error loading success page |error|alert"
            redirect(action: "index")
        }
    }

    /**
     * Clean up GORM session manually
     * @return JSON response with cleanup results
     */
    def cleanUpGormSession() {
        try {
            cleanUpGorm()
            
            render([
                success: true,
                message: "GORM session cleaned up successfully",
                timestamp: new Date()
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error cleaning up GORM session: ${e.message}", e)
            render([
                success: false,
                error: "GORM cleanup failed: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get system memory information
     * @return JSON response with memory data
     */
    def getSystemMemoryInfo() {
        try {
            def runtime = Runtime.getRuntime()
            def totalMemory = runtime.totalMemory()
            def freeMemory = runtime.freeMemory()
            def usedMemory = totalMemory - freeMemory
            def maxMemory = runtime.maxMemory()
            
            def memoryInfo = [
                totalMemory: totalMemory,
                freeMemory: freeMemory,
                usedMemory: usedMemory,
                maxMemory: maxMemory,
                usedPercentage: (usedMemory * 100 / totalMemory).intValue(),
                totalMemoryMB: (totalMemory / 1024 / 1024).intValue(),
                freeMemoryMB: (freeMemory / 1024 / 1024).intValue(),
                usedMemoryMB: (usedMemory / 1024 / 1024).intValue(),
                maxMemoryMB: (maxMemory / 1024 / 1024).intValue(),
                timestamp: new Date()
            ]
            
            render memoryInfo as JSON
            
        } catch (Exception e) {
            log.error("Error getting system memory info: ${e.message}", e)
            render([error: "Unable to get memory information"] as JSON)
        }
    }

    /**
     * Get database connection pool information
     * @return JSON response with connection pool data
     */
    def getDatabaseConnectionInfo() {
        try {
            // This would depend on your specific connection pool implementation
            def connectionInfo = [
                activeConnections: 0,
                idleConnections: 0,
                maxConnections: 0,
                connectionPoolType: "Unknown",
                timestamp: new Date()
            ]
            
            render connectionInfo as JSON
            
        } catch (Exception e) {
            log.error("Error getting database connection info: ${e.message}", e)
            render([error: "Unable to get connection information"] as JSON)
        }
    }

    /**
     * Get periodic operations statistics
     * @return JSON response with statistics
     */
    def getPeriodicOpsStatistics() {
        try {
            def stats = [:]
            
            // Count operations by type
            def operationCounts = PeriodicOpsLog.createCriteria().list {
                projections {
                    groupProperty("processType")
                    count("id")
                }
            }
            
            operationCounts.each { result ->
                stats["${result[0]}Count"] = result[1]
            }
            
            // Get success/failure rates
            def successCount = PeriodicOpsLog.countByStatus(1)
            def failureCount = PeriodicOpsLog.countByStatus(0)
            def totalCount = PeriodicOpsLog.count()
            
            stats.successCount = successCount
            stats.failureCount = failureCount
            stats.totalCount = totalCount
            stats.successRate = totalCount > 0 ? (successCount * 100 / totalCount).round(2) : 0
            
            // Get recent operations
            def recentOps = PeriodicOpsLog.createCriteria().list(max: 5) {
                order("runDate", "desc")
                order("startTime", "desc")
            }
            
            stats.recentOperations = recentOps.collect { op ->
                [
                    processType: op.processType,
                    runDate: op.runDate?.format('yyyy-MM-dd'),
                    status: getStatusDescription(op.status),
                    duration: calculateDuration(op.startTime, op.endTime)
                ]
            }
            
            stats.timestamp = new Date()
            
            render stats as JSON
            
        } catch (Exception e) {
            log.error("Error getting periodic ops statistics: ${e.message}", e)
            render([error: "Unable to get statistics"] as JSON)
        }
    }

    /**
     * Export periodic operations log to CSV
     */
    def exportPeriodicOpsLog() {
        try {
            def logs = PeriodicOpsLog.createCriteria().list {
                order("runDate", "desc")
                order("startTime", "desc")
            }
            
            def csv = new StringBuilder()
            csv.append("ID,Run Date,Process Type,Start Time,End Time,Status,Duration\n")
            
            logs.each { log ->
                csv.append("${log.id},")
                csv.append("${log.runDate?.format('yyyy-MM-dd')},")
                csv.append("${log.processType},")
                csv.append("${log.startTime},")
                csv.append("${log.endTime ?: 'N/A'},")
                csv.append("${getStatusDescription(log.status)},")
                csv.append("${calculateDuration(log.startTime, log.endTime)}\n")
            }
            
            response.setHeader("Content-disposition", "attachment; filename=\"periodic_ops_log_${new Date().format('yyyyMMdd')}.csv\"")
            response.contentType = "text/csv"
            response.outputStream << csv.toString().bytes
            response.outputStream.flush()
            
        } catch (Exception e) {
            log.error("Error exporting periodic ops log: ${e.message}", e)
            flash.message = "Export failed: ${e.message} |error|alert"
            redirect(action: "periodicOpsLog")
        }
    }

    /**
     * Clear old periodic operations logs
     * @return JSON response with cleanup results
     */
    @Transactional
    def clearOldPeriodicOpsLogs() {
        try {
            def daysToKeep = params.daysToKeep ? params.daysToKeep.toInteger() : 90
            def cutoffDate = new Date() - daysToKeep
            
            def oldLogs = PeriodicOpsLog.findAllByRunDateLessThan(cutoffDate)
            def deletedCount = 0
            
            oldLogs.each { log ->
                try {
                    log.delete(flush: true)
                    deletedCount++
                } catch (Exception e) {
                    log.error("Error deleting log ${log.id}: ${e.message}", e)
                }
            }
            
            // Log the cleanup operation
            AuditLogService.insert('870', 'UTL00870', 
                "Periodic ops log cleanup completed - ${deletedCount} records deleted", 
                'Utility', null, null, null, null)
            
            render([
                success: true,
                deletedCount: deletedCount,
                cutoffDate: cutoffDate,
                message: "Old logs cleaned up successfully"
            ] as JSON)
            
        } catch (Exception e) {
            log.error("Error clearing old periodic ops logs: ${e.message}", e)
            render([
                success: false,
                error: "Log cleanup failed: ${e.message}"
            ] as JSON)
        }
    }

    /**
     * Get status description from status code
     * @param status The status code
     * @return Status description string
     */
    private String getStatusDescription(Integer status) {
        switch (status) {
            case 0:
                return "Failed"
            case 1:
                return "Success"
            default:
                return "Unknown"
        }
    }

    /**
     * Calculate duration between start and end times
     * @param startTime Start time string
     * @param endTime End time string
     * @return Duration string
     */
    private String calculateDuration(String startTime, String endTime) {
        try {
            if (!startTime || !endTime) {
                return "N/A"
            }
            
            def start = Date.parse("EEE MMM dd HH:mm:ss zzz yyyy", startTime)
            def end = Date.parse("EEE MMM dd HH:mm:ss zzz yyyy", endTime)
            
            def duration = end.time - start.time
            def minutes = duration / (1000 * 60)
            def seconds = (duration % (1000 * 60)) / 1000
            
            if (minutes > 0) {
                return "${minutes.intValue()}m ${seconds.intValue()}s"
            } else {
                return "${seconds.intValue()}s"
            }
        } catch (Exception e) {
            return "N/A"
        }
    }

    /**
     * Clean up GORM session to prevent memory issues
     */
    private void cleanUpGorm() {
        try {
            def session = sessionFactory.currentSession
            session.flush()
            session.clear()
            def propertyInstanceMap = org.codehaus.groovy.grails.plugins.DomainClassGrailsPlugin.PROPERTY_INSTANCE_MAP
            propertyInstanceMap.get().clear()
        } catch (Exception e) {
            log.error("Error cleaning up GORM: ${e.message}", e)
            throw e
        }
    }
}
