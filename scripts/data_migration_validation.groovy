#!/usr/bin/env groovy

/**
 * QwikBanka Data Migration Validation Script
 * This script validates data integrity after migration and system upgrades
 */

@Grab('org.postgresql:postgresql:42.3.1')

import groovy.sql.Sql
import java.sql.DriverManager

println "🔍 QwikBanka Data Migration Validation"
println "======================================"

// Configuration
def dbUrl = System.getProperty('db.url', '******************************************')
def dbUser = System.getProperty('db.user', 'postgres')
def dbPassword = System.getProperty('db.password', 'changeme')

println "Database Configuration:"
println "  URL: ${dbUrl}"
println "  User: ${dbUser}"
println ""

// Validation results tracking
class ValidationResults {
    List<String> passed = []
    List<String> failed = []
    List<String> warnings = []
    Map<String, Object> metrics = [:]
    
    void addPassed(String test) {
        passed << test
        println "✅ PASSED: ${test}"
    }
    
    void addFailed(String test, String reason = null) {
        def message = reason ? "${test} - ${reason}" : test
        failed << message
        println "❌ FAILED: ${message}"
    }
    
    void addWarning(String test, String reason = null) {
        def message = reason ? "${test} - ${reason}" : test
        warnings << message
        println "⚠️ WARNING: ${message}"
    }
    
    void addMetric(String name, Object value) {
        metrics[name] = value
    }
    
    void printSummary() {
        println "\n" + "="*60
        println "📊 VALIDATION SUMMARY"
        println "="*60
        
        println "\n✅ PASSED TESTS (${passed.size()}):"
        passed.each { println "   ${it}" }
        
        if (warnings.size() > 0) {
            println "\n⚠️ WARNINGS (${warnings.size()}):"
            warnings.each { println "   ${it}" }
        }
        
        if (failed.size() > 0) {
            println "\n❌ FAILED TESTS (${failed.size()}):"
            failed.each { println "   ${it}" }
        }
        
        println "\n📈 METRICS:"
        metrics.each { key, value ->
            println "   ${key}: ${value}"
        }
        
        def totalTests = passed.size() + failed.size()
        def successRate = totalTests > 0 ? (passed.size() * 100.0 / totalTests) : 0
        
        println "\n🎯 OVERALL RESULT:"
        println "   Success Rate: ${successRate.round(2)}%"
        
        if (failed.size() == 0) {
            println "   Status: ✅ ALL VALIDATIONS PASSED"
        } else if (failed.size() <= 2) {
            println "   Status: ⚠️ MINOR ISSUES DETECTED"
        } else {
            println "   Status: ❌ CRITICAL ISSUES DETECTED"
        }
    }
}

def results = new ValidationResults()

try {
    // Connect to database
    def sql = Sql.newInstance(dbUrl, dbUser, dbPassword, 'org.postgresql.Driver')
    
    println "🔗 Connected to database successfully"
    
    // =====================================================
    // 1. SCHEMA VALIDATION
    // =====================================================
    
    println "\n1. 🏗️ Schema Validation..."
    
    // Check if migration tables exist
    def migrationTableExists = sql.firstRow("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'migration_log'
        )
    """)[0]
    
    if (migrationTableExists) {
        results.addPassed("Migration log table exists")
    } else {
        results.addFailed("Migration log table missing")
    }
    
    // Check security enhancement columns
    def securityColumns = sql.rows("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'user_master' 
        AND column_name IN ('password_type', 'password_migrated', 'failed_login_count')
    """)
    
    if (securityColumns.size() >= 3) {
        results.addPassed("Security enhancement columns added to user_master")
    } else {
        results.addFailed("Security enhancement columns missing", "Found ${securityColumns.size()}/3 columns")
    }
    
    // Check performance optimization indexes
    def performanceIndexes = sql.rows("""
        SELECT indexname 
        FROM pg_indexes 
        WHERE tablename IN ('customer', 'deposit', 'loan') 
        AND indexname LIKE 'idx_%'
    """)
    
    if (performanceIndexes.size() >= 10) {
        results.addPassed("Performance optimization indexes created")
        results.addMetric("Performance Indexes", performanceIndexes.size())
    } else {
        results.addWarning("Insufficient performance indexes", "Found ${performanceIndexes.size()} indexes")
    }
    
    // =====================================================
    // 2. DATA INTEGRITY VALIDATION
    // =====================================================
    
    println "\n2. 🔒 Data Integrity Validation..."
    
    // Check for orphaned records
    def orphanedDeposits = sql.firstRow("""
        SELECT COUNT(*) as count 
        FROM deposit d 
        LEFT JOIN customer c ON d.customer_id = c.id 
        WHERE c.id IS NULL
    """).count
    
    if (orphanedDeposits == 0) {
        results.addPassed("No orphaned deposit records")
    } else {
        results.addFailed("Orphaned deposit records found", "${orphanedDeposits} records")
    }
    
    def orphanedLoans = sql.firstRow("""
        SELECT COUNT(*) as count 
        FROM loan l 
        LEFT JOIN customer c ON l.customer_id = c.id 
        WHERE c.id IS NULL
    """).count
    
    if (orphanedLoans == 0) {
        results.addPassed("No orphaned loan records")
    } else {
        results.addFailed("Orphaned loan records found", "${orphanedLoans} records")
    }
    
    // Check for duplicate customer IDs
    def duplicateCustomerIds = sql.firstRow("""
        SELECT COUNT(*) as count 
        FROM (
            SELECT customer_id 
            FROM customer 
            GROUP BY customer_id 
            HAVING COUNT(*) > 1
        ) duplicates
    """).count
    
    if (duplicateCustomerIds == 0) {
        results.addPassed("No duplicate customer IDs")
    } else {
        results.addFailed("Duplicate customer IDs found", "${duplicateCustomerIds} duplicates")
    }
    
    // =====================================================
    // 3. SECURITY VALIDATION
    // =====================================================
    
    println "\n3. 🛡️ Security Validation..."
    
    // Check password migration status
    def passwordMigrationStats = sql.firstRow("""
        SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN password_type = 'BCRYPT' THEN 1 END) as bcrypt_users,
            COUNT(CASE WHEN password_type = 'MD5' THEN 1 END) as md5_users
        FROM user_master
    """)
    
    results.addMetric("Total Users", passwordMigrationStats.total_users)
    results.addMetric("BCrypt Users", passwordMigrationStats.bcrypt_users)
    results.addMetric("MD5 Users", passwordMigrationStats.md5_users)
    
    if (passwordMigrationStats.total_users > 0) {
        def migrationRate = (passwordMigrationStats.bcrypt_users * 100.0) / passwordMigrationStats.total_users
        if (migrationRate >= 80) {
            results.addPassed("Password migration progress acceptable (${migrationRate.round(1)}%)")
        } else {
            results.addWarning("Password migration incomplete", "${migrationRate.round(1)}% migrated")
        }
    }
    
    // Check for users with excessive failed login attempts
    def lockedUsers = sql.firstRow("""
        SELECT COUNT(*) as count 
        FROM user_master 
        WHERE failed_login_count >= 5
    """).count
    
    if (lockedUsers == 0) {
        results.addPassed("No users with excessive failed login attempts")
    } else {
        results.addWarning("Users with high failed login count", "${lockedUsers} users")
    }
    
    // Check security audit log
    def auditLogExists = sql.firstRow("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'security_audit_log'
        )
    """)[0]
    
    if (auditLogExists) {
        results.addPassed("Security audit log table exists")
        
        def auditRecords = sql.firstRow("SELECT COUNT(*) as count FROM security_audit_log").count
        results.addMetric("Audit Records", auditRecords)
    } else {
        results.addFailed("Security audit log table missing")
    }
    
    // =====================================================
    // 4. PERFORMANCE VALIDATION
    // =====================================================
    
    println "\n4. ⚡ Performance Validation..."
    
    // Check materialized views
    def materializedViews = sql.rows("""
        SELECT matviewname 
        FROM pg_matviews 
        WHERE schemaname = 'public'
    """)
    
    if (materializedViews.size() >= 2) {
        results.addPassed("Materialized views created for performance")
        results.addMetric("Materialized Views", materializedViews.size())
    } else {
        results.addWarning("Missing materialized views", "Found ${materializedViews.size()} views")
    }
    
    // Test query performance
    def startTime = System.currentTimeMillis()
    def customerCount = sql.firstRow("SELECT COUNT(*) as count FROM customer").count
    def endTime = System.currentTimeMillis()
    def queryTime = endTime - startTime
    
    results.addMetric("Customer Count", customerCount)
    results.addMetric("Customer Count Query Time (ms)", queryTime)
    
    if (queryTime < 1000) {
        results.addPassed("Customer count query performance acceptable")
    } else {
        results.addWarning("Slow customer count query", "${queryTime}ms")
    }
    
    // Check table sizes
    def tableSizes = sql.rows("""
        SELECT 
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
            pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
        FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename IN ('customer', 'deposit', 'loan', 'user_master')
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
    """)
    
    tableSizes.each { table ->
        results.addMetric("${table.tablename} size", table.size)
    }
    
    // =====================================================
    // 5. BUSINESS LOGIC VALIDATION
    // =====================================================
    
    println "\n5. 💼 Business Logic Validation..."
    
    // Check customer-deposit relationships
    def customerDepositConsistency = sql.firstRow("""
        SELECT COUNT(*) as count 
        FROM customer c 
        INNER JOIN deposit d ON c.id = d.customer_id 
        WHERE c.branch_id != d.branch_id
    """).count
    
    if (customerDepositConsistency == 0) {
        results.addPassed("Customer-deposit branch consistency maintained")
    } else {
        results.addFailed("Customer-deposit branch inconsistency", "${customerDepositConsistency} mismatches")
    }
    
    // Check for negative balances
    def negativeBalances = sql.firstRow("""
        SELECT COUNT(*) as count 
        FROM deposit 
        WHERE ledger_bal_amt < 0 AND type_id NOT IN (
            SELECT id FROM lov_item WHERE item_value = 'OVERDRAFT'
        )
    """).count
    
    if (negativeBalances == 0) {
        results.addPassed("No inappropriate negative balances")
    } else {
        results.addWarning("Negative balances found", "${negativeBalances} accounts")
    }
    
    // Check loan-customer relationships
    def loanCustomerConsistency = sql.firstRow("""
        SELECT COUNT(*) as count 
        FROM loan l 
        INNER JOIN customer c ON l.customer_id = c.id 
        WHERE l.branch_id != c.branch_id
    """).count
    
    if (loanCustomerConsistency == 0) {
        results.addPassed("Loan-customer branch consistency maintained")
    } else {
        results.addFailed("Loan-customer branch inconsistency", "${loanCustomerConsistency} mismatches")
    }
    
    // =====================================================
    // 6. SYSTEM CONFIGURATION VALIDATION
    // =====================================================
    
    println "\n6. ⚙️ System Configuration Validation..."
    
    // Check secure configuration table
    def secureConfigExists = sql.firstRow("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'secure_configuration'
        )
    """)[0]
    
    if (secureConfigExists) {
        results.addPassed("Secure configuration table exists")
        
        def configCount = sql.firstRow("SELECT COUNT(*) as count FROM secure_configuration").count
        results.addMetric("Configuration Items", configCount)
        
        // Check for essential configurations
        def essentialConfigs = [
            'SECURITY.PASSWORD_MIN_LENGTH',
            'SECURITY.MAX_LOGIN_ATTEMPTS',
            'SECURITY.SESSION_TIMEOUT'
        ]
        
        essentialConfigs.each { configKey ->
            def configExists = sql.firstRow("""
                SELECT EXISTS (
                    SELECT 1 FROM secure_configuration 
                    WHERE config_key = ?
                )
            """, [configKey])[0]
            
            if (configExists) {
                results.addPassed("Essential configuration exists: ${configKey}")
            } else {
                results.addFailed("Missing essential configuration: ${configKey}")
            }
        }
    } else {
        results.addFailed("Secure configuration table missing")
    }
    
    // =====================================================
    // 7. MIGRATION LOG VALIDATION
    // =====================================================
    
    println "\n7. 📋 Migration Log Validation..."
    
    if (migrationTableExists) {
        def migrations = sql.rows("""
            SELECT migration_name, migration_version, executed_at, success 
            FROM migration_log 
            ORDER BY executed_at
        """)
        
        results.addMetric("Total Migrations", migrations.size())
        
        def successfulMigrations = migrations.findAll { it.success }
        def failedMigrations = migrations.findAll { !it.success }
        
        results.addMetric("Successful Migrations", successfulMigrations.size())
        results.addMetric("Failed Migrations", failedMigrations.size())
        
        if (failedMigrations.size() == 0) {
            results.addPassed("All migrations executed successfully")
        } else {
            results.addFailed("Failed migrations detected", "${failedMigrations.size()} failures")
        }
        
        // Check for required migrations
        def requiredMigrations = [
            '001_security_enhancements',
            '002_performance_optimizations'
        ]
        
        requiredMigrations.each { migrationName ->
            def migrationExists = migrations.find { it.migration_name == migrationName }
            if (migrationExists) {
                results.addPassed("Required migration executed: ${migrationName}")
            } else {
                results.addFailed("Missing required migration: ${migrationName}")
            }
        }
    }
    
    sql.close()
    
} catch (Exception e) {
    results.addFailed("Database connection or query error", e.message)
    println "❌ Error: ${e.message}"
    e.printStackTrace()
}

// Print final summary
results.printSummary()

println "\n" + "="*60
println "Data migration validation completed at: ${new Date()}"
println "="*60

// Exit with appropriate code
System.exit(results.failed.size() > 0 ? 1 : 0)
