#!/usr/bin/env groovy

/**
 * QwikBanka Customer API Load Testing Script
 * This script performs comprehensive load testing on the Customer API endpoints
 */

@Grab('org.apache.httpcomponents:httpclient:4.5.13')
@Grab('com.fasterxml.jackson.core:jackson-databind:2.13.0')

import groovy.json.JsonBuilder
import groovy.json.JsonSlurper
import org.apache.http.client.methods.*
import org.apache.http.entity.StringEntity
import org.apache.http.impl.client.HttpClients
import org.apache.http.util.EntityUtils
import java.util.concurrent.Executors
import java.util.concurrent.Future
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger

println "🚀 QwikBanka Customer API Load Testing"
println "======================================"

// Configuration
def baseUrl = System.getProperty('api.base.url', 'http://localhost:8080/api/v1')
def authToken = System.getProperty('api.auth.token', 'test-token')
def concurrentUsers = Integer.parseInt(System.getProperty('load.concurrent.users', '50'))
def testDurationMinutes = Integer.parseInt(System.getProperty('load.duration.minutes', '5'))
def rampUpSeconds = Integer.parseInt(System.getProperty('load.rampup.seconds', '30'))

println "Configuration:"
println "  Base URL: ${baseUrl}"
println "  Concurrent Users: ${concurrentUsers}"
println "  Test Duration: ${testDurationMinutes} minutes"
println "  Ramp-up Time: ${rampUpSeconds} seconds"
println ""

// Metrics tracking
class LoadTestMetrics {
    AtomicInteger totalRequests = new AtomicInteger(0)
    AtomicInteger successfulRequests = new AtomicInteger(0)
    AtomicInteger failedRequests = new AtomicInteger(0)
    List<Long> responseTimes = Collections.synchronizedList([])
    Map<String, AtomicInteger> errorCounts = [:]
    Date startTime
    Date endTime
    
    void recordRequest(boolean success, long responseTime, String error = null) {
        totalRequests.incrementAndGet()
        if (success) {
            successfulRequests.incrementAndGet()
        } else {
            failedRequests.incrementAndGet()
            if (error) {
                errorCounts.computeIfAbsent(error, { new AtomicInteger(0) }).incrementAndGet()
            }
        }
        responseTimes.add(responseTime)
    }
    
    Map getStatistics() {
        def sortedTimes = responseTimes.sort()
        def size = sortedTimes.size()
        
        return [
            totalRequests: totalRequests.get(),
            successfulRequests: successfulRequests.get(),
            failedRequests: failedRequests.get(),
            successRate: size > 0 ? (successfulRequests.get() * 100.0 / totalRequests.get()) : 0,
            averageResponseTime: size > 0 ? (sortedTimes.sum() / size) : 0,
            minResponseTime: size > 0 ? sortedTimes.first() : 0,
            maxResponseTime: size > 0 ? sortedTimes.last() : 0,
            p50ResponseTime: size > 0 ? sortedTimes[(int)(size * 0.5)] : 0,
            p90ResponseTime: size > 0 ? sortedTimes[(int)(size * 0.9)] : 0,
            p95ResponseTime: size > 0 ? sortedTimes[(int)(size * 0.95)] : 0,
            p99ResponseTime: size > 0 ? sortedTimes[(int)(size * 0.99)] : 0,
            requestsPerSecond: startTime && endTime ? 
                totalRequests.get() / ((endTime.time - startTime.time) / 1000.0) : 0,
            errorBreakdown: errorCounts.collectEntries { k, v -> [k, v.get()] }
        ]
    }
}

def metrics = new LoadTestMetrics()

// HTTP Client setup
def httpClient = HttpClients.createDefault()
def jsonSlurper = new JsonSlurper()

// Test data
def testCustomers = []
def createdCustomerIds = Collections.synchronizedList([])

// API Helper methods
def makeRequest(String method, String endpoint, Map body = null) {
    def url = "${baseUrl}${endpoint}"
    def request
    
    switch (method.toUpperCase()) {
        case 'GET':
            request = new HttpGet(url)
            break
        case 'POST':
            request = new HttpPost(url)
            if (body) {
                def json = new JsonBuilder(body).toString()
                request.setEntity(new StringEntity(json, 'UTF-8'))
            }
            break
        case 'PUT':
            request = new HttpPut(url)
            if (body) {
                def json = new JsonBuilder(body).toString()
                request.setEntity(new StringEntity(json, 'UTF-8'))
            }
            break
        case 'DELETE':
            request = new HttpDelete(url)
            break
        default:
            throw new IllegalArgumentException("Unsupported method: ${method}")
    }
    
    // Set headers
    request.setHeader('Content-Type', 'application/json')
    request.setHeader('Authorization', "Bearer ${authToken}")
    
    def startTime = System.currentTimeMillis()
    try {
        def response = httpClient.execute(request)
        def endTime = System.currentTimeMillis()
        def responseTime = endTime - startTime
        
        def statusCode = response.getStatusLine().getStatusCode()
        def responseBody = EntityUtils.toString(response.getEntity())
        
        def success = statusCode >= 200 && statusCode < 300
        def error = success ? null : "HTTP ${statusCode}"
        
        metrics.recordRequest(success, responseTime, error)
        
        return [
            success: success,
            statusCode: statusCode,
            responseTime: responseTime,
            body: success ? jsonSlurper.parseText(responseBody) : responseBody
        ]
    } catch (Exception e) {
        def endTime = System.currentTimeMillis()
        def responseTime = endTime - startTime
        metrics.recordRequest(false, responseTime, e.class.simpleName)
        return [
            success: false,
            error: e.message,
            responseTime: responseTime
        ]
    }
}

// Test scenarios
def createCustomerScenario(int userId) {
    def customerData = [
        firstName: "LoadTest${userId}",
        middleName: "User",
        lastName: "Customer${System.currentTimeMillis()}",
        email: "loadtest${userId}@example.com",
        phoneNumber: "+1234567${String.format('%03d', userId)}",
        birthDate: "1990-01-15",
        identificationNumber: "LOAD${userId}${System.currentTimeMillis()}",
        branchId: 1,
        customerType: "INDIVIDUAL",
        taxExempt: false
    ]
    
    def result = makeRequest('POST', '/customers', customerData)
    if (result.success && result.body?.data?.id) {
        createdCustomerIds.add(result.body.data.id)
    }
    return result
}

def getCustomerScenario() {
    if (createdCustomerIds.isEmpty()) {
        return makeRequest('GET', '/customers?max=20')
    }
    
    def randomId = createdCustomerIds[new Random().nextInt(createdCustomerIds.size())]
    return makeRequest('GET', "/customers/${randomId}")
}

def listCustomersScenario() {
    def offset = new Random().nextInt(100)
    return makeRequest('GET', "/customers?offset=${offset}&max=20")
}

def searchCustomersScenario() {
    def searchTerms = ['LoadTest', 'User', 'Customer', 'Test']
    def searchTerm = searchTerms[new Random().nextInt(searchTerms.size())]
    return makeRequest('GET', "/customers/search?q=${searchTerm}&max=10")
}

def updateCustomerScenario() {
    if (createdCustomerIds.isEmpty()) {
        return [success: false, error: 'No customers to update']
    }
    
    def randomId = createdCustomerIds[new Random().nextInt(createdCustomerIds.size())]
    def updateData = [
        email: "updated${System.currentTimeMillis()}@example.com",
        phoneNumber: "+1987654321"
    ]
    
    return makeRequest('PUT', "/customers/${randomId}", updateData)
}

// Load test execution
def runLoadTest() {
    println "🏁 Starting load test..."
    metrics.startTime = new Date()
    
    def executor = Executors.newFixedThreadPool(concurrentUsers)
    def futures = []
    def testEndTime = System.currentTimeMillis() + (testDurationMinutes * 60 * 1000)
    
    // Ramp-up phase
    def rampUpDelay = (rampUpSeconds * 1000) / concurrentUsers
    
    (1..concurrentUsers).each { userId ->
        def future = executor.submit({
            // Ramp-up delay
            Thread.sleep(userId * rampUpDelay)
            
            def random = new Random()
            
            while (System.currentTimeMillis() < testEndTime) {
                try {
                    // Random scenario selection
                    def scenario = random.nextInt(100)
                    
                    if (scenario < 30) {
                        // 30% - List customers
                        listCustomersScenario()
                    } else if (scenario < 50) {
                        // 20% - Get specific customer
                        getCustomerScenario()
                    } else if (scenario < 70) {
                        // 20% - Search customers
                        searchCustomersScenario()
                    } else if (scenario < 85) {
                        // 15% - Create customer
                        createCustomerScenario(userId)
                    } else {
                        // 15% - Update customer
                        updateCustomerScenario()
                    }
                    
                    // Think time between requests
                    Thread.sleep(random.nextInt(1000) + 500) // 0.5-1.5 seconds
                    
                } catch (Exception e) {
                    println "Error in user ${userId}: ${e.message}"
                }
            }
        })
        futures.add(future)
    }
    
    // Wait for all threads to complete
    futures.each { it.get() }
    executor.shutdown()
    
    metrics.endTime = new Date()
    println "✅ Load test completed!"
}

// Progress monitoring
def startProgressMonitor() {
    def monitorThread = Thread.start {
        while (!Thread.currentThread().isInterrupted()) {
            try {
                Thread.sleep(10000) // Report every 10 seconds
                def stats = metrics.getStatistics()
                println "📊 Progress: ${stats.totalRequests} requests, ${stats.successRate.round(2)}% success rate, ${stats.averageResponseTime.round(0)}ms avg response time"
            } catch (InterruptedException e) {
                break
            }
        }
    }
    return monitorThread
}

// Main execution
try {
    def monitorThread = startProgressMonitor()
    runLoadTest()
    monitorThread.interrupt()
    
    // Final results
    def finalStats = metrics.getStatistics()
    
    println "\n" + "="*60
    println "📈 LOAD TEST RESULTS"
    println "="*60
    
    println "\n📊 Request Statistics:"
    println "  Total Requests: ${finalStats.totalRequests}"
    println "  Successful Requests: ${finalStats.successfulRequests}"
    println "  Failed Requests: ${finalStats.failedRequests}"
    println "  Success Rate: ${finalStats.successRate.round(2)}%"
    println "  Requests per Second: ${finalStats.requestsPerSecond.round(2)}"
    
    println "\n⏱️ Response Time Statistics (ms):"
    println "  Average: ${finalStats.averageResponseTime.round(0)}"
    println "  Minimum: ${finalStats.minResponseTime}"
    println "  Maximum: ${finalStats.maxResponseTime}"
    println "  50th Percentile: ${finalStats.p50ResponseTime}"
    println "  90th Percentile: ${finalStats.p90ResponseTime}"
    println "  95th Percentile: ${finalStats.p95ResponseTime}"
    println "  99th Percentile: ${finalStats.p99ResponseTime}"
    
    if (finalStats.errorBreakdown) {
        println "\n❌ Error Breakdown:"
        finalStats.errorBreakdown.each { error, count ->
            println "  ${error}: ${count}"
        }
    }
    
    println "\n🎯 Performance Assessment:"
    if (finalStats.successRate >= 99.5) {
        println "  ✅ EXCELLENT: Success rate above 99.5%"
    } else if (finalStats.successRate >= 95) {
        println "  ✅ GOOD: Success rate above 95%"
    } else if (finalStats.successRate >= 90) {
        println "  ⚠️ ACCEPTABLE: Success rate above 90%"
    } else {
        println "  ❌ POOR: Success rate below 90%"
    }
    
    if (finalStats.p95ResponseTime <= 500) {
        println "  ✅ EXCELLENT: 95th percentile response time under 500ms"
    } else if (finalStats.p95ResponseTime <= 1000) {
        println "  ✅ GOOD: 95th percentile response time under 1 second"
    } else if (finalStats.p95ResponseTime <= 2000) {
        println "  ⚠️ ACCEPTABLE: 95th percentile response time under 2 seconds"
    } else {
        println "  ❌ POOR: 95th percentile response time over 2 seconds"
    }
    
    if (finalStats.requestsPerSecond >= 100) {
        println "  ✅ EXCELLENT: Throughput above 100 requests/second"
    } else if (finalStats.requestsPerSecond >= 50) {
        println "  ✅ GOOD: Throughput above 50 requests/second"
    } else if (finalStats.requestsPerSecond >= 20) {
        println "  ⚠️ ACCEPTABLE: Throughput above 20 requests/second"
    } else {
        println "  ❌ POOR: Throughput below 20 requests/second"
    }
    
    println "\n📝 Test Summary:"
    println "  Duration: ${testDurationMinutes} minutes"
    println "  Concurrent Users: ${concurrentUsers}"
    println "  Total Data Created: ${createdCustomerIds.size()} customers"
    println "  Test Completed: ${new Date()}"
    
} catch (Exception e) {
    println "❌ Load test failed: ${e.message}"
    e.printStackTrace()
} finally {
    httpClient.close()
}

println "\n" + "="*60
println "Load test completed at: ${new Date()}"
println "="*60
