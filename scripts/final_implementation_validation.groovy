#!/usr/bin/env groovy

/**
 * Final Implementation Validation Script
 * Validates that all critical tasks from the documentation are 100% implemented
 */

println "🔍 QWIKBANKA FINAL IMPLEMENTATION VALIDATION"
println "=" * 60

def validationResults = []
def criticalIssues = []
def warnings = []

// =====================================================
// PHASE 1: SECURITY VALIDATION (CRITICAL)
// =====================================================

println "\n🔒 PHASE 1: Security Implementation Validation"
println "=" * 50

// 1.1 BCrypt Password Service
def securePasswordServiceFile = new File("grails-app/services/org/icbs/security/SecurePasswordService.groovy")
if (securePasswordServiceFile.exists()) {
    def content = securePasswordServiceFile.text
    if (content.contains("BCryptPasswordEncoder") && 
        content.contains("hashPassword") && 
        content.contains("verifyPassword") &&
        content.contains("isValidPassword")) {
        validationResults << "✅ SecurePasswordService implemented with BCrypt and validation"
    } else {
        criticalIssues << "❌ SecurePasswordService missing required methods"
    }
} else {
    criticalIssues << "❌ SecurePasswordService.groovy not found"
}

// 1.2 UserMaster Security Updates
def userMasterFile = new File("grails-app/domain/org/icbs/admin/UserMaster.groovy")
if (userMasterFile.exists()) {
    def content = userMasterFile.text
    if (content.contains("passwordType") && 
        content.contains("validatePassword") &&
        content.contains("migrateToSecurePassword") &&
        content.contains("beforeInsert") &&
        content.contains("beforeUpdate")) {
        validationResults << "✅ UserMaster enhanced with complete security features"
    } else {
        criticalIssues << "❌ UserMaster security enhancements incomplete"
    }
} else {
    criticalIssues << "❌ UserMaster domain not found"
}

// 1.3 SQL Injection Fixes
def sqlInjectionFiles = [
    "grails-app/services/org/icbs/periodicops/GlPeriodicOpsService.groovy",
    "grails-app/services/org/icbs/periodicops/LoanPeriodicOpsService.groovy",
    "grails-app/controllers/org/icbs/loans/LoanAddionalInfoController.groovy",
    "grails-app/controllers/org/icbs/gl/CashInBankController.groovy",
    "grails-app/controllers/org/icbs/admin/InstitutionController.groovy"
]

def sqlInjectionFixed = 0
sqlInjectionFiles.each { filePath ->
    def file = new File(filePath)
    if (file.exists()) {
        def content = file.text
        // Check for parameterized queries (should contain ? placeholders and no string concatenation)
        if (content.contains("sql.rows(") && content.contains("?") && 
            !content.contains('+"') && !content.contains("'${")) {
            sqlInjectionFixed++
        }
    }
}

if (sqlInjectionFixed >= 4) {
    validationResults << "✅ SQL injection vulnerabilities fixed in ${sqlInjectionFixed}/${sqlInjectionFiles.size()} files"
} else {
    criticalIssues << "❌ SQL injection fixes incomplete: ${sqlInjectionFixed}/${sqlInjectionFiles.size()} files fixed"
}

// 1.4 XSS Prevention
def xssInterceptorFile = new File("grails-app/interceptors/org/icbs/security/XssPreventionInterceptor.groovy")
if (xssInterceptorFile.exists()) {
    def content = xssInterceptorFile.text
    if (content.contains("sanitizeInput") && 
        content.contains("X-XSS-Protection") &&
        content.contains("Content-Security-Policy") &&
        content.contains("X-Content-Type-Options")) {
        validationResults << "✅ XSS prevention interceptor fully implemented"
    } else {
        criticalIssues << "❌ XSS prevention interceptor incomplete"
    }
} else {
    criticalIssues << "❌ XSS prevention interceptor not found"
}

// 1.5 CSRF Protection
def securityConfigFile = new File("grails-app/conf/spring/SecurityConfig.groovy")
def mainLayoutFile = new File("grails-app/views/layouts/main.gsp")
def applicationJsFile = new File("grails-app/assets/javascripts/application.js")

def csrfImplemented = 0
if (securityConfigFile.exists() && securityConfigFile.text.contains("csrf") && 
    securityConfigFile.text.contains("CookieCsrfTokenRepository")) {
    csrfImplemented++
}
if (mainLayoutFile.exists() && mainLayoutFile.text.contains("_csrf")) {
    csrfImplemented++
}
if (applicationJsFile.exists() && applicationJsFile.text.contains("getCSRFToken") && 
    applicationJsFile.text.contains("ajaxSetup")) {
    csrfImplemented++
}

if (csrfImplemented >= 3) {
    validationResults << "✅ CSRF protection fully implemented (Config, Layout, JavaScript)"
} else {
    criticalIssues << "❌ CSRF protection incomplete: ${csrfImplemented}/3 components implemented"
}

// 1.6 Database Credentials Security
def applicationPropertiesFile = new File("grails-app/conf/application.properties")
if (applicationPropertiesFile.exists()) {
    def content = applicationPropertiesFile.text
    if (content.contains('${DB_PASSWORD:') && !content.contains('Micr0banker@1989')) {
        validationResults << "✅ Database credentials externalized and secured"
    } else {
        criticalIssues << "❌ Database credentials still hardcoded"
    }
} else {
    criticalIssues << "❌ application.properties not found"
}

// =====================================================
// PHASE 2: PERFORMANCE VALIDATION
// =====================================================

println "\n⚡ PHASE 2: Performance Optimizations Validation"
println "=" * 50

// 2.1 N+1 Query Fixes in Domain Classes
def domainFiles = [
    "grails-app/domain/org/icbs/cif/Customer.groovy",
    "grails-app/domain/org/icbs/deposit/Deposit.groovy",
    "grails-app/domain/org/icbs/loans/Loan.groovy"
]

def n1QueryFixed = 0
domainFiles.each { filePath ->
    def file = new File(filePath)
    if (file.exists()) {
        def content = file.text
        if (content.contains("batchSize") && content.contains("cache true")) {
            n1QueryFixed++
        }
    }
}

if (n1QueryFixed >= 3) {
    validationResults << "✅ N+1 query optimizations implemented in all ${n1QueryFixed} domain classes"
} else {
    warnings << "⚠️ N+1 query fixes incomplete: ${n1QueryFixed}/3 domain classes optimized"
}

// 2.2 HikariCP Connection Pool
def applicationYmlFile = new File("grails-app/conf/application.yml")
if (applicationYmlFile.exists()) {
    def content = applicationYmlFile.text
    if (content.contains("HikariDataSource") && 
        content.contains("maximum-pool-size") &&
        content.contains("connection-timeout")) {
        validationResults << "✅ HikariCP connection pool configured and optimized"
    } else {
        warnings << "⚠️ HikariCP configuration incomplete"
    }
} else {
    criticalIssues << "❌ application.yml not found"
}

// 2.3 Optimized Services
def optimizedDepositServiceFile = new File("grails-app/services/org/icbs/deposit/OptimizedDepositService.groovy")
if (optimizedDepositServiceFile.exists()) {
    def content = optimizedDepositServiceFile.text
    if (content.contains("@Cacheable") && content.contains("FetchMode.JOIN")) {
        validationResults << "✅ OptimizedDepositService implemented with caching"
    } else {
        warnings << "⚠️ OptimizedDepositService missing caching annotations"
    }
} else {
    warnings << "⚠️ OptimizedDepositService not found"
}

// =====================================================
// PHASE 3: TESTING VALIDATION
// =====================================================

println "\n🧪 PHASE 3: Testing Framework Validation"
println "=" * 50

// 3.1 Security Test Suite
def securityTestFile = new File("src/test/groovy/org/icbs/security/SecurityFixesSpec.groovy")
if (securityTestFile.exists()) {
    def content = securityTestFile.text
    def testCount = content.count("def \"")
    if (testCount >= 15) {
        validationResults << "✅ Comprehensive security test suite with ${testCount} test cases"
    } else {
        warnings << "⚠️ Security test suite has only ${testCount} test cases (recommended: 15+)"
    }
} else {
    warnings << "⚠️ SecurityFixesSpec.groovy not found"
}

// 3.2 Integration Tests
def integrationTestFiles = [
    "src/test/groovy/org/icbs/integration/CustomerIntegrationSpec.groovy",
    "src/test/groovy/org/icbs/performance/PerformanceTestSpec.groovy"
]

def integrationTestsFound = 0
integrationTestFiles.each { filePath ->
    if (new File(filePath).exists()) {
        integrationTestsFound++
    }
}

if (integrationTestsFound >= 2) {
    validationResults << "✅ Integration and performance test suites implemented"
} else {
    warnings << "⚠️ Integration tests incomplete: ${integrationTestsFound}/2 test suites found"
}

// =====================================================
// PHASE 4: INFRASTRUCTURE VALIDATION
// =====================================================

println "\n🏗️ PHASE 4: Infrastructure Validation"
println "=" * 50

// 4.1 Security Dependencies
def buildGradleFile = new File("build.gradle")
if (buildGradleFile.exists()) {
    def content = buildGradleFile.text
    def securityDeps = 0
    if (content.contains("spring-security-crypto")) securityDeps++
    if (content.contains("spring-security-web")) securityDeps++
    if (content.contains("spring-security-config")) securityDeps++
    if (content.contains("HikariCP")) securityDeps++
    
    if (securityDeps >= 4) {
        validationResults << "✅ All security dependencies added to build.gradle"
    } else {
        criticalIssues << "❌ Security dependencies incomplete: ${securityDeps}/4 dependencies found"
    }
} else {
    criticalIssues << "❌ build.gradle not found"
}

// 4.2 Grails Version
def gradlePropertiesFile = new File("gradle.properties")
if (gradlePropertiesFile.exists()) {
    def content = gradlePropertiesFile.text
    if (content.contains("grailsVersion=6.2.")) {
        validationResults << "✅ Grails upgraded to version 6.2.x"
    } else {
        warnings << "⚠️ Grails version may need upgrading"
    }
} else {
    warnings << "⚠️ gradle.properties not found"
}

// =====================================================
// FINAL RESULTS
// =====================================================

println "\n" + "="*60
println "🎯 FINAL VALIDATION RESULTS"
println "="*60

println "\n✅ SUCCESSFUL IMPLEMENTATIONS (${validationResults.size()}):"
validationResults.each { result ->
    println "   ${result}"
}

if (warnings.size() > 0) {
    println "\n⚠️ WARNINGS (${warnings.size()}):"
    warnings.each { warning ->
        println "   ${warning}"
    }
}

if (criticalIssues.size() > 0) {
    println "\n❌ CRITICAL ISSUES (${criticalIssues.size()}):"
    criticalIssues.each { issue ->
        println "   ${issue}"
    }
}

// Calculate completion percentage
def totalTasks = validationResults.size() + warnings.size() + criticalIssues.size()
def completedTasks = validationResults.size()
def completionPercentage = totalTasks > 0 ? (completedTasks * 100 / totalTasks) : 0

println "\n📊 IMPLEMENTATION STATUS:"
println "   Total Tasks: ${totalTasks}"
println "   Completed: ${completedTasks}"
println "   Warnings: ${warnings.size()}"
println "   Critical Issues: ${criticalIssues.size()}"
println "   Completion: ${completionPercentage.round(1)}%"

if (criticalIssues.size() == 0) {
    println "\n🎉 ALL CRITICAL TASKS COMPLETED SUCCESSFULLY!"
    println "   QwikBanka is ready for production deployment."
} else {
    println "\n⚠️ CRITICAL ISSUES MUST BE RESOLVED BEFORE DEPLOYMENT"
}

println "\n" + "="*60
